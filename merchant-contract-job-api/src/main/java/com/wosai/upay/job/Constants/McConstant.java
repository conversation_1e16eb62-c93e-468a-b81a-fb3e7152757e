package com.wosai.upay.job.Constants;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.model.PayParamsModel;

import java.util.Map;

/**
 * 收单机构常量
 *
 * <AUTHOR>
 * @date 2019-09-10
 */
public class McConstant {

    //============= 收单机构 ==============

    /**
     * 拉卡拉
     */
    public static final String ACQUIRER_LKL = "lkl";

    /**
     * 通联
     */
    public static final String ACQUIRER_TONGLIAN = "tonglian";

    /**
     * 通联收银宝
     */
    public static final String ACQUIRER_TONGLIANV2 = "tonglianV2";

    /**
     * 银联商务
     */
    public static final String ACQUIRER_UMS = "ums";

    /**
     * 邮储银行
     */
    public static final String ACQUIRER_PSBC = "psbc";

    /**
     * 邮储银行
     */
    public static final String ACQUIRER_CCB = "ccb";


    /**
     * 拉卡拉V3
     */
    public static final String ACQUIRER_LKLV3 = "lklV3";

    /**
     * 广发
     */
    public static final String ACQUIRER_CGB = "cgb";

    /**
     * 华夏
     */
    public static final String ACQUIRER_HXB = "hxb";

    /**
     * 工商银行
     */
    public static final String ACQUIRER_ICBC = "icbc";


    /**
     * 海科
     */
    public static final String ACQUIRER_HAIKE = "haike";


    /**
     * 富友
     */
    public static final String ACQUIRER_FUYOU = "fuyou";


    /**
     * 平安
     */
    public static final String ACQUIRER_PAB = "pab";

    /**
     * 浙江泰隆商业银行
     */
    public static final String ACQUIRER_ZJTLCB = "zjtlcb";


    //============= 规则组 ==============

    /**
     * 拉卡拉默认报备规则组
     */
    public static final String RULE_GROUP_LKL = "default";

    /**
     * 通联默认报备规则组
     */
    public static final String RULE_GROUP_TONGLIAN = "tonglian";

    /**
     * 通联收银宝默认报备规则组
     */
    public static final String RULE_GROUP_TONGLIAN_V2 = "tonglianV2";
    /**
     * 银联商户默认报备规则组
     */
    public static final String RULE_GROUP_UMS = "ums";

    /**
     * 拉卡拉V3默认报备规则组
     */
    public static final String RULE_GROUP_LKLV3 = "lklV3";

    /**
     * 拉卡拉渠道报备规则组
     */
    public static final String RULE_GROUP_LKLORG = "lklorg";

    /**
     * 广发默认报备规则组
     */
    public static final String RULE_GROUP_CGB = "cgb";

    /**
     * 邮储银行默认报备规则组
     */
    public static final String RULE_GROUP_PSBC = "psbc";

    /**
     * 建设银行默认报备规则组
     */
    public static final String RULE_GROUP_CCB = "ccb";

    /**
     * 华夏银行默认报备规则组
     */
    public static final String RULE_GROUP_HXB = "hxb";

    public static final String RULE_GROUP_LZB = "lzb";

    /**
     * 工商银行默认报备规则组
     */
    public static final String RULE_GROUP_ICBC = "icbc";

    /**
     * 海科规则组
     */
    public static final String RULE_GROUP_HAIKE = "haike";

    /**
     * 海科规则组
     */
    public static final String RULE_GROUP_HAIKE_ORG = "haike-org";

    /**
     * 富友规则组
     */
    public static final String RULE_GROUP_FUYOU = "fuyou";




    public static final String RULE_GROUP_PAB = "pab";

    public static final String RULE_GROUP_CMBC = "cmbc-底价千二";

    /**
     * 通联收银宝-山东
     */
    public static final String RULE_GROUP_TONGLIAN_V2_SD = "tonglianV2-sd";
    /**
     * 浙江泰隆银行规则组
     */
    public static final String RULE_GROUP_ZJTLCB = "zjtlcb";
    /**
     * 国通星驿规则组
     */
    public static final String RULE_GROUP_GUOTONG = "guotong";

    public static final String RULE_GROUP_UMB = "umb";
    /**
     * 江苏银行规则组
     */
    public static final String RULE_GROUP_JSB = "jsb";


    /**
     * 富友小微升级规则组
     */
    public static final String RULE_GROUP_MICROUPGRADE_FUYOU = "microUpgrade2fuyou";

    /**
     * 海科小微升级规则组
     */
    public static final String RULE_GROUP_MICROUPGRADE_HAIKE = "microUpgrade2haike";
    /**
     * 拉卡拉渠道报备规则组
     */
    public static final String RULE_GROUP_MICROUPGRADE_LKLORG = "microUpgrade2lklorg";

}
