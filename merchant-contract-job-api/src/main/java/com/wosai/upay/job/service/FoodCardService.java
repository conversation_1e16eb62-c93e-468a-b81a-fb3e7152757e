package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.job.model.dto.request.FoodCardConfigTradeParamReqDTO;
import com.wosai.upay.job.model.dto.request.FoodCardOpenReqDTO;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.model.dto.response.FoodCardOpenRsqDTO;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * 饭卡支付
 */
@JsonRpcService("/rpc/food-card")
@Validated
public interface FoodCardService {

    /**
     * 判断新旧营业执照类型是否属于小微升级
     *
     * @param newLicenseType 新营业执照类型
     * @param oldLicenseType 旧营业执照类型
     * @return true：小微升级，false：非小微升级
     */
    Boolean isLicenseMicroUpgrade(Integer newLicenseType, Integer oldLicenseType);


    /**
     * 申请开通海科饭卡支付
     * 涉及对接海科的附件上传和饭卡业务申请接口
     * 注意：收单机构接口响应时间不可控，建议rpc超时时间设置长一点
     *
     * @param foodCardOpenReqDTO 开通请求
     * @return 开通结果
     */
    CuaCommonResultDTO openHaikeFoodCard(@Valid FoodCardOpenReqDTO foodCardOpenReqDTO);


    /**
     * 更新海科饭卡支付
     * 涉及对接海科的附件上传和饭卡业务申请接口
     * 注意：收单机构接口响应时间不可控，建议rpc超时时间设置长一点
     *
     * @param foodCardOpenReqDTO 开通请求
     * @return 开通结果
     */
    CuaCommonResultDTO modifyHaikeFoodCard(@Valid FoodCardOpenReqDTO foodCardOpenReqDTO);


    /**
     * 配置饭卡支付交易参数
     *
     * @param tradeParamReqDTO 交易参数请求
     * @return 配置结果
     */
    CuaCommonResultDTO configFoodCardTradeParam(@Valid FoodCardConfigTradeParamReqDTO tradeParamReqDTO);
}
