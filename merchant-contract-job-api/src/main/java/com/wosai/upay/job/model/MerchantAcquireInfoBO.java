package com.wosai.upay.job.model;

import com.wosai.upay.job.model.LklV3Term;
import lombok.Data;

/**
*@Description: 商户在收单机构的基本信息
*<AUTHOR> 
*@Date 2025/7/4 09:51 
*/
@Data
public class MerchantAcquireInfoBO {

    /**
     * 微信进件规则
     */
    private String wxContractRule;


    private String provider;
    /**
     * 商户对应的收单机构号
     */
    private String acquireMerchantId;

    /**
     * 在改收单机构下的支付宝商户号
     */
    private String aliNo;

    /**
     * 在改收单机构下的微信商户号
     */
    private String wxNo;


    /**
     * 在改收单机构下的银联商户号
     */
    private String unionNo;

    /**
     * 商户对应的收单机构终端号(一般只有拉卡拉会有,注意这里对应的是8位的termNo)
     */
    private String lklTermNo;


    /**
     * 商户在收钱吧的第一个门店对应的在拉卡拉的门店号(一般只有拉卡拉会有)
     */
    private String lklShopId;



    /**
     * 海科银联名称
     */
    private String haikeUnionName;

    /**
     * 海科8位终端号 收钱吧自己生成然后同步到海科
     */
    private String haikeTermId;


    /**
     * 拉卡拉V3终端信息
     */
    private LklV3Term lklV3Term;





}
