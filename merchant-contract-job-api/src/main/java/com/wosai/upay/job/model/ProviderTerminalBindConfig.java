package com.wosai.upay.job.model;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Description: 收单机构终端与支付源商户号绑定关系
 * <AUTHOR>
 * @Date 2023/4/7 16:04
 **/

@Data
@Accessors(chain = true)
@NoArgsConstructor
@Getter
public class ProviderTerminalBindConfig {

    private String id;

    private String merchant_sn;

    private String store_sn;

    private String terminal_sn;

    private Integer provider;

    private Integer payway;

    private String sub_mch_id;

    private String provider_terminal_id;

    /**
     * 状态: 1绑定成功；2失败；3解绑
     */
    private Integer status;

    private String result;

    private String request_body;

    private String response_body;

    private Date create_at;

    private Date update_at;

    public ProviderTerminalBindConfig(ProviderTerminal providerTerminal){
        this.merchant_sn = providerTerminal.getMerchant_sn();
        this.store_sn = providerTerminal.getStore_sn();
        this.terminal_sn = providerTerminal.getTerminal_sn();
        this.provider = providerTerminal.getProvider();
        this.provider_terminal_id = providerTerminal.getProvider_terminal_id();
    }

}