package com.wosai.upay.job.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotBlank;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/4/23 1:36 下午
 **/

@Data
@Accessors(chain = true)
public class LklV3Term {

    public static final String TERM_WAIT_BIND = "0";
    public static final String TERM_SUCCESS = "1";
    public static final String TERM_FAIL = "2";
    public static final String TERM_WAIT_UNBIND = "4";
    public static final String TERM_UNBIND = "3";

    /**
     * sqb 终端号
     */
    @NotBlank(message = "终端号不能为空")
    private String devSerialNo;

    /**
     * lkl 终端号
     */
    private String termNo;

    /**
     * lkl 终端id， 与终端号 1：1 对应
     */
    private String termId;

    /**
     * lkl 业务名称
     */
    private String busiTypeName;

    /**
     * lkl 产品代码
     */
    private String productCode;

    /**
     * lkl产品名称
     */
    private String productName;

    /**
     * 终端状态： 0->未报名/报名中  1->审核成功 2->审核失败 3->解绑
     */
    private String status;

    private Long subTaskId;

    /**
     * 激活码
     */
    private String activeNo;

    /**
     * 拉卡拉返回业务类型
     */
    private String busiTypeCode;

}