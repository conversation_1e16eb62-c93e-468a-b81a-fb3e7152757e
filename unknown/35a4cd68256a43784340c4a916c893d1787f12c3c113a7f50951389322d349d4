package com.wosai.upay.job.util;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.wosai.upay.job.config.OssAK;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;

/**
 * Created by x<PERSON><PERSON><PERSON> on 15/11/30.
 */

@Component
public class OSSFileUploader {

    public static final String ENDPOINT_URL = "http://oss-cn-hangzhou.aliyuncs.com/";
    private OSSClient client = OssAK.buildOSSClient(ENDPOINT_URL);
    private String bucketName = "wosai-images";


    public OSSFileUploader() {
    }

    public OSSFileUploader(String bucketName) {
        this.bucketName = bucketName;
    }

    public String upload(String key, String filePath) throws IOException {
        File file = new File(filePath);
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(file.length());
        client.putObject(bucketName, key, file, objectMeta);
        return "https://wosai-images.oss-cn-hangzhou.aliyuncs.com/" + key;
    }


}
