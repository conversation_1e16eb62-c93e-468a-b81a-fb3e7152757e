package com.wosai.upay.job.model.DO;

import java.util.ArrayList;
import java.util.List;

public class MerchantProviderParamsExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public MerchantProviderParamsExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(String value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(String value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(String value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(String value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(String value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(String value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLike(String value) {
            addCriterion("id like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotLike(String value) {
            addCriterion("id not like", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<String> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<String> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(String value1, String value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(String value1, String value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIsNull() {
            addCriterion("merchant_sn is null");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIsNotNull() {
            addCriterion("merchant_sn is not null");
            return (Criteria) this;
        }

        public Criteria andMerchant_snEqualTo(String value) {
            addCriterion("merchant_sn =", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotEqualTo(String value) {
            addCriterion("merchant_sn <>", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snGreaterThan(String value) {
            addCriterion("merchant_sn >", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_sn >=", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLessThan(String value) {
            addCriterion("merchant_sn <", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLessThanOrEqualTo(String value) {
            addCriterion("merchant_sn <=", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLike(String value) {
            addCriterion("merchant_sn like", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotLike(String value) {
            addCriterion("merchant_sn not like", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIn(List<String> values) {
            addCriterion("merchant_sn in", values, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotIn(List<String> values) {
            addCriterion("merchant_sn not in", values, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snBetween(String value1, String value2) {
            addCriterion("merchant_sn between", value1, value2, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotBetween(String value1, String value2) {
            addCriterion("merchant_sn not between", value1, value2, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snIsNull() {
            addCriterion("out_merchant_sn is null");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snIsNotNull() {
            addCriterion("out_merchant_sn is not null");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snEqualTo(String value) {
            addCriterion("out_merchant_sn =", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snNotEqualTo(String value) {
            addCriterion("out_merchant_sn <>", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snGreaterThan(String value) {
            addCriterion("out_merchant_sn >", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snGreaterThanOrEqualTo(String value) {
            addCriterion("out_merchant_sn >=", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snLessThan(String value) {
            addCriterion("out_merchant_sn <", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snLessThanOrEqualTo(String value) {
            addCriterion("out_merchant_sn <=", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snLike(String value) {
            addCriterion("out_merchant_sn like", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snNotLike(String value) {
            addCriterion("out_merchant_sn not like", value, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snIn(List<String> values) {
            addCriterion("out_merchant_sn in", values, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snNotIn(List<String> values) {
            addCriterion("out_merchant_sn not in", values, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snBetween(String value1, String value2) {
            addCriterion("out_merchant_sn between", value1, value2, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andOut_merchant_snNotBetween(String value1, String value2) {
            addCriterion("out_merchant_sn not between", value1, value2, "out_merchant_sn");
            return (Criteria) this;
        }

        public Criteria andChannel_noIsNull() {
            addCriterion("channel_no is null");
            return (Criteria) this;
        }

        public Criteria andChannel_noIsNotNull() {
            addCriterion("channel_no is not null");
            return (Criteria) this;
        }

        public Criteria andChannel_noEqualTo(String value) {
            addCriterion("channel_no =", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noNotEqualTo(String value) {
            addCriterion("channel_no <>", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noGreaterThan(String value) {
            addCriterion("channel_no >", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noGreaterThanOrEqualTo(String value) {
            addCriterion("channel_no >=", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noLessThan(String value) {
            addCriterion("channel_no <", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noLessThanOrEqualTo(String value) {
            addCriterion("channel_no <=", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noLike(String value) {
            addCriterion("channel_no like", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noNotLike(String value) {
            addCriterion("channel_no not like", value, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noIn(List<String> values) {
            addCriterion("channel_no in", values, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noNotIn(List<String> values) {
            addCriterion("channel_no not in", values, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noBetween(String value1, String value2) {
            addCriterion("channel_no between", value1, value2, "channel_no");
            return (Criteria) this;
        }

        public Criteria andChannel_noNotBetween(String value1, String value2) {
            addCriterion("channel_no not between", value1, value2, "channel_no");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idIsNull() {
            addCriterion("parent_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idIsNotNull() {
            addCriterion("parent_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andWx_settlement_idIsNotNull() {
            addCriterion("wx_settlement_id is not null");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idEqualTo(String value) {
            addCriterion("parent_merchant_id =", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idNotEqualTo(String value) {
            addCriterion("parent_merchant_id <>", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idGreaterThan(String value) {
            addCriterion("parent_merchant_id >", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idGreaterThanOrEqualTo(String value) {
            addCriterion("parent_merchant_id >=", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idLessThan(String value) {
            addCriterion("parent_merchant_id <", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idLessThanOrEqualTo(String value) {
            addCriterion("parent_merchant_id <=", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idLike(String value) {
            addCriterion("parent_merchant_id like", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idNotLike(String value) {
            addCriterion("parent_merchant_id not like", value, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idIn(List<String> values) {
            addCriterion("parent_merchant_id in", values, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idNotIn(List<String> values) {
            addCriterion("parent_merchant_id not in", values, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idBetween(String value1, String value2) {
            addCriterion("parent_merchant_id between", value1, value2, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andParent_merchant_idNotBetween(String value1, String value2) {
            addCriterion("parent_merchant_id not between", value1, value2, "parent_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProviderIsNull() {
            addCriterion("provider is null");
            return (Criteria) this;
        }

        public Criteria andProviderIsNotNull() {
            addCriterion("provider is not null");
            return (Criteria) this;
        }

        public Criteria andProviderEqualTo(Integer value) {
            addCriterion("provider =", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotEqualTo(Integer value) {
            addCriterion("provider <>", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderGreaterThan(Integer value) {
            addCriterion("provider >", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderGreaterThanOrEqualTo(Integer value) {
            addCriterion("provider >=", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderLessThan(Integer value) {
            addCriterion("provider <", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderLessThanOrEqualTo(Integer value) {
            addCriterion("provider <=", value, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderIn(List<Integer> values) {
            addCriterion("provider in", values, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotIn(List<Integer> values) {
            addCriterion("provider not in", values, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderBetween(Integer value1, Integer value2) {
            addCriterion("provider between", value1, value2, "provider");
            return (Criteria) this;
        }

        public Criteria andProviderNotBetween(Integer value1, Integer value2) {
            addCriterion("provider not between", value1, value2, "provider");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idIsNull() {
            addCriterion("provider_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idIsNotNull() {
            addCriterion("provider_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idEqualTo(String value) {
            addCriterion("provider_merchant_id =", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idNotEqualTo(String value) {
            addCriterion("provider_merchant_id <>", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idGreaterThan(String value) {
            addCriterion("provider_merchant_id >", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idGreaterThanOrEqualTo(String value) {
            addCriterion("provider_merchant_id >=", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idLessThan(String value) {
            addCriterion("provider_merchant_id <", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idLessThanOrEqualTo(String value) {
            addCriterion("provider_merchant_id <=", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idLike(String value) {
            addCriterion("provider_merchant_id like", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idNotLike(String value) {
            addCriterion("provider_merchant_id not like", value, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idIn(List<String> values) {
            addCriterion("provider_merchant_id in", values, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idNotIn(List<String> values) {
            addCriterion("provider_merchant_id not in", values, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idBetween(String value1, String value2) {
            addCriterion("provider_merchant_id between", value1, value2, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andProvider_merchant_idNotBetween(String value1, String value2) {
            addCriterion("provider_merchant_id not between", value1, value2, "provider_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPaywayIsNull() {
            addCriterion("payway is null");
            return (Criteria) this;
        }

        public Criteria andPaywayIsNotNull() {
            addCriterion("payway is not null");
            return (Criteria) this;
        }

        public Criteria andPaywayEqualTo(Integer value) {
            addCriterion("payway =", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayNotEqualTo(Integer value) {
            addCriterion("payway <>", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayGreaterThan(Integer value) {
            addCriterion("payway >", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayGreaterThanOrEqualTo(Integer value) {
            addCriterion("payway >=", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayLessThan(Integer value) {
            addCriterion("payway <", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayLessThanOrEqualTo(Integer value) {
            addCriterion("payway <=", value, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayIn(List<Integer> values) {
            addCriterion("payway in", values, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayNotIn(List<Integer> values) {
            addCriterion("payway not in", values, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayBetween(Integer value1, Integer value2) {
            addCriterion("payway between", value1, value2, "payway");
            return (Criteria) this;
        }

        public Criteria andPaywayNotBetween(Integer value1, Integer value2) {
            addCriterion("payway not between", value1, value2, "payway");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusIsNull() {
            addCriterion("params_config_status is null");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusIsNotNull() {
            addCriterion("params_config_status is not null");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusEqualTo(Integer value) {
            addCriterion("params_config_status =", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusNotEqualTo(Integer value) {
            addCriterion("params_config_status <>", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusGreaterThan(Integer value) {
            addCriterion("params_config_status >", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusGreaterThanOrEqualTo(Integer value) {
            addCriterion("params_config_status >=", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusLessThan(Integer value) {
            addCriterion("params_config_status <", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusLessThanOrEqualTo(Integer value) {
            addCriterion("params_config_status <=", value, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusIn(List<Integer> values) {
            addCriterion("params_config_status in", values, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusNotIn(List<Integer> values) {
            addCriterion("params_config_status not in", values, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusBetween(Integer value1, Integer value2) {
            addCriterion("params_config_status between", value1, value2, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andParams_config_statusNotBetween(Integer value1, Integer value2) {
            addCriterion("params_config_status not between", value1, value2, "params_config_status");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idIsNull() {
            addCriterion("pay_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idIsNotNull() {
            addCriterion("pay_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idEqualTo(String value) {
            addCriterion("pay_merchant_id =", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idNotEqualTo(String value) {
            addCriterion("pay_merchant_id <>", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idGreaterThan(String value) {
            addCriterion("pay_merchant_id >", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idGreaterThanOrEqualTo(String value) {
            addCriterion("pay_merchant_id >=", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idLessThan(String value) {
            addCriterion("pay_merchant_id <", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idLessThanOrEqualTo(String value) {
            addCriterion("pay_merchant_id <=", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idLike(String value) {
            addCriterion("pay_merchant_id like", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idNotLike(String value) {
            addCriterion("pay_merchant_id not like", value, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idIn(List<String> values) {
            addCriterion("pay_merchant_id in", values, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idNotIn(List<String> values) {
            addCriterion("pay_merchant_id not in", values, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idBetween(String value1, String value2) {
            addCriterion("pay_merchant_id between", value1, value2, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andPay_merchant_idNotBetween(String value1, String value2) {
            addCriterion("pay_merchant_id not between", value1, value2, "pay_merchant_id");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidIsNull() {
            addCriterion("weixin_sub_appid is null");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidIsNotNull() {
            addCriterion("weixin_sub_appid is not null");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidEqualTo(String value) {
            addCriterion("weixin_sub_appid =", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidNotEqualTo(String value) {
            addCriterion("weixin_sub_appid <>", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidGreaterThan(String value) {
            addCriterion("weixin_sub_appid >", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidGreaterThanOrEqualTo(String value) {
            addCriterion("weixin_sub_appid >=", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidLessThan(String value) {
            addCriterion("weixin_sub_appid <", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidLessThanOrEqualTo(String value) {
            addCriterion("weixin_sub_appid <=", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidLike(String value) {
            addCriterion("weixin_sub_appid like", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidNotLike(String value) {
            addCriterion("weixin_sub_appid not like", value, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidIn(List<String> values) {
            addCriterion("weixin_sub_appid in", values, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidNotIn(List<String> values) {
            addCriterion("weixin_sub_appid not in", values, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidBetween(String value1, String value2) {
            addCriterion("weixin_sub_appid between", value1, value2, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_appidNotBetween(String value1, String value2) {
            addCriterion("weixin_sub_appid not between", value1, value2, "weixin_sub_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidIsNull() {
            addCriterion("weixin_subscribe_appid is null");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidIsNotNull() {
            addCriterion("weixin_subscribe_appid is not null");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidEqualTo(String value) {
            addCriterion("weixin_subscribe_appid =", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidNotEqualTo(String value) {
            addCriterion("weixin_subscribe_appid <>", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidGreaterThan(String value) {
            addCriterion("weixin_subscribe_appid >", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidGreaterThanOrEqualTo(String value) {
            addCriterion("weixin_subscribe_appid >=", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidLessThan(String value) {
            addCriterion("weixin_subscribe_appid <", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidLessThanOrEqualTo(String value) {
            addCriterion("weixin_subscribe_appid <=", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidLike(String value) {
            addCriterion("weixin_subscribe_appid like", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidNotLike(String value) {
            addCriterion("weixin_subscribe_appid not like", value, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidIn(List<String> values) {
            addCriterion("weixin_subscribe_appid in", values, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidNotIn(List<String> values) {
            addCriterion("weixin_subscribe_appid not in", values, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidBetween(String value1, String value2) {
            addCriterion("weixin_subscribe_appid between", value1, value2, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_subscribe_appidNotBetween(String value1, String value2) {
            addCriterion("weixin_subscribe_appid not between", value1, value2, "weixin_subscribe_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidIsNull() {
            addCriterion("weixin_sub_mini_appid is null");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidIsNotNull() {
            addCriterion("weixin_sub_mini_appid is not null");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidEqualTo(String value) {
            addCriterion("weixin_sub_mini_appid =", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidNotEqualTo(String value) {
            addCriterion("weixin_sub_mini_appid <>", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidGreaterThan(String value) {
            addCriterion("weixin_sub_mini_appid >", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidGreaterThanOrEqualTo(String value) {
            addCriterion("weixin_sub_mini_appid >=", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidLessThan(String value) {
            addCriterion("weixin_sub_mini_appid <", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidLessThanOrEqualTo(String value) {
            addCriterion("weixin_sub_mini_appid <=", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidLike(String value) {
            addCriterion("weixin_sub_mini_appid like", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidNotLike(String value) {
            addCriterion("weixin_sub_mini_appid not like", value, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidIn(List<String> values) {
            addCriterion("weixin_sub_mini_appid in", values, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidNotIn(List<String> values) {
            addCriterion("weixin_sub_mini_appid not in", values, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidBetween(String value1, String value2) {
            addCriterion("weixin_sub_mini_appid between", value1, value2, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_sub_mini_appidNotBetween(String value1, String value2) {
            addCriterion("weixin_sub_mini_appid not between", value1, value2, "weixin_sub_mini_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidIsNull() {
            addCriterion("weixin_receipt_appid is null");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidIsNotNull() {
            addCriterion("weixin_receipt_appid is not null");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidEqualTo(String value) {
            addCriterion("weixin_receipt_appid =", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidNotEqualTo(String value) {
            addCriterion("weixin_receipt_appid <>", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidGreaterThan(String value) {
            addCriterion("weixin_receipt_appid >", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidGreaterThanOrEqualTo(String value) {
            addCriterion("weixin_receipt_appid >=", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidLessThan(String value) {
            addCriterion("weixin_receipt_appid <", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidLessThanOrEqualTo(String value) {
            addCriterion("weixin_receipt_appid <=", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidLike(String value) {
            addCriterion("weixin_receipt_appid like", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidNotLike(String value) {
            addCriterion("weixin_receipt_appid not like", value, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidIn(List<String> values) {
            addCriterion("weixin_receipt_appid in", values, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidNotIn(List<String> values) {
            addCriterion("weixin_receipt_appid not in", values, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidBetween(String value1, String value2) {
            addCriterion("weixin_receipt_appid between", value1, value2, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andWeixin_receipt_appidNotBetween(String value1, String value2) {
            addCriterion("weixin_receipt_appid not between", value1, value2, "weixin_receipt_appid");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Long value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Long value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Long value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Long value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Long value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Long> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Long> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Long value1, Long value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Long value1, Long value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Long value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Long value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Long value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Long value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Long value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Long> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Long> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Long value1, Long value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Long value1, Long value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andContract_ruleIsNull() {
            addCriterion("contract_rule is null");
            return (Criteria) this;
        }

        public Criteria andContract_ruleIsNotNull() {
            addCriterion("contract_rule is not null");
            return (Criteria) this;
        }

        public Criteria andContract_ruleEqualTo(String value) {
            addCriterion("contract_rule =", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleNotEqualTo(String value) {
            addCriterion("contract_rule <>", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleGreaterThan(String value) {
            addCriterion("contract_rule >", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleGreaterThanOrEqualTo(String value) {
            addCriterion("contract_rule >=", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleLessThan(String value) {
            addCriterion("contract_rule <", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleLessThanOrEqualTo(String value) {
            addCriterion("contract_rule <=", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleLike(String value) {
            addCriterion("contract_rule like", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleNotLike(String value) {
            addCriterion("contract_rule not like", value, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleIn(List<String> values) {
            addCriterion("contract_rule in", values, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleNotIn(List<String> values) {
            addCriterion("contract_rule not in", values, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleBetween(String value1, String value2) {
            addCriterion("contract_rule between", value1, value2, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andContract_ruleNotBetween(String value1, String value2) {
            addCriterion("contract_rule not between", value1, value2, "contract_rule");
            return (Criteria) this;
        }

        public Criteria andRule_group_idIsNull() {
            addCriterion("rule_group_id is null");
            return (Criteria) this;
        }

        public Criteria andRule_group_idIsNotNull() {
            addCriterion("rule_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andRule_group_idEqualTo(String value) {
            addCriterion("rule_group_id =", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idNotEqualTo(String value) {
            addCriterion("rule_group_id <>", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idGreaterThan(String value) {
            addCriterion("rule_group_id >", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idGreaterThanOrEqualTo(String value) {
            addCriterion("rule_group_id >=", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idLessThan(String value) {
            addCriterion("rule_group_id <", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idLessThanOrEqualTo(String value) {
            addCriterion("rule_group_id <=", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idLike(String value) {
            addCriterion("rule_group_id like", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idNotLike(String value) {
            addCriterion("rule_group_id not like", value, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idIn(List<String> values) {
            addCriterion("rule_group_id in", values, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idNotIn(List<String> values) {
            addCriterion("rule_group_id not in", values, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idBetween(String value1, String value2) {
            addCriterion("rule_group_id between", value1, value2, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andRule_group_idNotBetween(String value1, String value2) {
            addCriterion("rule_group_id not between", value1, value2, "rule_group_id");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusIsNull() {
            addCriterion("update_status is null");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusIsNotNull() {
            addCriterion("update_status is not null");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusEqualTo(Integer value) {
            addCriterion("update_status =", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusNotEqualTo(Integer value) {
            addCriterion("update_status <>", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusGreaterThan(Integer value) {
            addCriterion("update_status >", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_status >=", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusLessThan(Integer value) {
            addCriterion("update_status <", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusLessThanOrEqualTo(Integer value) {
            addCriterion("update_status <=", value, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusIn(List<Integer> values) {
            addCriterion("update_status in", values, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusNotIn(List<Integer> values) {
            addCriterion("update_status not in", values, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusBetween(Integer value1, Integer value2) {
            addCriterion("update_status between", value1, value2, "update_status");
            return (Criteria) this;
        }

        public Criteria andUpdate_statusNotBetween(Integer value1, Integer value2) {
            addCriterion("update_status not between", value1, value2, "update_status");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}