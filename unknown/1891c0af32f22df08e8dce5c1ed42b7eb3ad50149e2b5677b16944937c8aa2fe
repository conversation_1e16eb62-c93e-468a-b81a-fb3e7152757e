package com.wosai.upay.job.biz.directparams;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.TradeConstants;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.directparams.BaseParams;
import com.wosai.upay.job.model.directparams.WeixinDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
public class WeixinDirectParamsBiz extends DirectParamsBiz {

    private static final int PAYWAY = PaywayEnum.WEIXIN.getValue();

    @Override
    public void addDirectParams(BaseParams baseParams) {
        checkMerchant(baseParams);

        WeixinDirectParams params = (WeixinDirectParams) baseParams;

        Map<String, Object> allDirectParams = new HashMap<>(5);
        String payMchId = "";


        MerchantProviderParamsDto dto = paramsBiz.getDirectParams(params.getMerchant_sn(), PAYWAY, PAYWAY);
        if (dto == null) {
            dto = new MerchantProviderParamsDto()
                    .setMerchant_sn(params.getMerchant_sn())
                    .setProvider(PAYWAY)
                    .setPayway(PAYWAY);

        } else {
            allDirectParams = dto.getExtra();
        }

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(baseParams.getMerchant_id(), PAYWAY);

        Map updateInfo = new HashMap();

        if (isNotEmpty(params.getWeixin_trade_params())) {
            payMchId = params.getWeixin_trade_params().getWeixin_sub_mch_id();
            Map<String, Object> directParams = bean2Map(params.getWeixin_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.WEIXIN_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.C2B_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.C2B_AGENT_NAME));
            allDirectParams.put(TransactionParam.WEIXIN_TRADE_PARAMS, directParams);

            dto.setPay_merchant_id(params.getWeixin_trade_params().getWeixin_sub_mch_id())
                    .setWeixin_sub_appid(params.getWeixin_trade_params().getWeixin_sub_appid());

            tradeConfigService.updateWeixinTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.B2C_FEE_RATE, params.getWeixin_trade_params().getFee_rate(),
                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_FEE_RATE, params.getWeixin_trade_params().getFee_rate()
            ));
        }
        if (isNotEmpty(params.getWeixin_wap_trade_params())) {
            payMchId = params.getWeixin_wap_trade_params().getWeixin_sub_mch_id();
            Map<String, Object> directParams = bean2Map(params.getWeixin_wap_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.WEIXIN_WAP_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.WAP_AGENT_NAME));
            allDirectParams.put(TransactionParam.WEIXIN_WAP_TRADE_PARAMS, directParams);

            tradeConfigService.updateWeixinWapTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.WAP_FEE_RATE, params.getWeixin_wap_trade_params().getFee_rate()
            ));

            // 特殊处理，如果添加wap参数，并且不存在mini参数，则自动添加mini参数
            boolean miniFormal = BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.MINI_FORMAL, false);
            if (isEmpty(params.getWeixin_mini_trade_params()) && !miniFormal) {
                WeixinDirectParams.WeixinWapTradeParams wapTradeParams = params.getWeixin_wap_trade_params();
                WeixinDirectParams.WeixinTradeParams miniParam = new WeixinDirectParams.WeixinTradeParams();
                miniParam.setWeixin_sub_mch_id(wapTradeParams.getWeixin_sub_mch_id());
                miniParam.setWeixin_sub_appid("wxccbcac9a3ece5112"); // 门店码小程序
                miniParam.setFee_rate(wapTradeParams.getFee_rate());
                params.setWeixin_mini_trade_params(miniParam);
            }

        }
        if (isNotEmpty(params.getWeixin_mini_trade_params())) {
            payMchId = params.getWeixin_mini_trade_params().getWeixin_sub_mch_id();
            Map<String, Object> directParams = bean2Map(params.getWeixin_mini_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.WEIXIN_MINI_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.MINI_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.MINI_AGENT_NAME));
            allDirectParams.put(TransactionParam.WEIXIN_MINI_TRADE_PARAMS, directParams);

            tradeConfigService.updateWeixinMiniTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.MINI_FEE_RATE, params.getWeixin_mini_trade_params().getFee_rate()
            ));
        }
        if (isNotEmpty(params.getWeixin_h5_trade_params())) {
            payMchId = params.getWeixin_h5_trade_params().getWeixin_sub_mch_id();
            Map<String, Object> directParams = bean2Map(params.getWeixin_h5_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.WEIXIN_H5_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.H5_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.H5_AGENT_NAME));
            allDirectParams.put(TransactionParam.WEIXIN_H5_TRADE_PARAMS, directParams);

            tradeConfigService.updateWeixinH5TradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.H5_FEE_RATE, params.getWeixin_h5_trade_params().getFee_rate()
            ));
        }

        if (isNotEmpty(params.getWeixin_app_trade_params())) {
            payMchId = params.getWeixin_app_trade_params().getWeixin_sub_mch_id();
            Map<String, Object> directParams = bean2Map(params.getWeixin_app_trade_params());
            appendOldParams(directParams, WosaiMapUtils.getMap(allDirectParams, TransactionParam.WEIXIN_APP_TRADE_PARAMS));
            directParams.putIfAbsent(TradeConstants.OLD_FEE_RATE, WosaiMapUtils.getString(merchantConfig, MerchantConfig.APP_FEE_RATE));
            directParams.putIfAbsent(TradeConstants.OLD_AGENT_NAME, WosaiMapUtils.getString(merchantConfig, MerchantConfig.APP_AGENT_NAME));
            allDirectParams.put(TransactionParam.WEIXIN_APP_TRADE_PARAMS, directParams);

            tradeConfigService.updateWeixinAppTradeParams(params.getMerchant_id(), directParams);

            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.APP_FEE_RATE, params.getWeixin_app_trade_params().getFee_rate()
            ));
        }

        dto.setExtra(allDirectParams);
        dto.setPay_merchant_id(payMchId);
        paramsBiz.saveDirectMerchantProviderParams(dto);

        if (!updateInfo.isEmpty()) {
            // 未开通间联直接导参数，前面查询为空
            if (WosaiMapUtils.isEmpty(merchantConfig)) {
                merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(baseParams.getMerchant_id(), PAYWAY);
            }
            updateInfo.putAll(
                    CollectionUtil.hashMap(
                            DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID)
                    )
            );
            tradeConfigService.updateMerchantConfig(updateInfo);
        }

        supportService.removeCachedParams(params.getMerchant_sn());

    }

    @Override
    public void deleteDirectParams(MerchantProviderParamsDto params, String subPayway, String feeRate) {
        Map merchant = merchantService.getMerchantBySn(params.getMerchant_sn());
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        if (WosaiStringUtils.isEmpty(subPayway)) {
            throw new CommonPubBizException("sub_payway 不能为空");
        }

        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PAYWAY);
        Map merchantConfigParams = WosaiMapUtils.getMap(merchantConfig, MerchantConfig.PARAMS, new HashMap());
        Map updateInfo = new HashMap();

        Map extra = params.getExtra();

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_TRADE_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.WEIXIN_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.WEIXIN_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.B2C_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.B2C_AGENT_NAME, agentName,
                    MerchantConfig.B2C_FEE_RATE, feeRate,
                    MerchantConfig.C2B_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.C2B_AGENT_NAME, agentName,
                    MerchantConfig.C2B_FEE_RATE, feeRate
            ));
        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_WAP_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.WEIXIN_WAP_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.WAP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.WAP_AGENT_NAME, agentName,
                    MerchantConfig.WAP_FEE_RATE, feeRate
            ));
        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_APP_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.WEIXIN_APP_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.WEIXIN_APP_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.APP_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.APP_AGENT_NAME, agentName,
                    MerchantConfig.APP_FEE_RATE, feeRate
            ));
        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_H5_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.WEIXIN_H5_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.WEIXIN_H5_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.H5_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.H5_AGENT_NAME, agentName,
                    MerchantConfig.H5_FEE_RATE, feeRate
            ));
        }

        if (subPayway.equals(TradeConstants.SUB_PAYWAY_MINI_NAME)) {
            Map directParams = (Map) extra.remove(TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
            feeRate = getFeeRate(feeRate, WosaiMapUtils.getString(directParams, TradeConstants.OLD_FEE_RATE));

            merchantConfigParams.remove(TransactionParam.WEIXIN_MINI_TRADE_PARAMS);
            String agentName = getAgentName(directParams, merchantConfig, PAYWAY, params.getMerchant_sn());
            updateInfo.putAll(CollectionUtil.hashMap(
                    MerchantConfig.MINI_FORMAL, MerchantConfig.STATUS_CLOSED,
                    MerchantConfig.MINI_AGENT_NAME, agentName,
                    MerchantConfig.MINI_FEE_RATE, feeRate
            ));
        }

        if (extra.size() == 0) {
            paramsBiz.deleteParamsById(params.getId());
        } else {
            paramsBiz.updateByPrimaryKeySelective(
                    new MerchantProviderParamsDto().setId(params.getId())
                            .setExtra(extra)
            );
        }

        if (!updateInfo.isEmpty()) {
            updateInfo.putAll(
                    CollectionUtil.hashMap(
                            DaoConstants.ID, WosaiMapUtils.getString(merchantConfig, DaoConstants.ID),
                            MerchantConfig.PARAMS, merchantConfigParams
                    )
            );
            tradeConfigService.updateMerchantConfig(updateInfo);
        }

        if (extra.size() == 0) {
            //同时将sub_biz_params中的直连参数删除
            subBizParamsBiz.deleteDirectSubBizParams(params.getMerchant_sn(),PAYWAY);
            setDefaultParams(params.getMerchant_sn(), PAYWAY);
        }

        supportService.removeCachedParams(params.getMerchant_sn());
    }

    @Override
    public BaseParams getDirectParams(String merchantSn) {
        MerchantProviderParams providerParams = paramsMapper.getDirectParam(merchantSn, PAYWAY);
        if (Objects.isNull(providerParams)) {
            return null;
        }
        return JSONObject.parseObject(providerParams.getExtra(), WeixinDirectParams.class);
    }

    @Override
    public List<MerchantProviderParamsCustomDto> handleDirectParams(MerchantProviderParamsCustomDto source) {
        List<MerchantProviderParamsCustomDto> result = new ArrayList<>();
        if (WosaiMapUtils.isEmpty(source.getExtra())) {
            return result;
        }

        WeixinDirectParams params = JSON.parseObject(JSON.toJSONString(source.getExtra()), WeixinDirectParams.class);
        if (params.getWeixin_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id(params.getWeixin_trade_params().getWeixin_sub_mch_id());
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_TRADE_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.WEIXIN_TRADE_PARAMS));
            result.add(dto);
        }

        if (params.getWeixin_wap_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id(params.getWeixin_wap_trade_params().getWeixin_sub_mch_id());
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_WAP_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.WEIXIN_WAP_TRADE_PARAMS));
            result.add(dto);
        }

        if (params.getWeixin_app_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id(params.getWeixin_app_trade_params().getWeixin_sub_mch_id());
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_APP_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.WEIXIN_APP_TRADE_PARAMS));
            result.add(dto);
        }

        if (params.getWeixin_h5_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id(params.getWeixin_h5_trade_params().getWeixin_sub_mch_id());
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_H5_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.WEIXIN_H5_TRADE_PARAMS));
            result.add(dto);
        }

        if (params.getWeixin_mini_trade_params() != null) {
            MerchantProviderParamsCustomDto dto = new MerchantProviderParamsCustomDto();
            BeanUtils.copyProperties(source, dto);
            dto.setPay_merchant_id(params.getWeixin_mini_trade_params().getWeixin_sub_mch_id());
            dto.setSub_payway(TradeConstants.SUB_PAYWAY_MINI_NAME);
            dto.setExtra(WosaiMapUtils.getMap(source.getExtra(), TransactionParam.WEIXIN_MINI_TRADE_PARAMS));
            result.add(dto);
        }
        return result;
    }
}
