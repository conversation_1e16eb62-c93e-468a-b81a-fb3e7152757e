package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface ProviderTerminalBindConfigMapper {

    int insert(ProviderTerminalBindConfig record);

    int insertSelective(ProviderTerminalBindConfig record);

    int updateByPrimaryKey(ProviderTerminalBindConfig record);

    List<ProviderTerminalBindConfig> selectByExample(ProviderTerminalBindConfig example);

    ProviderTerminalBindConfig selectByPrimaryKey(@Param("id") String id);

    @Select("select * from provider_terminal_bind_config where provider_terminal_id = #{provider_terminal_id} and sub_mch_id = #{sub_mch_id} limit 1")
    ProviderTerminalBindConfig selectByProviderTerminalIdAndSubMchId(@Param("provider_terminal_id") String providerTerminalId, @Param("sub_mch_id") String subMchId);

}