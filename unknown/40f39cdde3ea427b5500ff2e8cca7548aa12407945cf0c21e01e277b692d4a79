package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * MerchantProviderCustomParams
 *
 * <AUTHOR>
 * @date 2019-08-05 18:47
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MerchantProviderParamsCustom extends MerchantProviderParams {
    private String channel;
    private String channel_name;

    private String acquirer;
    private String acquirer_name;
    private Integer clear_type;

    private String provider_name;
}
