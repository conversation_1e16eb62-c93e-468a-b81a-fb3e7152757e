package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description: 使用拉卡拉渠道参数，走拉卡拉前置服务进行交易
 * <AUTHOR>
 * Date 2020/6/3 4:55 下午
 **/
@Component
public class UnionParamsHandle extends ProviderParamsHandle {

    private static int PROVIDER = ProviderEnum.PROVIDER_UNIONPAY.getValue();

    @Override
    protected boolean accept(MerchantConfigParams merchantConfigParams) {
        return PROVIDER == merchantConfigParams.getProvider();
    }

    @Override
    protected Map getConfigParams(MerchantConfigParams merchantConfigParams) {
        int payWay = merchantConfigParams.getPayWay();
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.UNION_PAY_ALIPAY_SUB_MCH_ID, merchantConfigParams.getPayMerchantId());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.UNION_PAY_WEIXIN_SUB_MCH_ID, merchantConfigParams.getPayMerchantId(),
                    TransactionParam.UNION_PAY_WEIXIN_SUB_APP_ID, merchantConfigParams.getSubAppid(),
                    TransactionParam.UNION_PAY_WEIXIN_SUB_APP_SECRET, merchantConfigParams.getSubAppSecret(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_ID, merchantConfigParams.getMiniSubAppid(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, merchantConfigParams.getMiniSubAppSecret());
        }
        return null;
    }
}
