package com.wosai.upay.job.model.DO;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BankDirectApply {
    private Long id;

    private String merchant_sn;

    private Long task_id;

    private String dev_code;

    private Integer bank_ref;

    private Integer status;

    private String result;

    private Date create_at;

    private Date update_at;

    private Date priority;

    private Integer process_status;

    private String form_body;

    private String extra;

    public Map<String,Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }
}