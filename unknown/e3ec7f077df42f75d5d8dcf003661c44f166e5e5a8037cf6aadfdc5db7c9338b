package com.wosai.upay.job.monitor;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.request.GetLogsRequest;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.service.RedisService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.Tuple2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
public class MonitorWarn {

    public static final Logger logger = LoggerFactory.getLogger(MonitorWarn.class);

    private static final String PROJECT = "cua-group";
    private static final String LOG_STORE = "merchant-contrac-monitor";
    private static final String WARN_MONITOR_KEY = "contract_monitor_thread";


    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private RedisService redisLock;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    @Qualifier("redisTemplate")
    private RedisTemplate redisTemplate;

    private AtomicBoolean holdMonitorThread = new AtomicBoolean(false);

    private static Client client;

    @PostConstruct
    public void init() {
        client = new Client("cn-hangzhou.log.aliyuncs.com", "cua");
    }

    // 调度监控线程 MonitorSchedule
    public void schedule() {
        if (redisLock.setNx(WARN_MONITOR_KEY, WARN_MONITOR_KEY, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS, TimeUnit.SECONDS)) {
            new Thread(() -> {
                while (true) {
                    logger.info("start warn thread");
                    try {
                        TimeUnit.SECONDS.sleep(30);
                        redisTemplate.expire(WARN_MONITOR_KEY, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS, TimeUnit.SECONDS);
                        JSONObject warnConfig = JSON.parseObject(applicationApolloConfig.getMonitorWarn(), JSONObject.class);
                        if (warnConfig != null) {
//                          robot告警机器人地址
//                          duration: 监控近几分钟
//                          warns: 告警项逗号分隔 事件名称,事件sls筛选条件,告警百分比阈值,商户数告警阈值,次数告警阈
//                          diff_warn(商户数去重)   两个查询差值告警 事件名称,成功数筛选,总数筛选,告警阈值,次数阈值
                            String robot = warnConfig.getString("robot");
                            JSONArray warns = warnConfig.getJSONArray("warns");
                            JSONArray durations = warnConfig.getJSONArray("durations");
                            if (warns != null) {
                                warns.forEach(w -> durations.forEach(d -> logAndWarn((String) w, Long.parseLong(d.toString()), true, robot))); //商户个数告警
                                warns.forEach(w -> durations.forEach(d -> logAndWarn((String) w, Long.parseLong(d.toString()), false, robot))); //次数告警
                            }
                            JSONArray diffWarn = warnConfig.getJSONArray("diff_warn");
                            if (warns != null) {
                                diffWarn.forEach(k -> durations.forEach(d -> diffWarn((String) k, Long.parseLong(d.toString()), robot)));
                            }
                        }
                    } catch (Throwable e) {
                        logger.error("warn error ", e);
                    }
                }
            }).start();
            this.holdMonitorThread.set(true);
            chatBotUtil.sendMessageToContractWarnChatBot("监控线程启动成功");
            logger.info("监控线程启动成功");
        }
    }


    @PreDestroy
    public void shutdown() {
        if (this.holdMonitorThread.get()) {
            redisLock.del(WARN_MONITOR_KEY);
            logger.info("释放监控线程缓存成功");
            chatBotUtil.sendMessageToContractWarnChatBot("释放监控线程缓存成功");
        }
    }


    private void diffWarn(String k, long duration, String robot) {
        try {
            String[] splits = k.split(",");
            double thresholdRate = Double.parseDouble(splits[3]);
            int thresholdCount = Integer.parseInt(splits[4]);
            long now = System.currentTimeMillis();
            //拉卡拉一般3秒左右会回调 统计尽量准确
            long callback = now;
            long request = now - 3000;
            int total = getNum(splits[1] + DISTINCT_COUNT, duration, request); //请求总数
            int total2 = getNum(splits[2] + DISTINCT_COUNT, duration, callback); //回调总数
            int diff = total - total2;
            new WarnObject()
                    .setEvent(splits[0])
                    .setTime(now)
                    .setDistinct(true)
                    .setTotal(total)
                    .setFail(diff)
                    .setThresholdRate(thresholdRate)
                    .setThresholdCount(thresholdCount)
                    .setDuration(duration)
                    .tryWarn(robot);

        } catch (Exception e) {
            logger.error("warn error ", e);
        }
    }

    private static final String COUNT = "|select count(1)";
    private static final String DISTINCT_COUNT = "|select count(distinct(sn))";

    private static final String FAIL = " not status =200";


    private void logAndWarn(String conf, long duration, boolean isDistinct, String robot) {
        try {
            String[] splits = conf.split(",");
            long now = System.currentTimeMillis();
            String event = splits[0];
            double thresholdRate = Double.parseDouble(splits[2]);
            int thresholdCount = Integer.parseInt(splits[3]);
            if (isDistinct) {
                thresholdCount = Integer.parseInt(splits[4]);
            }
            String analysis = COUNT;
            if (isDistinct) {
                analysis = DISTINCT_COUNT;
            }
            int total = getNum(splits[1] + analysis, duration, now); //进件总数
            int fail = getNum(splits[1] + FAIL + analysis, duration, now); //进件失败数
            List<Tuple2<String, Integer>> fails = failReason(splits[1] + FAIL + "| select message,count(1) group by message", duration, now);
            new WarnObject()
                    .setEvent(event)
                    .setTime(now)
                    .setDistinct(isDistinct)
                    .setTotal(total)
                    .setFail(fail)
                    .setThresholdRate(thresholdRate)
                    .setThresholdCount(thresholdCount)
                    .setDuration(duration)
                    .setReason(fails)
                    .tryWarn(robot);
        } catch (Exception e) {
            logger.error("warn error ", e);
        }

    }

    private static int getNum(String source, long duration, long now) throws LogException {
        long from = now / 1000 - duration * 60;
        long to = now / 1000;
        GetLogsRequest req4 = new GetLogsRequest(PROJECT, LOG_STORE, (int) from, (int) to, "", source);
        GetLogsResponse res4 = client.GetLogs(req4);
        if (res4 == null || (!res4.IsCompleted())) {
            logger.error("query not complete {},{},{}", source, duration, now);
            throw new RuntimeException("query error not complete");
        }
        if (res4.IsCompleted()) {
            for (QueriedLog log : res4.GetLogs()) {
                LogItem item = log.GetLogItem();
                for (LogContent content : item.GetLogContents()) {
                    return Integer.parseInt(content.GetValue());
                }
            }
        }
        return 0;
    }

    private static List<Tuple2<String, Integer>> failReason(String source, long duration, long now) throws LogException {
        List<Tuple2<String, Integer>> result = Lists.newArrayList();
        long from = now / 1000 - duration * 60;
        long to = now / 1000;
        GetLogsRequest req4 = new GetLogsRequest(PROJECT, LOG_STORE, (int) from, (int) to, "", source);
        GetLogsResponse res4 = client.GetLogs(req4);
        if (res4 == null || (!res4.IsCompleted())) {
            logger.error("query not complete {},{},{}", source, duration, now);
            throw new RuntimeException("query error not complete");
        }
        if (res4.IsCompleted()) {
            for (QueriedLog log : res4.GetLogs()) {
                LogItem item = log.GetLogItem();
                String message = "";
                Integer count = 0;
                for (LogContent content : item.GetLogContents()) {
                    if ("message".equalsIgnoreCase(content.mKey)) {
                        message = content.mValue;
                    } else {
                        count = Integer.parseInt(content.mValue);
                    }
                }
                result.add(new Tuple2<>(message, count));
            }
        }
        result.sort((a, b) -> b._2.compareTo(a._2));
        return result;
    }

}
