package com.wosai.upay.job.mapper;


import com.wosai.upay.job.model.ccbConfig.CcbConfigChangeHistory;

import java.util.List;

public interface CcbConfigChangeHistoryMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CcbConfigChangeHistory record);

    int insertSelective(CcbConfigChangeHistory record);

    CcbConfigChangeHistory selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CcbConfigChangeHistory record);

    int updateByPrimaryKeyWithBLOBs(CcbConfigChangeHistory record);

    int updateByPrimaryKey(CcbConfigChangeHistory record);

    List<CcbConfigChangeHistory> selectByCcbConfigId(Long ccbConfigId);

}