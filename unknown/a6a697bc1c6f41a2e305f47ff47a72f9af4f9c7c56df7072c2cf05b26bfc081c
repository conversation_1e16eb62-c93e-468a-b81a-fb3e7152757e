package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.OfflineMultiTrade;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface OfflineMultiTradeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OfflineMultiTrade record);

    int insertSelective(OfflineMultiTrade record);

    OfflineMultiTrade selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OfflineMultiTrade record);

    int updateByPrimaryKeyWithBLOBs(OfflineMultiTrade record);

    int updateByPrimaryKey(OfflineMultiTrade record);

    @Select("select * from offline_multi_trade where merchant_sn =#{merchantSn} and provider=#{provider} and  trade_app_id=#{tradeAppId} order by mtime desc limit 1")
    OfflineMultiTrade selectByTradeAppId(@Param("merchantSn") String merchantSn, @Param("tradeAppId") String tradeAppId, @Param("provider") String provider);
}