package com.wosai.upay.job.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.turbo.TurboFilter;
import ch.qos.logback.core.spi.FilterReply;
import org.slf4j.Marker;

public class TraceLogFilter extends TurboFilter {
    public static final String TRACE_ID_KEY = "trace_id";
    @Override
    public FilterReply decide(Marker marker, Logger logger, Level level, String s, Object[] objects, Throwable throwable) {
//        Optional<Span> span = Optional.ofNullable(SpanHelper.getParentSpan());
//        String traceId = span.map(Span::context).map(TraceContext::traceIdString).orElse("");
//        MDC.put(TRACE_ID_KEY, traceId);
        return FilterReply.NEUTRAL;
    }
}

