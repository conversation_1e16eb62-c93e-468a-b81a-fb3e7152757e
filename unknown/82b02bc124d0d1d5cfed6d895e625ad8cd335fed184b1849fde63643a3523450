package com.wosai.upay.job.xxljob.context;

import com.wosai.upay.job.model.DO.McAcquirerChange;
import lombok.Data;

import java.util.concurrent.Semaphore;

/**
 * <AUTHOR>
 * @date 2025/5/7
 */
@Data
public class McAcquirerChangeContext {

    private McAcquirerChange mcAcquirerChange;

    private Semaphore semaphore;

    public McAcquirerChangeContext(McAcquirerChange mcAcquirerChange, Semaphore semaphore) {
        this.mcAcquirerChange = mcAcquirerChange;
        this.semaphore = semaphore;
    }

}
