package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.McAcquirerChangeExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface McAcquirerChangeMapper {
    int countByExample(McAcquirerChangeExample example);

    int deleteByExample(McAcquirerChangeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(McAcquirerChange record);

    int insertSelective(McAcquirerChange record);

    List<McAcquirerChange> selectByExampleWithBLOBs(McAcquirerChangeExample example);

    List<McAcquirerChange> selectByExample(McAcquirerChangeExample example);

    McAcquirerChange selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") McAcquirerChange record, @Param("example") McAcquirerChangeExample example);

    int updateByExampleWithBLOBs(@Param("record") McAcquirerChange record, @Param("example") McAcquirerChangeExample example);

    int updateByExample(@Param("record") McAcquirerChange record, @Param("example") McAcquirerChangeExample example);

    int updateByPrimaryKeySelective(McAcquirerChange record);

    int updateByPrimaryKeyWithBLOBs(McAcquirerChange record);

    int updateByPrimaryKey(McAcquirerChange record);

    @Select("select * from mc_acquirer_change where merchant_sn = #{merchantSn} and target_acquirer = #{targetAcquirer} and status=19 order by id limit 1")
    McAcquirerChange selectFirstSuccessChange(@Param("merchantSn") String merchantSn,@Param("targetAcquirer") String targetAcquirer);

    @Select("select * from mc_acquirer_change where merchant_sn = #{merchantSn} and target_acquirer = #{targetAcquirer} order by id desc limit 1")
    McAcquirerChange getLastedByMerchantSnAndTargetAcquirer(@Param("merchantSn") String merchantSn,@Param("targetAcquirer") String targetAcquirer);
}