package com.wosai.upay.job.model.guotong;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/5
 */
@Data
public class GuotongAuditCallbackModel {
    /**
     * 国通600商户号
     */
    @JSONField(name = "CUST_ID")
    private String custId;
    /**
     * 商户名称
     */
    @JSONField(name = "BUS_NAME")
    private String busName;
    /**
     * 商户手机号
     */
    @JSONField(name = "PHONE")
    private String phone;
    /**
     * 收单商户号
     */
    @JSONField(name = "THIRD_NO")
    private String thirdNo;

    /**
     * 结算账户名
     */
    @JSONField(name = "ACCOUNT_NAME")
    private String accountName;
    /**
     * 结算账户号
     */
    @JSONField(name = "ACCOUNT_NO")
    private String accountNo;
    /**
     * 开户行名称
     */
    @J<PERSON>NField(name = "BANK_NAME")
    private String bankName;
    /**
     * 进件/修改审核时间
     */
    @JSONField(name = "AUTH_DATE", format = "yyyy-MM-dd HH:mm:ss")
    private Date authDate;
    /**
     * 商户类型
     * 1.普通商户 2.小微商户
     */
    @JSONField(name = "CUST_TYPE")
    private String custType;
    /**
     * 所属机构号
     */
    @JSONField(name = "AGE_CUST_ID")
    private String ageCustId;
    /**
     * 所属机构名称
     */
    @JSONField(name = "AGE_NAME")
    private String ageName;
    /**
     * 客户经理编号
     */
    @JSONField(name = "MERGER_ID")
    private String mergerId;
    /**
     * 客户经理名称
     */
    @JSONField(name = "MERGER_NAME")
    private String mergerName;
    /**
     * 客户经理手机号
     */
    @JSONField(name = "MERGER_PH")
    private String mergerPh;
    /**
     * 商户行业类型
     * 中文行业+数字MCC  如：路边烧烤,5812
     */
    @JSONField(name = "IND_TYPE")
    private String indType;
    /**
     * 审核结果
     * 空值为默认值，00审核通过；后续字段皆为审核失败：01营业执照模糊 02营业执照注册号填写错误 03法人身份证号码填写错误 04法人姓名填写错误 05未上传法人身份证照片 06翻拍无效，请实物拍摄 07组合照缺失或不符合要求
     * 08营业执照已注销或经营异常 09营业资质不符合要求 10结算卡照片与通码结算信息不一致 11未上传结算人身份证正面 12未上传结算人身份证反面 13结算人身份证已过期 14结算人身份证照上传错误 15结算人身份证照模糊，请重新提交
     * 16结算人身份证照无法查看，请重新提交 17结算人姓名录入有误 18结算人身份证号录入有误 19结算人身份证有效期填写有误 20未上传结算卡照 21结算卡上传错误 22结算卡号录入有误 23结算卡照模糊，请重新提交 24结算卡照无法查看，请重新提交
     * 25请上传对公材料（如开户许可证、印鉴卡等） 26开户银行选择有误 27结算卡状态异常 28营业执照上传错误，非该商户营业执照 29未上传结算人身份证正面 30命中风险mcc黑名单
     */
    @JSONField(name = "AUTH_RESULT")
    private String authResult;
    /**
     * 审核类型
     * 参数示例：0首次进件、2信息变更、4结算卡修改
     */
    @JSONField(name = "AUTH_TYPE")
    private String authType;
    /**
     * 审核失败具体原因
     */
    @JSONField(name = "NOT_THROW_REASON")
    private String notThrowReason;

    private String sign;

}
