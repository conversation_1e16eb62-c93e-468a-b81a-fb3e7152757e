package com.wosai.upay.job.biz;

import com.github.pagehelper.PageHelper;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.mapper.CcbConfigChangeHistoryMapper;
import com.wosai.upay.job.mapper.CcbConfigMapper;
import com.wosai.upay.job.model.DO.CcbConfigExample;
import com.wosai.upay.job.model.ccbConfig.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/21
 */
@Component
public class CcbConfigBiz {

    @Autowired
    private CcbConfigMapper ccbConfigMapper;

    @Autowired
    private CcbConfigChangeHistoryMapper historyMapper;

    public CcbConfig getCcbConfigById(Long id) {
        return ccbConfigMapper.selectByPrimaryKey(id);
    }

    public List<CcbConfig> getAllCcbConfigs() {
        return ccbConfigMapper.selectAll();
    }

    public CcbConfig getCcbConfigByDistrictCode(String districtCode) {
        CcbConfigExample ccbConfigExample = new CcbConfigExample();
        ccbConfigExample.or()
                .andDistrict_codeEqualTo(districtCode)
                .andDeletedEqualTo(false);
        PageHelper.startPage(1, 1);
        List<CcbConfig> ccbConfigs = ccbConfigMapper.selectByExampleWithBLOBs(ccbConfigExample);
        return WosaiCollectionUtils.isEmpty(ccbConfigs) ? null : ccbConfigs.get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addCcbConfig(CreateCcbConfigReq req, String province, String city) {
        Long ctime = System.currentTimeMillis();
        CcbConfig create = CcbConfig.builder()
                .district_code(req.getDistrictCode())
                .province(province)
                .city(city)
                .private_min_price(req.getPrivateMinPrice())
                .public_min_price(req.getPublicMinPrice())
                .support_select_ins_no(req.getSupportSelectInsNo())
                .ins_no(req.getInsNo())
                .ins_no_list(req.getInsNoList())
                .account(req.getAccount())
                .is_auto_change(Boolean.valueOf(req.getIsAutoChange()))
                .filter_rules(req.getFilterRules())
                .delay_day(Integer.valueOf(req.getDelayDay()))
                .micro_info(req.getMicro_info())
                .black_mcc(req.getBlackMcc())
                .apply_decp(req.getApplyDecp())
                .apply_unionpay(req.getApplyUnionpay())
                .ctime(ctime)
                .mtime(ctime)
                .version(1L).build();
        ccbConfigMapper.insertSelective(create);
        addCcbConfigChangeHistory(create.getId(), CcbConfigChangeHistory.OP_TYPE_CREATE, req.getOperator());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCcbConfig(CcbConfig oldCcbConfig, UpdateCcbConfigReq req) {
        CcbConfig ccbConfig = CcbConfig.builder()
                .id(req.getId())
                .private_min_price(req.getPrivateMinPrice())
                .public_min_price(req.getPublicMinPrice())
                .support_select_ins_no(req.getSupportSelectInsNo())
                .ins_no(req.getInsNo())
                .ins_no_list(req.getInsNoList())
                .account(req.getAccount())
                .is_auto_change(Boolean.valueOf(req.getIsAutoChange()))
                .filter_rules(req.getFilterRules())
                .delay_day(Integer.valueOf(req.getDelayDay()))
                .micro_info(req.getMicro_info())
                .black_mcc(req.getBlackMcc())
                .apply_decp(req.getApplyDecp())
                .apply_unionpay(req.getApplyUnionpay())
                .mtime(System.currentTimeMillis())
                .version(oldCcbConfig.getVersion() + 1)
                .build();
        ccbConfigMapper.updateByPrimaryKeySelective(ccbConfig);
        addCcbConfigChangeHistory(oldCcbConfig.getId(), CcbConfigChangeHistory.OP_TYPE_UPDATE, req.getOperator());
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteCcbConfig(CcbConfig oldCcbConfig, DeleteCcbConfigReq req) {
        CcbConfig ccbConfig = CcbConfig.builder()
                .id(req.getId())
                .deleted(true)
                .mtime(System.currentTimeMillis())
                .version(oldCcbConfig.getVersion() + 1)
                .build();
        ccbConfigMapper.updateByPrimaryKeySelective(ccbConfig);
        addCcbConfigChangeHistory(req.getId(), CcbConfigChangeHistory.OP_TYPE_DELETE, req.getOperator());
    }

    public List<CcbConfig> getCcbConfig(QueryCcbConfigReq req) {
        CcbConfigExample ccbConfigExample = new CcbConfigExample();
        CcbConfigExample.Criteria criteria = ccbConfigExample.or();
        if (WosaiStringUtils.isNotEmpty(req.getDistrictCode())) {
            criteria.andDistrict_codeEqualTo(req.getDistrictCode());
        }
        if (WosaiStringUtils.isNotEmpty(req.getProvince())) {
            criteria.andProvinceEqualTo(req.getProvince());
        }
        if (WosaiStringUtils.isNotEmpty(req.getCity())) {
            criteria.andCityEqualTo(req.getCity());
        }
        criteria.andDeletedEqualTo(false);
        ccbConfigExample.setOrderByClause("id asc");
        PageHelper.startPage(req.getPage(), req.getPageSize());
        List<CcbConfig> ccbConfigs = ccbConfigMapper.selectByExampleWithBLOBs(ccbConfigExample);
        for (CcbConfig ccbConfig : ccbConfigs){
            ccbConfig.setIs_filter();
        }
        return ccbConfigs;
    }

    public List<CcbConfigChangeHistory> getCcbConfigChangeHistoryByCcbConfigId(QueryCcbChangeHistoryReq req) {
        PageHelper.startPage(req.getPage(), req.getPageSize());
        return historyMapper.selectByCcbConfigId(req.getCcbConfigId());
    }


    private void addCcbConfigChangeHistory(Long id, Integer opType, String operator) {
        CcbConfig ccbConfig = ccbConfigMapper.selectByPrimaryKey(id);
        CcbConfigChangeHistory history = CcbConfigChangeHistory.builder()
                .ccb_config_id(id)
                .op_type(opType)
                .private_min_price(ccbConfig.getPrivate_min_price())
                .public_min_price(ccbConfig.getPublic_min_price())
                .support_select_ins_no(ccbConfig.getSupport_select_ins_no())
                .ins_no(ccbConfig.getIns_no())
                .ins_no_list(ccbConfig.getIns_no_list())
                .account(ccbConfig.getAccount())
                .is_auto_change(ccbConfig.getIs_auto_change())
                .delay_day(ccbConfig.getDelay_day())
                .filter_rules(ccbConfig.getFilter_rules())
                .operator(operator)
                .update_time(ccbConfig.getMtime())
                .micro_info(ccbConfig.getMicro_info())
                .black_mcc(ccbConfig.getBlack_mcc())
                .apply_unionpay(ccbConfig.getApply_unionpay())
                .apply_decp(ccbConfig.getApply_decp())
                .build();
        historyMapper.insertSelective(history);
    }
}
