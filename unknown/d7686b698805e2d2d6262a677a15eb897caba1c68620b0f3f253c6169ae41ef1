package com.wosai.upay.job.biz.directparams;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019-08-29
 */
@Component
public class DirectParamsBizFactory {

    private static AlipayV2DirectParamsBiz alipayV2DirectParamsBiz;

    private static WeixinDirectParamsBiz weixinDirectParamsBiz;

    private static AlipayIntlDirectParamsBiz alipayIntlDirectParamsBiz;

    private static WeixinHKDirectParamsBiz weixinHKDirectParamsBiz;

    private static BestpayDirectParamsBiz bestpayDirectParamsBiz;

    public DirectParamsBizFactory(AlipayV2DirectParamsBiz _alipayV2DirectParamsBiz,
                                  WeixinDirectParamsBiz _weixinDirectParamsBiz,
                                  AlipayIntlDirectParamsBiz _alipayIntlDirectParamsBiz,
                                  WeixinHKDirectParamsBiz _weixinHKDirectParamsBiz,
                                  BestpayDirectParamsBiz _bestpayDirectParamsBiz) {
        alipayV2DirectParamsBiz = _alipayV2DirectParamsBiz;
        weixinDirectParamsBiz = _weixinDirectParamsBiz;
        alipayIntlDirectParamsBiz = _alipayIntlDirectParamsBiz;
        weixinHKDirectParamsBiz = _weixinHKDirectParamsBiz;
        bestpayDirectParamsBiz = _bestpayDirectParamsBiz;
    }

    public static DirectParamsBiz getDirectParamsBiz(int payway) {
        if (PaywayEnum.ALIPAY.getValue() == payway) {
            return alipayV2DirectParamsBiz;
        } else if (PaywayEnum.WEIXIN.getValue() == payway) {
            return weixinDirectParamsBiz;
        } else if (PaywayEnum.ALIPAY_INTL.getValue() == payway) {
            return alipayIntlDirectParamsBiz;
        } else if (PaywayEnum.WEIXIN_HK.getValue() == payway) {
            return weixinHKDirectParamsBiz;
        } else if (PaywayEnum.BESTPAY.getValue() == payway) {
            return bestpayDirectParamsBiz;
        }
        throw new CommonPubBizException("payway " + payway + " 不支持直连交易参数");
    }
}
