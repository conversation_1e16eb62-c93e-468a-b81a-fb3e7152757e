package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.BankDirectApply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankDirectApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BankDirectApply record);

    int insertSelective(BankDirectApply record);

    BankDirectApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BankDirectApply record);

    int updateByPrimaryKeyWithBLOBs(BankDirectApply record);

    int updateByPrimaryKey(BankDirectApply record);

    BankDirectApply getApplyByTaskId(@Param("taskId") Long taskId);

    BankDirectApply getApplyBySnAndDevCode(@Param("merchantSn") String merchantSn,@Param("devCode") String devCode);

    List<BankDirectApply> listByProcessStatusAndPriorityLimit(@Param("processStatus") List<Integer> processStatus, @Param("start") String start, @Param("limit") Integer limit);


    List<BankDirectApply> listByStatusAndMtimeLimit(@Param("status") Integer status, @Param("start") String start, @Param("end") String end, @Param("limit") Integer limit);
    /**
     * @param merchantSn 商户号
     * @return List<BankDirectApply>
     * <AUTHOR>
     * @Description: 获取该商户开通成功的申请
     * @time 14:58
     */
    List<BankDirectApply> getApplyList(@Param("merchantSn") String merchantSn);
}