package com.wosai.upay.job.util;

import com.wosai.data.util.CollectionUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021-08-11
 */
public class IdTypeUtil {

    private static Map<Integer, String> idTypeMap = CollectionUtil.hashMap(
            1, "身份证",
            2, "港澳居民来往内地通行证",
            3, "台湾居民来往大陆通行证",
            4, "非中华人民共和国护照",
            5, "中华人民共和国护照",
            6, "港澳居民证",
            7, "台湾居民证"
    );

    public static String getNameByType(Integer type) {
        return idTypeMap.get(type);
    }
}
