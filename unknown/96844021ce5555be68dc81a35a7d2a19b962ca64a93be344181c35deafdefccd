package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ProviderTerminal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProviderTerminalMapper {
    int deleteByPrimaryKey(Long id);


    int insertSelective(ProviderTerminal record);

    ProviderTerminal selectByPrimaryKey(Long id);

    List<ProviderTerminal> selectByStoreSn(String storeSn);


    int updateByPrimaryKeySelective(ProviderTerminal record);

    /**
     * 根据商户号  provider 查询终端信息
     *
     * @param merchantSn
     * @param provider
     * @return
     */
    List<ProviderTerminal> selectByMerchantSnAndProviderAndAcquirerMerchantId(@Param("merchantSn") String merchantSn, @Param("provider") Integer provider, @Param("acquirerMerchantId") String acquirerMerchantId);


    /**
     * 根据商户号  provider 查询终端信息
     *
     * @param merchantSn
     * @param provider
     * @return
     */
    List<ProviderTerminal> selectByMerchantSnAndProvider(@Param("merchantSn") String merchantSn, @Param("provider") Integer provider);



    /**
     * 根据ID 更新已绑定的子商户号
     *
     * @param boundSubMchIds
     * @param id
     * @return
     */
    int updateBoundSubMchIdsById(@Param("boundSubMchIds") String boundSubMchIds, @Param("id") Long id);

    /**
     * 根据条件查询终端信息
     *
     * @param providerTerminal
     * @return
     */
    List<ProviderTerminal> selectByCondition(@Param("providerTerminal") ProviderTerminal providerTerminal);

    void deleteProviderTerminal(String merchantSn, String storeSn, String terminalSn, Integer provider);
}