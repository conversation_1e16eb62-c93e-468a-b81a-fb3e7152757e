package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/6/3 4:51 下午
 **/
@Component
public class UnionOpenParamsHandle extends ProviderParamsHandle {
    private static int PROVIDER = ProviderEnum.PROVIDER_UION_OPEN.getValue();

    @Override
    protected boolean accept(MerchantConfigParams merchantConfigParams) {
        return PROVIDER == merchantConfigParams.getProvider();
    }

    @Override
    protected Map getConfigParams(MerchantConfigParams merchantConfigParams) {
        Integer payWay = merchantConfigParams.getPayWay();
        if (payWay.equals(PaywayEnum.UNIONPAY.getValue())) {
            return CollectionUtil.hashMap(
                    TransactionParam.UNION_PAY_OPEN_MCH_ID, merchantConfigParams.getPayMerchantId(),
                    TransactionParam.UNION_PAY_OPEN_TERM_ID, merchantConfigParams.getLklTermId());
        }
        return null;
    }
}
