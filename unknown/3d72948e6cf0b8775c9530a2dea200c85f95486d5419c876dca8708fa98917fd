package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.PayWayConfigChange;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PayWayConfigChangeMapper {


    int insertSelective(PayWayConfigChange record);

    PayWayConfigChange selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayWayConfigChange record);

    List<PayWayConfigChange> selectByUpdateAt(String begin, String end, Integer limit);

    PayWayConfigChange selectConfigChange(@Param("merchantSn") String merchantSn, @Param("channel") String channel, @Param("payway") Integer payway);
}