package com.wosai.upay.job.model.DO;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AntShopTask {
    private Long id;

    private String merchant_sn;

    private String merchant_id;

    private String store_sn;

    private String ali_mch_id;

    private Integer business_type;

    private Integer status;

    private String description;

    private String ant_shop_order_id;

    private String ant_shop_id;

    private Integer retry;

    private String extra;

    private Date create_at;

    private Date update_at;

    private Date priority;

    private String request_body;

    private String response_body;
}