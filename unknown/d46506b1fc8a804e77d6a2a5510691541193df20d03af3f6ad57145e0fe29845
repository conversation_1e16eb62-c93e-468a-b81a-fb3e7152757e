package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: industry_code_v2 收钱吧与各机构间的行业映射关系
 * <AUTHOR>
 * @Date 2020/8/27 7:05 PM
 **/
@Data
@Accessors(chain = true)
public class IndustryCodeV2 {
    private String id;

    /**
     * 收钱吧行业id
     */
    private String industry_id;

    /**
     * 银联对应行业编号(微信对私)
     */
    private String union_code_weixin_private;

    /**
     * 银联对应行业编号(微信对公)
     */
    private String union_code_weixin_public;

    /**
     * 网联对应行业编号(微信对私)
     */
    private String nucc_code_weixin_private;

    /**
     * 网联对应行业编号(微信对公)
     */
    private String nucc_code_weixin_public;

    /**
     * 银联对应行业编号(支付宝对私)
     */
    private String union_code_alipay_private;

    /**
     * 银联对应行业编号(支付宝对公)
     */
    private String union_code_alipay_public;

    /**
     * 网联对应行业编号(支付宝对私)
     */
    private String nucc_code_alipay_private;

    /**
     * 网联对应行业编号(支付宝对公)
     */
    private String nucc_code_alipay_public;

    /**
     * 银联对应行业编号(翼支付对公)
     */
    private String nucc_code_bestpay_public;

    /**
     * 银联对应行业编号(翼支付对私)
     */
    private String nucc_code_bestpay_private;

    /**
     * 翼支付对应行业编号
     */
    private String nucc_code_bestpay;

    /**
     * 拉卡拉对应行业编号
     */
    private String lakala_code;

    /**
     * 通联对应行业编号
     */
    private String tl_code;

    /**
     * 万码支付宝
     */
    private String wm_aly;

    /**
     * 万码微信对私
     */
    private String wm_weixin_private;

    /**
     * 万码微信对公
     */
    private String wm_weixin_public;

    /**
     * 银联开放平台
     */
    private String union_open_code;

    /**
     * 直连微信
     */
    private String direct_connect_weixin_code;

    /**
     * 万码支付宝MCC
     */
    private String wm_aly_mcc;

    private String version;


}