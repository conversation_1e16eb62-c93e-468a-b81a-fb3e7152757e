package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.model.ums.StoreUmsImportParamsExcel;
import com.wosai.upay.job.model.ums.StoreUmsImportParamsResult;
import com.wosai.upay.job.model.ums.UmsImportParamsExcel;
import com.wosai.upay.job.model.ums.UmsImportParamsResult;
import com.wosai.upay.job.model.dto.ImportUmsParamsDTO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.BatchChangeAcquireUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.shouqianba.cua.enums.contract.ProviderEnum.PROVIDER_UMS;

/**
 * <AUTHOR>
 * @date 2025/3/24
 */
@Component
@Slf4j
public class UmsImportBiz {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    @Qualifier("merchantContractJdbcTemplate")
    private JdbcTemplate merchantContractJdbcTemplate;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreService storeService;

    /**
     * 商户维度批量导入银商参数
     * @param importUmsParamsDTO 请求dto
     */
    public void importUmsParams(ImportUmsParamsDTO importUmsParamsDTO) {
        final CallBackBean callBackBean = importUmsParamsDTO.getCallBackBean();
        try {
            List<UmsImportParamsExcel> umsImportParams = BatchChangeAcquireUtil.getExcelInfoList(importUmsParamsDTO.getAttachmentUrl(), new UmsImportParamsExcel());
            log.info("解析得到的银商参数集合：{}", umsImportParams);
            List<UmsImportParamsResult> results = new ArrayList<>();
            for (UmsImportParamsExcel umsImportParam : umsImportParams) {
                UmsImportParamsResult result = new UmsImportParamsResult()
                        .setMerchantSn(umsImportParam.getMerchantSn())
                        .setResult("成功");
                try {
                    doImportUmsParams(umsImportParam);
                } catch (Exception e) {
                    result.setResult(e.getMessage());
                }
                results.add(result);
            }
            String resultUrl = BatchChangeAcquireUtil.uploadToOss(results, "umsImport/batch/");
            callBackBean.setMessage("处理结果: " + resultUrl);
            callBackService.addComment(callBackBean);
        } catch (Exception exception) {
            log.error("importUmsParams error {}", JSON.toJSONString(importUmsParamsDTO), exception);
            String msg = ExceptionUtil.getThrowableMsg(exception);
            callBackBean.setMessage("处理失败: " + msg);
            callBackService.addComment(callBackBean);
        }

    }

    private static final String TEMPLATE = "UPDATE `contract_status` SET `acquirer` = 'lklV3' WHERE `merchant_sn` = '1680007451370' and acquirer = 'ums';\n" +
            "DELETE FROM `merchant_provider_params` WHERE `merchant_sn` = '1680007451370' and `provider` = 1018;\n" +
            "DELETE FROM `sub_biz_params` WHERE `merchant_sn` = '1680007451370' and `provider` = 1018;\n" +
            "INSERT INTO `merchant_provider_params`(`id`,`merchant_sn`,`out_merchant_sn`,`merchant_name`,`channel_no`,`parent_merchant_id`,`provider`,`provider_merchant_id`,`payway`,`params_config_status`,`pay_merchant_id`,`weixin_sub_appid`,`weixin_subscribe_appid`,`weixin_sub_mini_appid`,`weixin_receipt_appid`,`status`,`extra`,`ctime`,`mtime`,`deleted`,`version`,`contract_rule`,`rule_group_id`,`update_status`,`auth_status`,`gold_status`,`wx_settlement_id`,`wx_use_type`,`ali_mcc`,`merchant_state`) " +
            "VALUES(uuid(),'1680007451370','1680007451370',NULL,'2088011691288213','89837015812ZCPY','1018','89837015812ZCPX','2','2','2088650338490985|2088650334575736',NULL,NULL,NULL,NULL,'0','{\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}}','1723597945177','1724133369866','0','1','ums-1018-2','ums','1','0','0',NULL,NULL,NULL,'1')," +
            "(uuid(),'1680007451370','1680007451370','济南市市中区桐睿餐饮服务铺','207597046','89837015812ZCPY','1018','89837015812ZCPX','3','1','675942833|675942834','wx72534f3638c59073',NULL,'wxccbcac9a3ece5112',NULL,'0','{\"auth_time\":1723597945177,\"appid_config_list\":[{\"sub_appid\":\"wxd2f16468474f61b8\",\"type\":2},{\"sub_appid\":\"wx72534f3638c59073\",\"type\":1},{\"sub_appid\":\"wxccbcac9a3ece5112\",\"type\":2}],\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}}','1723597945177','1724133369718','0','1','ums-1018-3','ums','1','1','2',NULL,NULL,NULL,'1')," +
            "(uuid(),'1680007451370','1680007451370',NULL,'KF042900','89837015812ZCPY','1018','89837015812ZCPX','0','2','89837015812ZCPY',NULL,NULL,NULL,NULL,'0','{\"2\":{\"trade\":{\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}},\"pay_merchant_id\":\"2088650338490985|2088650334575736\",\"provider_merchant_id\":\"89837015812ZCPX\",\"parent_merchant_id\":\"89837015812ZCPY\"},\"3\":{\"trade\":{\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}},\"pay_merchant_id\":\"675942833|675942834\",\"provider_merchant_id\":\"89837015812ZCPX\",\"parent_merchant_id\":\"89837015812ZCPY\"},\"17\":{\"trade\":{\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}},\"pay_merchant_id\":\"89837015812ZCPY|89837015812ZCPX\",\"provider_merchant_id\":\"89837015812ZCPX\",\"parent_merchant_id\":\"89837015812ZCPY\"}}','1723597945177','1723597945177','0','1','ums','ums','1','0','0',NULL,NULL,NULL,'1')," +
            "(uuid(),'1680007451370','1680007451370',NULL,'KF042900','89837015812ZCPY','1018','89837015812ZCPX','17','2','89837015812ZCPY|89837015812ZCPX',NULL,NULL,NULL,NULL,'0','{\"chinaums_trade_params\":{\"mch_code\":\"89837015812ZCPX\",\"term_code\":\"8699N5TW\",\"csb_mch_code\":\"89837015812ZCPY\",\"csb_term_code\":\"8699N5TX\"}}','1723597945177','1724133370041','0','1','ums-1018-17','ums','1','0','0',NULL,NULL,NULL,'1');";


    @Transactional
    public void doImportUmsParams(UmsImportParamsExcel umsImportParams) {
        String result = TEMPLATE.replaceAll("1680007451370", umsImportParams.getMerchantSn().trim())
                .replaceAll("89837015812ZCPX", umsImportParams.getBscUmsSn().trim())
                .replaceAll("8699N5TW", umsImportParams.getBscTerm().trim())
                .replaceAll("675942833", umsImportParams.getBscWx().trim())
                .replaceAll("2088650338490985", umsImportParams.getBscAli().trim())
                .replaceAll("89837015812ZCPY", umsImportParams.getCsbUmsSn().trim())
                .replaceAll("8699N5TX", umsImportParams.getCsbTerm().trim())
                .replaceAll("675942834", umsImportParams.getCsbWx().trim())
                .replaceAll("2088650334575736", umsImportParams.getCsbAli().trim());
        for (String sql : result.split("\n")) {
            merchantContractJdbcTemplate.update(sql);
        }
        AcquirerService acquirerService = applicationContext.getBean(AcquirerService.class);
        acquirerService.applyChangeAcquirer(umsImportParams.getMerchantSn(), "ums", true);
    }

    /**
     * 门店维度批量导入银商参数
     * @param importUmsParamsDTO 请求dto
     */
    public void storeImportUmsParams(ImportUmsParamsDTO importUmsParamsDTO) {
        final CallBackBean callBackBean = importUmsParamsDTO.getCallBackBean();
        try {
            List<StoreUmsImportParamsExcel> storeUmsImportParams = BatchChangeAcquireUtil.getExcelInfoList(importUmsParamsDTO.getAttachmentUrl(), new StoreUmsImportParamsExcel());
            log.info("解析得到的银商参数集合：{}", storeUmsImportParams);
            List<StoreUmsImportParamsResult> results = new ArrayList<>();
            for (StoreUmsImportParamsExcel storeUmsImportParam : storeUmsImportParams) {
                StoreUmsImportParamsResult result = new StoreUmsImportParamsResult()
                        .setStoreSn(storeUmsImportParam.getStoreSn())
                        .setResult("成功");
                try {
                  doStoreImportUmsParams(storeUmsImportParam);
                } catch (Exception e) {
                    result.setResult(e.getMessage());
                }
                results.add(result);
            }
            String resultUrl = BatchChangeAcquireUtil.uploadToOss(results, "storeUmsImport/batch/");
            callBackBean.setMessage("处理结果: " + resultUrl);
            callBackService.addComment(callBackBean);
        } catch (Exception exception) {
            log.error("importUmsParams error {}", JSON.toJSONString(importUmsParamsDTO), exception);
            String msg = ExceptionUtil.getThrowableMsg(exception);
            callBackBean.setMessage("处理失败: " + msg);
            callBackService.addComment(callBackBean);
        }
    }

    private void doStoreImportUmsParams(StoreUmsImportParamsExcel storeUmsImportParam) {
        String storeSn = storeUmsImportParam.getStoreSn();
        StoreInfo storeInfo = storeService.getStoreBySn(storeSn, null);
        if (Objects.isNull(storeInfo)) {
            log.error("门店sn:{}，该门店不存在", storeSn);
            throw new RuntimeException("查询该门店不存在");
        }
        final TradeExtConfigQueryRequest tradeExtConfigQueryRequest = new TradeExtConfigQueryRequest();
        tradeExtConfigQueryRequest.setSn(storeSn);
        tradeExtConfigQueryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
        tradeExtConfigQueryRequest.setProvider(PROVIDER_UMS.getValue());
        final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(tradeExtConfigQueryRequest);
        log.info("查询到的trade_ext_config对象：{}", JSON.toJSONString(tradeExtConfig));
        if (Objects.isNull(tradeExtConfig)) {
            TradeExtConfigCreateRequest createRequest = new TradeExtConfigCreateRequest();
            createRequest.setSn(storeSn);
            createRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
            createRequest.setProvider(PROVIDER_UMS.getValue());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(getStoreTradeTermId(storeUmsImportParam));
            createRequest.setContent(content);
            tradeConfigService.createTradeExtConfig(createRequest);
        } else {
            TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
            updateRequest.setSn(storeSn);
            updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_STORE);
            updateRequest.setProvider(PROVIDER_UMS.getValue());
            TradeExtConfigContentModel content = new TradeExtConfigContentModel();
            content.setTermId(getStoreTradeTermId(storeUmsImportParam));
            updateRequest.setContent(content);
            tradeConfigService.updateTradeExtConfig(updateRequest);
        }
        // 清除交易参数缓存
        supportService.removeCachedParams(getMerchantSnByStoreInfo(storeInfo));

    }

    private String getStoreTradeTermId(StoreUmsImportParamsExcel storeUmsImportParam) {
        return String.join(",", storeUmsImportParam.getBscTerm(), storeUmsImportParam.getCsbTerm());
    }

    private String getMerchantSnByStoreInfo(StoreInfo storeInfo) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(storeInfo.getMerchant_id(), null);
        return merchantInfo.getSn();
    }
}
