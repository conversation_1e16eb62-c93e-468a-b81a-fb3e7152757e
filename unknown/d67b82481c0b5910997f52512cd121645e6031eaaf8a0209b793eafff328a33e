package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.AuthAndComboTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface AuthAndComboTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AuthAndComboTask record);

    int insertSelective(AuthAndComboTask record);

    AuthAndComboTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthAndComboTask record);

    int updateByPrimaryKeyWithBLOBs(AuthAndComboTask record);

    int updateByPrimaryKey(AuthAndComboTask record);

    @Select("select * from auth_and_combo_task where create_at >= #{start} and create_at < current_timestamp() and status = 0 limit #{limit}")
    List<AuthAndComboTask> selectWaitForSubmit(@Param("start") Date start, @Param("limit") int limit);

    @Select("select * from auth_and_combo_task where create_at >= #{start} and create_at < current_timestamp() and status = 1 limit #{limit}")
    List<AuthAndComboTask> selectWaitForAuth(@Param("start") Date start, @Param("limit") int limit);

    @Select("select * from auth_and_combo_task where create_at >= #{start} and create_at < #{end} and status = 1 limit #{limit}")
    List<AuthAndComboTask> selectForceChangeTradeParams(@Param("start") Date start, @Param("end") Date end, @Param("limit") int limit);

    @Select("select * from auth_and_combo_task where create_at > #{start} and status = 2 limit #{limit}")
    List<AuthAndComboTask> selectWaitForApplyFeeRate(@Param("start") Date start, @Param("limit") int limit);

    @Select("select * from auth_and_combo_task where merchant_sn =  #{merchantSn} order by create_at desc limit 1")
    AuthAndComboTask getLatestTask(@Param("merchantSn") String merchantSn);
}