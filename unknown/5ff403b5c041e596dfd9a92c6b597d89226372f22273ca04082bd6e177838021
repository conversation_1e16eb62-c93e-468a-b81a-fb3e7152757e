package com.wosai.upay.job.util.umb;

import cfca.sadk.algorithm.common.CBCParam;
import cfca.sadk.algorithm.common.Mechanism;
import cfca.sadk.algorithm.common.PKCS7EnvelopedData;
import cfca.sadk.algorithm.common.PKCS7SignedData;
import cfca.sadk.algorithm.common.PKIException;
import cfca.sadk.algorithm.util.RSAAndItsCloseSymAlgUtil;
import cfca.sadk.algorithm.util.SM2AndItsCloseSymAlgUtil;
import cfca.sadk.lib.crypto.JCrypto;
import cfca.sadk.lib.crypto.Session;
import cfca.sadk.lib.crypto.jni.JNISoftLib;
import cfca.sadk.org.bouncycastle.asn1.ASN1OctetString;
import cfca.sadk.org.bouncycastle.asn1.ASN1Set;
import cfca.sadk.org.bouncycastle.asn1.DEROctetString;
import cfca.sadk.org.bouncycastle.asn1.cms.ContentInfo;
import cfca.sadk.org.bouncycastle.asn1.cms.EncryptedContentInfo;
import cfca.sadk.org.bouncycastle.asn1.cms.EnvelopedData;
import cfca.sadk.org.bouncycastle.asn1.cms.IssuerAndSerialNumber;
import cfca.sadk.org.bouncycastle.asn1.cms.KeyTransRecipientInfo;
import cfca.sadk.org.bouncycastle.asn1.cms.RecipientIdentifier;
import cfca.sadk.org.bouncycastle.asn1.cms.RecipientInfo;
import cfca.sadk.org.bouncycastle.asn1.x500.X500Name;
import cfca.sadk.org.bouncycastle.asn1.x509.AlgorithmIdentifier;
import cfca.sadk.org.bouncycastle.cms.CMSEnvelopedData;
import cfca.sadk.util.Base64;
import cfca.sadk.util.CertUtil;
import cfca.sadk.util.KeyUtil;
import cfca.sadk.util.Signature;
import cfca.sadk.x509.certificate.X509Cert;
import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;

@Slf4j
public class SM2Utils {
    private static Session session = null;
    private static final String SYMMETRIC_ALGORITHM = "SM4/ECB/PKCS7Padding";
    private static final String SM_3_WITH_SM_2_ENCRYPTION = "sm3WithSM2Encryption";

    static {
        try {
            JCrypto crypto = JCrypto.getInstance();
            crypto.initialize(JCrypto.JSOFT_LIB, null);
            session = crypto.openSession(JCrypto.JSOFT_LIB);
        } catch (PKIException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static byte[] envelopeMessage(String sourceString, String cerInBase64) throws Exception {
        byte[] decodedCertBytes = java.util.Base64.getDecoder().decode(cerInBase64);
        byte[] sourceData = sourceString.getBytes(StandardCharsets.UTF_8);
        X509Cert x509Cert = new X509Cert(decodedCertBytes);
        return SM2EnvelopeUtil.envelopeMessage(sourceData, SYMMETRIC_ALGORITHM, new X509Cert[]{x509Cert}, session);
    }

    public static String openEnveloped(String base64EnvelopeMessage, String sm2InBase64, String sm2Pwd) throws Exception {
        byte[] decodedCertBytes = java.util.Base64.getDecoder().decode(sm2InBase64);
        X509Cert certFromSM2 = CertUtil.getCertFromSM2(decodedCertBytes);
        PrivateKey privateKey = KeyUtil.getPrivateKeyFromSM2(decodedCertBytes, sm2Pwd);
        return new String(openEvelopedMessage(base64EnvelopeMessage.getBytes(StandardCharsets.UTF_8), privateKey, certFromSM2, session));
    }

    public static String P7SignMessageDetach(String base64SourceString, String sm2InBase64, String sm2Pwd) {
        try {
            byte[] sourceData = Base64.decode(base64SourceString);
            byte[] decodedCertBytes = java.util.Base64.getDecoder().decode(sm2InBase64);
            CertUtil.getCertFromSM2(decodedCertBytes);
            X509Cert certFromSM2 = CertUtil.getCertFromSM2(decodedCertBytes);
            PrivateKey privateKey = KeyUtil.getPrivateKeyFromSM2(decodedCertBytes, sm2Pwd);
            Signature signature = new Signature();
            byte[] base64Bytes = signature.p7SignMessageDetach(SM_3_WITH_SM_2_ENCRYPTION, sourceData, privateKey, certFromSM2, session);
            return new String(base64Bytes);
        } catch (Exception e) {
            return "";
        }
    }

    public static boolean P7VerifyMessageDetach(String base64SourceString, String base64SignatureString, String cerInBase64) {
        try {
            byte[] decodedCertBytes = java.util.Base64.getDecoder().decode(cerInBase64);
            byte[] sourceData = Base64.decode(base64SourceString);
            byte[] signatureData = Base64.decode(base64SignatureString);
            Signature signature = new Signature();
            PKCS7SignedData p7 = new PKCS7SignedData(session);
            p7.loadBase64(signatureData);
            X509Cert signerX509Cert = p7.getSignerX509Cert();
            X509Cert clientCer = new X509Cert(decodedCertBytes);
            BigInteger serialNumber = signerX509Cert.getSerialNumber();
            String subject = signerX509Cert.getSubject();
            BigInteger serialNumberOfclient = clientCer.getSerialNumber();
            String subjectOfclient = clientCer.getSubject();
            if (serialNumber.compareTo(serialNumberOfclient) != 0) {
                System.out.println("序列号[" + serialNumber + "]与商户上传证书序列号[" + serialNumberOfclient + "]不符");
                return false;
            }
            if (!subject.equals(subjectOfclient)) {
                System.out.println("证书DN[" + subject + "]与商户上传证书序列号DN[" + subjectOfclient + "]不一致{}");
                return false;
            }
            return signature.p7VerifyMessageDetach(sourceData, signatureData, session);
        } catch (Exception e) {
            return false;
        }
    }

    public static byte[] openEvelopedMessage(byte[] base64EnvelopeMessage, PrivateKey privateKey, X509Cert recipientCert, Session session) throws PKIException {
        if (session == null) {
            throw new IllegalArgumentException("session");
        } else {
            try {
                boolean isSM2Type = CertUtil.isSM2Cert(recipientCert);
                byte[] bEnvelop = Base64.decode(base64EnvelopeMessage);
                CMSEnvelopedData cmsEnData = new CMSEnvelopedData(bEnvelop);
                ContentInfo info = cmsEnData.toASN1Structure();
                EnvelopedData enData = EnvelopedData.getInstance(info.getContent());
                ASN1Set receivers = enData.getRecipientInfos();
                X500Name recipientIssuer = recipientCert.getIssuerX500Name();
                BigInteger recipientSN = recipientCert.getSerialNumber();
                byte[] subjectPubKeyID = recipientCert.getSubjectKeyIdentifier().getKeyIdentifier();
                if (receivers == null) {
                    throw new PKIException("the receiver is null!!!");
                } else {
                    ASN1OctetString encryptKey = null;
                    AlgorithmIdentifier algId = null;
                    int len = receivers.size();

                    for (int i = 0; i < len; ++i) {
                        RecipientInfo recip = RecipientInfo.getInstance(receivers.getObjectAt(i));
                        if (recip.getInfo() instanceof KeyTransRecipientInfo) {
                            KeyTransRecipientInfo inf = KeyTransRecipientInfo.getInstance(recip.getInfo());
                            if (hasRecipent(inf, subjectPubKeyID, recipientIssuer, recipientSN)) {
                                encryptKey = inf.getEncryptedKey();
                                algId = inf.getKeyEncryptionAlgorithm();
                                break;
                            }
                        }
                    }

                    if (encryptKey != null && algId != null) {
                        Mechanism contentEncryptionAlg = null;
                        if (isSM2Type) {
                            contentEncryptionAlg = new Mechanism("SM2");
                        } else {
                            contentEncryptionAlg = new Mechanism("RSA/ECB/PKCS1PADDING");
                        }

                        byte[] symmetricKey = session.decrypt(contentEncryptionAlg, privateKey, encryptKey.getOctets());
                        EncryptedContentInfo data = enData.getEncryptedContentInfo();
                        ASN1OctetString os = data.getEncryptedContent();
                        AlgorithmIdentifier symmetricAlgId = data.getContentEncryptionAlgorithm();
                        String encryptionAlgStr = (String) PKCS7EnvelopedData.OID_MECH.get(symmetricAlgId.getAlgorithm());
//                        String encryptionAlgStr = "SM4/ECB/PKCS7Padding";


                        Mechanism mechanism = null;
                        CBCParam sourceData;
                        if (encryptionAlgStr.indexOf("CBC") != -1) {
                            DEROctetString doct = (DEROctetString) symmetricAlgId.getParameters();
                            sourceData = new CBCParam(doct.getOctets());
                            if (encryptionAlgStr.equals("DESede/CBC/PKCS7Padding")) {
                                mechanism = new Mechanism("DESede/CBC/PKCS7Padding", sourceData);
                            } else if (encryptionAlgStr.equals("SM4/CBC/PKCS7Padding")) {
                                mechanism = new Mechanism("SM4/CBC/PKCS7Padding", sourceData);
                            }
                        } else if (encryptionAlgStr.indexOf("ECB") != -1) {
                            if (encryptionAlgStr.equals("DESede/ECB/PKCS7Padding")) {
                                mechanism = new Mechanism("DESede/ECB/PKCS7Padding");
                            } else if (encryptionAlgStr.equals("SM4/ECB/PKCS7Padding")) {
                                mechanism = new Mechanism("SM4/ECB/PKCS7Padding");
                            }
                        } else if (encryptionAlgStr.indexOf("RC4") != -1) {
                            mechanism = new Mechanism("RC4");
                        }

                        if (mechanism == null) {
                            throw new PKIException(PKIException.UNSUPPORT_ENCRYPT_ALG_SIGNANDENVELOP_ERR, PKIException.UNSUPPORT_ENCRYPT_ALG_SIGNANDENVELOP_ERR_DES + "Algorithm is:" + encryptionAlgStr);
                        } else {
                            boolean useJNI = false;
                            if (session != null && session instanceof JNISoftLib) {
                                useJNI = true;
                            }

                            sourceData = null;
                            byte[] sourceDatas;
                            if (isSM2Type) {
                                sourceDatas = SM2AndItsCloseSymAlgUtil.crypto(useJNI, false, symmetricKey, os.getOctets(), mechanism);
                            } else {
                                sourceDatas = RSAAndItsCloseSymAlgUtil.crypto(useJNI, false, symmetricKey, os.getOctets(), mechanism);
                            }

                            return sourceDatas;
                        }
                    } else {
                        throw new PKIException("can not find the receiver!!!");
                    }
                }
            } catch (Exception var25) {
                throw new PKIException("850935", "解析消息数字信封失败", var25);
            }
        }
    }

    private static boolean hasRecipent(KeyTransRecipientInfo inf, byte[] subjectPubKeyID, X500Name recipientIssuer, BigInteger recipientSN) {
        RecipientIdentifier id = inf.getRecipientIdentifier();
        DEROctetString oct = new DEROctetString(subjectPubKeyID);
        IssuerAndSerialNumber issu = new IssuerAndSerialNumber(recipientIssuer, recipientSN);
        return id.getId().toASN1Primitive().asn1Equals(oct) || id.getId().toASN1Primitive().asn1Equals(issu.toASN1Primitive());
    }

    public static void main(String[] args) throws Exception {

        //签名
//        String signdata = "测试一下sign";
//        String merSignPath = "D:\\dev_docs\\interface_docs\\多级账户\\cms-demo\\src\\resources\\cert\\mechant_sign_sm2.sm2";
//        String mac = SM2Utils.P7SignMessageDetach("sm3WithSM2Encryption", new String(Base64.encode(signdata.getBytes("UTF-8"))), merSignPath, "UMBPAY2022");
//        System.out.println("软算法签名结果:" + mac);
//        //加密
//        String encdata = "测试一下enc";
//        String content = new String(Files.readAllBytes(Paths.get("D:\\dev_docs\\interface_docs\\多级账户\\cms-demo\\src\\resources\\cert\\mechant_enc_sm2.cer")));
//        content = content.replaceAll("\\n", "");
//        content = content.replace("\\r", "");
//        String encryptdata = SM2Utils.encryptByCer(encdata, content);
//        System.out.println("软算法加密结果:" + encryptdata);

        //解密
        String encodeData = "MIIIPAYKKoEcz1UGAQQCA6CCCCwwgggoAgEAMYHCMIG/AgEAMC4wJTELMAkGA1UEBhMCQ04xFjAUBgNVBAoMDUNGQ0EgU00yIE9DQTECBRUiVxJzMA0GCSqBHM9VAYItAwUABHsweQIgYIQD/JeVWk6xbeT/paZO+sf2q31jpppSw51JnJWKxFcCIQD3mjj35zaJ83uDPOmu1RhOT+GwkOs+4xvq+m5FuU8HbgQgTw3depsORh5KCM7kSgJc/Cc3pKaqFhOfqBItY/aBBsIEEIeBiW3q4vYLK3BmuMmfzKQwggdcBgoqgRzPVQYBBAIBMAoGCCqBHM9VAWgBgIIHQJYsVpBWQ/poijUpiAvP8iwSHqPPtOOtaJ8IRUUz/lsQV3GGMmAURu1ZE3RRrZJMAxbT9VpSsCE2NaV5GdTlf8sD1DCcIaZbVnTWsBS4VwrSuJFXNOUm5DQiOhyRjdEmrrwMJ4yeN1W8fVTI76jrcIZUsefbLs6gmjRR5U5J+bVN0tTePso07w4Zzmu0H4fmaZx624CGy+kINfG05H2juZT81f8kQbL8B9GZOUbuzoqU/ts36fn21c6wSb4RSxa4x+enKjGCPuo61ChOWCFLxK5myV1Yzg1/xtYJ+G3xQROdndjszU1OfUOfrtNPkkxQG5PVGUCSwPzn4seLQASeeLX4mIarapYVSCvwohNg1ZsEIWcYD5CGr0a6GQe+HZjZqOXAYm7kdnytcpfkPh3x7mNw8BesNRd+Aly32biXn7Y36e1x6wFFKPkavXpivdub1H16VSBDXiL/MlyQq5HBqqpbN0+aq/mVONB0/lv/QSx8TQYYtx/q7Ril4g+yl7MzrMncMtNoHMUWZOlIWImDx1yMYFtaV4yD25UxLEyCgyjwIaNTUMLgS+VTHddQuM+l2520JimxetNIXF3xiWug7z2qowTMT1vWa1ZJkawB7oNjK4/JEmMSlMC+qsYL54BnkhMiGDYrS/o5+6tDZVYvRz89gDiKbvIHUmgO3pPSZMC4ku5vGluNShbHHm9n/UlTAPIWsMjOwxmewa5p0iQ0g0v7Rr7V4GsA0vws5aV7chuwQPPg/NEfxIaLlTKgaUVUp4y3KwhbkE3mV9ChBgJYcfsfFkYxiuJXaxWFZpyzNjfVfdPLjXIi4Ruw3XZnYdRUiTxjC7B9n5l8e456ljyQvykgM6lhQQKUqQWahdoiza+fc66K7gwN1Ws/7ztqrO5IxcKNdyXt8Tb2p0qxd0lUS2V8knuN+fyMUxkJgwFCJQ6m3BkK176M2N/xo9CNCjyMjmEUi11W+Gyy2caA2lrYZXAhXAM2xpm//pYpPlygOuMaMgEHQon6y7XcGVs8ye4wpM+tzAzOmImusAZbO3ZGlFu0IxxrpW59WJbTyEZUyrbOtMjTZtmpvGI2XbEQ3YvY3Yyah4hTL2XNJ3M2svcbEaLBSVhqxBM4M5ymIsXC/B5J9GaMOIZ+pVRcYzPL0i8aRASbzqzyutdXwZNENMrS4JHRE6ZQgqWmYPKy6Owuz1Oa3MienY/dd4Uai/gqPEvdL7NUt8CXa5h+8i12XgiJusvY7tWkQjK+GjypAOOePHYl5u0l3iZYR0dbtkIMizmpwMP5YPIaGb3m6ZRzrIeMqNNddywbfN3owIDQZf02seNtaCp7UFMDyNaq6euu0Be49UtYGDlrrPWZfWaF8jVazOa+J+/3RL3GkXO5Re/mskNU2WFwN/TGOxzSXLqbJ7ybeh1fQjoyNR9ZoRbIN7/w+0QcU96dkXgqiQX5ZCUcJg6jvAPL7eeUoOI7/JWizDlqqrLxHHlp8TqjfVczkqp9XFbTHt+85vzxLP3vWzY/uqoeEWKvEww2H3nV69xgBR1JFOjPrk4bxH+1Wl+DOTmlH5nVzybkCyoUnXvbMdQya5kyN4mws0jqnDsVHsWVxtqLOArN3jBytMLpOpveqyk/h1UUH+91A1FusefNNpPOYwOFvAZdVt+TlbQ5jxvShWVNvnguQ/mSqRwrE039ECZzzHEB6+rx9qDzju8YvN3NW7wf2hvZwwU+Tsi8BEPxCyQp0UwT7mqi/QLv8w7L1maxRDxKsZWcQIiVR5ZPsy/ttGWmBwJsrz9MnaxEwVP6kXx4Jbg3Vk8RGyi4EYTcClWUdeI8Seya3k3bD5SqNHc7W0K168wpoKQA5e88lP5ddZOHKVqU+gJs6MdLt2yD1lYRvZm47WeLX4sg1pAj2pPYAGyvUud9SjHKTHnyDGYxyiwwq1H7PdberCITJDabSijpprNI6l7ydUAihPjMJqq27UhdW9BFWTZ9nVXUH4ApjWyzthlJgB4vMoe1zZnUZdjZdGEiMdKVverjBbrRGOeK6P0h9506HX+G1yeS/uHjfUNjkOUVGRI+ERcLeTkqLOLQaCBY33d1KB6pAH09F0QjMX1+DDEAkqhc1omxfY0dtcPlwkDybyEFiUsbbV4t5wJVarcIl5pxYOau/QlNwcETfLMjEAa2PqVRIKhygWqAyc0FdiKBR/MlPbbwWjl9ACG/oly6itfPd/GBhLW7bmit5e61yJFT5SuNmxgldvaAdAE6Reb8MHxmb/4JvYfbUPPfVOnF5iu4oVc3RvUNtwEswmgflLGBT0nb5TDX6IfXLQuB/e/bKppOmlr6KWzfPMSy+O7ypbrXvsJPvIjEEANH8NpWyjK6t5BoMSLdMP/2LlkAtNNau8t38r+THnhERN3XhHBTOIGvvzguGELGHDCxENUkkfxWwxuK7vKJA3V2lDRxpKjYVwhriDkW+1njqynVzKRyH5MTQIRGom32bcU+1K1P";
        String decodeData = SM2Utils.openEnveloped(encodeData,
                "TUlJQyt3SUJBVEJIQmdvcWdSelBWUVlCQkFJQkJnY3FnUnpQVlFGb0JEREtycFN2cEdiQ2ZNMUMwN1VXbkZvL0tkTUtoL1YxQmRkZQ0KdUFibk01VGtFWUU5YW9oWWlCdVRVN3A4WFF2cHhHY3dnZ0tyQmdvcWdSelBWUVlCQkFJQkJJSUNtekNDQXBjd2dnSTdvQU1DQVFJQw0KQlJVaVZ4SnpNQXdHQ0NxQkhNOVZBWU4xQlFBd0pURUxNQWtHQTFVRUJoTUNRMDR4RmpBVUJnTlZCQW9NRFVOR1EwRWdVMDB5SUU5RA0KUVRFd0hoY05NalV3TXpFM01EZ3dNakk0V2hjTk1qZ3dNekUzTURnd01qSTRXakI4TVFzd0NRWURWUVFHRXdKamJqRVNNQkFHQTFVRQ0KQ2d3SlEwWkRRU0JQUTBFeE1STXdFUVlEVlFRTERBcENTbHBJVDA1SFZFOVZNUlF3RWdZRFZRUUxEQXRGYm5SbGNuQnlhWE5sY3pFdQ0KTUN3R0ExVUVBd3dsTURReFFGcERSakl3TURJek1qTXlPREJBYzJodmRYRnBZVzVpWVVBd01EQXdNREF3TVRCWk1CTUdCeXFHU000OQ0KQWdFR0NDcUJITTlWQVlJdEEwSUFCTXVYOVdjK3R0NEZST1B5L3ZOSkQwckJrajl6cTJIT1BvckIzOE41VWdiaE5QU1hhVERUOU5tNw0KSFhJeTZaYzBhZEpXRkQ5QUtlMkd0OE5rMUFabzVLU2pnZjR3Z2Zzd0h3WURWUjBqQkJnd0ZvQVVYSk5ZSUZva2MxWVFHMlJRRU96cA0KcDhvSFFSRXdDUVlEVlIwVEJBSXdBREJJQmdOVkhTQUVRVEEvTUQwR0NHQ0JISWJ2S2dFQk1ERXdMd1lJS3dZQkJRVUhBZ0VXSTJoMA0KZEhBNkx5OTNkM2N1WTJaallTNWpiMjB1WTI0dmRYTXZkWE10TVRRdWFIUnRNRGdHQTFVZEh3UXhNQzh3TGFBcm9DbUdKMmgwZEhBNg0KTHk5amNtd3VZMlpqWVM1amIyMHVZMjR2VTAweUwyTnliREV3TXpJd0xtTnliREFMQmdOVkhROEVCQU1DQStnd0hRWURWUjBPQkJZRQ0KRk0xcDV1dDluejJUajNRYTFXQkpYa01TWWVqSk1CMEdBMVVkSlFRV01CUUdDQ3NHQVFVRkJ3TUNCZ2dyQmdFRkJRY0RCREFNQmdncQ0KZ1J6UFZRR0RkUVVBQTBnQU1FVUNJRTFBVjNNcFpmTlFjWHpDMUxCWlhSNWxJaXZHblFZQjVWNXRoVkVpdWNrK0FpRUF1UjJGMXBDOA0KUUxCVi91blNjWDh4NGFObllvaFpRMnp0QnllWGQza3BPQ2c9",
                "wosai7890");
        System.out.println("解密结果:" +decodeData);
        //验签
//        String signencdata = "MIICdAYKKoEcz1UGAQQCAqCCAmQwggJgAgEBMQ4wDAYIKoEcz1UBgxEFADAMBgoqgRzPVQYBBAIBoIIBfjCCAXowggEdoAMCAQICCH0bmmUy5wEqMAwGCCqBHM9VAYN1BQAwQTEQMA4GA1UEAwwHU00yUk9PVDEPMA0GA1UECgwGU0FOU0VDMQ8wDQYDVQQLDAZTQU5TRUMxCzAJBgNVBAYTAkNOMB4XDTIwMDcwNjA2MDI0OFoXDTMwMDcwNjA2MDI0OFowQDEPMA0GA1UEAwwGc20yMDAxMQ8wDQYDVQQKDAZTQU5TRUMxDzANBgNVBAsMBlNBTlNFQzELMAkGA1UEBhMCQ04wWTATBgcqhkjOPQIBBggqgRzPVQGCLQNCAAS/6jVkhsWytKVdmtflqX9LhkU+dLApthBO1Y28PTwVmLW1ph1ggT9qjmYxxc/h2bRyuryPz7wmlOBlSlbV4aM3MAwGCCqBHM9VAYN1BQADSQAwRgIhAK5cTEqSgB/aNAljGFstp7ze9nw5toOPSfBNtaktBoh6AiEA9vJr34EaGRpVUg+7INzxo2YX2ieYmJOmuL5tGSaHgEwxgbowgbcCAQEwTTBBMRAwDgYDVQQDDAdTTTJST09UMQ8wDQYDVQQKDAZTQU5TRUMxDzANBgNVBAsMBlNBTlNFQzELMAkGA1UEBhMCQ04CCH0bmmUy5wEqMAwGCCqBHM9VAYMRBQAwDQYJKoEcz1UBgi0BBQAERjBEAiAMHxbWRr5gVdfjjINBB9/SkWKeozAEXpSXO7bKSp+n9QIgIH0wgPysxQwvyZWuS/AiAdS+gmqhqsoOqu7O17nzMao=";
//        boolean b1 = SM2Utils.P7VerifyMessageDetach(new String(Base64.encode("测试一下sign".getBytes("UTF-8"))), mac, content);
//        System.out.println("验签结果" + b1);
    }
}
