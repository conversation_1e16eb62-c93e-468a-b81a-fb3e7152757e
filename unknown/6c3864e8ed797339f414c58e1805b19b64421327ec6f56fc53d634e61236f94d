package com.wosai.upay.job.util;

import java.util.Random;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by hzq on 17/12/28.
 */
public class StringFilter {

    private static Pattern REPLACE_NUMBER = Pattern.compile("[\\d]");
    private static Pattern FILTER = Pattern.compile("[`_ ~!@#$%^&*+＋=|{}':;',\\[\\].·<>/?~！@#￥%……&*——+\\-|{}《》【】‘；：\"”“’。，？•−]");

    public static String replaceNumber(String source) {
        if (source == null) {
            return null;
        }
        Matcher matcher = REPLACE_NUMBER.matcher(source);
        return matcher.replaceAll("").trim();
    }

    public static String filter(String str) {
        Matcher m = FILTER.matcher(str);
        return m.replaceAll("").trim();
    }

    public static String replaceRandomNumber(String source) {
        if (source == null) {
            return null;
        }
        Matcher matcher = REPLACE_NUMBER.matcher(source);
        StringBuffer stringBuffer = new StringBuffer();
        boolean result = matcher.find();
        while (result) {
            matcher.appendReplacement(stringBuffer, String.valueOf(new Random().nextInt(9) + 1));
            result = matcher.find();
        }
        matcher.appendTail(stringBuffer);
        return stringBuffer.toString();
    }

    public static void main(String[] args) {
        String test = "呵呵呵c23132231dsdasd";
    }
}
