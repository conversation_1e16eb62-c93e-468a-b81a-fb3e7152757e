package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AddAffectStatusSuccessTaskCountBiz {

    /**
     * 以下类型的task需要单独检查是否需要发送bank换卡消息
     */
    private static final List<String> NEED_CHECK_TYPES = Arrays.asList(ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE);
    private static final List<Integer> NEED_CHECK_SUB_TYPES = Arrays.asList(ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE, ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS);

    @Autowired
    BankCardServiceImpl bankCardService;

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper subTaskMapper;

    public void addAffectStatusSuccessTaskCount(Long id) {

        contractTaskMapper.addAffectStatusSuccessTaskCount(id);
        ContractTask task = null;
        try {
            task = contractTaskMapper.selectByPrimaryKey(id);

            if (NEED_CHECK_TYPES.contains(task.getType())) {
                List<ContractSubTask> contractSubTasks = subTaskMapper.selectByPTaskId(id);

                //看看有没有换卡子任务
                List<ContractSubTask> changeBankCardSub = contractSubTasks.stream()
                        .filter(subTask -> NEED_CHECK_SUB_TYPES.contains(subTask.getTask_type()) && subTask.getStatus_influ_p_task() == 1)
                        .collect(Collectors.toList());

                if (WosaiCollectionUtils.isEmpty(changeBankCardSub)) {
                    return;
                }

                ContractSubTask changeBankSub = changeBankCardSub.get(0);
                Map context = JSON.parseObject(task.getEvent_context(), Map.class);
                Map cardRequestParam = (Map) context.get("cardRequestParam");
                //换卡子任务成功,失败,审核中
                if (changeBankSub.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
                    bankCardService.sendMesaageToBank(cardRequestParam, MerchantBankAccount.VERIFY_STATUS_SUCC, task.getMerchant_sn(), changeBankSub.getResult());
                } else if (changeBankSub.getStatus().equals(TaskStatus.FAIL.getVal())) {
                    bankCardService.sendMesaageToBank(cardRequestParam, MerchantBankAccount.VERIFY_STATUS_FAIL, task.getMerchant_sn(), changeBankSub.getResult());
                }
            }
        } catch (Exception e) {
            String sn = task != null ? task.getMerchant_sn() : null;
            log.error("特定换卡类型发送换卡消息异常,taskId : {} ,sn : {}", id, sn, e);
        }

    }
}
