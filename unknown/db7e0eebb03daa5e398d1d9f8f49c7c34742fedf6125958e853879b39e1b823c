package com.wosai.upay.job.monitor;

import avro.shaded.com.google.common.collect.Lists;
import avro.shaded.com.google.common.collect.Maps;
import com.shouqianba.cua.chatbot.client.FeishuChatBotClient;
import com.shouqianba.cua.chatbot.message.TextMessage;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by hzq on 19/6/14.
 */
@Data
@Accessors(chain = true)
public class WarnObject {

    private String event;
    private long time;
    private boolean distinct;
    private int total;
    private int fail;
    private double thresholdRate;
    private int thresholdCount;
    private long duration;
    private List<Tuple2<String, Integer>> reason = Lists.newArrayList();

    public static final Logger logger = LoggerFactory.getLogger(WarnObject.class);

    private static Map<String, Long> warningMap = Maps.newConcurrentMap();

    private static FeishuChatBotClient client = new FeishuChatBotClient();

    void tryWarn(String robot) {
        try {
            if (total != 0 && failRate() > thresholdRate && fail > thresholdCount) {
                long now = System.currentTimeMillis();
                warningMap.putIfAbsent(key(), now);
                long l = now - warningMap.get(key()); //持续时间
                client.send(robot, toMarkDownMessage(false, l), null);
            } else {
                Long remove = warningMap.remove(key());
                if (!StringUtils.isEmpty(remove)) {
                    long l = System.currentTimeMillis() - remove; //持续时间
                    client.send(robot, toMarkDownMessage(true, l), null);
                }
            }
        } catch (Exception e) {
            logger.error("warn error ", e);
        }
    }

    public String key() {
        return event + duration + distinct;
    }

    private double failRate() {
        return total == 0 ? 0 : new BigDecimal(fail).divide(new BigDecimal(total), 3, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).doubleValue();
    }

    /**
     * @param recover   是否恢复
     * @param warn_time 持续时间
     */
    private TextMessage toMarkDownMessage(boolean recover, long warn_time) {
        String unit = "次数";
        if (distinct) {
            unit = "商户数";
        }

        StringBuilder builder = new StringBuilder();
        builder.append("#进件异常告警");
        String s = recover ? " 恢复正常" : " 告警";
        builder.append("## 近" + duration + "分钟" + event + s);
        String tmp = "";//持续时间
        if (warn_time != 0) {
            tmp += "持续时间 ";
            long l = warn_time / 60000;
            long l1 = (warn_time / 1000) % 60;
            tmp += (l + "分" + l1 + "秒");
        }
        builder.append("> " + new SimpleDateFormat("MM-dd HH:mm:ss").format(new Date()) + "  " + tmp + "\r\n");
        builder.append(">  总" + unit + " " + total + "\r\n");

        String countCom = fail > thresholdCount ? ">" : "<";
        String rateCom = failRate() > thresholdRate ? ">" : "<";
        String count = " 异常" + unit + " " + fail + " " + countCom + " " + thresholdCount;
        String rate = "异常率 " + failRate() + "% " + rateCom + " " + thresholdRate + "%";
        builder.append(">  " + count + "  " + rate);
        reason.forEach(r -> builder.append("- " + r._1 + "   " + r._2 + "次"));
        return new TextMessage(builder.toString());
    }

}
