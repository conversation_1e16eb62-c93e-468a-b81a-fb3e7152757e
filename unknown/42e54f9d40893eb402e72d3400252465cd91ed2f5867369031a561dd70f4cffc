package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.CcbMerchantFeeRate;

import java.util.List;

public interface CcbMerchantFeeRateMapper {
    int deleteByPrimaryKey(Long id);

    int insert(CcbMerchantFeeRate record);

    int insertSelective(CcbMerchantFeeRate record);

    CcbMerchantFeeRate selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CcbMerchantFeeRate record);

    int updateByPrimaryKey(CcbMerchantFeeRate record);

    List<CcbMerchantFeeRate> selectByIdRange(long minId, int batchSize);

    CcbMerchantFeeRate selectByMerchantSn(String merchantSn);
}