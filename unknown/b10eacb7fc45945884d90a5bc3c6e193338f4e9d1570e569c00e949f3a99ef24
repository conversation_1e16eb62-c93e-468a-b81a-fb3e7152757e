package com.wosai.upay.job.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class AuthAndComboTask {
    private Long id;

    private String merchant_id;

    private String merchant_sn;

    private Integer status;

    private String sub_mch_id;

    private Date create_at;

    private Date update_at;

    private String form_body;

    private String result;

    private String extra;

    public Map<String, Object> getExtraMap() {
        return JSONObject.parseObject(extra, Map.class);
    }

}