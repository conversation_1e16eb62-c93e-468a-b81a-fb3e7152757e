package com.wosai.upay.job.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2019-10-18
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
public class WeixinSubDevResp {

    public static final WeixinSubDevResp CONFIG_SUCCESS = new WeixinSubDevResp(1, "配置成功");
    public static final WeixinSubDevResp NOT_NEED_CONFIG = new WeixinSubDevResp(2, "不需要配置");

    /**
     * 0：失败  1：成功   2：不需要配置
     */
    private int code;

    private String message;


    public static WeixinSubDevResp fail(String message) {
        return new WeixinSubDevResp(0, message);
    }

}
