package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.payLater.PayLaterApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PayLaterApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(PayLaterApply record);

    int insertSelective(PayLaterApply record);

    PayLaterApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayLaterApply record);

    int updateByPrimaryKeyWithBLOBs(PayLaterApply record);

    int updateByPrimaryKey(PayLaterApply record);

    @Select("select * from pay_later_apply where merchant_sn=#{merchantSn} and account=#{account} order by update_at desc limit 1")
    PayLaterApply selectByCondition(@Param("merchantSn") String merchantSn, @Param("account") String account);

    @Select("select * from pay_later_apply where merchant_sn=#{merchantSn} and account=#{account} order by update_at desc")
    List<PayLaterApply> selectListByCondition(@Param("merchantSn") String merchantSn, @Param("account") String account);

    @Select("select * from pay_later_apply where merchant_sn=#{merchantSn} order by update_at desc  limit 1")
    PayLaterApply selectByMerchantSn(@Param("merchantSn") String merchantSn);

    @Select("select * from pay_later_apply where zft_merchant_apply_id=#{zftMerchantApplyId} order by update_at desc limit 1")
    PayLaterApply selectByZftMerchantApplyId(@Param("zftMerchantApplyId") Long zftMerchantApplyId);

    List<PayLaterApply> selectByProcessStatus(@Param("processStatus") List<Integer> processStatus,
                                              @Param("limit") int limit,
                                              @Param("startTime") String startTime,
                                              @Param("endTime") String endTime);

}