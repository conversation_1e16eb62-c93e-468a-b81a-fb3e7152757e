package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SubBizParamsMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(SubBizParams record);

    List<SubBizParams> selectByExampleWithBLOBs(SubBizParamsExample example);

    List<SubBizParams> selectByExample(SubBizParamsExample example);

    SubBizParams selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SubBizParams record, @Param("example") SubBizParamsExample example);

    int updateByExampleWithBLOBs(@Param("record") SubBizParams record, @Param("example") SubBizParamsExample example);

    int updateByExample(@Param("record") SubBizParams record, @Param("example") SubBizParamsExample example);

    int updateByPrimaryKeySelective(SubBizParams record);

    int updateByPrimaryKeyWithBLOBs(SubBizParams record);

    int updateByPrimaryKey(SubBizParams record);
}