package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.TransactionParam;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * Date 2020/6/3 4:32 下午
 **/
@Component
public class NuccParamsHandle extends ProviderParamsHandle {
    private static int PROVIDER = ProviderEnum.PROVIDER_NUCC.getValue();

    @Override
    protected boolean accept(MerchantConfigParams configParams) {
        return PROVIDER == configParams.getProvider();
    }

    @Override
    protected Map getConfigParams(MerchantConfigParams configParams) {
        int payWay = configParams.getPayWay();
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.NUCC_ALIPAY_SUB_MCH_ID, configParams.getPayMerchantId());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.NUCC_WEIXIN_SUB_MCH_ID, configParams.getPayMerchantId(),
                    TransactionParam.NUCC_WEIXIN_SUB_APP_ID, configParams.getSubAppid(),
                    TransactionParam.NUCC_WEIXIN_SUB_APP_SECRET, configParams.getSubAppSecret(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_ID, configParams.getMiniSubAppid(),
                    TransactionParam.WEIXIN_MINI_SUB_APP_SECRET, configParams.getMiniSubAppSecret());
        }
        if (payWay == PaywayEnum.BESTPAY.getValue()) {
            return CollectionUtil.hashMap(
                    TransactionParam.NUCC_SP_MCH_ID, configParams.getPayMerchantId());
        }
        return null;
    }


}
