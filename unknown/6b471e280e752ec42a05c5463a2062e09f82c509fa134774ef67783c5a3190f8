package com.wosai.upay.job.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.upay.common.bean.OrderBy;
import org.apache.commons.collections.CollectionUtils;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;

public class StringUtil {
    private final static ObjectMapper mapper = new ObjectMapper();
    private static Pattern Number_Replace = Pattern.compile("[\\d]");

    public static String replaceNum(String str){
        return Number_Replace.matcher(str).replaceAll("").trim();
    }

    public static boolean empty(String str) {
        return str == null || str.isEmpty();
    }

    public static boolean listEmpty(List list) {
        return list == null || list.size() == 0;
    }

    public static String randomPassword() {
        return (new Random().nextInt(899999) + 100000) + "";
    }


    public static String randomParma() {
        return (new Random().nextInt(899) + 100) + "";
    }


    public static String toString(Object obj) {
        return obj == null ? "" : obj.toString();
    }


    public static String concatObject(String type, Object... params) {
        StringBuilder sb = new StringBuilder();
        if (type == null) {
            for (Object param : params) {
                sb.append(param);
            }
        } else {
            for (Object param : params) {
                if (param.equals(params[params.length - 1])) {
                    sb.append(param);
                    break;
                }
                sb.append(param + type);
            }
        }
        return sb.toString();

    }

    public static Map StringToMap(String params) {
        try {
            return mapper.readValue(params, Map.class);
        } catch (IOException e) {
            return new HashMap();
        }
    }


    public static Map hashMap(Object... objects) {
        Map result = new LinkedHashMap();
        for (int i = 0; i < objects.length / 2; ++i) {
            result.put(objects[2 * i], objects[2 * i + 1]);
        }
        return result;
    }


    public static List arrayList(Object... objects) {
        List result = new ArrayList();
        for (Object object : objects) {
            result.add(object);
        }
        return result;
    }


    /**
     * Map转String
     *
     * @param map
     * @return
     */
    public static String getMapToString(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        //将set集合转换为数组
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        //给数组排序(升序)
        Arrays.sort(keyArray);
        //因为String拼接效率会很低的，所以转用StringBuilder
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < keyArray.length; i++) {
            // 参数值为空，则不参与签名 这个方法trim()是去空格
            if ((String.valueOf(map.get(keyArray[i]))).trim().length() > 0) {
                sb.append(keyArray[i]).append(":").append(String.valueOf(map.get(keyArray[i])).trim());
            }
            if (i != keyArray.length - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static String formatDate(long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String date = sdf.format(timeStamp);
        return date;
    }

    public static String formatDateMMdd(long timeStamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm:ss");
        String date = sdf.format(timeStamp);
        return date;
    }

    public static Date parseDate(String dstr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date;
        try {
            date = sdf.parse(dstr);
        } catch (ParseException e) {
            return null;
        }
        return date;
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static String formatDate(String pattern, Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    public static String orderByListToOrderByClause(List<OrderBy> orderByList, String defaultOrder) {
        if (CollectionUtils.isEmpty(orderByList)) {
            return defaultOrder;
        }

        StringBuilder orderByClause = new StringBuilder();
        for (Iterator<OrderBy> it = orderByList.iterator(); it.hasNext(); ) {
            OrderBy orderBy = it.next();
            orderByClause.append(orderBy.getField());
            if (orderBy.getOrder() == OrderBy.OrderType.ASC) {
                orderByClause.append(" ").append("ASC");
            } else if (orderBy.getOrder() == OrderBy.OrderType.DESC) {
                orderByClause.append(" ").append("DESC");
            }

            if (it.hasNext()) {
                orderByClause.append(", ");
            }
        }

        return orderByClause.toString();
    }

    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
