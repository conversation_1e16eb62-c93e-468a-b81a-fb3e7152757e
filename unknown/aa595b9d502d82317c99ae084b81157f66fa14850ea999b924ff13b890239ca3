package com.wosai.upay.job.service;

import avro.shaded.com.google.common.collect.Lists;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.bank.model.verify.AmountVerify;
import com.wosai.upay.bank.model.verify.CheckRemitReq;
import com.wosai.upay.bank.model.verify.CheckRemitResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.core.model.BaseConfig;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.constant.WechatAuthUrlConstants;
import com.wosai.upay.job.enume.*;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.DO.TaskMch;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.job.util.WeixinApplyUtil;
import com.wosai.upay.merchant.contract.model.MerchantLakalaContract;
import com.wosai.upay.merchant.contract.model.MerchantProviderParams;
import com.wosai.upay.merchant.contract.model.weixin.ApplymentParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: jerry
 * @date: 2019/4/19 11:22
 * @Description:报备状态对外接口
 */
@Service
@AutoJsonRpcServiceImpl
public class ContractStatusServieImpl implements ContractStatusService {

    public static final Logger logger = LoggerFactory.getLogger(ContractStatusServieImpl.class);

    private static final Integer SUB_TASK_STATUS_PROCESSING = 1;
    private static final Integer TASK_STATUS_SUCCESS = 5;
    private static final Integer TASK_STATUS_FAIL = 6;
    private static final Integer WEIXIN_AUTH = 2;
    private static final String ERROR_CODE = "error_code";
    private static final String CONTRACT_MEMO = "contract_memo";
    private static final String CONTRACT_CODE = "contract_code";

    private static final String REVIEW_COMPLETE = "review_complete";
    private static final String LKL_APPLYING_CODE = "10001";
    private static final String APPLYING_CODE = "10002";

    private static final String LKL_REVIEW_CODE = "10004";
    private static final String PUBLIC_PAY_FOR_CODE = "10077";
    private static final String CHANNEL_CODE = "channelCode";


    private static final String TYPE_CRM = "crm_msg";
    private static final String SH_LKL_WM_NORMAL = "36002013293";
    private static final String SH_TL_NORMAL = "313848752";
    private static final String CONTRACT_MEMO_FAIL = "任务执行失败";


    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private MchAuthApplyMapper mchAuthApplyMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    MerchantService merchantService;
    @Autowired
    TradeConfigService tradeConfigService;
    @Autowired
    RedisService redisService;
    @Autowired
    ProviderTradeParamsService providerTradeParamsService;
    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;
    @Autowired
    ScheduleUtil scheduleUtil;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private ComposeAcquirerBiz acquirerBiz;
    @Autowired
    private AccountVerifyService accountVerifyService;
    @Autowired
    private PayForTaskMapper payForTaskMapper;
    @Autowired
    private PayForResultBiz payForResultBiz;
    @Autowired
    CompleteDateBiz completeDateBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Lazy
    @Autowired
    private ContractEventService contractEventService;


    @Autowired
    private AuthTaskBiz authtaskBiz;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private WeixinApplyUtil weixinApplyUtil;

    @Autowired
    private SubtaskResultService subtaskResultService;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Override
    public List<ContractStatus> selectByUpdateTime(long mtime) {
        String create = StringUtil.formatDate(mtime);
        return contractStatusMapper.selectByUpdate(create);
    }

    @Override
    public ContractStatus selectByPrimaryKey(long id) {
        return contractStatusMapper.selectByPrimaryKey(id);
    }


    @Override
    public Map getMerchantContractStatusByType(Map params) {
        String merchantSn = BeanUtil.getPropString(params, MerchantProviderParams.MERCHANT_SN);
        String type = BeanUtil.getPropString(params, BaseConfig.TYPE);
        type = getTypeKey(type);
        ContractTask contractTask = contractTaskMapper.selectForTipsByMerchantSn(merchantSn);
        return getMessageByContractTaskByType(contractTask, type);
    }

    @Override
    public Map getMerchantBankContractStatusByType(Map params) {
        String merchantSn = BeanUtil.getPropString(params, MerchantProviderParams.MERCHANT_SN);
        String type = BeanUtil.getPropString(params, BaseConfig.TYPE);
        type = getTypeKey(type);
        ContractTask contractTask = contractTaskMapper.getBySnAndTypeByCreateDesc(merchantSn, ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT);
        return getMessageByContractTaskByType(contractTask, type);
    }

    private Map getMessageByContractTaskByType(ContractTask contractTask, String type) {
        Map resp = new HashMap(2);
        if (contractTask == null) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.NO_TASK.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.NO_TASK.getCode());
            return resp;
        }
        if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.PENDING_TASK.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.PENDING_TASK.getCode());
            return resp;
        }
        //审核中
        if (contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal()) || contractTask.getStatus().equals(TaskStatus.PAY_FOR_WAIT.getVal())) {
            resp = processingTaskTip(contractTask, type);
            if (TYPE_CRM.equals(type) && !StringUtils.isEmpty(contractTask.getEvent_msg())) {
                resp.put(CONTRACT_MEMO, contractTask.getEvent_msg());
                return resp;
            }
            return resp;
        } else if (TASK_STATUS_FAIL.equals(contractTask.getStatus())) {
            resp = failTaskTip(contractTask, type);
            if (TYPE_CRM.equals(type) && !StringUtils.isEmpty(contractTask.getEvent_msg())) {
                resp.put(CONTRACT_MEMO, contractTask.getEvent_msg());
                return resp;
            }
            return resp;
        } else if (WEIXIN_AUTH.equals(contractTask.getStatus())) {
            return weixinAuthTip(contractTask, type);
        } else if (TASK_STATUS_SUCCESS.equals(contractTask.getStatus())) {
            resp.put(MerchantLakalaContract.CONTRACT_MEMO, "进件任务处理成功");
            resp.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_SUCCESS.getCode());
            return resp;
        } else {
            resp.put(CONTRACT_MEMO, ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.UNKNOWN_STATUS_CODE.getCode());
            return resp;
        }
    }

    @Override
    public Map getMerchantNetInStatusByMultiEvent(Map params) {
        String merchantSn = BeanUtil.getPropString(params, MerchantProviderParams.MERCHANT_SN);
        String type = BeanUtil.getPropString(params, BaseConfig.TYPE);
        type = getTypeKey(type);
        MultiProviderContractEvent event = multiEventMapper.selectLatestMultiEventByMerchantSn(merchantSn);
        // 如果不是多通道
        if (event == null) {
            return getMerchantContractStatusByType(params);
        }
        // 当前event被阻塞中
        if (event.getStatus() == MultiProviderContractEvent.STATUS_PENDING && event.getUpdate_at().equals(ScheduleUtil.PAUSE_DATE)) {
            String message = MapUtils.getString(JSON.parseObject(event.getResult(), Map.class), "message");
            if (WosaiStringUtils.contains(message, "异地开户，待商家提交店铺照片")) {
                return CollectionUtil.hashMap(
                        CONTRACT_CODE, ContractStatusCode.PHOTO_NOT_SUBMIT.getCode(),
                        CONTRACT_MEMO, message
                );
            }
            return CollectionUtil.hashMap(
                    CONTRACT_CODE, ContractStatusCode.PENDING_EVENT.getCode(),
                    CONTRACT_MEMO, message
            );
        }
        ContractTask primaryTask = event.getPrimary_task_id() == null ? null : contractTaskMapper.selectByPrimaryKey(event.getPrimary_task_id());
        ContractTask secondaryTask = event.getSecondary_task_id() == null ? null : contractTaskMapper.selectByPrimaryKey(event.getSecondary_task_id());
        if (primaryTask == null) {
            return getMessageByContractTaskByType(null, type);
        }
        // 待处理，用主通道任务
        if (event.getStatus() == MultiProviderContractEvent.STATUS_PENDING) {
            return getMessageByContractTaskByType(primaryTask, type);
        }
        // 处理中，如果主通道不是失败则返回主通道的文案 否则返回次通道的文案
        else if (event.getStatus() == MultiProviderContractEvent.STATUS_PROCESS) {
            if (!TaskStatus.FAIL.getVal().equals(primaryTask.getStatus())) {
                return getMessageByContractTaskByType(primaryTask, type);
            }
            return getMessageByContractTaskByType(secondaryTask, type);
        }
        // 成功，如果主通道是成功则返回主通道的文案 否则返回次通道的文案
        else if (event.getStatus() == MultiProviderContractEvent.STATUS_SUCCESS) {
            if (TaskStatus.SUCCESS.getVal().equals(primaryTask.getStatus())) {
                return getMessageByContractTaskByType(primaryTask, type);
            }
            return getMessageByContractTaskByType(secondaryTask, type);
        }
        // 失败，如果文案相同则返回一个，不同则都返回
        else {
            Map primaryTaskMessage = getMessageByContractTaskByType(primaryTask, type);
            String primaryTaskMemo = BeanUtil.getPropString(primaryTaskMessage, CONTRACT_MEMO);
            if (secondaryTask == null) {
                return primaryTaskMessage;
            }
            Map secondaryTaskMessage = getMessageByContractTaskByType(secondaryTask, type);
            String secondaryTaskMemo = BeanUtil.getPropString(secondaryTaskMessage, CONTRACT_MEMO);

            if (Objects.equals(primaryTaskMemo, secondaryTaskMemo)) {
                primaryTaskMessage.put(CONTRACT_MEMO, primaryTask.getRule_group_id() + "," + secondaryTask.getRule_group_id() + ":" + primaryTaskMemo);
            } else {
                primaryTaskMessage.put(CONTRACT_MEMO, primaryTask.getRule_group_id() + ":" + primaryTaskMemo + "|" + secondaryTask.getRule_group_id() + ":" + secondaryTaskMemo);
            }
            return primaryTaskMessage;

        }

    }

    @Override
    public Map getMerchantChangeDataTaskStatusByType(Map params) {
        String merchantSn = BeanUtil.getPropString(params, MerchantProviderParams.MERCHANT_SN);
        String type = BeanUtil.getPropString(params, BaseConfig.TYPE);
        ContractTask task = contractTaskMapper.getBySnAndTypeByCreateDesc(merchantSn, ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE);
        return getMessageByContractTaskByType(task, getTypeKey(type));
    }

    @Override
    public ContractStatus selectByMerchantSn(String merchantSn) {
        return contractStatusMapper.selectByMerchantSn(merchantSn);
    }

    @Override
    public String getMessageByContractTask(ContractTask contractTask) {
        if (Objects.equals(contractTask.getStatus(), ContractTaskProcessStatusEnum.AUDITING.getValue())) {
            return "待收单机构业务审核";
        }
        if ("hxb".equals(contractTask.getRule_group_id())
                && !Objects.equals(contractTask.getStatus(), TaskStatus.FAIL.getVal())) {
            return contractTask.getResult();
        }
        if (ProviderUtil.CONTRACT_TYPE_ALIPAY_AUTH.equals(contractTask.getType())) {
            return "请从支付业务-支付源申请单管理中具体进度及状态描述";
        } else if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
            return ContractStatusCode.PENDING_TASK.getMsg();
        }
        if (contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal()) || contractTask.getStatus().equals(TaskStatus.PAY_FOR_WAIT.getVal())) {
            Map process = processingTaskTip(contractTask, "sp_msg");
            if (CollectionUtils.isEmpty(process)) {
                return "审核处理中";
            }
            return (String) process.get(CONTRACT_MEMO);
            //失败
        } else if (TASK_STATUS_FAIL.equals(contractTask.getStatus())) {
            Map fail = failTaskTip(contractTask, "sp_msg");
            if (CollectionUtils.isEmpty(fail)) {
                return "失败原因未知";
            }
            return (String) fail.get(CONTRACT_MEMO);
        } else if (WEIXIN_AUTH.equals(contractTask.getStatus()) && ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType())) {
            Map auth = weixinAuthTip(contractTask, "sp_msg");
            if (CollectionUtils.isEmpty(auth)) {
                return "未知实名审核中";
            }
            return (String) auth.get(CONTRACT_MEMO);
        } else if (TASK_STATUS_SUCCESS.equals(contractTask.getStatus())) {
            return "进件任务处理完成";
        } else {
            Optional<ContractTaskProcessStatusEnum> statusEnum = EnumUtils.ofNullable(ContractTaskProcessStatusEnum.class, contractTask.getStatus());
            if (statusEnum.isPresent()) {
                return statusEnum.get().getText();
            }
            return ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg();
        }
    }


    @Override
    public String getMessageByContractTaskV2(ContractTask contractTask, String type) {
        type = getTypeKey(type);
        if (contractTask.getStatus().equals(TaskStatus.PENDING.getVal())) {
            return ContractStatusCode.PENDING_TASK.getMsg();
        }
        if (contractTask.getStatus().equals(TaskStatus.PROGRESSING.getVal()) || contractTask.getStatus().equals(TaskStatus.PAY_FOR_WAIT.getVal())) {
            Map process = processingTaskTip(contractTask, type);
            if (CollectionUtils.isEmpty(process)) {
                return "审核处理中";
            }
            return (String) process.get(CONTRACT_MEMO);
            //失败
        } else if (TASK_STATUS_FAIL.equals(contractTask.getStatus())) {
            Map fail = failTaskTip(contractTask, type);
            if (CollectionUtils.isEmpty(fail)) {
                return "失败原因未知";
            }
            return (String) fail.get(CONTRACT_MEMO);
        } else if (WEIXIN_AUTH.equals(contractTask.getStatus())) {
            Map auth = weixinAuthTip(contractTask, type);
            if (CollectionUtils.isEmpty(auth)) {
                return "未知实名审核中";
            }
            return (String) auth.get(CONTRACT_MEMO);
        } else if (TASK_STATUS_SUCCESS.equals(contractTask.getStatus())) {
            return "进件任务处理完成";
        } else {
            return ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg();
        }
    }

    @Override
    public String getMessageByTaskId(long taskId) {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        if (contractTask == null) {
            throw new CommonInvalidParameterException("报备任务不存在");
        }
        return getOriginalMemo(contractTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map rejectContractFields(String merchantSn, List<Map<String, Object>> fields) {
        return rejectContractFieldsInternal(merchantSn, fields, true);
    }

    private Map rejectContractFieldsInternal(String merchantSn, List<Map<String, Object>> fields, boolean sleep) {
        boolean result = false;
        Map response = rejectContractCheck(merchantSn, fields, sleep);
        if (!BeanUtil.getPropBoolean(response, "result")) {
            return response;
        }

        ContractEvent contractEvent = new ContractEvent();
        contractEvent.setMerchant_sn(merchantSn);

        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(0)
                .andDeletedEqualTo(false);
        List<com.wosai.upay.job.model.DO.MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);

        boolean hasContract = WosaiCollectionUtils.isNotEmpty(merchantProviderParams);
        if (hasContract) {
            contractEvent.setEvent_type(9);
        } else {
            contractEvent.setEvent_type(4);
        }

        Map eventMsg = Maps.newHashMap();
        eventMsg.put("crmUpdate", fields);
        contractEvent.setEvent_msg(JSON.toJSONString(eventMsg));
        String ruleGroupId = acquirerBiz.getMerchantDefaultRuleGroup(merchantSn, "");
        if (Objects.equals(ruleGroupId, McConstant.RULE_GROUP_LKL)) {
            ruleGroupId = McConstant.RULE_GROUP_LKLORG;
            composeAcquirerBiz.updateMerchantAcquirer(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        }
        contractEvent.setRule_group_id(ruleGroupId);

        //实名失败重新提交event
        ContractTask last = contractTaskMapper.selectLastByMerchantSn(merchantSn);
        if (last != null) {
            Map taskResult = JSON.parseObject(last.getResult(), Map.class);
            //微信实名失败
            if (ProviderUtil.WECHAT_AUTH.equals(BeanUtil.getPropString(taskResult, "channel"))) {
                contractEvent.setEvent_type(ContractEvent.OPT_TYPE_RE_AUTH);
            }
        }
        // 如果是入网驳回重新提交 特殊处理这种情况
        if (contractEvent.getEvent_type() == ContractEvent.OPT_TYPE_NET_CRM_UPDATE || contractEvent.getEvent_type() == ContractEvent.OPT_TYPE_NET_IN) {
            MultiProviderContractEvent event = multiEventMapper.selectLatestMultiEventByMerchantSn(merchantSn);
            if (event != null) {
                // 确定到底是9还是4
                eventMsg.put("eventType", contractEvent.getEvent_type());
                MultiProviderContractEvent multiEvent = new MultiProviderContractEvent()
                        .setMerchant_sn(merchantSn).setPrimary_group_id(event.getPrimary_group_id()).setSecondary_group_id(event.getSecondary_group_id())
                        .setEvent_msg(JSON.toJSONString(eventMsg)).setStatus(MultiProviderContractEvent.STATUS_PENDING);
                multiEventMapper.insertSelective(multiEvent);
                logger.info("rejectContract multi_provider_contract_event id {}", multiEvent.getId());
                return CollectionUtil.hashMap("result", true);
            }
        }

        contractEventMapper.insertSelective(contractEvent);
        Long id = contractEvent.getId();
        logger.info("rejectContract contract_event id {}", id);
        if (id != null) {
            result = true;
        }
        return CollectionUtil.hashMap("result", result);
    }

    private Map rejectContractCheck(String merchantSn, List<Map<String, Object>> fields, boolean sleep) {
        boolean result = false;
        String message;
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            message = "商户contractStatus记录不存在";
            return CollectionUtil.hashMap("result", result, "message", message);
        }
        if (ContractStatus.STATUS_BIZ_FAIL != contractStatus.getStatus()) {
            message = "非进件驳回状态";
            return CollectionUtil.hashMap("result", result, "message", message);
        }
        logger.info("rejectContractFields merchantSn {} contractStatus {}  fields {}", merchantSn, contractStatus.getStatus(), fields);
        try {
            if (sleep) {
                Thread.sleep(2000);
            }
        } catch (InterruptedException e) {
            logger.error("rejectContract sleep error", e);
            return CollectionUtil.hashMap("result", result, "message", "InterruptedException");
        }
        List<ContractTask> pendingTasks = contractTaskMapper.selectTaskTodoByMerchantSn(merchantSn);
        if (!CollectionUtils.isEmpty(pendingTasks)) {
            message = "当前任务正在执行";
            return CollectionUtil.hashMap("result", result, "message", message);
        }
        List<ContractEvent> contractEventList = contractEventMapper.selectByMerchantSnRejectContract(merchantSn);
        if (!CollectionUtils.isEmpty(contractEventList)) {
            message = "已经生成重新报备事件正在处理 持续未进件请联系紧急处理";
            return CollectionUtil.hashMap("result", result, "message", message);
        }
        return CollectionUtil.hashMap("result", true);
    }

    @Override
    public Map rejectContractFieldsV2(String merchantSn, List<Map<String, Object>> fields, FailEventConf conf) {
        List<String> forbidPay = applicationApolloConfig.getForbidPay();
        if (WosaiCollectionUtils.isNotEmpty(forbidPay)) {
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, devCode);
            String area = String.format("%s-%s-%s", merchant.getProvince(), merchant.getCity(), merchant.getDistrict());
            boolean anyMatch = forbidPay.stream().anyMatch(forbid -> area.contains(forbid));
            if (anyMatch) {
                conf = new FailEventConf().setFail(true).setFail_msg("该地区不允许开展收单业务");
            }
        }

        Map response = rejectContractCheck(merchantSn, fields, true);
        if (!BeanUtil.getPropBoolean(response, "result")) {
            return response;
        }
        if (conf.isFail()) {
            NewMchNetInReq req = new NewMchNetInReq().setMerchantSn(merchantSn).setPlatform(Request.KEY_PLATFORM_CRM).setFailMsg(WosaiStringUtils.isNotEmpty(conf.getFail_msg()) ? conf.getFail_msg() : "黑名单校验不通过");
            contractEventService.saveFailEventV2(req);
            return CollectionUtil.hashMap("result", true);
        } else {
            return rejectContractFieldsInternal(merchantSn, fields, false);
        }
    }

    @Override
    public boolean existMerchant(Integer type, String identity) {
        MchAuthApply applyment = mchAuthApplyMapper.getAuthApplyByTypeAndAuthNumAndStatus(type, identity, Lists.newArrayList(AuthApplyStatus.PASSED.getVal(), AuthApplyStatus.PASSED_FREEZE.getVal()));
        return applyment != null;
    }

    @Override
    public Map getWeixinUpgrade(String merchantSn, String soucre, String taskType) {
        ContractTask task = contractTaskMapper.getUpgradeTaskByMerchangtSn(merchantSn, taskType);
        return doGetWeixinUpgrade(task, soucre);
    }

    public Map doGetWeixinUpgrade(ContractTask task, String soucre) {
        Map resp = Maps.newHashMap();
        if (task == null) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.NO_TASK.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.NO_TASK.getCode());
            return resp;
        }
        String type = getTypeKey(soucre);
        if (task.getStatus().equals(TaskStatus.PENDING.getVal())) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.PENDING_TASK.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.PENDING_TASK.getCode());
            return resp;
        }

        if (task.getStatus().equals(TaskStatus.PROGRESSING.getVal())) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.UPGRADE_TASK_CONTRACTING.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode());
            return resp;
        } else if (TASK_STATUS_FAIL.equals(task.getStatus())) {
            Map fail = failTaskTip(task, type);
            String msg = BeanUtil.getPropString(fail, CONTRACT_MEMO);
            if (!StringUtils.isEmpty(msg)) {
                msg = msg.replace("null", "");
                fail.put(CONTRACT_MEMO, msg);
            }
            if (TYPE_CRM.equals(type) && !StringUtils.isEmpty(task.getEvent_msg())) {
                fail.put(CONTRACT_MEMO, task.getEvent_msg());
            }
            fail.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_FAIL.getCode());
            Map context = JSON.parseObject(task.getEvent_context(), Map.class);
            fail.put(MICRO_RESUB, context.getOrDefault(MICRO_RESUB, Boolean.FALSE));
            return fail;
        } else if (WEIXIN_AUTH.equals(task.getStatus())) {
            Map auth = weixinAuthTip(task, type);
            if (TYPE_CRM.equals(type) && !StringUtils.isEmpty(task.getEvent_msg())) {
                auth.put(CONTRACT_MEMO, task.getEvent_msg());
            }
            return auth;
        } else if (TASK_STATUS_SUCCESS.equals(task.getStatus())) {
            resp.put(CONTRACT_MEMO, ContractStatusCode.UPGRADE_TASK_SUCCESS.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_SUCCESS.getCode());
            resp.putAll(packageWeixinUpgrade(task));
            return resp;
        } else {
            resp.put(CONTRACT_MEMO, ContractStatusCode.UNKNOWN_STATUS_CODE.getMsg());
            resp.put(CONTRACT_CODE, ContractStatusCode.UNKNOWN_STATUS_CODE.getCode());
            return resp;
        }
    }


    private Map packageWeixinUpgrade(ContractTask task) {
        com.wosai.upay.job.model.DO.MerchantProviderParams params = merchantProviderParamsMapper.getUseWeiXinParam(task.getMerchant_sn());
        if (Objects.isNull(params)) {
            return new HashMap();
        }
        String payMerchantId = params.getPay_merchant_id();
        List<TaskMch> taskMches = weixinAuthApplyBiz.getTaskMchByPayMchId(payMerchantId);
        List<TaskMch> result = taskMches.stream().filter(taskMch ->
                Objects.equals(Optional.ofNullable(contractTaskMapper.selectByPrimaryKey(taskMch.getTask_id())).orElseGet(ContractTask::new).getStatus(), 5) && Objects.nonNull(taskMch.getAuth_apply_id())
        ).collect(Collectors.toList());
        if (StringUtil.listEmpty(result)) {
            return CollectionUtil.hashMap("weixinMerchantId", payMerchantId);
        }
        MchAuthApply apply = mchAuthApplyMapper.selectByPrimaryKey(result.get(0).getAuth_apply_id());
        if (Objects.isNull(apply)) {
            return CollectionUtil.hashMap("weixinMerchantId", payMerchantId);
        }
        ApplymentParam applymentParam = JSON.parseObject(apply.getRequest_body(), ApplymentParam.class);
        if (Objects.isNull(applymentParam)) {
            return CollectionUtil.hashMap("type", apply.getType(), "weixinMerchantId", payMerchantId);
        }
        return CollectionUtil.hashMap("type", apply.getType(), "concatName", applymentParam.getContact_info().getName(), "weixinMerchantId", payMerchantId);
    }

    @Override
    public int verifyAmount(String merchantSn, BigDecimal amount) {
        PayForTask payForTask = payForTaskMapper.getVerifyPayFor(merchantSn);
        if (payForTask == null) {
            return VerifyStatus.FAIL.getCode();
        }
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
        if (contractSubTask == null) {
            return VerifyStatus.FAIL.getCode();
        }
        AmountVerify amountVerify = new AmountVerify()
                .setAmount(amount)
                .setPlat_form(ProviderUtil.PLAT_FORM)
                .setBusiness_id(payForTask.getRequest_flow_no());
        com.wosai.upay.bank.model.verify.VerifyResp bankResp = accountVerifyService.verifyAmount(amountVerify);
        if (bankResp.getValid()) {
            payForResultBiz.successHandle(contractSubTask, payForTask.getId());
            return VerifyStatus.SUCESS.getCode();
        }
        if (bankResp.getMax_retry() == bankResp.getRetried()) {
            payForResultBiz.failHandler(contractSubTask, payForTask.getId(), AccountApplyStatus.FAIL.getStatus());
            return VerifyStatus.FAIL.getCode();
        }
        return VerifyStatus.PROGRESSING.getCode();
    }

    @Override
    public Map queryPayForResult(String merchantSn) {
        PayForTask payForTask = payForTaskMapper.getProgressBySn(merchantSn);
        if (payForTask == null) {
            Map res = CollectionUtil.hashMap("status", "FAIL", "msg", "代付任务不存在");
            return res;
        }
        CheckRemitReq remitReq = new CheckRemitReq();
        remitReq.setPlatform("merchant-contract-job");
        remitReq.setBusiness_id(payForTask.getRequest_flow_no());
        CheckRemitResp checkRemitResp = accountVerifyService.checkRemitResult(remitReq);
        if (CheckRemitResp.FAIL == checkRemitResp.getStatus()) {
            ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
            payForResultBiz.failHandler(contractSubTask, payForTask.getId(), AccountApplyStatus.BANK_CHARGEBACK_FAIL.getStatus());
        }
        return CollectionUtil.hashMap("status", checkRemitResp.getStatus(), "msg", checkRemitResp.getMessage());
    }

    @Override
    public String authChannelCode(String subMchId) {
        com.wosai.upay.job.model.DO.MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
        if (params == null) {
            throw new CommonPubBizException("子商户号不存在");
        }
        if (SH_LKL_WM_NORMAL.equals(params.getChannel_no())) {
            return WechatAuthUrlConstants.LKL;
        }
        if (SH_TL_NORMAL.equals(params.getChannel_no())) {
            return WechatAuthUrlConstants.TONGLIAN;
        }
        ContractChannel channel = ruleContext.getContractChannel(params.getPayway(), params.getProvider() + "", params.getChannel_no());
        return (String) applicationApolloConfig.getChannelAuthUrl().get(channel.getChannel());
    }

    @Override
    public int getContractStatus(String merchantSn) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null) {
            return ContractStatus.STATUS_PENDING;
        }
        if (contractStatus.getStatus() != ContractStatus.STATUS_BIZ_FAIL) {
            return contractStatus.getStatus();
        }
        List<ContractEvent> events = contractEventMapper.selectEventTodoByMerchantSn(merchantSn);
        if (WosaiCollectionUtils.isNotEmpty(events)) {
            return ContractStatus.STATUS_PROCESS;
        }
        List<MultiProviderContractEvent> multiEvents = multiEventMapper.selectMultiEventTodoByMerchantSn(merchantSn);
        if (WosaiCollectionUtils.isNotEmpty(multiEvents)) {
            return ContractStatus.STATUS_PROCESS;
        }
        return ContractStatus.STATUS_BIZ_FAIL;
    }


    private Map weixinAuthTip(ContractTask task, String type) {
        logger.info("打点 weixinAuthTip");
        Map result = new HashMap();
        Long taskId = task.getId();
        Map contextParam = JSON.parseObject(task.getEvent_context(), Map.class);
        Map license = (Map) contextParam.get(ParamContextBiz.KEY_BUSINESS_LICENCE);
        Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        Map merchant = (Map) contextParam.get(ParamContextBiz.KEY_MERCHANT);
        Boolean forceMicro = (Boolean) contextParam.getOrDefault(ParamContextBiz.FORCE_MICRO, Boolean.FALSE);
        String channel = BeanUtil.getPropString(contextParam, ParamContextBiz.NEDD_AUTH_CHANNEL);
        MchAuthApply authApplyment = mchAuthApplyMapper.selectByTaskId(taskId);
        if (Objects.isNull(authApplyment)) {
            TaskMch recentMchByTask = weixinAuthApplyBiz.getTaskMchByTaskId(taskId);
            if (Objects.nonNull(recentMchByTask) && Objects.nonNull(recentMchByTask.getAuth_apply_id())) {
                authApplyment = mchAuthApplyMapper.selectByPrimaryKey(recentMchByTask.getAuth_apply_id());
            }
        }
        Map errorMessage = applicationApolloConfig.getProcessingContractMessage();
        List<Map> channelMessage = (List) errorMessage.get(ProviderUtil.WECHAT_AUTH);
        ApplymentParam.Contact_info concatInfo = weixinApplyUtil.getConcatInfo(merchant, bankAccount, license, forceMicro);
        String concatName = concatInfo.getName();
        result.put("weixinMerchantId", authtaskBiz.getAuthPayMchId(task));
        if (authApplyment == null) {
            //返回渠道商二维码
            result.put(CONTRACT_MEMO, getApploMessage("联系人完成商户信息确认，微信返回待授权", channelMessage, type).replace("#", concatName));
            setMerchantChannelCode(result, merchant, bankAccount, license, channel, contextParam, true, task);
            result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_WAIT_FOR_AUTH.getCode());
            return result;
        }
        AuthApplyStatus authApplyStatus = AuthApplyStatus.toStatus(authApplyment.getStatus());
        String remark;
        if (authApplyStatus == AuthApplyStatus.UN_SUBMIT) {
            result.put(CONTRACT_MEMO, "等待申请单提交");
            result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode());
        }
        if (authApplyStatus == AuthApplyStatus.MICRO_WATI_SUBMIT) {
            result.put(CONTRACT_CODE, "10013");
            result.put(CONTRACT_MEMO, getApploMessage("获取到微信子商户号，小微商户缺少店铺照片", channelMessage, type));
        }
        if (authApplyStatus == AuthApplyStatus.SUBMIT || authApplyStatus == AuthApplyStatus.WAITTING_FOR_AUDIT) {
            remark = getApploMessage("提交微信商户申请，查询结果返回审核中", channelMessage, type);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(authApplyment.getCommit_at());
            calendar.add(Calendar.MINUTE, applicationApolloConfig.getAuthAuditTime());
            remark = remark.replace("*", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", calendar.getTime())).replace("#", concatName);
            result.put(MerchantLakalaContract.CONTRACT_MEMO, remark);
            result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_TASK_CONTRACTING.getCode());
        }
        //按照我们提交规则来
        if (authApplyStatus == AuthApplyStatus.WAITTING_FOR_CONFIRM_CONTACT || authApplyStatus == AuthApplyStatus.WAITTING_FOR_CONFIRM_LEGALPERSON) {
            String leagalPersonName = BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME);
            if (StringUtils.isEmpty(leagalPersonName) || concatName.equals(leagalPersonName)) {
                remark = getApploMessage("提交商户申请，查询结果返回待联系人确认", channelMessage, type);
                result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_MICRO_WAIT_FOR_CONFIRM.getCode());
            } else {
                remark = getApploMessage("企业营业执照，联系人姓名不等于法人姓名时，提交商户申请，查询结果返回待联系人确认", channelMessage, type);
                result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_PUB_WAIT_FOR_CONFIRM.getCode());
            }
            if (forceMicro) {
                remark = getApploMessage("提交商户申请，查询结果返回待联系人确认", channelMessage, type);
                result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_MICRO_WAIT_FOR_CONFIRM.getCode());
            }
            result.put(MerchantLakalaContract.CONTRACT_MEMO, remark);
            result.put("qrCode", authApplyment.getQrcode_data_pre());
            result.put("merchantQrCode", authApplyment.getQrcode_data_after());
            setMerchantChannelCode(result, merchant, bankAccount, license, channel, contextParam, false, task);
        }
        if (authApplyStatus == AuthApplyStatus.PASSED) {
            remark = getApploMessage("联系人完成商户信息确认，微信返回待授权", channelMessage, type).replace("#", concatName);
            result.put(MerchantLakalaContract.CONTRACT_MEMO, remark);
            result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_WAIT_FOR_AUTH.getCode());
            result.put("qrCode", authApplyment.getQrcode_data_pre());
            result.put("merchantQrCode", authApplyment.getQrcode_data_after());
            setMerchantChannelCode(result, merchant, bankAccount, license, channel, contextParam, true, task);
        }
        if (authApplyStatus == AuthApplyStatus.PASSED_FREEZE) {
            remark = getApploMessage("微信返回申请单冻结", channelMessage, type).replace("#", concatName);
            result.put(MerchantLakalaContract.CONTRACT_MEMO, remark);
            result.put(CONTRACT_CODE, ContractStatusCode.UPGRADE_APPLY_FREEZE.getCode());
            result.put("qrCode", authApplyment.getQrcode_data_pre());
            result.put("merchantQrCode", authApplyment.getQrcode_data_after());
            setMerchantChannelCode(result, merchant, bankAccount, license, channel, contextParam, true, task);
        }
        return result;
    }

    /**
     * @param: 设置加工后的渠道商码(设置商户信息 渠道商码)
     * setCode 为false不会返回码
     * @return:
     * @date: 16:05
     */
    private void setMerchantChannelCode(Map result, Map merchant, Map bankAccount, Map license, String channel, Map contextParam, boolean setCode, ContractTask task) {
        logger.info("打点 setMerchantChannelCode");
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        String merchantBusinessName = BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME);
        String servicePhone = BeanUtil.getPropString(merchant, Merchant.CUSTOMER_PHONE);
        Boolean forceMicro = (Boolean) contextParam.getOrDefault(ParamContextBiz.FORCE_MICRO, Boolean.FALSE);
        WechatAuthEnum wechatAuthEnum = authtaskBiz.getWechatAuthEnum(task);
        String channelCodeUrl = wechatAuthEnum.getChannelCodeUrl();
        if (StringUtils.isEmpty(servicePhone)) {
            servicePhone = BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE);
        }
        String merchantName = BeanUtil.getPropString(merchant, Merchant.NAME);
        //商户联系人
        ApplymentParam.Contact_info contact_info = weixinApplyUtil.getConcatInfo(merchant, bankAccount, license, forceMicro);
        String concatName = contact_info.getName();
        String weixinMerchantId = MapUtils.getString(result, "weixinMerchantId");
        result.put("merchantBusinessName", merchantBusinessName);
        result.put("servicePhone", servicePhone);
        result.put("merchantName", merchantName);
        result.put("concatName", concatName);
        if (!setCode) {
            return;
        }
        String key = CHANNEL_CODE + merchantSn + weixinMerchantId;
        String merchantChannelCode = redisService.getKeyWithoutPrefix(key);
        if (StringUtils.isEmpty(merchantChannelCode)) {
            Map info = packageImgInfo(task);
            if (MapUtils.isEmpty(info)) {
                info = CollectionUtil.hashMap(
                        "merchantBusinessName", merchantBusinessName,
                        "servicePhone", servicePhone,
                        "merchantName", merchantName,
                        "concatName", concatName,
                        "weixinMerchantId", weixinMerchantId
                );
            }
            try {
                merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(info, wechatAuthEnum.getContractChannel());
                result.put("merchantChannelCode", merchantChannelCode);
                redisService.setNx(key, merchantChannelCode, 2L, TimeUnit.DAYS);
                result.put("channelCode", channelCodeUrl);
                return;
            } catch (IOException e) {
                logger.error("生成商户渠道码失败", e);
            }
        }
        result.put("merchantChannelCode", merchantChannelCode);
        result.put("channelCode", channelCodeUrl);
    }

    private Map packageImgInfo(ContractTask task) {
        TaskMch taskMch = weixinAuthApplyBiz.getTaskMchByTaskId(task.getId());
        String payMerchantId = taskMch.getPay_merchant_id();
        MchAuthApply mchAuthApply = mchAuthApplyMapper.selectByPrimaryKey(taskMch.getAuth_apply_id());
        if (Objects.isNull(mchAuthApply)) {
            return null;
        }
        com.wosai.upay.job.model.DO.MerchantProviderParams providerParams = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId);
        MchInfo mchInfo = composeAcquirerBiz.getWxMchInfo(providerParams).getMchInfo();
        String paywayChannelNo = ruleContext.getContractChannel(providerParams.getPayway(), String.valueOf(providerParams.getProvider()), providerParams.getChannel_no()).getPayway_channel_no();
        ApplymentParam applymentParam = JSON.parseObject(mchAuthApply.getRequest_body(), ApplymentParam.class);
        return CollectionUtil.hashMap("payway_channel_no", paywayChannelNo,
                "merchantName", mchInfo == null ? "" : mchInfo.getMerchant_name(),
                "concatName", applymentParam.getContact_info().getName(),
                "merchantBusinessName", mchInfo == null ? "" : mchInfo.getMerchant_shortname(),
                "weixinMerchantId", payMerchantId
        );
    }

    private String getApploMessage(String remark, List<Map> configs, String type) {
        for (Map config : configs) {
            String errorMsg = BeanUtil.getPropString(config, "error_msg");
            if (errorMsg.contains(remark)) {
                return BeanUtil.getPropString(config, type);
            }
        }
        return "未配置";
    }

    private Map failTaskTip(ContractTask contractTask, String type) {
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        if (CollectionUtils.isEmpty(result)) {
            return MapUtil.hashMap(CONTRACT_MEMO, "未知失败原因", CONTRACT_CODE, ContractStatusCode.UNKNOWN_STATUS_CODE.getCode());
        }
        String channel = (String) result.get("channel");
        Integer payWay = (Integer) result.get("payway");
        String source = getOriginalMemo(contractTask);

        if (ProviderUtil.SHOUQIANBA_CHANNEL.equals(channel)) {
            return MapUtil.hashMap(CONTRACT_MEMO, source, CONTRACT_CODE, ContractStatusCode.UNKNOWN_STATUS_CODE.getCode());
        }

        //由于后续业务发展导致导致result信息存储不规范 payWay == null,或者 channel == null 的情况越来越多因此需要主动选择
        final Long id = contractTask.getId();
        final List<ContractSubTask> tasks = contractSubTaskMapper.selectByPTaskIdAndStatus(id, TaskStatus.FAIL.getVal());
        if (!CollectionUtils.isEmpty(tasks)) {
            final ContractSubTask contractSubTask = tasks.get(0);
            channel = contractSubTask.getChannel();
            payWay = contractSubTask.getPayway();
        }

        //只有拉卡拉payWay 为null
        String platform = null;
        if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            if (ProviderUtil.WECHAT_AUTH.equals(channel) || "微信商家认证".equals(contractTask.getType())) {
                platform = ErrorCodeManageBiz.PLATFORM_WECHAT_AUTH;
            } else {
                platform = ErrorCodeManageBiz.PLATFORM_WECHAT;
            }
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            platform = ErrorCodeManageBiz.PLATFORM_ALY;
        } else if (Objects.equals(0, payWay) || Objects.isNull(payWay)) {
            //注意这里使用的是contains不是equals是由于有点商户还在老的lkl而不是lklV3
            if (channel.contains(ChannelEnum.LKL.getValue())) {
                platform = ErrorCodeManageBiz.PLATFORM_LKL;
            } else if (ProviderUtil.TONG_LIAN_CHANNEL.equals(channel)) {
                platform = ErrorCodeManageBiz.PLATFORM_TONGLIAN;
            } else if (ProviderUtil.UMS_PROVIDER_CHANNEL.equals(channel)) {
                platform = ErrorCodeManageBiz.PLATFORM_UMS;
            } else {
                platform = channel;
            }
        } else {
            //do nothing
        }
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager(type, source, platform);
        return MapUtil.hashMap(CONTRACT_MEMO, errorInfo.getMsg(), CONTRACT_CODE, errorInfo.getCode());
    }

    private String getOriginalMemo(ContractTask contractTask) {
        String source = "";
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        if (CollectionUtils.isEmpty(result)) {
            return "未知失败原因";
        }
        String channel = (String) result.get("channel");

        if (ProviderUtil.LKL_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.NUCC_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.UNION_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.UNION_WM_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.PAY_FOR_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.LKL_CALLBACK_CHANNEL.equals(channel)) {
            try {
                source = parseLklCallBackContractMemo(result);
            } catch (Exception e) {
                source = (String) BeanUtil.getNestedProperty(result, String.format("%s.%s", MerchantLakalaContract.CONTRACT_CALLBACK_MSG, MerchantLakalaContract.CONTRACT_CONTRACT_MEMO));
                if (source == null) {
                    source = "";
                }
            }
        } else if (ProviderUtil.UNION_OPEN_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.WECHAT_AUTH.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.TONG_LIAN_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.TONGLIAN_V2_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.UMS_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else if (ProviderUtil.LKL_V3_PROVIDER_CHANNEL.equals(channel)) {
            source = (String) result.get("message");
        } else {
            source = BeanUtil.getPropString(result, "message");
            if (WosaiStringUtils.isEmpty(source)) {
                source = "未知通道异常";
            }
        }
        return source;
    }


    private String parseLklCallBackContractMemo(Map result) {
        String contractMemo = MapUtils.getString(result, "result");
        if (!CONTRACT_MEMO_FAIL.equalsIgnoreCase(contractMemo)) {
            return contractMemo;
        }
        List<Map> callBackmsg = (List) result.get("message");
        String message = "";
        if (CollectionUtils.isEmpty(callBackmsg)) {
            return message;
        }
        Map callBack = callBackmsg.get(0);
        if (callBack.get("validateBankThreeElements") != null) {
            Map validateThree = (Map) callBackmsg.get(0).get("validateBankThreeElements");
            if (!CollectionUtils.isEmpty(validateThree)) {
                message = "对私+身份证，喔噻调第三方三要素验证失败";
            }
            return message;
        } else {
            message = BeanUtil.getPropString(callBack, MerchantLakalaContract.CONTRACT_CONTRACT_MEMO, "");
        }
        if (StringUtil.empty(message)) {
            message = (String) BeanUtil.getNestedProperty(callBack, String.format("%s.%s", CommonModel.RESPDATA, MerchantLakalaContract.CONTRACT_CONTRACT_MEMO));
        }
        if (StringUtil.empty(message)) {
            message = (String) BeanUtil.getNestedProperty(callBack, String.format("%s.%s", CommonModel.DATA, MerchantLakalaContract.CONTRACT_CONTRACT_MEMO));
        }
        return message;
    }


    private Map processingTaskTip(ContractTask contractTask, String type) {
        Map errorMessage = applicationApolloConfig.getProcessingContractMessage();
        ContractSubTask acquireSubTask = subtaskResultService.getAcquireSubTask(contractTask.getId());
        String message = "";
        if (acquireSubTask == null) {
//            return MapUtil.hashMap(CONTRACT_CODE, 9999, CONTRACT_MEMO, "收单机构进件任务不存在");
            return MapUtil.hashMap(CONTRACT_CODE, 9999, CONTRACT_MEMO, "微信申请单在处理中，请点击详情查看当前进度，或让" + CommonConstants.OPERATOR_NAME + "提供销售端微信认证详情页截图");
        }
        /**
         *
         *审核中的情况：
         *
         */
        //代付文案
        if (acquireSubTask.getSchedule_status() == 0) {
            Map result = JSON.parseObject(contractTask.getResult(), Map.class);
            if (!CollectionUtils.isEmpty(result)) {
                String channel = (String) result.get("channel");
                if (ProviderUtil.PAY_FOR_CHANNEL.equals(channel)) {
                    message = (String) result.get("message");
                }
            }
        }
        if (SUB_TASK_STATUS_PROCESSING.equals(acquireSubTask.getStatus()) || SubTaskStatus.isWaiting(acquireSubTask.getStatus())) {
            message = "拉卡拉进件返回applying，通用文案";
        }
        if (TASK_STATUS_SUCCESS.equals(acquireSubTask.getStatus())) {
            message = "拉卡拉进件通过，进件总状态为审核中，通用文案";
        }
        String key = org.apache.commons.lang.StringUtils.isBlank(contractTask.getRule_group_id()) ? ProviderUtil.LKL_PROVIDER_CHANNEL : contractTask.getRule_group_id();
        List<Map> channelError = (List) errorMessage.get(key);
        if (CollectionUtils.isEmpty(channelError)) {
            channelError = (List) errorMessage.get(ProviderUtil.LKL_PROVIDER_CHANNEL);
        }
        return getProcessApploConfig(channelError, type, message, contractTask);
    }


    private Map getProcessApploConfig(List<Map> channelError, String type, String contractMemo, ContractTask contractTask) {
        if (CollectionUtils.isEmpty(channelError)) {
            Map noConfigMap = MapUtil.hashMap("contract_memo", contractMemo, "contract_code", 9999);
            return noConfigMap;
        }
        for (Map errorMap : channelError) {
            String errorMsg = BeanUtil.getPropString(errorMap, "error_msg");
            if (contractMemo.contains(errorMsg)) {
                String msg = BeanUtil.getPropString(errorMap, type);
                //String contract_code=errorMap.get(type_code);
                String contract_code = (String) errorMap.get("error_code");
                Map configMap = MapUtil.hashMap(CONTRACT_MEMO, msg, CONTRACT_CODE, contract_code);
                //审核中的在产品需求列表中的需要处理预计审核完成时间
                configMap = processReviewComplete(configMap, contractTask, type);
                //审核中在产品需求列表中的的银行直连需要显示出银行状态
                configMap = processDirectBankStatus(configMap, contractTask);
                return configMap;
            }
        }
        Map unKnown = MapUtil.hashMap(CONTRACT_MEMO, contractMemo, CONTRACT_CODE, 9999);


        return unKnown;
    }

    private Map processReviewComplete(Map config, ContractTask contractTask, String type) {
        if (CollectionUtils.isEmpty(config) || contractTask == null) {
            return config;
        }
        String message = (String) config.get(CONTRACT_MEMO);
        String contract_code = (String) config.get(CONTRACT_CODE);
        if (StringUtils.isEmpty(message) || StringUtils.isEmpty(contract_code)) {
            return config;
        }
        if (LKL_APPLYING_CODE.equals(contract_code) || APPLYING_CODE.equals(contract_code)) {
            ApplyingTipsMinutes applyingTipsMinutes = JSON.parseObject(
                    applicationApolloConfig.getTipsForApplyingAddMinutes(), ApplyingTipsMinutes.class);
            if (applyingTipsMinutes == null) {
                applyingTipsMinutes = new ApplyingTipsMinutes();
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(contractTask.getUpdate_at());
            String reviewComplete = "";
            if ("app_msg".equals(type)) {
                calendar.add(Calendar.MINUTE, applyingTipsMinutes.getApp() == null ? ScheduleUtil.DEFAULT_TIPS_FOR_APP_MINUTES : applyingTipsMinutes.getApp());
                reviewComplete = StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", calendar.getTime());
                message = message.contains("#") ? message.replaceAll("#", reviewComplete) : message;
            } else if ("crm_msg".equals(type)) {
                calendar.add(Calendar.MINUTE, applyingTipsMinutes.getCrm() == null ? ScheduleUtil.DEFAULT_TIPS_FOR_CRM_MINUTES : applyingTipsMinutes.getCrm());
                reviewComplete = StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", calendar.getTime());
                message = message.contains("#") ? message.replaceAll("#", reviewComplete) : message;
            } else if ("sp_msg".equals(type)) {
                calendar.add(Calendar.MINUTE, applyingTipsMinutes.getSp() == null ? ScheduleUtil.DEFAULT_TIPS_FOR_SP_MINUTES : applyingTipsMinutes.getSp());
                reviewComplete = StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", calendar.getTime());
            } else {

            }
            message = message.contains("#") ? message.replaceAll("#", reviewComplete) : message;
            config.put(CONTRACT_MEMO, message);
            config.put(REVIEW_COMPLETE, calendar.getTimeInMillis());
        } else if (LKL_REVIEW_CODE.equals(contract_code)) {
            message = message.contains("#") ?
                    message.replaceAll("#", StringUtil.formatDate(completeDateBiz.getLklDate(contractTask.getPriority()))) : message;
            config.put(CONTRACT_MEMO, message);
            config.put(REVIEW_COMPLETE, completeDateBiz.getLklDate(contractTask.getPriority()).getTime());
            return config;
        } else if (PUBLIC_PAY_FOR_CODE.equals(contract_code)) {
            String date1 = completeDateBiz.getPayCompleteTime(contractTask.getPriority(), 30);
            String date2 = completeDateBiz.getWeekDay(contractTask.getPriority(), 1);
            message = MessageFormat.format(message, date1, date2);
            config.put(CONTRACT_MEMO, message);
            config.put(REVIEW_COMPLETE, contractTask.getComplete_at().getTime());
            return config;
        } else {
            message = message.contains("#") ?
                    message.replaceAll("#", StringUtil.formatDate(contractTask.getComplete_at())) : message;
            config.put(CONTRACT_MEMO, message);
            config.put(REVIEW_COMPLETE, contractTask.getComplete_at().getTime());
            return config;
        }
        return config;
    }


    /***
     * @param: [type]
     * @return: java.lang.String
     * @date: 18:15
     */
    private String getTypeKey(String type) {
        return type + "_msg";
    }

    private Map processDirectBankStatus(Map configMap, ContractTask contractTask) {
        if (CollectionUtils.isEmpty(configMap) || contractTask == null) {
            return configMap;
        }
        String message = (String) configMap.get(CONTRACT_MEMO);
        String contract_code = (String) configMap.get(CONTRACT_CODE);
        if (StringUtils.isEmpty(message) || StringUtils.isEmpty(contract_code)) {
            return configMap;
        }
        ContractSubTask acquireSubTask = subtaskResultService.getAcquireSubTask(contractTask.getId());
        final String result = StringUtils.isEmpty(acquireSubTask.getResult()) ? "审核中" : acquireSubTask.getResult();
        if (message.contains("*")) {
            message = message.replace("*", result);
            configMap.put(CONTRACT_MEMO, message);
        }
        return configMap;
    }

}
