package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class MultiProviderContractEvent {

    /**
     * 待处理
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 处理中
     */
    public static final int STATUS_PROCESS = 1;

    /**
     * 处理成功
     */
    public static final int STATUS_SUCCESS = 5;

    /**
     * 业务失败
     */
    public static final int STATUS_BIZ_FAIL = 6;

    private Long id;

    private String merchant_sn;

    private Long primary_task_id;

    private Long secondary_task_id;

    private Integer status;

    private Date create_at;

    private Date update_at;

    private Long version;

    private String primary_group_id;

    private String secondary_group_id;

    private String event_msg;

    private String result;
}