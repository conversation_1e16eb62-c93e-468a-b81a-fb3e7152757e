package com.wosai.upay.job.model.DO;

import lombok.Data;

@Data
public class CcbDecpMerchant {

    /**
     * 数币开通成功
     */
    public static final int OPEN_SUCCESS = 2;

    /**
     * 数币开通失败
     */
    public static final int OPEN_FAILED = 3;

    /**
     * 待申请
     */
    public static final int WAIT_OPEN_STATUS = 0;
    /**
     * 开通中
     */
    public static final int PROCESS_OPEN_STATUS = 1;
    /**
     * 开通成功
     */
    public static final int SUCCESS_OPEN_STATUS = 2;
    /**
     * 开通失败
     */
    public static final int FAIL_OPEN_STATUS = 3;

    private Long id;

    private String merchant_sn;

    private String identity;

    private Integer status;

    private Integer open_status;

    private String associated_success_sn;

    /**
     * 废弃
     */
    private Integer activated;
    /**
     * 废弃
     */
    private Integer submitted;

    private String result;

    private Long ctime;

    private Long mtime;

    private Long version;

    private String request_body;

    private String response_body;
}