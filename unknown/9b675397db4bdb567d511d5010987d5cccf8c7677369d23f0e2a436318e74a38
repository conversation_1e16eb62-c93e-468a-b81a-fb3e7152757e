package com.wosai.upay.job.model.callback.res;

import lombok.Data;

@Data
public class LuzhouCallBackRes {
    /**
     *
     */
    private String retCode = "000000";
    /**
     *
     */
    private String retMsg = "SUCCESS";
    /**
     * 处理状态
     */
    private String status = "SUCCESS";
    /**
     * sm2公钥
     */
    private String mchtPublicKey;

    public static LuzhouCallBackRes success(String mchtPublicKey) {
        LuzhouCallBackRes res = new LuzhouCallBackRes();
        res.setMchtPublicKey(mchtPublicKey);
        return res;
    }

    public static LuzhouCallBackRes fail() {
        LuzhouCallBackRes res = new LuzhouCallBackRes();
        res.setRetCode("");
        res.setRetMsg("参数解析失败");
        res.setStatus("FAIL");
        return res;
    }

    public static LuzhouCallBackRes fail(String msg) {
        LuzhouCallBackRes res = new LuzhouCallBackRes();
        res.setRetCode("");
        res.setRetMsg(msg);
        res.setStatus("FAIL");
        return res;
    }
}
