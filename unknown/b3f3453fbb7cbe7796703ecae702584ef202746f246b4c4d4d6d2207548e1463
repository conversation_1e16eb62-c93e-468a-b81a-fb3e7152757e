package com.wosai.upay.job.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import javax.sql.DataSource;

/**
 * @Description:JdbcTemplate配置
 * <AUTHOR>
 * Date 2020/5/27 4:39 下午
 **/
@Configuration
@Slf4j
public class DataSourceConfig {


    @Bean(name = "merchantContractJdbcTemplate")
    public JdbcTemplate merchantContractJdbcTemplate(@Qualifier("dataSource") DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        jdbcTemplate.setQueryTimeout(3);
        return jdbcTemplate;
    }

    @Bean(name = "merchantContractNPJdbcTemplate")
    public NamedParameterJdbcTemplate merchantContractNPJdbcTemplate(@Qualifier("dataSource") DataSource dataSource) {
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        jdbcTemplate.setQueryTimeout(3);
        return new NamedParameterJdbcTemplate(jdbcTemplate);
    }

}
