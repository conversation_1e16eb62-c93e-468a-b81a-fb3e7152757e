/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MicroUpgradeSuccessDTO extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 264638077830083023L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MicroUpgradeSuccessDTO\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchantSn\",\"type\":[\"null\",\"string\"],\"meta\":\"商户号\"},{\"name\":\"originalAcquirer\",\"type\":[\"string\",\"null\"],\"meta\":\"原收单机构\"},{\"name\":\"originalAcquirerMerchantId\",\"type\":[\"null\",\"string\"],\"meta\":\"原收单机构商户号\"},{\"name\":\"newAcquirer\",\"type\":[\"null\",\"string\"],\"meta\":\"新的收单机构\"},{\"name\":\"newAcquirerMerchantId\",\"type\":[\"string\",\"null\"],\"meta\":\"新的收单机构商户号\"},{\"name\":\"successTimeMillis\",\"type\":[\"long\",\"null\"],\"meta\":\"成功切换时间戳\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MicroUpgradeSuccessDTO> ENCODER =
      new BinaryMessageEncoder<MicroUpgradeSuccessDTO>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MicroUpgradeSuccessDTO> DECODER =
      new BinaryMessageDecoder<MicroUpgradeSuccessDTO>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MicroUpgradeSuccessDTO> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MicroUpgradeSuccessDTO> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MicroUpgradeSuccessDTO>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MicroUpgradeSuccessDTO to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MicroUpgradeSuccessDTO from a ByteBuffer. */
  public static MicroUpgradeSuccessDTO fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchantSn;
  @Deprecated public java.lang.CharSequence originalAcquirer;
  @Deprecated public java.lang.CharSequence originalAcquirerMerchantId;
  @Deprecated public java.lang.CharSequence newAcquirer;
  @Deprecated public java.lang.CharSequence newAcquirerMerchantId;
  @Deprecated public java.lang.Long successTimeMillis;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MicroUpgradeSuccessDTO() {}

  /**
   * All-args constructor.
   * @param merchantSn The new value for merchantSn
   * @param originalAcquirer The new value for originalAcquirer
   * @param originalAcquirerMerchantId The new value for originalAcquirerMerchantId
   * @param newAcquirer The new value for newAcquirer
   * @param newAcquirerMerchantId The new value for newAcquirerMerchantId
   * @param successTimeMillis The new value for successTimeMillis
   */
  public MicroUpgradeSuccessDTO(java.lang.CharSequence merchantSn, java.lang.CharSequence originalAcquirer, java.lang.CharSequence originalAcquirerMerchantId, java.lang.CharSequence newAcquirer, java.lang.CharSequence newAcquirerMerchantId, java.lang.Long successTimeMillis) {
    this.merchantSn = merchantSn;
    this.originalAcquirer = originalAcquirer;
    this.originalAcquirerMerchantId = originalAcquirerMerchantId;
    this.newAcquirer = newAcquirer;
    this.newAcquirerMerchantId = newAcquirerMerchantId;
    this.successTimeMillis = successTimeMillis;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchantSn;
    case 1: return originalAcquirer;
    case 2: return originalAcquirerMerchantId;
    case 3: return newAcquirer;
    case 4: return newAcquirerMerchantId;
    case 5: return successTimeMillis;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchantSn = (java.lang.CharSequence)value$; break;
    case 1: originalAcquirer = (java.lang.CharSequence)value$; break;
    case 2: originalAcquirerMerchantId = (java.lang.CharSequence)value$; break;
    case 3: newAcquirer = (java.lang.CharSequence)value$; break;
    case 4: newAcquirerMerchantId = (java.lang.CharSequence)value$; break;
    case 5: successTimeMillis = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchantSn' field.
   * @return The value of the 'merchantSn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchantSn;
  }

  /**
   * Sets the value of the 'merchantSn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchantSn = value;
  }

  /**
   * Gets the value of the 'originalAcquirer' field.
   * @return The value of the 'originalAcquirer' field.
   */
  public java.lang.CharSequence getOriginalAcquirer() {
    return originalAcquirer;
  }

  /**
   * Sets the value of the 'originalAcquirer' field.
   * @param value the value to set.
   */
  public void setOriginalAcquirer(java.lang.CharSequence value) {
    this.originalAcquirer = value;
  }

  /**
   * Gets the value of the 'originalAcquirerMerchantId' field.
   * @return The value of the 'originalAcquirerMerchantId' field.
   */
  public java.lang.CharSequence getOriginalAcquirerMerchantId() {
    return originalAcquirerMerchantId;
  }

  /**
   * Sets the value of the 'originalAcquirerMerchantId' field.
   * @param value the value to set.
   */
  public void setOriginalAcquirerMerchantId(java.lang.CharSequence value) {
    this.originalAcquirerMerchantId = value;
  }

  /**
   * Gets the value of the 'newAcquirer' field.
   * @return The value of the 'newAcquirer' field.
   */
  public java.lang.CharSequence getNewAcquirer() {
    return newAcquirer;
  }

  /**
   * Sets the value of the 'newAcquirer' field.
   * @param value the value to set.
   */
  public void setNewAcquirer(java.lang.CharSequence value) {
    this.newAcquirer = value;
  }

  /**
   * Gets the value of the 'newAcquirerMerchantId' field.
   * @return The value of the 'newAcquirerMerchantId' field.
   */
  public java.lang.CharSequence getNewAcquirerMerchantId() {
    return newAcquirerMerchantId;
  }

  /**
   * Sets the value of the 'newAcquirerMerchantId' field.
   * @param value the value to set.
   */
  public void setNewAcquirerMerchantId(java.lang.CharSequence value) {
    this.newAcquirerMerchantId = value;
  }

  /**
   * Gets the value of the 'successTimeMillis' field.
   * @return The value of the 'successTimeMillis' field.
   */
  public java.lang.Long getSuccessTimeMillis() {
    return successTimeMillis;
  }

  /**
   * Sets the value of the 'successTimeMillis' field.
   * @param value the value to set.
   */
  public void setSuccessTimeMillis(java.lang.Long value) {
    this.successTimeMillis = value;
  }

  /**
   * Creates a new MicroUpgradeSuccessDTO RecordBuilder.
   * @return A new MicroUpgradeSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder newBuilder() {
    return new com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder();
  }

  /**
   * Creates a new MicroUpgradeSuccessDTO RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MicroUpgradeSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder newBuilder(com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder other) {
    return new com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder(other);
  }

  /**
   * Creates a new MicroUpgradeSuccessDTO RecordBuilder by copying an existing MicroUpgradeSuccessDTO instance.
   * @param other The existing instance to copy.
   * @return A new MicroUpgradeSuccessDTO RecordBuilder
   */
  public static com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder newBuilder(com.wosai.upay.job.avro.MicroUpgradeSuccessDTO other) {
    return new com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder(other);
  }

  /**
   * RecordBuilder for MicroUpgradeSuccessDTO instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MicroUpgradeSuccessDTO>
    implements org.apache.avro.data.RecordBuilder<MicroUpgradeSuccessDTO> {

    private java.lang.CharSequence merchantSn;
    private java.lang.CharSequence originalAcquirer;
    private java.lang.CharSequence originalAcquirerMerchantId;
    private java.lang.CharSequence newAcquirer;
    private java.lang.CharSequence newAcquirerMerchantId;
    private java.lang.Long successTimeMillis;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[0].schema(), other.merchantSn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.originalAcquirer)) {
        this.originalAcquirer = data().deepCopy(fields()[1].schema(), other.originalAcquirer);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.originalAcquirerMerchantId)) {
        this.originalAcquirerMerchantId = data().deepCopy(fields()[2].schema(), other.originalAcquirerMerchantId);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.newAcquirer)) {
        this.newAcquirer = data().deepCopy(fields()[3].schema(), other.newAcquirer);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.newAcquirerMerchantId)) {
        this.newAcquirerMerchantId = data().deepCopy(fields()[4].schema(), other.newAcquirerMerchantId);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.successTimeMillis)) {
        this.successTimeMillis = data().deepCopy(fields()[5].schema(), other.successTimeMillis);
        fieldSetFlags()[5] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MicroUpgradeSuccessDTO instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.MicroUpgradeSuccessDTO other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchantSn)) {
        this.merchantSn = data().deepCopy(fields()[0].schema(), other.merchantSn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.originalAcquirer)) {
        this.originalAcquirer = data().deepCopy(fields()[1].schema(), other.originalAcquirer);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.originalAcquirerMerchantId)) {
        this.originalAcquirerMerchantId = data().deepCopy(fields()[2].schema(), other.originalAcquirerMerchantId);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.newAcquirer)) {
        this.newAcquirer = data().deepCopy(fields()[3].schema(), other.newAcquirer);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.newAcquirerMerchantId)) {
        this.newAcquirerMerchantId = data().deepCopy(fields()[4].schema(), other.newAcquirerMerchantId);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.successTimeMillis)) {
        this.successTimeMillis = data().deepCopy(fields()[5].schema(), other.successTimeMillis);
        fieldSetFlags()[5] = true;
      }
    }

    /**
      * Gets the value of the 'merchantSn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchantSn;
    }

    /**
      * Sets the value of the 'merchantSn' field.
      * @param value The value of 'merchantSn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchantSn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchantSn' field has been set.
      * @return True if the 'merchantSn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchantSn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearMerchantSn() {
      merchantSn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'originalAcquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getOriginalAcquirer() {
      return originalAcquirer;
    }

    /**
      * Sets the value of the 'originalAcquirer' field.
      * @param value The value of 'originalAcquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setOriginalAcquirer(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.originalAcquirer = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'originalAcquirer' field has been set.
      * @return True if the 'originalAcquirer' field has been set, false otherwise.
      */
    public boolean hasOriginalAcquirer() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'originalAcquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearOriginalAcquirer() {
      originalAcquirer = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'originalAcquirerMerchantId' field.
      * @return The value.
      */
    public java.lang.CharSequence getOriginalAcquirerMerchantId() {
      return originalAcquirerMerchantId;
    }

    /**
      * Sets the value of the 'originalAcquirerMerchantId' field.
      * @param value The value of 'originalAcquirerMerchantId'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setOriginalAcquirerMerchantId(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.originalAcquirerMerchantId = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'originalAcquirerMerchantId' field has been set.
      * @return True if the 'originalAcquirerMerchantId' field has been set, false otherwise.
      */
    public boolean hasOriginalAcquirerMerchantId() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'originalAcquirerMerchantId' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearOriginalAcquirerMerchantId() {
      originalAcquirerMerchantId = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'newAcquirer' field.
      * @return The value.
      */
    public java.lang.CharSequence getNewAcquirer() {
      return newAcquirer;
    }

    /**
      * Sets the value of the 'newAcquirer' field.
      * @param value The value of 'newAcquirer'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setNewAcquirer(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.newAcquirer = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'newAcquirer' field has been set.
      * @return True if the 'newAcquirer' field has been set, false otherwise.
      */
    public boolean hasNewAcquirer() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'newAcquirer' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearNewAcquirer() {
      newAcquirer = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'newAcquirerMerchantId' field.
      * @return The value.
      */
    public java.lang.CharSequence getNewAcquirerMerchantId() {
      return newAcquirerMerchantId;
    }

    /**
      * Sets the value of the 'newAcquirerMerchantId' field.
      * @param value The value of 'newAcquirerMerchantId'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setNewAcquirerMerchantId(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.newAcquirerMerchantId = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'newAcquirerMerchantId' field has been set.
      * @return True if the 'newAcquirerMerchantId' field has been set, false otherwise.
      */
    public boolean hasNewAcquirerMerchantId() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'newAcquirerMerchantId' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearNewAcquirerMerchantId() {
      newAcquirerMerchantId = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'successTimeMillis' field.
      * @return The value.
      */
    public java.lang.Long getSuccessTimeMillis() {
      return successTimeMillis;
    }

    /**
      * Sets the value of the 'successTimeMillis' field.
      * @param value The value of 'successTimeMillis'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder setSuccessTimeMillis(java.lang.Long value) {
      validate(fields()[5], value);
      this.successTimeMillis = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'successTimeMillis' field has been set.
      * @return True if the 'successTimeMillis' field has been set, false otherwise.
      */
    public boolean hasSuccessTimeMillis() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'successTimeMillis' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MicroUpgradeSuccessDTO.Builder clearSuccessTimeMillis() {
      successTimeMillis = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MicroUpgradeSuccessDTO build() {
      try {
        MicroUpgradeSuccessDTO record = new MicroUpgradeSuccessDTO();
        record.merchantSn = fieldSetFlags()[0] ? this.merchantSn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.originalAcquirer = fieldSetFlags()[1] ? this.originalAcquirer : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.originalAcquirerMerchantId = fieldSetFlags()[2] ? this.originalAcquirerMerchantId : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.newAcquirer = fieldSetFlags()[3] ? this.newAcquirer : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.newAcquirerMerchantId = fieldSetFlags()[4] ? this.newAcquirerMerchantId : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.successTimeMillis = fieldSetFlags()[5] ? this.successTimeMillis : (java.lang.Long) defaultValue(fields()[5]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MicroUpgradeSuccessDTO>
    WRITER$ = (org.apache.avro.io.DatumWriter<MicroUpgradeSuccessDTO>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MicroUpgradeSuccessDTO>
    READER$ = (org.apache.avro.io.DatumReader<MicroUpgradeSuccessDTO>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
