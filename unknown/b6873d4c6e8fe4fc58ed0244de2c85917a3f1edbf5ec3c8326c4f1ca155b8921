package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.AlyDistrictCode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AlyDistrictCodeMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AlyDistrictCode record);

    int insertSelective(AlyDistrictCode record);

    AlyDistrictCode selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AlyDistrictCode record);

    int updateByPrimaryKey(AlyDistrictCode record);

    List<AlyDistrictCode> selectByNames(@Param("list") List<String> names);

    List<AlyDistrictCode> selectByCondition(@Param("param") AlyDistrictCode record);
}