package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ContractReconsiderTask;

public interface ContractReconsiderTaskMapper {

    int insertSelective(ContractReconsiderTask record);

    ContractReconsiderTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ContractReconsiderTask record);

    ContractReconsiderTask selectByContractIdAndStatus(String contractId, int status);
}