package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.PayForTask;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface PayForTaskMapper {


    int insertSelective(PayForTask record);

    PayForTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PayForTask record);

    PayForTask selectBySnAndHashReq(String merchantSn, int hash);

    List<PayForTask> selectByCreate(String createStart, String createEnd, Integer limit);

    PayForTask selectBySubmitRemitOrderId(String id);

    List<PayForTask> selectByStatus(int status);

    List<PayForTask> selectByRequestFlowNo(String flowNo);

    List<PayForTask> selectToSendMessage(String before, String end);

    PayForTask selectBySubTaskId(long sub_task_id);


    @Select("select * from pay_for_task where merchant_sn=#{merchantSn} and status=4 limit 1")
    PayForTask getVerifyPayFor(String merchantSn);

    @Select("select * from pay_for_task where merchant_sn=#{merchantSn} and status not in(2,3) order by create_at desc limit 1")
    PayForTask getProgressBySn(String merchantSn);
}