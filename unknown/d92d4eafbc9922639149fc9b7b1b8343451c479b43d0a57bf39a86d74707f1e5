package com.wosai.upay.job.biz.bankDirect;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.model.psbc.SelfAuditRejectRequest;
import com.wosai.upay.job.providers.PabProvider;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.merchant.contract.constant.Constant;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.wosai.upay.job.providers.PabProvider.PAB_SIGN_RULE;
import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

@Component
@Slf4j
public class PabDirectBiz extends AbstractBankDirectApplyBiz{


    @Autowired
    private PabProvider pabProvider;

    public static final String MCHT_APTITUDE = "mchtAptitude";
    public static final String STORE_LOCA = "storeLoca";
    public static final String MCHT_LANDUSE = "mchtLanduse";
    public static final String LICENSE_AMOUNT = "licenseAmount";
    public static final String CONTACT_PHONE = "contactPhone";


    public static final String MCHT_SALE_NAME = "mchtSaleName";
    public static final String MCHT_SALE_NO = "mchtSaleNo";
    public static final String ORG_ID= "orgId";
    public static final String LEASE_AGREEMENT= "leaseAgreement";

    public static final List<String> customeFields =Lists.newArrayList(MCHT_APTITUDE,STORE_LOCA,MCHT_LANDUSE,LICENSE_AMOUNT,
            CONTACT_PHONE,MCHT_SALE_NAME,MCHT_SALE_NO,ORG_ID,LEASE_AGREEMENT);

    @Value("${pab_dev_code}")
    public String pabDevCode;

    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        return McConstant.RULE_GROUP_PAB;
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        //放入费率
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("pab_feeRate", config);
        //放入套餐
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code",bankDirectReq.getDev_code());
        //crm页面采集信息
        customeFields.forEach(field -> paramContext.put(field,BeanUtil.getPropString(formBody, field)));

        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES,list);
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.PAB.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.PAB.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_PAB.getValue();
    }

    @Override
    public String getDevCode() {
        return pabDevCode;
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(getAcquire());
        if(CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        //设置微信图片地址链接
        viewProcesses.stream().forEach(x-> {
            if(Objects.equals(x.getExtra(),Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
                x.setAliMessage(replaceHttp("https://images.wosaimg.com/43/94c324ceebb13328dd8d980818e6d3f4f57756.png"));
            }
        });
        return viewProcesses;
    }


    @Override
    public com.wosai.upay.merchant.contract.model.ContractResponse doReject(String merchantSn, SelfAuditRejectRequest request, ContractSubTask contractSubTask) {
        final com.wosai.upay.merchant.contract.model.ContractResponse response = new com.wosai.upay.merchant.contract.model.ContractResponse();
        response.setCode(Constant.RESULT_CODE_BIZ_EXCEPTION);
        try {
            //传入的不是签约任务,默认不可以驳回
            if(!Objects.equals(contractSubTask.getContract_rule(),PAB_SIGN_RULE)) {
                return  response.setMessage("当前商户审核状态不支持自助驳回");
            }
            final Boolean toBeSigned = pabProvider.checkBankContractToBeSigned(contractSubTask);
            if(toBeSigned) {
                response.setCode(Constant.RESULT_CODE_SUCCESSS);
            } else {
                response.setMessage("当前商户审核状态不支持自助驳回");
            }
        } catch (Exception exception) {
            response.setMessage(ExceptionUtil.getThrowableMsg(exception));
        }
        return response;
    }
}
