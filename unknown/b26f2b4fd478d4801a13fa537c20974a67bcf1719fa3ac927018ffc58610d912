package com.wosai.upay.job.enume;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * <AUTHOR>
 * @date 2024/4/15
 */
public enum ScheduleEnum implements ITextValueEnum<Integer> {
    SCHEDULE_ENABLE(1, "可以调度"),
    SCHEDULE_DISABLE(0, "不可调度")
    ;

    private final int value;
    private final String text;

    private ScheduleEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getText() {
        return text;
    }
}
