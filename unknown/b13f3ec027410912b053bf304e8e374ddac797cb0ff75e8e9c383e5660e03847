package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.McChannel;
import com.wosai.upay.job.model.DO.McChannelCustom;
import com.wosai.upay.job.model.DO.McChannelExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface McChannelMapper {
    int countByExample(McChannelExample example);

    int deleteByExample(McChannelExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(McChannel record);

    int insertSelective(McChannel record);

    List<McChannel> selectByExampleWithBLOBs(McChannelExample example);

    List<McChannel> selectByExample(McChannelExample example);

    List<McChannelCustom> selectByMcChannelCustom(@Param("params") McChannelCustom params);

    McChannel selectByPrimaryKey(Integer id);

    McChannel selectByChannel(@Param("channel") String channel);

    McChannelCustom selectChannelCustomByChannel(@Param("channel") String channel);

    int updateByExampleSelective(@Param("record") McChannel record, @Param("example") McChannelExample example);

    int updateByExampleWithBLOBs(@Param("record") McChannel record, @Param("example") McChannelExample example);

    int updateByExample(@Param("record") McChannel record, @Param("example") McChannelExample example);

    int updateByPrimaryKeySelective(McChannel record);

    int updateByChannelSelective(McChannel record);

    int updateByPrimaryKeyWithBLOBs(McChannel record);

    int updateByPrimaryKey(McChannel record);
}