package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.PendingTasks;

import java.util.Date;
import java.util.List;

public interface PendingTasksMapper {

    int insert(PendingTasks record);

    int insertSelective(PendingTasks record);

    PendingTasks selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PendingTasks record);

    List<PendingTasks> selectByMerchantSn(String merchantSn);

    List<PendingTasks> selectByMerchantSnAndEventType(String merchantSn,String eventType);

    int selectCountByCreateDateAndType(Date createAt,String eventType);

    int selectMinIdByCreateDateAndType(Date createAt,String eventType);

    List<PendingTasks> selectBetweenIds(int minId,int maxId,String eventType);

    List<PendingTasks> selectAfterTargetDate(Date createAt,String eventType,int pageSize);

    List<PendingTasks> selectFeeRateAfterTargetDate(Date createAt, Date endDate, int pageSize);

    List<PendingTasks> selectPendingTasksByCreateAtAndType(String createAt, String eventType, Integer limit);

    List<PendingTasks> selectPendingTasksByUpdateAtAndType(String updateAt, String eventType, Integer limit);

    int deleteBeforeTargetDate(Date createAt);

}