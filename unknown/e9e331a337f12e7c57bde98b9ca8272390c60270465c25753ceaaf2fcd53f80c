package com.wosai.upay.job.util;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.request.GetLogsRequest;
import com.aliyun.openservices.log.response.GetLogsResponse;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class PreCreateWarn {

    public static final Logger logger = LoggerFactory.getLogger(PreCreateWarn.class);

    private static final String PROJECT = "upay-gateway";
    private static final String LOG_STORE = "upay-gateway";
    private static final String QUERY_BASIC = "message: FAIL_CANCELED and message: ";

    private static Client client;

    @PostConstruct
    public void init() {
        client = new Client("cn-hangzhou.log.aliyuncs.com", "cua");
    }



    public String getErrorMessage(String orderSn) {
        String errorMessage = "未获得错误信息，请自行在日志查询";
        try {
            long to = System.currentTimeMillis() / 1000 + 50;
            long from = to - 300;

            GetLogsRequest request = new GetLogsRequest(PROJECT,
                    LOG_STORE,
                    (int) from,
                    (int) to,
                    "",
                    QUERY_BASIC + orderSn);
            GetLogsResponse response = client.GetLogs(request);
            if (response == null || (!response.IsCompleted())) {
                throw new RuntimeException("query error not complete");
            }
            if (response.IsCompleted()) {
                for (QueriedLog log : response.GetLogs()) {
                    LogItem item = log.GetLogItem();
                    for (LogContent content : item.GetLogContents()) {
                        if ("message".equalsIgnoreCase(content.mKey)) {
                            errorMessage = StringUtils.substringBetween(content.mValue, "provider_error_info=", ", biz_error_code");
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("preCreateWarn error", e);
        }
        return errorMessage;
    }

}
