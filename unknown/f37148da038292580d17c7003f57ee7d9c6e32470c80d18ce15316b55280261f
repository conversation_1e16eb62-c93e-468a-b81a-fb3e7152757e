package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WeixinDirectApply {
    private Long id;

    private String merchant_sn;

    private Long task_id;

    private String dev_code;

    private Integer submit_type;

    private Integer status;

    private String sign_url;

    private String result;

    private Date create_at;

    private Date update_at;

    private Date priority;

    private String request_body;

    private String response_body;

    private String qrcode_data;

    private String form_body;
}