package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.acquirer.JobContractChannel;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: RuleServiceImpl
 * <AUTHOR>
 * @Date 2021/11/23 6:06 下午
 **/

@Service
@AutoJsonRpcServiceImpl
public class RuleServiceImpl implements RuleService {

    @Autowired
    RuleContext ruleContext;

    @Override
    public JobContractChannel getContractChannel(String channel) {
        JobContractChannel jobContractChannel = new JobContractChannel();
        BeanUtils.copyProperties(ruleContext.getContractChannel(channel), jobContractChannel);
        return jobContractChannel;
    }

}