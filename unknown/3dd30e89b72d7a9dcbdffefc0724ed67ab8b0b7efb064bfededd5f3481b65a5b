/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class WxApply extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 7893835023958812749L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"WxApply\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"wx_keyname\",\"type\":\"string\",\"meta\":\"微信主体名称\"},{\"name\":\"wx_appid\",\"type\":\"string\",\"meta\":\"微信APPID\"},{\"name\":\"invalid_apply\",\"type\":\"string\",\"meta\":\"校验无效\"},{\"name\":\"success_apply\",\"type\":\"string\",\"meta\":\"校验成功\"},{\"name\":\"finish_apply\",\"type\":\"string\",\"meta\":\"提交申请成功\"},{\"name\":\"result_apply\",\"type\":\"string\",\"meta\":\"申请结果\"},{\"name\":\"failreason_apply\",\"type\":\"string\",\"meta\":\"申请失败原因\"},{\"name\":\"wx_apply_time\",\"type\":\"long\",\"meta\":\"申请时间\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<WxApply> ENCODER =
      new BinaryMessageEncoder<WxApply>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<WxApply> DECODER =
      new BinaryMessageDecoder<WxApply>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<WxApply> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<WxApply> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<WxApply>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this WxApply to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a WxApply from a ByteBuffer. */
  public static WxApply fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence wx_keyname;
  @Deprecated public java.lang.CharSequence wx_appid;
  @Deprecated public java.lang.CharSequence invalid_apply;
  @Deprecated public java.lang.CharSequence success_apply;
  @Deprecated public java.lang.CharSequence finish_apply;
  @Deprecated public java.lang.CharSequence result_apply;
  @Deprecated public java.lang.CharSequence failreason_apply;
  @Deprecated public long wx_apply_time;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public WxApply() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param wx_keyname The new value for wx_keyname
   * @param wx_appid The new value for wx_appid
   * @param invalid_apply The new value for invalid_apply
   * @param success_apply The new value for success_apply
   * @param finish_apply The new value for finish_apply
   * @param result_apply The new value for result_apply
   * @param failreason_apply The new value for failreason_apply
   * @param wx_apply_time The new value for wx_apply_time
   */
  public WxApply(java.lang.CharSequence merchant_id, java.lang.CharSequence wx_keyname, java.lang.CharSequence wx_appid, java.lang.CharSequence invalid_apply, java.lang.CharSequence success_apply, java.lang.CharSequence finish_apply, java.lang.CharSequence result_apply, java.lang.CharSequence failreason_apply, java.lang.Long wx_apply_time) {
    this.merchant_id = merchant_id;
    this.wx_keyname = wx_keyname;
    this.wx_appid = wx_appid;
    this.invalid_apply = invalid_apply;
    this.success_apply = success_apply;
    this.finish_apply = finish_apply;
    this.result_apply = result_apply;
    this.failreason_apply = failreason_apply;
    this.wx_apply_time = wx_apply_time;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return wx_keyname;
    case 2: return wx_appid;
    case 3: return invalid_apply;
    case 4: return success_apply;
    case 5: return finish_apply;
    case 6: return result_apply;
    case 7: return failreason_apply;
    case 8: return wx_apply_time;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_id = (java.lang.CharSequence)value$; break;
    case 1: wx_keyname = (java.lang.CharSequence)value$; break;
    case 2: wx_appid = (java.lang.CharSequence)value$; break;
    case 3: invalid_apply = (java.lang.CharSequence)value$; break;
    case 4: success_apply = (java.lang.CharSequence)value$; break;
    case 5: finish_apply = (java.lang.CharSequence)value$; break;
    case 6: result_apply = (java.lang.CharSequence)value$; break;
    case 7: failreason_apply = (java.lang.CharSequence)value$; break;
    case 8: wx_apply_time = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'wx_keyname' field.
   * @return The value of the 'wx_keyname' field.
   */
  public java.lang.CharSequence getWxKeyname() {
    return wx_keyname;
  }

  /**
   * Sets the value of the 'wx_keyname' field.
   * @param value the value to set.
   */
  public void setWxKeyname(java.lang.CharSequence value) {
    this.wx_keyname = value;
  }

  /**
   * Gets the value of the 'wx_appid' field.
   * @return The value of the 'wx_appid' field.
   */
  public java.lang.CharSequence getWxAppid() {
    return wx_appid;
  }

  /**
   * Sets the value of the 'wx_appid' field.
   * @param value the value to set.
   */
  public void setWxAppid(java.lang.CharSequence value) {
    this.wx_appid = value;
  }

  /**
   * Gets the value of the 'invalid_apply' field.
   * @return The value of the 'invalid_apply' field.
   */
  public java.lang.CharSequence getInvalidApply() {
    return invalid_apply;
  }

  /**
   * Sets the value of the 'invalid_apply' field.
   * @param value the value to set.
   */
  public void setInvalidApply(java.lang.CharSequence value) {
    this.invalid_apply = value;
  }

  /**
   * Gets the value of the 'success_apply' field.
   * @return The value of the 'success_apply' field.
   */
  public java.lang.CharSequence getSuccessApply() {
    return success_apply;
  }

  /**
   * Sets the value of the 'success_apply' field.
   * @param value the value to set.
   */
  public void setSuccessApply(java.lang.CharSequence value) {
    this.success_apply = value;
  }

  /**
   * Gets the value of the 'finish_apply' field.
   * @return The value of the 'finish_apply' field.
   */
  public java.lang.CharSequence getFinishApply() {
    return finish_apply;
  }

  /**
   * Sets the value of the 'finish_apply' field.
   * @param value the value to set.
   */
  public void setFinishApply(java.lang.CharSequence value) {
    this.finish_apply = value;
  }

  /**
   * Gets the value of the 'result_apply' field.
   * @return The value of the 'result_apply' field.
   */
  public java.lang.CharSequence getResultApply() {
    return result_apply;
  }

  /**
   * Sets the value of the 'result_apply' field.
   * @param value the value to set.
   */
  public void setResultApply(java.lang.CharSequence value) {
    this.result_apply = value;
  }

  /**
   * Gets the value of the 'failreason_apply' field.
   * @return The value of the 'failreason_apply' field.
   */
  public java.lang.CharSequence getFailreasonApply() {
    return failreason_apply;
  }

  /**
   * Sets the value of the 'failreason_apply' field.
   * @param value the value to set.
   */
  public void setFailreasonApply(java.lang.CharSequence value) {
    this.failreason_apply = value;
  }

  /**
   * Gets the value of the 'wx_apply_time' field.
   * @return The value of the 'wx_apply_time' field.
   */
  public java.lang.Long getWxApplyTime() {
    return wx_apply_time;
  }

  /**
   * Sets the value of the 'wx_apply_time' field.
   * @param value the value to set.
   */
  public void setWxApplyTime(java.lang.Long value) {
    this.wx_apply_time = value;
  }

  /**
   * Creates a new WxApply RecordBuilder.
   * @return A new WxApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.WxApply.Builder newBuilder() {
    return new com.wosai.upay.job.avro.WxApply.Builder();
  }

  /**
   * Creates a new WxApply RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new WxApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.WxApply.Builder newBuilder(com.wosai.upay.job.avro.WxApply.Builder other) {
    return new com.wosai.upay.job.avro.WxApply.Builder(other);
  }

  /**
   * Creates a new WxApply RecordBuilder by copying an existing WxApply instance.
   * @param other The existing instance to copy.
   * @return A new WxApply RecordBuilder
   */
  public static com.wosai.upay.job.avro.WxApply.Builder newBuilder(com.wosai.upay.job.avro.WxApply other) {
    return new com.wosai.upay.job.avro.WxApply.Builder(other);
  }

  /**
   * RecordBuilder for WxApply instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<WxApply>
    implements org.apache.avro.data.RecordBuilder<WxApply> {

    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence wx_keyname;
    private java.lang.CharSequence wx_appid;
    private java.lang.CharSequence invalid_apply;
    private java.lang.CharSequence success_apply;
    private java.lang.CharSequence finish_apply;
    private java.lang.CharSequence result_apply;
    private java.lang.CharSequence failreason_apply;
    private long wx_apply_time;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.WxApply.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.wx_keyname)) {
        this.wx_keyname = data().deepCopy(fields()[1].schema(), other.wx_keyname);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.wx_appid)) {
        this.wx_appid = data().deepCopy(fields()[2].schema(), other.wx_appid);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.invalid_apply)) {
        this.invalid_apply = data().deepCopy(fields()[3].schema(), other.invalid_apply);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.success_apply)) {
        this.success_apply = data().deepCopy(fields()[4].schema(), other.success_apply);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.finish_apply)) {
        this.finish_apply = data().deepCopy(fields()[5].schema(), other.finish_apply);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.result_apply)) {
        this.result_apply = data().deepCopy(fields()[6].schema(), other.result_apply);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.failreason_apply)) {
        this.failreason_apply = data().deepCopy(fields()[7].schema(), other.failreason_apply);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.wx_apply_time)) {
        this.wx_apply_time = data().deepCopy(fields()[8].schema(), other.wx_apply_time);
        fieldSetFlags()[8] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing WxApply instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.WxApply other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.wx_keyname)) {
        this.wx_keyname = data().deepCopy(fields()[1].schema(), other.wx_keyname);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.wx_appid)) {
        this.wx_appid = data().deepCopy(fields()[2].schema(), other.wx_appid);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.invalid_apply)) {
        this.invalid_apply = data().deepCopy(fields()[3].schema(), other.invalid_apply);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.success_apply)) {
        this.success_apply = data().deepCopy(fields()[4].schema(), other.success_apply);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.finish_apply)) {
        this.finish_apply = data().deepCopy(fields()[5].schema(), other.finish_apply);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.result_apply)) {
        this.result_apply = data().deepCopy(fields()[6].schema(), other.result_apply);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.failreason_apply)) {
        this.failreason_apply = data().deepCopy(fields()[7].schema(), other.failreason_apply);
        fieldSetFlags()[7] = true;
      }
      if (isValidValue(fields()[8], other.wx_apply_time)) {
        this.wx_apply_time = data().deepCopy(fields()[8].schema(), other.wx_apply_time);
        fieldSetFlags()[8] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'wx_keyname' field.
      * @return The value.
      */
    public java.lang.CharSequence getWxKeyname() {
      return wx_keyname;
    }

    /**
      * Sets the value of the 'wx_keyname' field.
      * @param value The value of 'wx_keyname'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setWxKeyname(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.wx_keyname = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'wx_keyname' field has been set.
      * @return True if the 'wx_keyname' field has been set, false otherwise.
      */
    public boolean hasWxKeyname() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'wx_keyname' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearWxKeyname() {
      wx_keyname = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'wx_appid' field.
      * @return The value.
      */
    public java.lang.CharSequence getWxAppid() {
      return wx_appid;
    }

    /**
      * Sets the value of the 'wx_appid' field.
      * @param value The value of 'wx_appid'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setWxAppid(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.wx_appid = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'wx_appid' field has been set.
      * @return True if the 'wx_appid' field has been set, false otherwise.
      */
    public boolean hasWxAppid() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'wx_appid' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearWxAppid() {
      wx_appid = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'invalid_apply' field.
      * @return The value.
      */
    public java.lang.CharSequence getInvalidApply() {
      return invalid_apply;
    }

    /**
      * Sets the value of the 'invalid_apply' field.
      * @param value The value of 'invalid_apply'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setInvalidApply(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.invalid_apply = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'invalid_apply' field has been set.
      * @return True if the 'invalid_apply' field has been set, false otherwise.
      */
    public boolean hasInvalidApply() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'invalid_apply' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearInvalidApply() {
      invalid_apply = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'success_apply' field.
      * @return The value.
      */
    public java.lang.CharSequence getSuccessApply() {
      return success_apply;
    }

    /**
      * Sets the value of the 'success_apply' field.
      * @param value The value of 'success_apply'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setSuccessApply(java.lang.CharSequence value) {
      validate(fields()[4], value);
      this.success_apply = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'success_apply' field has been set.
      * @return True if the 'success_apply' field has been set, false otherwise.
      */
    public boolean hasSuccessApply() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'success_apply' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearSuccessApply() {
      success_apply = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'finish_apply' field.
      * @return The value.
      */
    public java.lang.CharSequence getFinishApply() {
      return finish_apply;
    }

    /**
      * Sets the value of the 'finish_apply' field.
      * @param value The value of 'finish_apply'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setFinishApply(java.lang.CharSequence value) {
      validate(fields()[5], value);
      this.finish_apply = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'finish_apply' field has been set.
      * @return True if the 'finish_apply' field has been set, false otherwise.
      */
    public boolean hasFinishApply() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'finish_apply' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearFinishApply() {
      finish_apply = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'result_apply' field.
      * @return The value.
      */
    public java.lang.CharSequence getResultApply() {
      return result_apply;
    }

    /**
      * Sets the value of the 'result_apply' field.
      * @param value The value of 'result_apply'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setResultApply(java.lang.CharSequence value) {
      validate(fields()[6], value);
      this.result_apply = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'result_apply' field has been set.
      * @return True if the 'result_apply' field has been set, false otherwise.
      */
    public boolean hasResultApply() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'result_apply' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearResultApply() {
      result_apply = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'failreason_apply' field.
      * @return The value.
      */
    public java.lang.CharSequence getFailreasonApply() {
      return failreason_apply;
    }

    /**
      * Sets the value of the 'failreason_apply' field.
      * @param value The value of 'failreason_apply'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setFailreasonApply(java.lang.CharSequence value) {
      validate(fields()[7], value);
      this.failreason_apply = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'failreason_apply' field has been set.
      * @return True if the 'failreason_apply' field has been set, false otherwise.
      */
    public boolean hasFailreasonApply() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'failreason_apply' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearFailreasonApply() {
      failreason_apply = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    /**
      * Gets the value of the 'wx_apply_time' field.
      * @return The value.
      */
    public java.lang.Long getWxApplyTime() {
      return wx_apply_time;
    }

    /**
      * Sets the value of the 'wx_apply_time' field.
      * @param value The value of 'wx_apply_time'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder setWxApplyTime(long value) {
      validate(fields()[8], value);
      this.wx_apply_time = value;
      fieldSetFlags()[8] = true;
      return this;
    }

    /**
      * Checks whether the 'wx_apply_time' field has been set.
      * @return True if the 'wx_apply_time' field has been set, false otherwise.
      */
    public boolean hasWxApplyTime() {
      return fieldSetFlags()[8];
    }


    /**
      * Clears the value of the 'wx_apply_time' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.WxApply.Builder clearWxApplyTime() {
      fieldSetFlags()[8] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public WxApply build() {
      try {
        WxApply record = new WxApply();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.wx_keyname = fieldSetFlags()[1] ? this.wx_keyname : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.wx_appid = fieldSetFlags()[2] ? this.wx_appid : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.invalid_apply = fieldSetFlags()[3] ? this.invalid_apply : (java.lang.CharSequence) defaultValue(fields()[3]);
        record.success_apply = fieldSetFlags()[4] ? this.success_apply : (java.lang.CharSequence) defaultValue(fields()[4]);
        record.finish_apply = fieldSetFlags()[5] ? this.finish_apply : (java.lang.CharSequence) defaultValue(fields()[5]);
        record.result_apply = fieldSetFlags()[6] ? this.result_apply : (java.lang.CharSequence) defaultValue(fields()[6]);
        record.failreason_apply = fieldSetFlags()[7] ? this.failreason_apply : (java.lang.CharSequence) defaultValue(fields()[7]);
        record.wx_apply_time = fieldSetFlags()[8] ? this.wx_apply_time : (java.lang.Long) defaultValue(fields()[8]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<WxApply>
    WRITER$ = (org.apache.avro.io.DatumWriter<WxApply>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<WxApply>
    READER$ = (org.apache.avro.io.DatumReader<WxApply>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
