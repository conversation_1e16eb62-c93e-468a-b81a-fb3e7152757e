package com.wosai.upay.job.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import com.wosai.middleware.vault.VaultConfig;
import com.wosai.upay.merchant.contract.exception.ContractBizException;

import static com.wosai.upay.job.config.CustomerApplicationProvider.MERCHANT_CONTRACT_JOB;

/**
 * <AUTHOR>
 * @date 2022/5/14
 */
public class OssAK {

    public static OSSClient buildOSSClient(String endpoint) {
        try {
            Vault vault = Vault.load("cua", MERCHANT_CONTRACT_JOB);
            CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault);
            return new OSSClient(endpoint, credentialsProvider, new ClientBuilderConfiguration());
//        return new OSSClient(endpoint, OssAK.accessKeyID, OssAK.accessKeySecret);
        } catch (Exception e) {
            throw new ContractBizException("获取ossclient失败", e);
        }
    }
}
