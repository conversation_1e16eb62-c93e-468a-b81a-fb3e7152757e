package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.OpenCcbDecp;

public interface OpenCcbDecpMapper {
    int deleteByPrimaryKey(Long id);

    int insert(OpenCcbDecp record);

    int insertSelective(OpenCcbDecp record);

    OpenCcbDecp selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OpenCcbDecp record);

    int updateByPrimaryKeyWithBLOBs(OpenCcbDecp record);

    int updateByPrimaryKey(OpenCcbDecp record);
}