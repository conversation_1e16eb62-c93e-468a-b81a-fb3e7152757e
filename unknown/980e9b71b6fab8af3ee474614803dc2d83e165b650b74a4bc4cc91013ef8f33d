package com.wosai.upay.job.model.callback.req;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 泸州银行方商户审核通过或者商户入网成功后，使用该请求 请求我们配置的回调接口。
 */
@Data
@JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.PROPERTY,
        property = "notifyType")
@JsonSubTypes({
        @JsonSubTypes.Type(value = LuzhouNetInCallBack.class, name = "NOTICE_PARAMS"),
        @JsonSubTypes.Type(value = LuzhouAuditPassCallBack.class, name = "NOTICE_CHECK")
})
@ToString(callSuper = true)
public class LuzhouCallBackReqBasic {
    /**
     * 入网成功则是： NOTICE_PARAMS。 商户审核通过，则是NOTICE_CHECK
     */
    @Pattern(regexp = "^(NOTICE_PARAMS|NOTICE_CHECK)$", message = "泸州银行回调结果类型不正确,只能为 NOTICE_PARAMS 或者 NOTICE_CHECK")
    private String notifyType;
    /**
     * 商户号，由平泸州银行分配，M00xxxxxxxxxx
     */
    @NotNull(message = "银行商户号不能为空")
    private String merchantNo;
}
