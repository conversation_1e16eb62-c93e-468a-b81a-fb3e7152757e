package com.wosai.upay.job.model.payLater;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.model.payLater.vo.NotifyVO;
import lombok.Data;
import lombok.experimental.Accessors;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

@Data
@Accessors(chain = true)
public class PayLaterApply {
    private Long id;

    private String merchant_sn;

    private Long zft_merchant_apply_id;

    private Integer status;

    private Integer sub_status;

    private Integer process_status;

    private String account;

    private Date create_at;

    private Date update_at;

    private String result;

    private String form_body;

    private String extra;
    public Map<String,Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }


    public PayLaterInfoDTO getFormBody() {
        return JSONObject.parseObject(form_body,PayLaterInfoDTO.class);
    }

    public static Boolean isFinish(Integer status) {
        boolean contains = Lists.newArrayList(PayLaterConstant.Status.SUCCESS, PayLaterConstant.Status.ALI_FAIL, PayLaterConstant.Status.ZHIMA_FAIL, PayLaterConstant.Status.ANT_SHOP_FAIL).contains(status);
        return contains;
    }

    public boolean needProcessNotify(String notifyId, String bizTime) {
        // 回调通知的业务时间肯定比申请单创建时间要晚
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date parsedDate;
        try {
            parsedDate = dateFormat.parse(bizTime);
            if (create_at.compareTo(parsedDate) > 0) {
                return false;
            }
        } catch (ParseException ignore) {
        }
        NotifyVO notifyVO = JSON.parseObject(JSON.toJSONString(getExtraMap().get(PayLaterConstant.Extra.NOTIFY)), NotifyVO.class);
        if (Objects.isNull(notifyVO)) {
            return true;
        }
        if (!Objects.equals(notifyVO.getNotifyId(), notifyId) && notifyVO.getBizTime().compareTo(bizTime) < 0) {
            return true;
        }
        return false;
    }

    public void recordNotifyInfo(String notifyId, String bizTime) {
        NotifyVO notifyVO = new NotifyVO();
        notifyVO.setNotifyId(notifyId);
        notifyVO.setBizTime(bizTime);
        Map<String, Object> extraMap = getExtraMap();
        extraMap.put(PayLaterConstant.Extra.NOTIFY, notifyVO);
        this.extra = JSON.toJSONString(extraMap);
    }


}