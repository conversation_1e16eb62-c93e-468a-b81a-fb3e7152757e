/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantMessage extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 4591895285970983428L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantMessage\",\"namespace\":\"com.wosai.upay.job.avro\",\"fields\":[{\"name\":\"sn\",\"type\":\"string\"},{\"name\":\"opt_type\",\"type\":\"long\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MerchantMessage> ENCODER =
      new BinaryMessageEncoder<MerchantMessage>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MerchantMessage> DECODER =
      new BinaryMessageDecoder<MerchantMessage>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MerchantMessage> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MerchantMessage> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MerchantMessage>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MerchantMessage to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MerchantMessage from a ByteBuffer. */
  public static MerchantMessage fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence sn;
  @Deprecated public long opt_type;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MerchantMessage() {}

  /**
   * All-args constructor.
   * @param sn The new value for sn
   * @param opt_type The new value for opt_type
   */
  public MerchantMessage(java.lang.CharSequence sn, java.lang.Long opt_type) {
    this.sn = sn;
    this.opt_type = opt_type;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return sn;
    case 1: return opt_type;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: sn = (java.lang.CharSequence)value$; break;
    case 1: opt_type = (java.lang.Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'sn' field.
   * @return The value of the 'sn' field.
   */
  public java.lang.CharSequence getSn() {
    return sn;
  }

  /**
   * Sets the value of the 'sn' field.
   * @param value the value to set.
   */
  public void setSn(java.lang.CharSequence value) {
    this.sn = value;
  }

  /**
   * Gets the value of the 'opt_type' field.
   * @return The value of the 'opt_type' field.
   */
  public java.lang.Long getOptType() {
    return opt_type;
  }

  /**
   * Sets the value of the 'opt_type' field.
   * @param value the value to set.
   */
  public void setOptType(java.lang.Long value) {
    this.opt_type = value;
  }

  /**
   * Creates a new MerchantMessage RecordBuilder.
   * @return A new MerchantMessage RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantMessage.Builder newBuilder() {
    return new com.wosai.upay.job.avro.MerchantMessage.Builder();
  }

  /**
   * Creates a new MerchantMessage RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MerchantMessage RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantMessage.Builder newBuilder(com.wosai.upay.job.avro.MerchantMessage.Builder other) {
    return new com.wosai.upay.job.avro.MerchantMessage.Builder(other);
  }

  /**
   * Creates a new MerchantMessage RecordBuilder by copying an existing MerchantMessage instance.
   * @param other The existing instance to copy.
   * @return A new MerchantMessage RecordBuilder
   */
  public static com.wosai.upay.job.avro.MerchantMessage.Builder newBuilder(com.wosai.upay.job.avro.MerchantMessage other) {
    return new com.wosai.upay.job.avro.MerchantMessage.Builder(other);
  }

  /**
   * RecordBuilder for MerchantMessage instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantMessage>
    implements org.apache.avro.data.RecordBuilder<MerchantMessage> {

    private java.lang.CharSequence sn;
    private long opt_type;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.job.avro.MerchantMessage.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.sn)) {
        this.sn = data().deepCopy(fields()[0].schema(), other.sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.opt_type)) {
        this.opt_type = data().deepCopy(fields()[1].schema(), other.opt_type);
        fieldSetFlags()[1] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MerchantMessage instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.job.avro.MerchantMessage other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.sn)) {
        this.sn = data().deepCopy(fields()[0].schema(), other.sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.opt_type)) {
        this.opt_type = data().deepCopy(fields()[1].schema(), other.opt_type);
        fieldSetFlags()[1] = true;
      }
    }

    /**
      * Gets the value of the 'sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getSn() {
      return sn;
    }

    /**
      * Sets the value of the 'sn' field.
      * @param value The value of 'sn'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantMessage.Builder setSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'sn' field has been set.
      * @return True if the 'sn' field has been set, false otherwise.
      */
    public boolean hasSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'sn' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantMessage.Builder clearSn() {
      sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'opt_type' field.
      * @return The value.
      */
    public java.lang.Long getOptType() {
      return opt_type;
    }

    /**
      * Sets the value of the 'opt_type' field.
      * @param value The value of 'opt_type'.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantMessage.Builder setOptType(long value) {
      validate(fields()[1], value);
      this.opt_type = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'opt_type' field has been set.
      * @return True if the 'opt_type' field has been set, false otherwise.
      */
    public boolean hasOptType() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'opt_type' field.
      * @return This builder.
      */
    public com.wosai.upay.job.avro.MerchantMessage.Builder clearOptType() {
      fieldSetFlags()[1] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MerchantMessage build() {
      try {
        MerchantMessage record = new MerchantMessage();
        record.sn = fieldSetFlags()[0] ? this.sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.opt_type = fieldSetFlags()[1] ? this.opt_type : (java.lang.Long) defaultValue(fields()[1]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MerchantMessage>
    WRITER$ = (org.apache.avro.io.DatumWriter<MerchantMessage>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MerchantMessage>
    READER$ = (org.apache.avro.io.DatumReader<MerchantMessage>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
