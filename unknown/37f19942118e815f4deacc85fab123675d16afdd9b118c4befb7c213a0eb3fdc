package com.wosai.upay.job.config;

import com.wosai.upay.job.util.TxnSeqNoWorker;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.Inet4Address;
import java.net.UnknownHostException;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/11/26 18:49
 */

@Configuration
public class TxnSeqNoWorkerConfig {

    @Bean
    public TxnSeqNoWorker txnSeqNoWorker() {
        return new TxnSeqNoWorker(getWorkId(), getDataCenterId());
    }

    private Long getWorkId() {
        try {
            String hostAddress = Inet4Address.getLocalHost().getHostAddress();
            int[] ints = StringUtils.toCodePoints(hostAddress);
            int sums = 0;
            for (int b : ints) {
                sums += b;
            }
            return (long) (sums % 32);
        } catch (UnknownHostException e) {
            // 如果获取失败，则使用随机数备用
            return RandomUtils.nextLong(0, 31);
        }
    }

    private Long getDataCenterId() {
      /*  int[] ints = StringUtils.toCodePoints("");
        int sums = 0;
        for (int i : ints) {
            sums += i;
        }
        return (long) (sums % 32);*/
        return 1L;
    }
}
