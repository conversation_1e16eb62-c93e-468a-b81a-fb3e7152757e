package com.wosai.upay.job.model.DO;

import java.util.Date;

public class McAcquirerChange {
    private Integer id;

    private String apply_id;

    private String merchant_sn;

    private String merchant_id;

    private String source_acquirer;

    private String target_acquirer;

    private Integer status;

    private String memo;

    private Date create_at;

    private Date update_at;

    private Boolean immediately;

    private String process;

    private String extra;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getApply_id() {
        return apply_id;
    }

    public void setApply_id(String apply_id) {
        this.apply_id = apply_id;
    }

    public String getMerchant_sn() {
        return merchant_sn;
    }

    public void setMerchant_sn(String merchant_sn) {
        this.merchant_sn = merchant_sn;
    }

    public String getMerchant_id() {
        return merchant_id;
    }

    public void setMerchant_id(String merchant_id) {
        this.merchant_id = merchant_id;
    }

    public String getSource_acquirer() {
        return source_acquirer;
    }

    public void setSource_acquirer(String source_acquirer) {
        this.source_acquirer = source_acquirer;
    }

    public String getTarget_acquirer() {
        return target_acquirer;
    }

    public void setTarget_acquirer(String target_acquirer) {
        this.target_acquirer = target_acquirer;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Date getCreate_at() {
        return create_at;
    }

    public void setCreate_at(Date create_at) {
        this.create_at = create_at;
    }

    public Date getUpdate_at() {
        return update_at;
    }

    public void setUpdate_at(Date update_at) {
        this.update_at = update_at;
    }

    public Boolean getImmediately() {
        return immediately;
    }

    public void setImmediately(Boolean immediately) {
        this.immediately = immediately;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }
}