package com.wosai.upay.job.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021-06-18
 */
@Data
@Accessors(chain = true)
public class HandleQueryStatusResp {

    /**
     * 进件处理成功
     */
    private boolean success = false;

    /**
     * 进件处理失败
     */
    private boolean fail = false;

    /**
     * 进件中或者出现可重试异常
     */
    private boolean retry = false;

    /**
     * 结果
     */
    private String message;
}
