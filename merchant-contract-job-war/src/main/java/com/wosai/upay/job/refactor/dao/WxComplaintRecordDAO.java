package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.WxComplaintRecordMapper;
import com.wosai.upay.job.refactor.model.entity.WxComplaintRecordDO;

import javax.annotation.Resource;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;


/**
 * 微信投诉记录表表数据库访问层 {@link WxComplaintRecordDO}
 * 对WxComplaintRecordMapper层做出简单封装 {@link WxComplaintRecordMapper}
 *
 * <AUTHOR>
 */
@Repository
public class WxComplaintRecordDAO extends AbstractBaseDAO<WxComplaintRecordDO, WxComplaintRecordMapper> {

    public WxComplaintRecordDAO(SqlSessionFactory sqlSessionFactory, WxComplaintRecordMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据大于等于指定日期和provider查询投诉记录
     * 按照id排序
     *
     * @param creatDate 创建日期
     * @param provider  provider
     * @param id        起始id
     * @param limit     查询数量
     * @return 投诉记录
     */
    public List<WxComplaintRecordDO> listByDateAndProviderAndId(Date creatDate, int provider, Long id, int limit) {
        LambdaQueryWrapper<WxComplaintRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(WxComplaintRecordDO::getProvider, provider)
                .eq(WxComplaintRecordDO::getCreateDate, creatDate)
                .gt(WxComplaintRecordDO::getId, id)
                .orderByAsc(WxComplaintRecordDO::getId)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }
}
