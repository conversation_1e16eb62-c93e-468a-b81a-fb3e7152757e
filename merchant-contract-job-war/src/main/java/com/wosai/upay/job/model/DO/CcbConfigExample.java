package com.wosai.upay.job.model.DO;

import java.util.ArrayList;
import java.util.List;

public class CcbConfigExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CcbConfigExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeIsNull() {
            addCriterion("district_code is null");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeIsNotNull() {
            addCriterion("district_code is not null");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeEqualTo(String value) {
            addCriterion("district_code =", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeNotEqualTo(String value) {
            addCriterion("district_code <>", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeGreaterThan(String value) {
            addCriterion("district_code >", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeGreaterThanOrEqualTo(String value) {
            addCriterion("district_code >=", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeLessThan(String value) {
            addCriterion("district_code <", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeLessThanOrEqualTo(String value) {
            addCriterion("district_code <=", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeLike(String value) {
            addCriterion("district_code like", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeNotLike(String value) {
            addCriterion("district_code not like", value, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeIn(List<String> values) {
            addCriterion("district_code in", values, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeNotIn(List<String> values) {
            addCriterion("district_code not in", values, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeBetween(String value1, String value2) {
            addCriterion("district_code between", value1, value2, "district_code");
            return (Criteria) this;
        }

        public Criteria andDistrict_codeNotBetween(String value1, String value2) {
            addCriterion("district_code not between", value1, value2, "district_code");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNull() {
            addCriterion("province is null");
            return (Criteria) this;
        }

        public Criteria andProvinceIsNotNull() {
            addCriterion("province is not null");
            return (Criteria) this;
        }

        public Criteria andProvinceEqualTo(String value) {
            addCriterion("province =", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotEqualTo(String value) {
            addCriterion("province <>", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThan(String value) {
            addCriterion("province >", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceGreaterThanOrEqualTo(String value) {
            addCriterion("province >=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThan(String value) {
            addCriterion("province <", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLessThanOrEqualTo(String value) {
            addCriterion("province <=", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceLike(String value) {
            addCriterion("province like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotLike(String value) {
            addCriterion("province not like", value, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceIn(List<String> values) {
            addCriterion("province in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotIn(List<String> values) {
            addCriterion("province not in", values, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceBetween(String value1, String value2) {
            addCriterion("province between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andProvinceNotBetween(String value1, String value2) {
            addCriterion("province not between", value1, value2, "province");
            return (Criteria) this;
        }

        public Criteria andCityIsNull() {
            addCriterion("city is null");
            return (Criteria) this;
        }

        public Criteria andCityIsNotNull() {
            addCriterion("city is not null");
            return (Criteria) this;
        }

        public Criteria andCityEqualTo(String value) {
            addCriterion("city =", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotEqualTo(String value) {
            addCriterion("city <>", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThan(String value) {
            addCriterion("city >", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityGreaterThanOrEqualTo(String value) {
            addCriterion("city >=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThan(String value) {
            addCriterion("city <", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLessThanOrEqualTo(String value) {
            addCriterion("city <=", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityLike(String value) {
            addCriterion("city like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotLike(String value) {
            addCriterion("city not like", value, "city");
            return (Criteria) this;
        }

        public Criteria andCityIn(List<String> values) {
            addCriterion("city in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotIn(List<String> values) {
            addCriterion("city not in", values, "city");
            return (Criteria) this;
        }

        public Criteria andCityBetween(String value1, String value2) {
            addCriterion("city between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andCityNotBetween(String value1, String value2) {
            addCriterion("city not between", value1, value2, "city");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceIsNull() {
            addCriterion("private_min_price is null");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceIsNotNull() {
            addCriterion("private_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceEqualTo(String value) {
            addCriterion("private_min_price =", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceNotEqualTo(String value) {
            addCriterion("private_min_price <>", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceGreaterThan(String value) {
            addCriterion("private_min_price >", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceGreaterThanOrEqualTo(String value) {
            addCriterion("private_min_price >=", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceLessThan(String value) {
            addCriterion("private_min_price <", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceLessThanOrEqualTo(String value) {
            addCriterion("private_min_price <=", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceLike(String value) {
            addCriterion("private_min_price like", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceNotLike(String value) {
            addCriterion("private_min_price not like", value, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceIn(List<String> values) {
            addCriterion("private_min_price in", values, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceNotIn(List<String> values) {
            addCriterion("private_min_price not in", values, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceBetween(String value1, String value2) {
            addCriterion("private_min_price between", value1, value2, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPrivate_min_priceNotBetween(String value1, String value2) {
            addCriterion("private_min_price not between", value1, value2, "private_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceIsNull() {
            addCriterion("public_min_price is null");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceIsNotNull() {
            addCriterion("public_min_price is not null");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceEqualTo(String value) {
            addCriterion("public_min_price =", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceNotEqualTo(String value) {
            addCriterion("public_min_price <>", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceGreaterThan(String value) {
            addCriterion("public_min_price >", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceGreaterThanOrEqualTo(String value) {
            addCriterion("public_min_price >=", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceLessThan(String value) {
            addCriterion("public_min_price <", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceLessThanOrEqualTo(String value) {
            addCriterion("public_min_price <=", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceLike(String value) {
            addCriterion("public_min_price like", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceNotLike(String value) {
            addCriterion("public_min_price not like", value, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceIn(List<String> values) {
            addCriterion("public_min_price in", values, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceNotIn(List<String> values) {
            addCriterion("public_min_price not in", values, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceBetween(String value1, String value2) {
            addCriterion("public_min_price between", value1, value2, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andPublic_min_priceNotBetween(String value1, String value2) {
            addCriterion("public_min_price not between", value1, value2, "public_min_price");
            return (Criteria) this;
        }

        public Criteria andIns_noIsNull() {
            addCriterion("ins_no is null");
            return (Criteria) this;
        }

        public Criteria andIns_noIsNotNull() {
            addCriterion("ins_no is not null");
            return (Criteria) this;
        }

        public Criteria andIns_noEqualTo(String value) {
            addCriterion("ins_no =", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noNotEqualTo(String value) {
            addCriterion("ins_no <>", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noGreaterThan(String value) {
            addCriterion("ins_no >", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noGreaterThanOrEqualTo(String value) {
            addCriterion("ins_no >=", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noLessThan(String value) {
            addCriterion("ins_no <", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noLessThanOrEqualTo(String value) {
            addCriterion("ins_no <=", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noLike(String value) {
            addCriterion("ins_no like", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noNotLike(String value) {
            addCriterion("ins_no not like", value, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noIn(List<String> values) {
            addCriterion("ins_no in", values, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noNotIn(List<String> values) {
            addCriterion("ins_no not in", values, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noBetween(String value1, String value2) {
            addCriterion("ins_no between", value1, value2, "ins_no");
            return (Criteria) this;
        }

        public Criteria andIns_noNotBetween(String value1, String value2) {
            addCriterion("ins_no not between", value1, value2, "ins_no");
            return (Criteria) this;
        }

        public Criteria andAccountIsNull() {
            addCriterion("account is null");
            return (Criteria) this;
        }

        public Criteria andAccountIsNotNull() {
            addCriterion("account is not null");
            return (Criteria) this;
        }

        public Criteria andAccountEqualTo(String value) {
            addCriterion("account =", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotEqualTo(String value) {
            addCriterion("account <>", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThan(String value) {
            addCriterion("account >", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountGreaterThanOrEqualTo(String value) {
            addCriterion("account >=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThan(String value) {
            addCriterion("account <", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLessThanOrEqualTo(String value) {
            addCriterion("account <=", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountLike(String value) {
            addCriterion("account like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotLike(String value) {
            addCriterion("account not like", value, "account");
            return (Criteria) this;
        }

        public Criteria andAccountIn(List<String> values) {
            addCriterion("account in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotIn(List<String> values) {
            addCriterion("account not in", values, "account");
            return (Criteria) this;
        }

        public Criteria andAccountBetween(String value1, String value2) {
            addCriterion("account between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andAccountNotBetween(String value1, String value2) {
            addCriterion("account not between", value1, value2, "account");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeIsNull() {
            addCriterion("is_auto_change is null");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeIsNotNull() {
            addCriterion("is_auto_change is not null");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeEqualTo(Boolean value) {
            addCriterion("is_auto_change =", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeNotEqualTo(Boolean value) {
            addCriterion("is_auto_change <>", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeGreaterThan(Boolean value) {
            addCriterion("is_auto_change >", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_change >=", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeLessThan(Boolean value) {
            addCriterion("is_auto_change <", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeLessThanOrEqualTo(Boolean value) {
            addCriterion("is_auto_change <=", value, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeIn(List<Boolean> values) {
            addCriterion("is_auto_change in", values, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeNotIn(List<Boolean> values) {
            addCriterion("is_auto_change not in", values, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_change between", value1, value2, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andIs_auto_changeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_auto_change not between", value1, value2, "is_auto_change");
            return (Criteria) this;
        }

        public Criteria andDelay_dayIsNull() {
            addCriterion("delay_day is null");
            return (Criteria) this;
        }

        public Criteria andDelay_dayIsNotNull() {
            addCriterion("delay_day is not null");
            return (Criteria) this;
        }

        public Criteria andDelay_dayEqualTo(Integer value) {
            addCriterion("delay_day =", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayNotEqualTo(Integer value) {
            addCriterion("delay_day <>", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayGreaterThan(Integer value) {
            addCriterion("delay_day >", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayGreaterThanOrEqualTo(Integer value) {
            addCriterion("delay_day >=", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayLessThan(Integer value) {
            addCriterion("delay_day <", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayLessThanOrEqualTo(Integer value) {
            addCriterion("delay_day <=", value, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayIn(List<Integer> values) {
            addCriterion("delay_day in", values, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayNotIn(List<Integer> values) {
            addCriterion("delay_day not in", values, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayBetween(Integer value1, Integer value2) {
            addCriterion("delay_day between", value1, value2, "delay_day");
            return (Criteria) this;
        }

        public Criteria andDelay_dayNotBetween(Integer value1, Integer value2) {
            addCriterion("delay_day not between", value1, value2, "delay_day");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Long value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Long value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Long value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Long value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Long value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Long> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Long> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Long value1, Long value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Long value1, Long value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Long value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Long value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Long value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Long value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Long value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Long value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Long> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Long> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Long value1, Long value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Long value1, Long value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andMicro_infoIsNull() {
            addCriterion("micro_info is null");
            return (Criteria) this;
        }

        public Criteria andMicro_infoIsNotNull() {
            addCriterion("micro_info is not null");
            return (Criteria) this;
        }

        public Criteria andMicro_infoEqualTo(Boolean value) {
            addCriterion("micro_info =", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoNotEqualTo(Boolean value) {
            addCriterion("micro_info <>", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoGreaterThan(Boolean value) {
            addCriterion("micro_info >", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoGreaterThanOrEqualTo(Boolean value) {
            addCriterion("micro_info >=", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoLessThan(Boolean value) {
            addCriterion("micro_info <", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoLessThanOrEqualTo(Boolean value) {
            addCriterion("micro_info <=", value, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoIn(List<Boolean> values) {
            addCriterion("micro_info in", values, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoNotIn(List<Boolean> values) {
            addCriterion("micro_info not in", values, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoBetween(Boolean value1, Boolean value2) {
            addCriterion("micro_info between", value1, value2, "micro_info");
            return (Criteria) this;
        }

        public Criteria andMicro_infoNotBetween(Boolean value1, Boolean value2) {
            addCriterion("micro_info not between", value1, value2, "micro_info");
            return (Criteria) this;
        }

        public Criteria andBlack_mccIsNull() {
            addCriterion("black_mcc is null");
            return (Criteria) this;
        }

        public Criteria andBlack_mccIsNotNull() {
            addCriterion("black_mcc is not null");
            return (Criteria) this;
        }

        public Criteria andBlack_mccEqualTo(String value) {
            addCriterion("black_mcc =", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccNotEqualTo(String value) {
            addCriterion("black_mcc <>", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccGreaterThan(String value) {
            addCriterion("black_mcc >", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccGreaterThanOrEqualTo(String value) {
            addCriterion("black_mcc >=", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccLessThan(String value) {
            addCriterion("black_mcc <", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccLessThanOrEqualTo(String value) {
            addCriterion("black_mcc <=", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccLike(String value) {
            addCriterion("black_mcc like", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccNotLike(String value) {
            addCriterion("black_mcc not like", value, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccIn(List<String> values) {
            addCriterion("black_mcc in", values, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccNotIn(List<String> values) {
            addCriterion("black_mcc not in", values, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccBetween(String value1, String value2) {
            addCriterion("black_mcc between", value1, value2, "black_mcc");
            return (Criteria) this;
        }

        public Criteria andBlack_mccNotBetween(String value1, String value2) {
            addCriterion("black_mcc not between", value1, value2, "black_mcc");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}