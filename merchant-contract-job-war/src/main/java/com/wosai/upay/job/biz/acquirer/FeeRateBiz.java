package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.avro.FeeRateResult;
import com.wosai.upay.job.avro.TradeAppAcquirerChange;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.merchant.contract.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 *
 *  @Description: 一些费率变更相关的操作
 *  <AUTHOR>
 *  @Date 2024/8/29
 *
 */
@Component
@Slf4j
public class FeeRateBiz {

    @Resource(name = "kafkaAliTemplate")
    private KafkaTemplate<String, Object> kafkaAliTemplate;
    private static final String FEE_RATE_RESULT_TOPIC = "events_CUA_fee_rate_result";



    /**
     * 发送费率变更结果的消息
     * @param merchantSn
     * @param task
     * @param result
     */
    public void sendFeeRateKafkaMsg(String provider, String merchantSn, ContractTask task, Boolean result){
        try{
            Map contextParam = JSON.parseObject(task.getEvent_context(), Map.class);
            List<Map> sqbFeeRates = (List) MapUtils.getObject(contextParam, CommonConstant.SQB_FEE_RATES);
            FeeRateResult feeRateResult = new FeeRateResult(provider, result, merchantSn, JSON.toJSONString(sqbFeeRates), task.getId());
            kafkaAliTemplate.send(FEE_RATE_RESULT_TOPIC, feeRateResult);
        } catch (Exception e) {
            log.error("发送费率变更成功消息通知异常:taskId:{},商户号:{},异常信息:{}", task.getId(), merchantSn, e);
        }
    }

}
