package com.wosai.upay.job.externalservice.brand.model;

import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */
@Data
@Accessors(chain = true)
public class BrandMerchantInfoQueryResp {

    /**
     * 是否是品牌商户
     */
    private boolean brandMerchant;
    /**
     * 品牌ID
     */
    private String brandId;
    /**
     * 品牌商户的支付模式
     */
    private PaymentModeEnum paymentMode;

    /**
     * 商户类型
     */
    private String merchantType;

    public boolean isSubBrandMerchant() {
        return brandMerchant && !"BRAND_ADMIN".equals(merchantType);
    }

    public boolean isMainBrandMerchant() {
        return brandMerchant && "BRAND_ADMIN".equals(merchantType);
    }

    /**
     * 是否是品牌支付模式
     * @return true是 false不是
     */
    public boolean isBrandPayMode() {
        return PaymentModeEnum.ALI_BRAND_MODE.equals(paymentMode) || PaymentModeEnum.WX_BRAND_MODE.equals(paymentMode) || PaymentModeEnum.ALI_WX_BRAND_MODE.equals(paymentMode);
    }
}
