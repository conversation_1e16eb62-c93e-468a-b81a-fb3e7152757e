package com.wosai.upay.job.refactor.biz.acquirer.fuyou;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 富友入网处理
 *
 * <AUTHOR>
 * @date 2024/7/24 10:12
 */
@Component
@Slf4j
public class FuYouContractProcessor {

    public static final String FU_YOU_CONTRACT_SETTLE_TYPE_KEY = "settle_tp";

    @Resource
    protected ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    protected ApplicationApolloConfig apolloConfig;

    /**
     * 从富友的进件报文中，获取结算类型
     * 获取不到从进件的阿波罗配置中获取默认值
     *
     * @param merchantSn 商户号
     * @return 结算类型
     */
    public String  getContractSettleTypeByMerchantSn(String merchantSn) {
        try {
            List<ContractSubTaskDO> contractSubTaskDOS = contractSubTaskDAO.listContractSubTaskDOs(merchantSn, McConstant.RULE_GROUP_FUYOU,
                    ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(),
                    ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
            Optional<ContractSubTaskDO> subTaskDOOptional = contractSubTaskDOS.stream()
                    .filter(subTaskDO -> Objects.equals(subTaskDO.getPayway(), PaywayEnum.ACQUIRER.getValue())
                            && Objects.equals(subTaskDO.getContractRule(), AcquirerTypeEnum.FU_YOU.getValue()))
                    .findFirst();
            String requestBody = subTaskDOOptional.map(ContractSubTaskDO::getRequestBody).orElse("");
            String settleTypeFromRequest = MapUtils.getString(JSON.parseObject(requestBody), FU_YOU_CONTRACT_SETTLE_TYPE_KEY);
            if (StringUtils.isNotBlank(settleTypeFromRequest)) {
                return settleTypeFromRequest;
            }
        } catch (Exception e) {
            log.warn("解析富友进件报文获取结算类型失败, 商户号: {}", merchantSn, e);
        }
        return apolloConfig.getFuYouDefaultContractSettleType();
    }
}
