package com.wosai.upay.job.model.acquirer;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AcquirerChangeSaveDTO {
    public static final String SKIP_SYNC_BANK_ACCOUNT_KEY  = "skipSyncBankAccount";
    private String merchantSn;
    private String merchantId;
    private String sourceAcquirer;
    private String targetAcquirer;
    private Boolean immediately;
    private String tradeAppId;
    /**
     * true: 强制切换
     */
    private Boolean forceChange;

    /**
     * true:可以取消
     */
    private Boolean cancelable;

    private String extra;



}