package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * bns关联表表实体对象
 *
 * <AUTHOR>
 */
@TableName("bns_mch")
@Data
public class BnsMchDO {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 子商户号
     */
    @TableField(value = "sub_mch_id")
    private String subMchId;
    /**
     * 商户sn
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 商户名称
     */
    @TableField(value = "merchant_name")
    private String merchantName;
    /**
     * 绑定的商户号
     */
    @TableField(value = "bound_merchant_sn")
    private String boundMerchantSn;
    /**
     * 绑定的商户名
     */
    @TableField(value = "bound_merchant_name")
    private String boundMerchantName;
    /**
     * 1 成功  2 失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 描述
     */
    @TableField(value = "`desc`")
    private String desc;
    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Timestamp createAt;
    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    private Timestamp updateAt;

    public static Integer STATUS_SUCCESS = 1;

    public static Integer STATUS_Fail = 2;

    /**
     * 是否绑定
     *
     * @return 是否绑定
     */
    public boolean isBound() {
        return Objects.equals(status, STATUS_SUCCESS);
    }

    /**
     * 绑定失败
     *
     * @return 是否绑定
     */
    public boolean isBoundFail() {
        return Objects.equals(status, STATUS_Fail);
    }



}

