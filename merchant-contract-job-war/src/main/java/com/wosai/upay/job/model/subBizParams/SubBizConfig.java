package com.wosai.upay.job.model.subBizParams;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/1
 */
@Data
@Accessors(chain = true)
public class SubBizConfig {

    /**
     * 业务名称，比如【储值】
     */
    private String tradeName;

    /**
     * 映射业务的名称，比如【智慧经营业务】
     */
    private String mappingTradeName;

    /**
     * 映射业务的tradeAppId，比如【4707】
     */
    private String mappingTradeAppId;

    /**
     * 映射业务的comboId，比如【5333】
     */
    private long mappingComboId;

    /**
     * 自定义费率
     */
    private Map<String,String> feeRate;

    private boolean forceCombo;

    /**
     * 是否按照payway设置套餐
     */
    private boolean paywayCombo;
}
