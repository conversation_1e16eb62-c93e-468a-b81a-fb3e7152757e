package com.wosai.upay.job.refactor.service.localcache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.upay.job.refactor.biz.rule.GroupCombinedStrategyBiz;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDAO;
import com.wosai.upay.job.refactor.dao.GroupCombinedStrategyDetailDAO;
import com.wosai.upay.job.refactor.dao.GroupRouteRuleDetailDAO;
import com.wosai.upay.job.refactor.dao.GroupRouteRulesDecisionDAO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRuleDetailDO;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 进件规则本地缓存服务
 *
 * <AUTHOR>
 * @date 2023/11/28 15:04
 */
@Service
public class McRulesLocalCacheService {

    @Resource
    private GroupRouteRulesDecisionDAO groupRouteRulesDecisionDAO;

    @Resource
    private GroupRouteRuleDetailDAO groupRouteRuleDetailDAO;

    @Resource
    private GroupCombinedStrategyDAO groupCombinedStrategyDAO;

    @Resource
    private GroupCombinedStrategyDetailDAO groupCombinedStrategyDetailDAO;


    private static final Long DEFAULT_DURATION = 5L;

    private static final Integer INITIAL_CAPACITY = 100;

    private static final Integer MAXIMUM_SIZE = 2000;

    private static final Long DEFAULT_KEY = 1L;

    private static final String ALL_DECISION_KEY = "rule:decision:all";


    private final Cache<Long, List<GroupRouteRuleDetailDO>> ruleDetailCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<String, List<GroupRouteRulesDecisionDO>> rulesDecisionCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<Long, GroupCombinedStrategyDO> combinedStrategyCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();

    private final Cache<Long, List<GroupCombinedStrategyDetailDO>> combinedStrategyDetailCache =
            Caffeine.newBuilder()
                    .initialCapacity(INITIAL_CAPACITY)
                    .maximumSize(MAXIMUM_SIZE)
                    .expireAfterWrite(DEFAULT_DURATION, TimeUnit.MINUTES).build();


    /**
     * 从缓存中获取有效的规则决策
     *
     * @return 规则决策
     */
    public List<GroupRouteRuleDetailDO> listAllValidRuleDetails() {
        return ruleDetailCache.get(DEFAULT_KEY, defaultKey -> groupRouteRuleDetailDAO.listAllValid());
    }

    /**
     * 从缓存中获取规则详情
     *
     * @return 规则详情
     */
    public List<GroupRouteRulesDecisionDO> listAllValidRuleDecisions() {
        return rulesDecisionCache.get(ALL_DECISION_KEY, t -> groupRouteRulesDecisionDAO.listAllValid());
    }


    /**
     * 根据进件报备规则组策略组合主键id获取
     *
     * @param id 主键id
     * @return 进件报备规则组策略组合
     */
    public Optional<GroupCombinedStrategyDO> getCombinedStrategyById(Long id) {
        return Optional.ofNullable(combinedStrategyCache.get(id, t -> groupCombinedStrategyDAO.getByPrimaryKey(t).orElse(null)));
    }


    /**
     * 根据进件报备规则组策略组合id获取策略组合detail
     *
     * @param strategyId 策略组合id
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listStrategyDetailByStrategyId(Long strategyId) {
        if (Objects.isNull(strategyId)) {
            return Collections.emptyList();
        }
        return combinedStrategyDetailCache.get(strategyId, t -> groupCombinedStrategyDetailDAO
                .listByStrategyId(t).stream().filter(detailDO -> Objects.equals(detailDO.getValidStatus(), ValidStatusEnum.VALID.getValue()))
                .collect(Collectors.toList()));
    }

}
