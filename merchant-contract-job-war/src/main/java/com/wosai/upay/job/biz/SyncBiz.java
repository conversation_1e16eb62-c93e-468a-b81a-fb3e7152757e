package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonDatabaseDuplicateKeyException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-11-01
 */
@Component
@Slf4j
public class SyncBiz {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private MerchantProviderParamsMapper paramsMapper;

    public void syncMerchantConfigToParams(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

        List<Map> merchantConfigs = tradeConfigService.getMerchantConfigsByMerchantId(merchantId);
        if (merchantConfigs != null && !merchantConfigs.isEmpty()) {
            for (Map merchantConfig : merchantConfigs) {
                doSync(merchant, merchantConfig);
            }
        }

    }

    private void doSync(Map merchant, Map merchantConfig) {
        int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        switch (payway) {
            case PayParamsModel.PAYWAY_ALIPAY2:
                convertAlipay(merchantSn, merchantConfig);
                break;
            case PayParamsModel.PAYWAY_WEIXIN:
                convertWeixin(merchantSn, merchantConfig);
                break;
            case PayParamsModel.PAYWAY_BESTPAY:
                convertBestPay(merchantSn, merchantConfig);
                break;
            case PayParamsModel.PAYWAY_WEIXIN_HK:
                convertWeixinHK(merchantSn, merchantConfig);
                break;
            case PayParamsModel.PAYWAY_ALIPAY_INTL:
                convertAlipayIntl(merchantSn, merchantConfig);
                break;
            case PayParamsModel.PAYWAY_LKL_UNIONPAY:
                convertUnionPay(merchantSn, merchantConfig);
                break;
            default:
                break;
        }
    }

    private void convertAlipay(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }
        int provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
        String prefix = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
        String payMerchantId = BeanUtil.getPropString(params, prefix + ".alipay_sub_mch_id");
        String providerMerchantId = BeanUtil.getPropString(params, prefix + ".provider_mch_id");
        useParams(PaywayEnum.ALIPAY.getValue(), merchantSn, payMerchantId, providerMerchantId);

        if (isFormal(merchantConfig)) {
            Map directParams = BeanUtil.getPart(params, Arrays.asList("alipay_wap_v2_trade_params", "alipay_v2_trade_params", "alipay_h5_v2_trade_params", "alipay_mini_v2_trade_params", "alipay_app_v2_trade_params"));
            if (WosaiMapUtils.isNotEmpty(directParams)) {
                saveMerchantProviderParams(
                        new MerchantProviderParams()
                                .setMerchant_sn(merchantSn)
                                .setExtra(CommonUtil.map2Bytes(directParams))
                                .setProvider(ProviderEnum.ALI_PAY.getValue())
                                .setPayway(PaywayEnum.ALIPAY.getValue())
                );
            }
        }

    }

    private void convertWeixin(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }

        int provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
        String prefix = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
        String payMerchantId = BeanUtil.getPropString(params, prefix + ".weixin_sub_mch_id");
        String providerMerchantId = BeanUtil.getPropString(params, prefix + ".provider_mch_id");
        useParams(PaywayEnum.WEIXIN.getValue(), merchantSn, payMerchantId, providerMerchantId);

        if (isFormal(merchantConfig)) {
            Map directParams = BeanUtil.getPart(params, Arrays.asList("weixin_wap_trade_params", "weixin_trade_params", "weixin_mini_trade_params", "weixin_h5_trade_params", "weixin_app_trade_params"));
            if (WosaiMapUtils.isNotEmpty(directParams)) {
                saveMerchantProviderParams(
                        new MerchantProviderParams()
                                .setMerchant_sn(merchantSn)
                                .setExtra(CommonUtil.map2Bytes(directParams))
                                .setProvider(ProviderEnum.WEI_XIN.getValue())
                                .setPayway(PaywayEnum.WEIXIN.getValue())
                                .setPay_merchant_id(BeanUtil.getPropString(directParams, "weixin_wap_trade_params.weixin_sub_mch_id"))
                                .setWeixin_sub_appid(BeanUtil.getPropString(directParams, "weixin_wap_trade_params.weixin_sub_appid"))
                );
            }
        }

    }

    private void convertBestPay(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }

        int provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
        String prefix = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
        String sp_mch_id = BeanUtil.getPropString(params, prefix + ".sp_mch_id");
        useParams(PaywayEnum.BESTPAY.getValue(), merchantSn, sp_mch_id, sp_mch_id);

        if (isFormal(merchantConfig)) {
            Map directParams = BeanUtil.getPart(params, Arrays.asList("bestpay_trade_params"));
            if (WosaiMapUtils.isNotEmpty(directParams)) {
                saveMerchantProviderParams(
                        new MerchantProviderParams()
                                .setMerchant_sn(merchantSn)
                                .setExtra(CommonUtil.map2Bytes(directParams))
                                .setProvider(ProviderEnum.BESTPAY.getValue())
                                .setPayway(PaywayEnum.BESTPAY.getValue())
                                .setPay_merchant_id(BeanUtil.getPropString(directParams, "bestpay_trade_params.merchant_id"))
                );
            }
        }
    }

    private void convertWeixinHK(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }

        if (isFormal(merchantConfig)) {
            Map directParams = BeanUtil.getPart(params, Arrays.asList("weixin_trade_params"));
            if (WosaiMapUtils.isNotEmpty(directParams)) {
                saveMerchantProviderParams(
                        new MerchantProviderParams()
                                .setMerchant_sn(merchantSn)
                                .setExtra(CommonUtil.map2Bytes(directParams))
                                .setProvider(ProviderEnum.WEIXIN_HK.getValue())
                                .setPayway(PaywayEnum.WEIXIN_HK.getValue())
                                .setPay_merchant_id(BeanUtil.getPropString(directParams, "weixin_trade_params.weixin_mch_id"))
                );
            }
        }
    }

    private void convertAlipayIntl(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }

        if (isFormal(merchantConfig)) {
            Map directParams = BeanUtil.getPart(params, Arrays.asList("alipay_intl_trade_params"));
            if (WosaiMapUtils.isNotEmpty(directParams)) {
                saveMerchantProviderParams(
                        new MerchantProviderParams()
                                .setMerchant_sn(merchantSn)
                                .setExtra(CommonUtil.map2Bytes(directParams))
                                .setProvider(ProviderEnum.ALIPAY_INTL.getValue())
                                .setPayway(PaywayEnum.ALIPAY_INTL.getValue())
                                .setPay_merchant_id(BeanUtil.getPropString(directParams, "alipay_intl_trade_params.merchant_id"))
                );
            }
        }
    }

    private void convertUnionPay(String merchantSn, Map merchantConfig) {
        Map params = getParams(merchantConfig);
        if (WosaiMapUtils.isEmpty(params)) {
            return;
        }

        int provider = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER);
        String prefix = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
        String upo_mch_id = BeanUtil.getPropString(params, prefix + ".upo_mch_id");
        useParams(PaywayEnum.UNIONPAY.getValue(), merchantSn, upo_mch_id, upo_mch_id);
    }

    private void saveMerchantProviderParams(MerchantProviderParams providerParams) {
        MerchantProviderParams existParams = getDirectParams(providerParams.getMerchant_sn(), providerParams.getProvider(), providerParams.getPayway());
        if (existParams == null) {
            providerParams.setId(UUID.randomUUID().toString())
                    .setCtime(System.currentTimeMillis())
                    .setMtime(System.currentTimeMillis())
                    .setChannel_no(providerParams.getProvider().toString())
                    .setStatus(UseStatusEnum.IN_USE.getValue());
            paramsMapper.insertSelective(providerParams);
        } else {
            Map<String, Object> extra = CommonUtil.bytes2Map(existParams.getExtra());
            extra.putAll(CommonUtil.bytes2Map(providerParams.getExtra()));
            providerParams.setId(existParams.getId())
                    .setExtra(CommonUtil.map2Bytes(extra))
                    .setMtime(System.currentTimeMillis())
                    .setStatus(UseStatusEnum.IN_USE.getValue());
            paramsMapper.updateByPrimaryKeySelective(providerParams);
        }
    }

    private void useParams(int payway, String merchantSn, String payMerchantId, String providerMerchantId) {
        if (StringUtil.empty(payMerchantId) || StringUtil.empty(providerMerchantId)) {
            return;
        }
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(payway);
        paramsMapper.updateByExampleSelective(new MerchantProviderParams().setStatus(UseStatusEnum.NO_USE.getValue()), example);

        example = new MerchantProviderParamsExample();
        example.or().andPaywayEqualTo(payway)
                .andMerchant_snEqualTo(merchantSn)
                .andPay_merchant_idEqualTo(payMerchantId)
                .andProvider_merchant_idEqualTo(providerMerchantId);
        paramsMapper.updateByExampleSelective(new MerchantProviderParams().setStatus(UseStatusEnum.IN_USE.getValue()), example);
    }

    private MerchantProviderParams getDirectParams(String merchantSn, int provider, int payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider)
                .andPaywayEqualTo(payway)
                .andDeletedEqualTo(false);

        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            return null;
        } else if (params.size() == 1) {
            return params.get(0);
        } else {
            throw new CommonDatabaseDuplicateKeyException(String.format("直连参数重复：%s %s %s", merchantSn, provider, payway));
        }

    }

    public boolean isFormal(Map merchantConfig) {
        return BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.WAP_FORMAL, Boolean.FALSE)  ||
                BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.B2C_FORMAL, Boolean.FALSE) ||
                BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.C2B_FORMAL, Boolean.FALSE) ||
                BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.H5_FORMAL, Boolean.FALSE) ||
                BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.MINI_FORMAL, Boolean.FALSE)||
                BeanUtil.getPropBoolean(merchantConfig, MerchantConfig.APP_FORMAL, Boolean.FALSE);
    }

    private Map getParams(Map merchantConfig) {
        return (Map) merchantConfig.get(MerchantConfig.PARAMS);
    }
}
