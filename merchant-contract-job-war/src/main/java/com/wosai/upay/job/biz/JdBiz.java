package com.wosai.upay.job.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.wosai.bsm.creditpaybackend.service.FqConfigService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.constant.UnionConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 京东钱包相关操作
 * <AUTHOR>
 * @Date 2024/9/12 14:50
 */
@Component
@Slf4j
public class JdBiz {

    @Value("${jd.combId}")
    public Long jdCombId;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private TradeConfigService tradeConfigService;


    @Autowired
    private MerchantService merchantService;
    @Autowired
    private com.wosai.mc.service.MerchantService mcMerchantService;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private FqConfigService fqConfigService;
    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ContractRelatedMappingConfigService contractRelatedMappingConfigService;


    public static final String LADDER_FEE_RATES = "ladder_fee_rates";

    /**
     * 渠道-阶梯费率
     */
    public static final String CHANNEL_LADDER = "channel_ladder";
    /**
     * 渠道费率
     */
    public static final String CHANNEL = "channel";

    public static final String LADDER_FEE_RATES_MAX = "max";
    public static final String LADDER_FEE_RATES_MIN = "min";

    /**
     * 禁止行业
     */
    public static List<Integer> FORBID_LIST =  Lists.newArrayList(5933, 6211, 6015, 6010, 6011, 6012, 4829, 6050, 6051);


    /**
     * 支持白条的收单机构
     */

    public static List<String>  SUPPORT_ACQUIRE_LIST =  Lists.newArrayList(AcquirerTypeEnum.LKL_V3.getValue(),AcquirerTypeEnum.FU_YOU.getValue());
    public static List<Integer>  SUPPORT_PROVIDER_LIST =  Lists.newArrayList(ProviderEnum.PROVIDER_FUYOU.getValue(),ProviderEnum.PROVIDER_LKL_OPEN.getValue());


    /**
     * 设置京东钱包费率
     * @param merchantProviderParamsId
     */
    public void setJdComb(String merchantProviderParamsId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(merchantProviderParamsId);

        // 非 JD_WALLET 通道，直接返回
        if (merchantProviderParams == null || !Objects.equals(merchantProviderParams.getPayway(), PaywayEnum.JD_WALLET.getValue())) {
            return;
        }

        final String merchantSn = merchantProviderParams.getMerchant_sn();

        doComb(merchantSn);
    }

   public static List<String> SqbFeeRateKeys = Arrays.asList(
            MerchantConfig.PAYWAY,
            MerchantConfig.LADDER_STATUS,
            MerchantConfig.FEE_RATE_TYPE,
            MerchantConfig.B2C_FEE_RATE,
            MerchantConfig.C2B_FEE_RATE,
            MerchantConfig.WAP_FEE_RATE,
            TransactionParam.LADDER_FEE_RATES,
            TransactionParam.PARAMS_BANKCARD_FEE,
            TransactionParam.CHANNEL_LADDER_FEE_RATES
    );


    /**
     * 为商户设置京东钱包套餐
     * @param merchantSn
     */
    public void doComb(String merchantSn) {
        // 获取商户信息
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (merchant == null) {
            // 如果获取不到商户信息，直接返回
            return;
        }
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);

        // 获取京东费率
        List<Map<String, Object>> jdPayWayList = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{PaywayEnum.JD_WALLET.getValue()});
        //merchant_config表是否存在京东白条费率
        final Map jdConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.JD_WALLET.getValue());
        final Integer merchantConfigProvider = BeanUtil.getPropInt(jdConfig, MerchantConfig.PROVIDER);
        final String miniFeeRate = BeanUtil.getPropString(jdConfig, MerchantConfig.MINI_FEE_RATE);
        if(CollectionUtils.isEmpty(jdPayWayList) || !SUPPORT_PROVIDER_LIST.contains(merchantConfigProvider) || StrUtil.isBlank(miniFeeRate)) {
            jdPayWayList = tradeConfigService.getAnalyzedMerchantConfigsByPayWayList(merchantId, new int[]{PaywayEnum.ALIPAY.getValue()});
        }
        final List<Map> maps = jdPayWayList.stream()
                .map(map -> Maps.filterKeys(map, SqbFeeRateKeys::contains))
                .collect(Collectors.toList());

        if (WosaiCollectionUtils.isEmpty(jdPayWayList)) {
            // 如果没有获取到支付方式配置，直接返回
            return;
        }

        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setTradeComboId(jdCombId)
                .setAuditSn("入网成功设置京东钱包套餐");

        Map<String, String> applyFeeRateMap = new HashMap<>();

        for (Map config : maps) {
            if (config == null) {
                // 防止空配置
                continue;
            }

            String payway = String.valueOf(PaywayEnum.JD_WALLET.getValue());
            if (payway == null) {
                // 如果没有支付方式标识，跳过此配置
                continue;
            }
            // 判断费率类型
            if (WosaiCollectionUtils.isNotEmpty((Collection<?>) config.get(LADDER_FEE_RATES))) {
                applyFeeRateMap.put(payway, processLadderFeeRates(config));
            } else if (WosaiCollectionUtils.isNotEmpty((Collection<?>) config.get(CHANNEL_LADDER))) {
                applyFeeRateMap.put(payway, processChannelLadder(config));
            } else if (WosaiCollectionUtils.isNotEmpty((Collection<?>) config.get(CHANNEL))) {
                applyFeeRateMap.put(payway, processChannel(config));
            } else {
                applyFeeRateMap.put(payway, BeanUtil.getPropString(config, MerchantConfig.WAP_FEE_RATE));
            }
        }

        // 设置费率并提交
        applyFeeRateRequest.setApplyPartialPayway(true);
        applyFeeRateRequest.setApplyFeeRateMap(applyFeeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
    }

    /**
     * 处理 LADDER_FEE_RATES 的公共方法
     * @param config
     * @return
     */
    private String processLadderFeeRates(Map config) {
        List<Map> waitLadder = new ArrayList<>();
        List<Map> ladderReq = (List<Map>) config.get(LADDER_FEE_RATES);

        if (ladderReq == null) {
            // 防止空列表
            return "";
        }

        for (Map ladder : ladderReq) {
            if (ladder == null) {
                // 防止空列表
                continue;
            }

            Integer min = MapUtils.getInteger(ladder, LADDER_FEE_RATES_MIN, 0);
            Integer max = MapUtils.getInteger(ladder, LADDER_FEE_RATES_MAX);
            String rate = MapUtils.getString(ladder, "rate", "0");

            waitLadder.add(CollectionUtil.hashMap("min", min, "max", max, "fee_rate", rate));
        }

        return JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", "ladder", "value", waitLadder));
    }

    /**
     * 处理 CHANNEL_LADDER 的公共方法
     * @param config
     * @return
     */
    private String processChannelLadder(Map config) {
        List<Map> ladderReq = (List<Map>) config.get(CHANNEL_LADDER);
        if (ladderReq == null) {
            // 防止空列表
            return "";
        }
        List<Map> value = new ArrayList<>();

        for (Map ladder : ladderReq) {
            if (ladder == null) {
                continue;
            }

            String type = MapUtils.getString(ladder, "type", "debit");
            List<Map> waitLadderInner = new ArrayList<>();
            List<Map> ladderFeeRates = (List<Map>) ladder.get(LADDER_FEE_RATES);

            if (ladderFeeRates != null) {
                for (Map ladderFeeRate : ladderFeeRates) {
                    if (ladderFeeRate == null) {
                        continue;
                    }

                    Integer min = MapUtils.getInteger(ladderFeeRate, LADDER_FEE_RATES_MIN, 0);
                    Integer max = MapUtils.getInteger(ladderFeeRate, LADDER_FEE_RATES_MAX);
                    String rate = MapUtils.getString(ladderFeeRate, "rate", "0");
                    waitLadderInner.add(CollectionUtil.hashMap("min", min, "max", max, "fee_rate", rate));
                }
            }

            value.add(CollectionUtil.hashMap("type", type, LADDER_FEE_RATES, waitLadderInner));
        }

        return JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", CHANNEL_LADDER, "value", value));
    }

    /**
     * 处理 CHANNEL 的公共方法
     * @param config
     * @return
     */
    private String processChannel(Map config) {
        List<Map> ladderReq = (List<Map>) config.get(CHANNEL);
        if (ladderReq == null) {
            // 防止空列表
            return "";
        }
        List<Map> value = new ArrayList<>();

        for (Map ladder : ladderReq) {
            if (ladder == null) {
                continue;
            }

            String type = MapUtils.getString(ladder, "type", "debit");
            String rate = MapUtils.getString(ladder, "rate", "0");
            value.add(CollectionUtil.hashMap("type", type, "fee_rate", rate));
        }

        return JSON.toJSONString(CollectionUtil.hashMap("fee_rate_type", CHANNEL, "value", value));
    }


    /**
     * 白条开通成功通知支付
     */
    public void openSuccess(String merchantSn,String acquirer) {
        String provider = contractRelatedMappingConfigService.getProviderByAcquirer(acquirer);
        if (StringUtils.isBlank(provider)) {
            return;
        }
        final MerchantInfo merchantInfo = mcMerchantService.getMerchantBySn(merchantSn, null);
        Boolean forbid = preCheckIndustry(merchantInfo.getIndustry());
        if(forbid) {
            openFail(merchantSn,"商户所属行业不支持");
            return;
        }

        Boolean notSupport = preCheckAcquirerAbility(acquirer);
        if(notSupport) {
            openFail(merchantSn,"当前收单机构不支持");
            return;
        }
        // 没有白条参数
        final Optional<MerchantProviderParamsDO> jdParamsDO = merchantProviderParamsDAO.getBySnAndProviderAndPayWay(merchantSn, Integer.valueOf(provider), PaywayEnum.JD_WALLET.getValue());
        if(!jdParamsDO.isPresent()) {
            return;
        }
        final Map map = getMap("成功", merchantInfo.getId(), 1);
        try {
            fqConfigService.updateFqConfig(map);
            doComb(merchantSn);
        } catch (Exception e) {
            log.error("openSuccess exception merchantSn:{}",merchantSn,e);
        }

    }


    /**
     * 行业校验
     * @param industry
     * @return
     */
    public Boolean preCheckIndustry(String industry) {
        //银联mcc
        String unionMcc = industryMappingCommonBiz.getAliIndirectMcc(industry);
        return FORBID_LIST.contains(Integer.valueOf(unionMcc));
    }


    /**
     * 收单机构能力校验
     * @param acquirer
     * @return
     */
    public Boolean preCheckAcquirerAbility(String acquirer) {
        return !SUPPORT_ACQUIRE_LIST.contains(acquirer);
    }


    /**
     * "merchant_id":"440fdc7c-b968-444b-b6b1-2906398f9104",
     *             "fq_type_code":"jdbt_fq",
     *             "source":"SYSTEM",
     *             "status":0,
     *             "remark":"我是备注原因 写你那边的失败原因"
     */
    /**
     * 白条开通失败通知支付
     */
    public void openFail(String merchantSn,String failMessage) {
        final MerchantInfo merchantInfo = mcMerchantService.getMerchantBySn(merchantSn, null);
        final String merchantInfoId = merchantInfo.getId();
        Integer status = 0;
        final Map map = getMap(failMessage, merchantInfoId, status);
        try {
            fqConfigService.updateFqConfig(map);
        } catch (Exception e) {
            log.error("openFail exception merchantSn:{}",merchantSn,e);
        }
    }


    @NotNull
    private Map getMap(String message, String merchantInfoId, Integer status) {
        final Map map = CollectionUtil.hashMap(
                "merchant_id", merchantInfoId,
                "fq_type_code", "jdbt_fq",
                "source", "SYSTEM",
                "status", status,
                "remark", message
        );
        return map;
    }
}
