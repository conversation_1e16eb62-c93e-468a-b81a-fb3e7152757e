package com.wosai.upay.job.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.job.mapper.McRuleGroupMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.McRuleGroup;
import com.wosai.upay.job.model.DO.McRuleGroupExample;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 用于加载报备通道、规则、规则组等相关配置，并提供查询、刷新等功能
 *
 * <AUTHOR>
 * @date 2019-07-04
 */
@Component
@Slf4j
public class RuleContext {

    @Autowired
    @Qualifier("jsonRedisTemplate")
    private RedisTemplate jsonRedisTemplate;
    @Autowired
    private RedisLock redisLock;

    private static final String BASE_KEY = "RuleContext:";
    private static final String CHANNEL_CACHE_KEY = BASE_KEY + "channel";
    private static final String RULE_CACHE_KEY = BASE_KEY + "rule";
    private static final String GROUP_CACHE_KEY = BASE_KEY + "group";

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Autowired
    private McContractRuleDAO mcContractRuleDAO;

    @Autowired
    private McRuleGroupMapper mcRuleGroupMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;
    @PostConstruct
    public void init() {
        refresh();
    }

    /**
     * 定时刷新
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void scheduleRefresh() {
        if (!redisLock.lock("RuleContext:scheduleRefresh", "RuleContext:scheduleRefresh", ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        refresh();
    }

    public synchronized void refresh() {
        McJobThreadPoolFactory.getInstance().submit(() -> {
            log.info("refresh start");
            try {
                refreshChannel();
                refreshRule();
                refreshRuleGroup();
                chatBotUtil.sendMessageToContractWarnChatBot("刷新 RuleContext 成功");
            } catch (Exception e) {
                log.error("RuleContext refresh error", e);
                chatBotUtil.sendMessageToContractWarnChatBot("刷新 RuleContext 失败" + ExceptionUtil.getThrowableMsg(e));
                return;
            }
            log.info("refresh success");
        });

    }

    private void refreshChannel() {
        List<McChannelDO> mcChannelDOS = mcChannelDAO.listAll();
        BoundHashOperations boundHashOperations = jsonRedisTemplate.boundHashOps(CHANNEL_CACHE_KEY);

        Set<String> allKeys = boundHashOperations.keys();
        mcChannelDOS.forEach(channelDO -> {
            ContractChannel contractChannel = new ContractChannel(channelDO);
            boundHashOperations.put(contractChannel.getChannel(), contractChannel);
            allKeys.remove(contractChannel.getChannel());
        });
        if (WosaiCollectionUtils.isNotEmpty(allKeys)) {
            boundHashOperations.delete(allKeys.toArray());
        }
        boundHashOperations.expire(2, TimeUnit.DAYS);
    }

    private void refreshRule() {
        List<McContractRuleDO> mcContractRules = mcContractRuleDAO.listAllRule();
        BoundHashOperations boundHashOperations = jsonRedisTemplate.boundHashOps(RULE_CACHE_KEY);
        Set<String> allKeys = boundHashOperations.keys();
        mcContractRules.forEach(mcContractRule -> {
            ContractRule contractRule = ContractRule.fromMcContractRule(mcContractRule, jsonRedisTemplate.boundHashOps(CHANNEL_CACHE_KEY));
            boundHashOperations.put(contractRule.getRule(), contractRule);
            allKeys.remove(contractRule.getRule());
        });
        if (WosaiCollectionUtils.isNotEmpty(allKeys)) {
            boundHashOperations.delete(allKeys.toArray());
        }
        boundHashOperations.expire(2, TimeUnit.DAYS);
    }

    private void refreshRuleGroup() {
        McRuleGroupExample example = new McRuleGroupExample();
        List<McRuleGroup> allGroup = mcRuleGroupMapper.selectByExampleWithBLOBs(example);
        BoundHashOperations boundHashOperations = jsonRedisTemplate.boundHashOps(GROUP_CACHE_KEY);
        Set<String> allKeys = boundHashOperations.keys();
        allGroup.forEach(mcRuleGroup -> {
            RuleGroup ruleGroup = RuleGroup.fromMcRuleGroup(mcRuleGroup, jsonRedisTemplate.boundHashOps(RULE_CACHE_KEY));
            boundHashOperations.put(ruleGroup.getGroup_id(), ruleGroup);
            allKeys.remove(ruleGroup.getGroup_id());
        });
        if (WosaiCollectionUtils.isNotEmpty(allKeys)) {
            boundHashOperations.delete(allKeys.toArray());
        }
        boundHashOperations.expire(2, TimeUnit.DAYS);
    }

    /**
     * @param channel contract_rule
     * @return
     */
    public ContractChannel getContractChannel(String channel) {
        return (ContractChannel) jsonRedisTemplate.boundHashOps(CHANNEL_CACHE_KEY).get(channel);
    }

    public ContractChannel getContractChannel(int payway, String provider, String channelNo) {
        Optional<McChannelDO> mcChannel = mcChannelDAO.getMcChannel(payway, provider, channelNo);
        if (!mcChannel.isPresent()) {
            throw new CommonPubBizException("渠道 " + channelNo + "不存在");
        }
        return new ContractChannel(mcChannel.get());
    }


    public ContractRule getContractRule(String rule) {
        // 获取原始的 contractRule
        ContractRule contractRule = (ContractRule) jsonRedisTemplate.opsForHash().get(RULE_CACHE_KEY, rule);
        if (contractRule == null) {
            throw new CommonPubBizException(rule + " 规则不存在");
        }
        return contractRule;
    }



    public RuleGroup getRuleGroup(String ruleGroupId) {
        RuleGroup ruleGroup = (RuleGroup) jsonRedisTemplate.opsForHash().get(GROUP_CACHE_KEY, ruleGroupId);
        if (ruleGroup == null) {
            throw new CommonPubBizException(ruleGroupId + " 规则组不存在");
        }
        return ruleGroup;
    }

    /**
     * 获取默认报备规则
     * 仅用于灰度期间存量数据没有报备规则的情况，全量上线后应去除
     *
     * @return
     */
    public String getDefaultContractRule(String provider, Integer payway, String channelNo) {
        log.info("getDefaultContractRule {} {} {}", payway, provider, channelNo);
        ContractRule contractRule = getDefaultRule(provider, payway, channelNo);
        if (contractRule == null) {
            return null;
        }
        return contractRule.getRule();
    }

    public ContractRule getDefaultRule(String provider, Integer payway, String channelNo) {
        for (ContractRule contractRule : (List<ContractRule>) jsonRedisTemplate.boundHashOps(RULE_CACHE_KEY).values()) {
            if (contractRule.getProvider().equals(provider) && contractRule.getPayway().equals(payway) && contractRule.getChannelNo().equals(channelNo)) {
                return contractRule;
            }
        }
        return null;
    }
}
