package com.wosai.upay.job.refactor.event;

import com.wosai.upay.job.biz.OnlinePaymentBiz;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 跨城收款开通中的申请单在切换收单机构后要设置为失败
 */
@Component
public class AcquirerChangeEventListener implements ApplicationListener<AcquirerChangeEvent> {

    @Autowired
    private OnlinePaymentBiz onlinePaymentBiz;

    @Override
    public void onApplicationEvent(AcquirerChangeEvent event) {
        onlinePaymentBiz.setFailWhenChangeAcquirer(event.getMerchantSn());
    }
}
