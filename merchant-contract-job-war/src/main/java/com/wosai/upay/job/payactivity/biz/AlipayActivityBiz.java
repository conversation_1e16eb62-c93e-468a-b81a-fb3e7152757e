package com.wosai.upay.job.payactivity.biz;

import com.alipay.api.domain.ActivityMerchantOrder;
import com.alipay.api.request.AlipayCommerceEducateCampusInstitutionsAddRequest;
import com.alipay.api.request.AntMerchantExpandIndirectActivityQueryRequest;
import com.alipay.api.response.AlipayCommerceEducateCampusInstitutionsAddResponse;
import com.alipay.api.response.AntMerchantExpandIndirectActivityQueryResponse;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.merchant.contract.enume.AlipayLearningStageEnum;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.alipay.AlipayEducateCampusAddRequest;
import com.wosai.upay.merchant.contract.model.alipay.AlipayEducateCampusQueryRequest;
import com.wosai.upay.merchant.contract.model.alipay.sdk.MyAlipayCommerceEducateCampusInstitutionsQueryRequest;
import com.wosai.upay.merchant.contract.model.alipay.sdk.MyAlipayCommerceEducateCampusInstitutionsQueryResponse;
import com.wosai.upay.merchant.contract.model.bluesea.AlipayIndirectActivityQueryRequest;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.Constants.BlueSeaConstant.FAIL;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/12/3 3:23 下午
 */
@Component
@Slf4j
public class AlipayActivityBiz {
    @Autowired
    private NewBlueSeaService newBlueSeaService;
    @Autowired
    private DistrictsServiceV2 districtsServiceV2;
    @Autowired
    private BlueSeaBiz blueSeaBiz;


    public District getCode(String province, String city, String district) {
        return districtsServiceV2.getCodeByName(province + " " + city + " " + district);
    }


    /**
     * 支付宝调用接口查询学校ID 有返回 没有创建返回
     *
     * @param request
     * @return
     */
    public String getInstId(AlipayEducateCampusAddRequest request) {
        String instId = "";

        AlipayEducateCampusQueryRequest queryRequest = new AlipayEducateCampusQueryRequest();
        queryRequest.setInst_name(request.getInst_name());
        queryRequest.setCity_code(request.getCity_code());
        AliCommResponse<MyAlipayCommerceEducateCampusInstitutionsQueryRequest, MyAlipayCommerceEducateCampusInstitutionsQueryResponse>
                queryResponse = newBlueSeaService.alipayEducateCampusQuery(queryRequest);
        if (queryResponse.isSuccess()) {
            MyAlipayCommerceEducateCampusInstitutionsQueryResponse responseResp = queryResponse.getResp();
            if (responseResp != null && WosaiCollectionUtils.isNotEmpty(responseResp.getSchoolInfo())
                    && StringUtils.isNotBlank(responseResp.getSchoolInfo().get(0).getInstId())) {
                instId = responseResp.getSchoolInfo().get(0).getInstId();
            } else {
                throw new ContractBizException("根据学校名称和所在省市查询不到，请确认信息是否准确");
            }
        } else if (queryResponse.isBusinessFail()) {
            log.error("支付宝学校查询业务异常:{}", queryResponse.getMessage());
            throw new ContractBizException(queryResponse.getMessage());
        } else {
            log.error("支付宝学校查询系统异常:{}", queryResponse.getMessage());
            throw new RuntimeException("学校查询系统异常");
        }
        return instId;
    }

    /**
     * 组装学校查询接口
     *
     * @param task
     * @return
     */
    public AlipayEducateCampusAddRequest assembleRequest(BlueSeaTask task) {
        Map formBody = task.getFormBody();
        Map info = WosaiMapUtils.getMap(formBody, "pic_info");
        List schoolInfos = (List) info.get("schoolAddress");
        Map school = (Map) schoolInfos.get(0);

        AlipayEducateCampusAddRequest request = new AlipayEducateCampusAddRequest();

        request.setInst_name(WosaiMapUtils.getString(info, "schoolName"));

        String province = WosaiMapUtils.getString(school, "province");
        String city = WosaiMapUtils.getString(school, "city");
        String district = WosaiMapUtils.getString(school, "district");
        District code = getCode(province, city, district);
        if(code==null){
            throw new ContractBizException("学校省市区有误，请重新填写");
        }
        request.setProvince_code(code.getProvince_code());
        request.setCity_code(code.getCity_code());

        //社会信用编码
        request.setInst_std_code(WosaiMapUtils.getString(info, "instStdCode"));
        //学校属性 公立/民办   审批为public_school/private_school 转换成
        String school_type = WosaiMapUtils.getString(info, "school_type");
        request.setSchool_property(school_type.equals("public_school") ? "1" : "2");
        //learning_stage 多选
        List<String> stages = (List) info.get("learningStage");
        List<String> stagesCode=new ArrayList<>();
        for (String stage : stages) {
            String codeByName = AlipayLearningStageEnum.getCodeByName(stage);
            if (StringUtils.isNotBlank(codeByName)) {
                stagesCode.add(codeByName);
            }
        }
        if (CollectionUtils.isEmpty(stagesCode)) {
            throw new ContractBizException("请选择办学阶段");
        }

        StringBuilder learningStage= new StringBuilder();
        for (int i=0;i<stagesCode.size();i++){
            if (i == stagesCode.size() - 1) {
                learningStage.append(stagesCode.get(i));
            } else {
                learningStage.append(stagesCode.get(i)).append(",");
            }
        }
        request.setLearning_stage(learningStage.toString());
        return request;
    }


    /**
     * 查询子商户号是否有参加过 类型为type的活动
     * true 参加过
     * false 没有参加过
     *
     * @param aliMerchantId
     * @param type
     * @return
     */
    public boolean checkActivityByType(String aliMerchantId, String type) {
        //查询活动
        List<ActivityMerchantOrder> query = query(aliMerchantId);
        if (CollectionUtils.isNotEmpty(query)) {
            for (ActivityMerchantOrder order : query) {
                if (order.getActivityType().equals(type)) {
                    return "99".equals(order.getAuditResult());
                }
            }
        }
        return false;
    }


    /**
     * 查询支付宝子商户号参加的活动
     *
     * @param aliMerchantId
     * @return
     */
    public List<ActivityMerchantOrder> query(String aliMerchantId) {
        List<ActivityMerchantOrder> res = null;
        try {
            AlipayIndirectActivityQueryRequest request = new AlipayIndirectActivityQueryRequest();
            request.setSub_merchant_id(aliMerchantId);
            AliCommResponse<AntMerchantExpandIndirectActivityQueryRequest, AntMerchantExpandIndirectActivityQueryResponse>
                    response = newBlueSeaService.alipayIndirectActivityQuery(request);
            if (response.isSuccess()) {
                List<ActivityMerchantOrder> multiResult = response.getResp().getMultiResult();
                if (CollectionUtils.isNotEmpty(multiResult)) {
                    res = multiResult;
                }
            }
        } catch (Exception e) {
            log.error("查询支付宝{}活动状态异常", aliMerchantId, e);
        }
        return res;
    }


}
