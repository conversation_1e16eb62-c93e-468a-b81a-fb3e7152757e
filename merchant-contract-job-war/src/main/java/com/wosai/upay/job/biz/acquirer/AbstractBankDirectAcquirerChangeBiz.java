package com.wosai.upay.job.biz.acquirer;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.bank.entity.FeeRateSnapshot;
import com.wosai.trade.service.bank.entity.request.GetCurrentSnapshotRequest;
import com.wosai.trade.service.bank.entity.request.RestoreFeeRateSnapshotRequest;
import com.wosai.trade.service.bank.entity.response.CurrentFeeRateSnapshotResponse;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.DO.*;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.CommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/10/20 11:24 上午
 */
@Slf4j
public abstract class AbstractBankDirectAcquirerChangeBiz extends AbstractAcquirerChangeBiz {

    @Autowired
    private McAcquirerChangeMapper acquirerChangeMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;

    @Value("${bank_biz.devCode}")
    private String bankBizDevCode;

    @Value("${bank_biz.first_change_acquirer_crm_template_code}")
    private String firstChangeAcquirerCrmTemplateCode;

    @Value("${bank_biz.change_acquirer_crm_template_code}")
    private String changeAcquirerCrmTemplateCode;

    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private McChannelDAO mcChannelDAO;

    @Override
    protected void updateClearanceProvider(McAcquirerChange change) {

    }

    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderIn(Arrays.asList(getProviderCode(change.getTarget_acquirer())))
                .andPaywayNotEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        return paramsMapper.selectByExampleWithBLOBs(example);
    }

    /**
     * @Description: 银行收单机构切换间连收单机构 后置处理
     * @ime 11:40
     */
    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        //获取最新记录
        change = acquirerChangeMapper.selectByPrimaryKey(change.getId());
        String merchantSn = change.getMerchant_sn();
        log.info("{} 切换收单机构后置业务处理", merchantSn);
        //关闭银行通道花呗
        merchantProviderParamsService.closeHuaBei(merchantSn);
        //银行通道支持哪些支付方式
        final String sourceAcquirer = change.getSource_acquirer();
        List<Integer> supportPayWays = applicationApolloConfig.getBankSupportPayWay().get(String.valueOf(getProviderCode(sourceAcquirer)));
        if(CollectionUtils.isEmpty(supportPayWays)) {
            supportPayWays =  mcChannelDAO.listAllEffectiveMcChannel().stream()
                    .filter(mcChannel -> Objects.equals(mcChannel.getAcquirer(), sourceAcquirer)
                            && !Objects.equals(mcChannel.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                    .map(McChannelDO::getPayway)
                    .collect(Collectors.toList());
        }
        //保存在银行通道的交易参数快照
        saveBankSnapshot(change,supportPayWays);
        //开启原有支付方式
        //如有不同类型 只需重写 间连收单支持的交易类型 默认为2，3，17，18
        List<Integer> payWays = Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue(), PaywayEnum.BESTPAY.getValue());
        payWays.removeAll(supportPayWays);
        if (CollectionUtils.isEmpty(payWays)) {
            return;
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, com.wosai.upay.common.dao.DaoConstants.ID);
        String merchantName = BeanUtil.getPropString(merchant, Merchant.NAME);
        //调用交易组接口开启已经关闭的交易方式
        payWays.parallelStream().forEach(payWay -> {
            tradeConfigService.updateMerchantConfigStatusAndFeeRate(merchantId, CollectionUtil.hashMap(
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.C2B_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.WAP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.MINI_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.H5_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.APP_STATUS, MerchantConfig.STATUS_OPENED,
                    MerchantConfig.PAYWAY, payWay
            ));
        });
        //删除缓存
        supportService.removeCachedParams(merchantSn);

        // 给BD发送通知
        aopBiz.sendNoticeToCrm(change.getMerchant_id(), bankBizDevCode, changeAcquirerCrmTemplateCode, CollectionUtil.hashMap(
                "merchant_name", merchantName,
                "merchant_sn", change.getMerchant_sn(),
                "acquirer", getAcquirerName(change.getTarget_acquirer())
        ));
    }

    private String getAcquirerName(String acquirer) {
        McAcquirerDO byAcquirer = mcAcquirerDAO.getByAcquirer(acquirer);
        return Objects.isNull(byAcquirer) ? acquirer : byAcquirer.getName();
    }


    /**
     * 有银行切到间连收单机构的时候保存当前商户在银行的套餐
     * @param change 收单机构切换任务
     * @param supportPayWays 当前银行支持哪些支付方式
     */
    public void saveBankSnapshot(McAcquirerChange change,List<Integer> supportPayWays) {
        String merchantSn = change.getMerchant_sn();
        GetCurrentSnapshotRequest snapshotRequest = new GetCurrentSnapshotRequest();
        snapshotRequest.setMerchantSn(merchantSn);
        snapshotRequest.setPayWayList(supportPayWays);
        CurrentFeeRateSnapshotResponse snapshotResponse = null;
        try {
            snapshotResponse = bankFeeRateService.getFeeRateSnapshot(snapshotRequest);
            List<FeeRateSnapshot> snapshotList = snapshotResponse.getFeeRateSnapshotList();
            log.info("saveBankSnapshot merchantSn:{},getFeeRateSnapshot返回:{}",merchantSn,JSONObject.toJSONString(snapshotList));
            // 查找商户间连费率信息快照保存下来
            Map extra = CommonUtil.string2Map(change.getExtra());
            extra.put(BANK_COMBO_SNAPSHOT, snapshotList);
            McAcquirerChange updateValue = new McAcquirerChange();
            updateValue.setId(change.getId());
            updateValue.setExtra(CommonUtil.map2String(extra));
            log.info("merchantSn:{},updateValue:{}",merchantSn,JSONObject.toJSONString(updateValue));
            acquirerChangeMapper.updateByPrimaryKeySelective(updateValue);
        } catch (Exception exception) {
            log.error("saveBankSnapshot merchantSn:{},error:{}",merchantSn,exception);
        }

    }


    /**
     * 取消套餐
     * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
     *
     * @param change
     */
    public void cancelCombo(McAcquirerChange change) {
        String merchantSn = change.getMerchant_sn();
        List<ListMchFeeRateResult> feeRates = feeRateService.listMchEffectFeeRates(merchantSn);
        Optional<ListMchFeeRateResult> alipayFeeRate = feeRates.stream()
                .filter(feeRate -> feeRate.getPayWay().equals(PaywayEnum.ALIPAY.getValue())
                        && (feeRate.getTradeAppName().contains("银行")
                        || feeRate.getTradeComboShortName().contains("银行"))
                        //获取这几项至少有一个费率不为空的
                        && !ObjectUtil.isAllEmpty(feeRate.getBscFeeRate(),feeRate.getLadderFeeRates(),feeRate.getChannelFeeRates(),feeRate.getChannelLadderFeeRates())
                ).findFirst();
        // 没有套餐或者使用基础标准套餐  不用取消套餐
        if (!alipayFeeRate.isPresent() || alipayFeeRate.get().getTradeComboShortName().contains("基础标准费率")) {
            return;
        }

        // 查找商户当前使用套餐以及费率，保存下来
        final Long usedTradeComboId = alipayFeeRate.get().getTradeComboId();
        if (Objects.isNull(usedTradeComboId)) {
            return;
        }
        List<Map> merchantConfig = feeRates.stream()
                .filter(feeRate -> Objects.equals(feeRate.getTradeComboId(), usedTradeComboId))
                .map(feeRate -> CollectionUtil.hashMap(
                        "payway", feeRate.getPayWay(),
                        "rate", feeRate.getBscFeeRate(),
                        "ladder_fee_rates", feeRate.getLadderFeeRates()
                )).collect(Collectors.toList());

        Map comboSnapshot = CollectionUtil.hashMap(
                "trade_combo_id", usedTradeComboId,
                "merchant_config", merchantConfig
        );

        Map extra = CommonUtil.string2Map(change.getExtra());
        extra.put("comboSnapshot", comboSnapshot);

        McAcquirerChange updateValue = new McAcquirerChange();
        updateValue.setId(change.getId());
        updateValue.setExtra(CommonUtil.map2String(extra));
        acquirerChangeMapper.updateByPrimaryKeySelective(updateValue);

        try {
            feeRateService.cancelFeeRate(
                    new CancelFeeRateRequest()
                            .setMerchantSn(merchantSn)
                            .setTradeComboId(usedTradeComboId)
                            .setAuditSn("切换收单机构取消银行活动套餐")
            );
        } catch (Exception e) {
            log.error("取消套餐失败,商户号: {}", merchantSn, e);
        }
    }

    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        //获取最新记录
        change = acquirerChangeMapper.selectByPrimaryKey(change.getId());
        //恢复商户银行套餐快照
        restoreBankComb(change);
        // 给BD发送通知
        Map merchant = merchantService.getMerchantBySn(change.getMerchant_sn());
        String merchantName = BeanUtil.getPropString(merchant, Merchant.NAME);
        McAcquirerChange firstSuccessChange = acquirerChangeMapper.selectFirstSuccessChange(change.getMerchant_sn(), change.getTarget_acquirer());
        String templateCode = Objects.nonNull(firstSuccessChange) && Objects.equals(change.getId(), firstSuccessChange.getId()) ? firstChangeAcquirerCrmTemplateCode : changeAcquirerCrmTemplateCode;

        aopBiz.sendNoticeToCrm(change.getMerchant_id(), bankBizDevCode, templateCode, CollectionUtil.hashMap(
                "merchant_name", merchantName,
                "merchant_sn", change.getMerchant_sn(),
                "acquirer", getAcquirerName(change.getTarget_acquirer())
        ));
    }


    /**
     * 恢复银行套餐,这里的逻辑是如果数据表里如果有"bankComboSnapshot"字段就优先使用bankComboSnapshot中的信息,没有的话走applyCombo方法
     * @param change
     */
    public void restoreBankComb(McAcquirerChange change) {
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = changeDao.getLatestSuccessApply(change.getMerchant_sn(), change.getTarget_acquirer());
        List<FeeRateSnapshot> snapshotList = null;
        if(Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            snapshotList = (List<FeeRateSnapshot>) WosaiMapUtils.getObject(extra, BANK_COMBO_SNAPSHOT);
        }
        String merchantSn = change.getMerchant_sn();
        //兼容上线前没有切换记录的数据,没有数据就走之前的逻辑
        if(CollectionUtils.isEmpty(snapshotList)) {
            log.info("restoreBankComb merchantSn:{},snapshotList为空",merchantSn);
            // 指定收单机构切换不处理套餐
            String notHandleCombo = applicationApolloConfig.getNotHandleCombo();
            if (!notHandleCombo.contains(change.getTarget_acquirer())) {
                applyCombo(change);
            }
            return;
        }
        //调用交易组恢复在间连的费率套餐
        List<FeeRateSnapshot> finalSnapshotList = snapshotList;
        CompletableFuture.runAsync(() -> {
            try {
                //支付组会校验当前收单机构,由于事务原因所以把这个延迟调用一下
                Thread.sleep(1000);
                RestoreFeeRateSnapshotRequest restoreFeeRateSnapshotRequest = new RestoreFeeRateSnapshotRequest();
                restoreFeeRateSnapshotRequest.setMerchantSn(merchantSn);
                restoreFeeRateSnapshotRequest.setFeeRateSnapshotList(finalSnapshotList);
                bankFeeRateService.restoreFeeRateSnapshot(restoreFeeRateSnapshotRequest);
            } catch (Exception exception) {
                log.error("restoreFeeRateSnapshot merchantSn:{},error:{}",merchantSn,exception);
            }

        });
    }



    /**
     * 设置套餐
     * https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********
     *
     * @param change
     */
    protected void applyCombo(McAcquirerChange change) {
        Map combo = new HashMap();
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = changeDao.getLatestSuccessApply(change.getMerchant_sn(), change.getTarget_acquirer());
        if (Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            combo = WosaiMapUtils.getMap(extra, "comboSnapshot");
        }

        if (WosaiMapUtils.isEmpty(combo)) {
            BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(change.getMerchant_sn(), getDevCode(change.getTarget_acquirer()));
            if (Objects.nonNull(apply)) {
                combo = CommonUtil.string2Map(apply.getForm_body());
            }
        }

        long tradeComboId = BeanUtil.getPropLong(combo, "trade_combo_id");
        if (Objects.equals(tradeComboId, 0L)) {
            tradeComboId = getDefaultComboId(change.getTarget_acquirer());
        }
        //银行通道支持哪些支付方式
        List<Integer> supportPayWays = applicationApolloConfig.getBankSupportPayWay().get(String.valueOf(getProviderCode(change.getTarget_acquirer())));
        if(CollectionUtils.isEmpty(supportPayWays)) {
            //如果没有单独配置,只需要从数据库中读取
            supportPayWays =  mcChannelDAO.listAllEffectiveMcChannel().stream()
                    .filter(mcChannel -> Objects.equals(mcChannel.getAcquirer(), change.getTarget_acquirer())
                            && !Objects.equals(mcChannel.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                    .map(McChannelDO::getPayway)
                    .collect(Collectors.toList());
        }
        List<Integer> finalSupportPayWays = supportPayWays;
        List<Map<String, Object>> config = JSONObject.parseArray(BeanUtil.getPropString(combo, "merchant_config"), Map.class)
                .stream()
                .filter(payWayConfig -> finalSupportPayWays.contains(BeanUtil.getPropInt(payWayConfig,"payway")))
                .map(map -> (Map<String, Object>) map)
                .collect(Collectors.toList()); // 使用toCollection收集

        // 支持阶梯费率
        Map<String, String> applyFeeRateMap = buildApplyFeeRateMap(config);
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(change.getMerchant_sn())
                .setTradeComboId(tradeComboId)
                .setAuditSn("银行业务开通成功设置费率")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(applyFeeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
    }

    private Map<String, String> buildApplyFeeRateMap(List<Map<String, Object>> configs) {
        Map<String, String> applyFeeRateMap = new HashMap<>();
        for (Map config : configs) {
            if (WosaiCollectionUtils.isNotEmpty((Collection) config.get("ladder_fee_rates"))) {
                List<Map> waitLadder = new ArrayList<>();
                List<Map> ladderReq = (List<Map>) config.get("ladder_fee_rates");
                for (Map map : ladderReq) {
                    Integer min = MapUtils.getInteger(map, "min");
                    Integer max = MapUtils.getInteger(map, "max");
                    // 银行通道切换到间连再切回, configs里会没有rate信息,取bscFeeRate字段
                    String rate = MapUtils.getString(map, "rate", MapUtils.getString(map, "bscFeeRate"));
                    waitLadder.add(CollectionUtil.hashMap("min", min == null ? 0 : min, "max", max == null ? Integer.MAX_VALUE : max, "fee_rate", rate));
                }
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_type", "ladder", "value", waitLadder)));
            } else if (BeanUtil.getPropString(config, "rate").contains("以上")) {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), JSON.toJSONString(CollectionUtil.hashMap("fee_type", "ladder", "value", getLadderFeeRateFromText(BeanUtil.getPropString(config, "rate")))));
            }
            else {
                applyFeeRateMap.put(BeanUtil.getPropString(config, "payway"), BeanUtil.getPropString(config, "rate"));
            }
        }
        return applyFeeRateMap;
    }

    /**
     * 有些阶梯费率格式是 300以下0.25, 300以上0.38
     * @param rate
     * @return
     */
    private static List<Map> getLadderFeeRateFromText(String rate) {
        List<Map> waitLadder = new ArrayList<>();
        String[] rates = rate.split("\\,");
        for (String s : rates) {
            if (s.contains("以下")) {
                String[] mins = s.split("以下");
                waitLadder.add(CollectionUtil.hashMap("min", 0, "max", Integer.valueOf(mins[0].trim()), "fee_rate", mins[1].trim()));
            } else {
                String[] maxs = s.split("以上");
                waitLadder.add(CollectionUtil.hashMap("min", Integer.valueOf(maxs[0].trim()), "max", Integer.MAX_VALUE, "fee_rate", maxs[1].trim()));
            }
        }
        return waitLadder;
    }

    /**
     * 获取银行标示code
     *
     * @return
     */
    protected abstract String getDevCode(String acquirer);

    /**
     * 获取默认结算id
     *
     * @return
     */
    protected abstract long getDefaultComboId(String acquirer);


    @Override
    public  void changeSuccess(McAcquirerChange change) {
        changeDao.updateStatus(change, AcquirerChangeStatus.SUCCESS, "切换成功");
        Map combo = new HashMap();
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = changeDao.getLatestSuccessApply(change.getMerchant_sn(), change.getTarget_acquirer());
        if (Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            combo = WosaiMapUtils.getMap(extra, "comboSnapshot");
        }
        if (WosaiMapUtils.isEmpty(combo)) {
            BankDirectApply apply = bankDirectApplyMapper.getApplyBySnAndDevCode(change.getMerchant_sn(), getDevCode(change.getTarget_acquirer()));
            if (Objects.nonNull(apply)) {
                combo = CommonUtil.string2Map(apply.getForm_body());
            }
        }
        long tradeComboId = BeanUtil.getPropLong(combo, "trade_combo_id");
        if (Objects.equals(tradeComboId, 0L)) {
            tradeComboId = getDefaultComboId(change.getTarget_acquirer());
        }
        Map map = CollectionUtil.hashMap("费率信息", JSONArray.parseObject(BeanUtil.getPropString(combo, "merchant_config"),List.class), "套餐Id", tradeComboId);
        sendChangeSuccessLog(change, "切换收单机构成功: "+JSONObject.toJSONString(map));
        sendKafkaMsg(change);
        sendTradeAppKafkaMsg(change);
    }


}
