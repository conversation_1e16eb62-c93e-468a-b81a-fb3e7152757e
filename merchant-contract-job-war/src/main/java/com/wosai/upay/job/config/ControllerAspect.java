package com.wosai.upay.job.config;

import com.alibaba.fastjson.JSON;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


/**
 * 控制器切面类
 *
 * <AUTHOR>
 */
@Aspect
@Order(2)
@Component
public class ControllerAspect {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());


    /**
     * region log method
     */
    @Pointcut("@within(org.springframework.web.bind.annotation.RestController) && !within(com.wosai.upay.job.controller.MetricsController)")
    public void logMethod() {
        // the pointcut expression
    }

    /**
     * 环绕通知，记录请求信息和方法执行信息
     *
     * @param pjp 切点
     * @return 放行结果
     * @throws Throwable 异常
     */
    @Around("logMethod()")
    public Object logAroundAllControllers(ProceedingJoinPoint pjp) throws Throwable {
        long beginTime = System.currentTimeMillis();
        String method = pjp.getSignature().getName();
        Object retVal = null;
        Object[] args = pjp.getArgs();
        String argsStr = JSON.toJSONString(args);
        try {
            retVal = pjp.proceed();
            logger.info("method: {}, duration: {} ms, params: {}, return: {}",
                    method, System.currentTimeMillis() - beginTime, argsStr, JSON.toJSONString(retVal));
        } catch (Throwable throwable) {
            logger.error("exception occurred in method: {}, error message: {}", method, throwable.getMessage());
            throw throwable;
        }
        return retVal;
    }
}
