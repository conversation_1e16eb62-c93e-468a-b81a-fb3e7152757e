package com.wosai.upay.job.refactor.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 收单机构切换成功的事件
 * <AUTHOR>
 * @date 2024/7/10
 */
@Getter
public class AcquirerChangeEvent extends ApplicationEvent {

    /**
     * 商户号
     */
    private String merchantSn;

    public AcquirerChangeEvent(Object source, String merchantSn) {
        super(source);
        this.merchantSn = merchantSn;
    }

}
