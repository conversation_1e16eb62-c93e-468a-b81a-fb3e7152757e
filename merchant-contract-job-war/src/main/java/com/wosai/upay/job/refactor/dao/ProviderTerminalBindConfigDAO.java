package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ProviderTerminalBindConfigDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalBindConfigDO;

import java.util.List;


/**
 * 收单机构终端绑定详情表表数据库访问层 {@link ProviderTerminalBindConfigDO}
 * 对ProviderTerminalBindConfigMapper层做出简单封装 {@link ProviderTerminalBindConfigDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ProviderTerminalBindConfigDAO extends AbstractBaseDAO<ProviderTerminalBindConfigDO, ProviderTerminalBindConfigDynamicMapper> {

    public ProviderTerminalBindConfigDAO(SqlSessionFactory sqlSessionFactory, ProviderTerminalBindConfigDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和收单机构对应的provider获取终端列表
     *
     * @param merchantSn 商户号
     * @param provider   收单机构对应的provider
     * @return 终端列表
     */
    public List<ProviderTerminalBindConfigDO> listByMerchantSnAndProvider(String merchantSn, String provider) {
        LambdaQueryWrapper<ProviderTerminalBindConfigDO> query = new LambdaQueryWrapper<>();
        query.eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalBindConfigDO::getProvider, provider);
        return entityMapper.selectList(query);
    }

    /**
     * 根据商户号,收单机构对应的provider和payway获取终端列表
     *
     * @param merchantSn 商户号
     * @param provider   收单机构对应的provider
     * @param payWay   支付方式
     * @return 终端列表
     */
    public List<ProviderTerminalBindConfigDO> listByMerchantSnProviderPayWay(String merchantSn, Integer provider,Integer payWay) {
        LambdaQueryWrapper<ProviderTerminalBindConfigDO> query = new LambdaQueryWrapper<>();
        query.eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalBindConfigDO::getProvider, provider)
                .eq(ProviderTerminalBindConfigDO::getPayway,payWay);
        return entityMapper.selectList(query);
    }



    /**
     * 根据商户号,收单机构对应的provider和payway获取终端列表
     *
     * @param merchantSn 商户号
     * @param provider   收单机构对应的provider
     * @param payWay   支付方式
     * @param terminalSn   终端号
     * @return 终端记录
     */
    public ProviderTerminalBindConfigDO getByMerchantSnProviderPayWayTerminalSn(String merchantSn, Integer provider, Integer payWay, String terminalSn) {
        LambdaQueryWrapper<ProviderTerminalBindConfigDO> query = new LambdaQueryWrapper<>();
        query.eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalBindConfigDO::getProvider, provider)
                .eq(ProviderTerminalBindConfigDO::getTerminalSn, terminalSn)
                .eq(ProviderTerminalBindConfigDO::getPayway,payWay);
        return entityMapper.selectOne(query);
    }


    /**
     * 根据商户号和provider删除终端绑定参数
     *
     * @param merchantSn    商户号
     */
    public void deleteByMerchantSnAndProviders(String merchantSn, List<String> providers) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(providers)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<ProviderTerminalBindConfigDO>()
                .eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                .in(ProviderTerminalBindConfigDO::getProvider, providers));
    }

    public void deleteTerminalLevelProviderTerminalBindConfig(String merchantSn, String provider) {
        entityMapper.delete(
                new LambdaQueryWrapper<ProviderTerminalBindConfigDO>()
                        .eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                        .eq(ProviderTerminalBindConfigDO::getProvider, provider)
                        .isNotNull(ProviderTerminalBindConfigDO::getTerminalSn)
        );
    }

    public void deleteTerminalLevelProviderTerminalBindConfig(String merchantSn, Integer provider, String terminalSn,Integer payWay) {
        entityMapper.delete(
                new LambdaQueryWrapper<ProviderTerminalBindConfigDO>()
                        .eq(ProviderTerminalBindConfigDO::getMerchantSn, merchantSn)
                        .eq(ProviderTerminalBindConfigDO::getProvider, provider)
                        .eq(ProviderTerminalBindConfigDO::getPayway,payWay)
                        .eq(ProviderTerminalBindConfigDO::getTerminalSn,terminalSn)
        );
    }
}
