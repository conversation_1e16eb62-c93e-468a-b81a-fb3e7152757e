package com.wosai.upay.job.refactor.biz.acquirer.tonglian;

import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.biz.acquirer.TongLianAcquirerBiz;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 通联收单处理策略
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class TongLianAcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private TongLianMerchantInfoProcessor tongLianMerchantInfoProcessor;

    @Resource(type = TongLianAcquirerBiz.class)
    private TongLianAcquirerBiz tongLianAcquirerBiz;

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.TONG_LIAN;
    }

    /**
     * 判断商户所在收单机构的银行卡是否和收钱吧银行卡一致
     *
     * @param merchantSn 商户号
     * @return true-一致 false-不一致
     */
    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> tongLianMerchantInfoProcessor.getBankAccountNo(t).orElse(null),
                null
        );
    }

    /**
     * 获取商户所在收单机构的商户状态
     * todo 待重构 本次来不及
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        return tongLianAcquirerBiz.getAcquirerMchStatus(merchantSn) ? AcquirerMerchantStatusEnum.NORMAL : AcquirerMerchantStatusEnum.CLOSE;
    }
}
