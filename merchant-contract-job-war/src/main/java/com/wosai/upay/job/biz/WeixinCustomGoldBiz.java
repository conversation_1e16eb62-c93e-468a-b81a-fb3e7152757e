package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.mapper.MerchantProviderParamsExtMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import com.wosai.upay.job.service.RedisService;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.WeixinService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Description:微信点金计划商家小票管理
 * <AUTHOR>
 * Date 2020/7/15 11:57 上午
 **/
@Component
@Slf4j
public class WeixinCustomGoldBiz {

    public static final String EXT_FIELD_OPEN = "OPEN";
    public static final String EXT_FIELD_GOLD_OPEN_FAIL = "GOLD_OPEN_FAIL";
    public static final String EXT_FIELD_CUSTOM_PAGE_OPEN_FAIL = "CUSTOM_PAGE_OPEN_FAIL";
    public static final String EXT_FIELD_CLOSE= "CLOSE";
    private static final String CUSTOM_GOLD_CACHE_KEY_PREFIX = "GOLD_OPEN";

    @Autowired
    MerchantProviderParamsExtMapper providerParamsExtMapper;
    @Autowired
    BusinessRuleBiz businessRuleBiz;
    @Autowired
    WeixinService weixinService;
    @Autowired
    RuleContext ruleContext;
    @Autowired
    RedisService redisService;
    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    /**
     * 过滤需要开通的点金计划商户
     *
     * @param params
     * @return
     */
    public List<MerchantProviderParams> filtParams(List<MerchantProviderParams> params) {
        List<MerchantProviderParams> filt = new ArrayList<>();
        params.forEach(param -> {
            if (StringUtils.isEmpty(redisService.getKeyWithoutPrefix(getCacheKey(param.getId())))) {
                if (!extParamExist(param)) {
                    filt.add(param);
                }
            }
        });
        return filt;
    }


    /**
     * 是否存在params_ext记录
     */
    public boolean extParamExist(MerchantProviderParams param) {
        return Objects.nonNull(providerParamsExtMapper.getByParamId(param.getId(), MerchantProviderParamsExt.WEIXIN_GOLD_TYPE));
    }


    public void open(MerchantProviderParams params) {
        ContractResponse openGoldResponse = changeGoldPlanStatus(params);
        log.info("开通点金计划 {} {}", params.getId(), openGoldResponse);
        if (openGoldResponse.isSuccess()) {
            ContractResponse openCustomPage = changeCustomPageStatus(params);
            log.info("开通商家小票 {} {}", params.getId(), openCustomPage);
            if (openCustomPage.isSuccess()) {
                WeixinCustomGoldBiz biz = (WeixinCustomGoldBiz) AopContext.currentProxy();
                biz.saveExtParams(buildExtParams(params, EXT_FIELD_OPEN, null));
            } else if (openCustomPage.isBusinessFail()) {
                Map extra = CollectionUtil.hashMap("openCustomPage", openCustomPage);
                WeixinCustomGoldBiz biz = (WeixinCustomGoldBiz) AopContext.currentProxy();
                biz.saveExtParams(buildExtParams(params, EXT_FIELD_CUSTOM_PAGE_OPEN_FAIL, extra));
                return;
            } else {
                //do nothing
            }
        } else if (openGoldResponse.isBusinessFail()) {
            Map extra = CollectionUtil.hashMap("openGoldResponse", openGoldResponse);
            WeixinCustomGoldBiz biz = (WeixinCustomGoldBiz) AopContext.currentProxy();
            biz.saveExtParams(buildExtParams(params, EXT_FIELD_GOLD_OPEN_FAIL, extra));
        } else {
            //do nothing;
        }

    }

    public String getCacheKey(String paramId) {
        return CUSTOM_GOLD_CACHE_KEY_PREFIX + paramId;
    }

    /**
     * 开通点金计划
     */
    private ContractResponse changeGoldPlanStatus(MerchantProviderParams params) {
        ContractChannel mcChannel = ruleContext.getContractChannel(params.getPayway(), params.getProvider() + "", params.getChannel_no());
        //兼容银商特殊逻辑 两个微信子商户号
        String payMerchantId = params.getPay_merchant_id();
        if (payMerchantId.contains("|")) {
            String[] payIds = payMerchantId.split("\\|");
            for (String payId : payIds) {
                ContractResponse response = weixinService.openGolden(payId, mcChannel.getPayway_channel_no());
                if (response.isBusinessFail()) {
                    return response;
                }
            }
            ContractResponse result = new ContractResponse();
            result.setCode(200);
            return result;
        } else {
            //一个微信子商户号
            return weixinService.openGolden(payMerchantId, mcChannel.getPayway_channel_no());
        }
    }

    /**
     * 开通商家小票
     **/
    private ContractResponse changeCustomPageStatus(MerchantProviderParams params) {
        ContractChannel mcChannel = ruleContext.getContractChannel(params.getPayway(), params.getProvider() + "", params.getChannel_no());
        //兼容银商特殊逻辑 两个微信子商户号
        String payMerchantId = params.getPay_merchant_id();
        if (payMerchantId.contains("|")) {
            String[] payIds = payMerchantId.split("\\|");
            for (String payId : payIds) {
                ContractResponse response = weixinService.openTicket(payId, mcChannel.getPayway_channel_no());
                if (response.isBusinessFail()) {
                    return response;
                }
            }
            ContractResponse result = new ContractResponse();
            result.setCode(200);
            return result;
        } else {
            return weixinService.openTicket(payMerchantId, mcChannel.getPayway_channel_no());
        }
    }


    /**
     * @return
     * <AUTHOR>
     * @Description:保存微信点金计划记录
     * @time 10:18 上午
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveExtParams(MerchantProviderParamsExt providerParamsExt) {
        MerchantProviderParamsExt paramsExt = providerParamsExtMapper.getByParamId(providerParamsExt.getParam_id(), MerchantProviderParamsExt.WEIXIN_GOLD_TYPE);
        if (paramsExt == null) {
            providerParamsExtMapper.insertSelective(providerParamsExt);
        } else {
            providerParamsExt.setId(paramsExt.getId());
            providerParamsExtMapper.updateByPrimaryKeySelective(providerParamsExt);
        }
        int status = MerchantProviderParams.GOLD_STATUS_FAIL;
        if (EXT_FIELD_OPEN.equals(providerParamsExt.getExt_field_1())) {
            status = MerchantProviderParams.GOLD_STATUS_SUCCESS;
        }
        merchantProviderParamsMapper.updateGoldStatusById(status, providerParamsExt.getParam_id());
        redisService.setNx(getCacheKey(providerParamsExt.getParam_id()), getCacheKey(providerParamsExt.getParam_id()), 5L, TimeUnit.MINUTES);
    }


    private MerchantProviderParamsExt buildExtParams(MerchantProviderParams params, String field, Map extra) {
        MerchantProviderParamsExt providerParamsExt = new MerchantProviderParamsExt();
        providerParamsExt.setParam_id(params.getId()).setType(MerchantProviderParamsExt.WEIXIN_GOLD_TYPE)
                .setExt_field_2(params.getPay_merchant_id()).setExt_field_1(field);
        if (!CollectionUtils.isEmpty(extra)) {
            providerParamsExt.setExtra(JSON.toJSONString(extra));
        }
        return providerParamsExt;
    }
}
