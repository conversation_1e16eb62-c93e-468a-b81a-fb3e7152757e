package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 子任务扩展表实体对象
 *
 * <AUTHOR>
 */
@TableName("contract_sub_task_ext")
@Data
public class ContractSubTaskExtDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 子任务编号
     */
    @TableField(value = "sub_task_id")
    private Long subTaskId;
    /**
     * 支付源商户号
     */
    @TableField(value = "pay_merchant_id")
    private String payMerchantId;

    private Timestamp ctime;

    private Timestamp mtime;

}

