package com.wosai.upay.job.biz;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.ContractConfig;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.merchant.contract.model.WeixinAppidConfig;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-10-11
 */
@Component
@Slf4j
public class AgentAppidBiz {

    /**
     * agentName，appid配置
     */
    public static final String AGENT_APPID_CONFIG = "agentAppidConfig";

    public static final String AGENT_NAME_KEY = "agent_name";

    @Autowired
    private ConfigSupportService configSupportService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    Environment environment;

    private static final String BETA_PAY_PATH = "https://qr-wap-pay.iwosai.com/";

    public WeixinConfig getConfig(String merchantSn, int provider, String channelNo, String weixinMchId) {
        String city = configSupportService.getMerchantCity(merchantSn);
        Map weixinSubdevConfig = getAppidConfig(PaywayEnum.WEIXIN.getValue(), provider, channelNo, city);
        if (weixinSubdevConfig == null) {
            return null;
        }

        String weixinAppId = BeanUtil.getPropString(weixinSubdevConfig, com.wosai.upay.merchant.contract.model.ContractChannel.WEIXIN_APP_ID);
        String weixinMiniAppId = BeanUtil.getPropString(weixinSubdevConfig, com.wosai.upay.merchant.contract.model.ContractChannel.WEIXIN_MINI_APP_ID);
        String weixinReceiptAppid = BeanUtil.getPropString(weixinSubdevConfig, com.wosai.upay.merchant.contract.model.ContractChannel.WEIXIN_RECEIPT_APP_ID);

        List<WeixinAppidConfig> appIdConfigs = new ArrayList<>();
        appIdConfigs.addAll(buildConfigs(weixinAppId, weixinReceiptAppid, false));
        appIdConfigs.addAll(buildConfigs(weixinMiniAppId, weixinReceiptAppid, true));

        WeixinConfig weixinConfig = new WeixinConfig();
        weixinConfig.setAppidConfigs(appIdConfigs);
        weixinConfig.setWeixinMchId(weixinMchId);
        weixinConfig.setPayAuthPath(new ArrayList<>(ContractConfig.WEIXIN_PAYPAY_AUTH_PATH));
        if (!ArrayUtils.isEmpty(environment.getActiveProfiles()) && !Objects.equals(environment.getActiveProfiles()[0], "prod")) {//添加测试环境支付目录
            final List<String> payAuthPath = weixinConfig.getPayAuthPath();
            payAuthPath.add(BETA_PAY_PATH);
        }
        return weixinConfig;
    }

    private List<WeixinAppidConfig> buildConfigs(String appIds, String receiptAppid, boolean isMini) {
        List<WeixinAppidConfig> configs = new ArrayList<>();
        if (WosaiStringUtils.isNotEmpty(appIds)) {
            String[] split = appIds.split(",");
            for (String appId : split) {
                WeixinAppidConfig mini = new WeixinAppidConfig();
                mini.setSub_appid(appId);
                if (applicationApolloConfig.getAutoReceiptAppid() && isMini && WosaiStringUtils.isEmpty(receiptAppid)) {
                    receiptAppid = appId;
                }
                mini.setReceipt_appid(receiptAppid);
                mini.setMini(isMini);
                configs.add(mini);
            }
        }
        return configs;
    }


    public String getAgentName(int payWay, int provider, String channelNo, String merchantCity) {
        Map cityConfig = getAppidConfig(payWay, provider, channelNo, merchantCity);
        return BeanUtil.getPropString(cityConfig, AGENT_NAME_KEY);
    }

    public String getSubAppid(int payWay, int provider, String channelNo, String merchantSn) {
        String merchantCity = configSupportService.getMerchantCity(merchantSn);
        Map cityConfig = getAppidConfig(payWay, provider, channelNo, merchantCity);
        return BeanUtil.getPropString(cityConfig, com.wosai.upay.merchant.contract.model.ContractChannel.WEIXIN_APP_ID);
    }

    public Map getAppidConfig(int payWay, int provider, String channelNo, String merchantCity) {
        String key = payWay + "_" + provider + "_" + channelNo;
        return getConfigByKeyAndCity(key, merchantCity);
    }

    public Map getConfigByKeyAndCity(String key, String merchantCity) {
        Map content = applicationApolloConfig.getAgentAppidConfig();
        Map<String, String> cityMap = (Map) BeanUtil.getProperty(content, key);
        if (CollectionUtils.isEmpty(cityMap)) {
            log.info("根据子商户信息拼接 key {} 获取阿波罗值为空 ", key, content);
            return null;
        }
        Map cityConfig = null;
        for (String city : cityMap.keySet()) {
            if (merchantCity.contains(city)) {
                cityConfig = (Map) BeanUtil.getProperty(cityMap, city);
                break;
            }
        }
        if (cityConfig == null) {
            cityConfig = (Map) BeanUtil.getProperty(cityMap, ContractConfig.DEFAULT_ROUTING_CITY);
        }
        return cityConfig;
    }
}
