package com.wosai.upay.job.externalservice.mail;

import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.externalservice.mail.model.MailSendResp;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
@Component
public class MailClient {

    @Value("${mail-gateway}")
    private String mailGateway;
    private static final String ERRCODE = "errcode";
    private static final String ERRMSG = "errmsg";

    private final RestTemplate restTemplate = new RestTemplate();

    public MailSendResp sendEmail(MailSendReq req) {
        Map result = restTemplate.postForObject(mailGateway, req.genMailSendRequest(), Map.class);
        if (MapUtils.getInteger(result, ERRCODE) == 0) {
            return MailSendResp.success();
        } else {
            return MailSendResp.fail(MapUtils.getString(result, ERRMSG));
        }
    }
}
