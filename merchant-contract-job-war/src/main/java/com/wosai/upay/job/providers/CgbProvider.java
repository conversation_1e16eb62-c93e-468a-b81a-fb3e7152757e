package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.GuangFaParam;
import com.wosai.upay.merchant.contract.service.GuangFaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_MSG;
import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_TIME;

/**
 * 广发银行卡通道
 *
 * <AUTHOR>
 * @date 2021-06-15
 */
@Component(ProviderUtil.CGB_CHANNEL)
public class CgbProvider extends AbstractProvider {

    @Autowired
    private GuangFaService guangFaService;

    @Autowired
    private RuleContext ruleContext;

    @Value("${cgb_dev_code}")
    public String devCode;


    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        //广发目前暂不支持更新操作
        return null;
    }

    /**
     * 任务新增处理
     *
     * @param contractTask
     * @param contractChannel
     * @param sub
     * @return
     */
    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        GuangFaParam guangFaParam = buildParam(contractChannel, sub, GuangFaParam.class);
        //新增商户入网
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
//                if (needPayFor(contextParam, sub, contractTask)) {
//                    return null;
//                }
                ContractResponse response = guangFaService.contractMerchant(contextParam, guangFaParam);
                String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
                updateClearProvider(merchantId, sub.getMerchant_sn());
                return response;
            } else {
                //其他支付类型：微信，支付宝
                return guangFaService.contractMerchantOtherPayWay(sub, guangFaParam);
            }
        }
        //微信重新入网报备,微信商家实名认证
        if (ProviderUtil.CONTRACT_RECONTRACT.equals(contractTask.getType()) || ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType())) {
//            return guangFaService.contractWeiXinWithParams(contextParam, sub, guangFaParam);
        }
        return null;
    }

    /**
     * 任务更新处理
     *
     * @param contractTask
     * @param contractChannel
     * @param sub
     * @return
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        GuangFaParam guangFaParam = buildParam(contractChannel, sub, GuangFaParam.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
            }

            if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(sub.getTask_type()) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(sub.getTask_type())) {
                return guangFaService.updateMerchantBankAccount(contextParam, guangFaParam);
            } else {
                return guangFaService.updateMerchant(contextParam, guangFaParam);
            }
        }

        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        GuangFaParam guangFaParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, GuangFaParam.class);
        return guangFaService.wechatSubDevConfig(guangFaParam, weixinConfig);
    }


    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        GuangFaParam guangFaParam = buildParam(contractChannel, contractSubTask, GuangFaParam.class);
        return guangFaService.queryContractStatusByContractId(contractSubTask, guangFaParam);
    }


    /**
     * 处理进件状态
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return doHandleContractStatus(contractSubTask, response);
    }

    /**
     * 查询的状态做处理
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            //返回值
            Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
            Map<String, Object> callbackMsg = response.getResponseParam();
            callbackMsg.put(CONTRACT_CALLBACK_TIME, System.currentTimeMillis());
            ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
            if (resp == null) {
                resp = new HashMap();
            }
            resp.put(CONTRACT_CALLBACK_MSG, callBack);
            contractSubTask.setResponse_body(JSONObject.toJSONString(resp));
            return new HandleQueryStatusResp().setSuccess(true)
                    .setMessage("广发审核通过");
        }
    }
}
