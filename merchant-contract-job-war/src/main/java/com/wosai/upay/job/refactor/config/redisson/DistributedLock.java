package com.wosai.upay.job.refactor.config.redisson;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 分布式锁
 *
 * <AUTHOR>
 * @date 2023/9/13 11:23
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {

    /**
     * lock key
     *
     * @return lock key
     */
    String key();

    /**
     * 是否开启自动续约 默认开启
     *
     * @return true 开启自动续约
     */
    boolean autoLease() default true;

    /**
     * 为了获取锁而等待的时间 默认0s
     *
     * @return 等待时间
     */
    long waitTime() default RedisRelatedConstant.DEFAULT_LOCK_WAIT_TIME;

    /**
     *  锁的过期时间 需要关闭自动续约
     *  如果自定义过期时间,则无法使用watchdog自动续约
     *
     * @return 续约时间
     */
    long leaseTime() default RedisRelatedConstant.DEFAULT_LOCK_LEASE_TIME;

    /**
     * 时间单位 默认为秒
     *
     * @return 时间单位
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 是否在加锁失败时抛出异常，默认不抛出
     *
     * @return true:抛出异常
     */
    boolean throwExceptionOnFailure() default false;

    /**
     * 重试次数，默认不重试
     *
     * @return 重试次数
     */
    int retryTimes() default 0;


    /**
     * 加锁失败处理
     *
     * @return 加锁失败处理逻辑
     */
    Class<? extends Consumer<String>> lockFailedHandler() default DefaultLockFailedHandler.class;
}
