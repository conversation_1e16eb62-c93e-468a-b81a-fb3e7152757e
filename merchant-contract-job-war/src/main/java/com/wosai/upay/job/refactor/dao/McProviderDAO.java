package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.model.dto.ProviderDto;
import com.wosai.upay.job.refactor.mapper.McProviderDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 结算通道表表数据库访问层 {@link McProviderDO}
 * 对McProviderMapper层做出简单封装 {@link McProviderDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class McProviderDAO {

    private static final Cache<String, McProviderDO> CACHE = CacheBuilder.newBuilder()
            .maximumSize(100)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();

    @Resource
    private McProviderDynamicMapper mcProviderDynamicMapper;

    /**
     * 根据provider标识获取
     *
     * @param provider 唯一标识
     * @return 结算通道对象
     */
    public Optional<McProviderDO> getByProvider(String provider) {
        if (StringUtils.isBlank(provider)) {
            return Optional.empty();
        }
        try {
            McProviderDO mcProviderDO = CACHE.get(provider, () ->
                    mcProviderDynamicMapper.selectOne(
                            new LambdaQueryWrapper<McProviderDO>()
                                    .eq(McProviderDO::getProvider, provider)
                    )
            );
            return Optional.ofNullable(mcProviderDO);
        } catch (Exception e) {
            log.error("getByProvider error {}", provider, e);
        }
        return Optional.empty();
    }

    /**
     *
     * @param beanName
     * @return
     */
    public Optional<McProviderDO> getByBeanName(String beanName) {
        if (StringUtils.isBlank(beanName)) {
            return Optional.empty();
        }
        try {
            McProviderDO mcProviderDO = CACHE.get(beanName, () ->
                    mcProviderDynamicMapper.selectOne(
                            new LambdaQueryWrapper<McProviderDO>()
                                    .eq(McProviderDO::getBeanName, beanName)
                    )
            );
            return Optional.ofNullable(mcProviderDO);
        } catch (Exception e) {
            log.error("getByBeanName error {}", beanName, e);
        }
        return Optional.empty();
    }

    /**
     * 根据provider标识列表批量获取
     *
     * @param providers provider标识列表
     * @return 结算通道对象列表
     */
    public List<McProviderDO> listByProviders(Collection<String> providers) {
        if (CollectionUtils.isEmpty(providers)) {
            return Collections.emptyList();
        }
        return mcProviderDynamicMapper.selectList(new LambdaQueryWrapper<McProviderDO>().in(McProviderDO::getProvider, providers));
    }

    /**
     * 判断某个provider是否属于银行通道
     *
     * @param provider
     * @return
     */
    public boolean isBankProvider(String provider) {
        Optional<McProviderDO> mcProviderDO = getByProvider(provider);
        return mcProviderDO.isPresent() && mcProviderDO.get().getName().contains("银行");
    }


    public Page<McProviderDO> pageProviderList(PageInfo pageInfo, ProviderDto providerDto) {
        QueryWrapper queryWrapper = new QueryWrapper<McAcquirerDO>()
                .eq(providerDto.getId() != null, "id", providerDto.getId())
                .eq(WosaiStringUtils.isNotBlank(providerDto.getProvider()), "provider", providerDto.getProvider())
                .like(WosaiStringUtils.isNotBlank(providerDto.getName()), "name", providerDto.getName());

        List<OrderBy> orderBys = pageInfo.getOrderBy();
        if (WosaiCollectionUtils.isEmpty(orderBys)) {
            queryWrapper.orderByDesc("create_at");
        } else {
            for (OrderBy orderBy : orderBys) {
                if (Objects.equals(OrderBy.OrderType.ASC, orderBy.getOrder())) {
                    queryWrapper.orderByAsc(orderBy.getField());
                } else {
                    queryWrapper.orderByDesc(orderBy.getField());
                }
            }
        }

        return PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize()).doSelectPage(() -> mcProviderDynamicMapper.selectList(queryWrapper));
    }

    /**
     * 根据收单机构标识获取结算通道列表
     *
     * @param acquirer 收单机构标识
     * @return 结算通道列表
     */
    public List<McProviderDO> listByAcquirer(String acquirer) {
        if (StringUtils.isBlank(acquirer)) {
            return Collections.emptyList();
        }
        return mcProviderDynamicMapper.selectList(new LambdaQueryWrapper<McProviderDO>().in(McProviderDO::getAcquirer, acquirer));
    }


    public int insert(McProviderDO providerDO) {
        final int row = mcProviderDynamicMapper.insert(providerDO);
        CACHE.invalidateAll();
        return row;
    }
}
