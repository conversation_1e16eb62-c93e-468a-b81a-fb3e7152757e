package com.wosai.upay.job.biz.messageStrategy;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.newBlueSea.AlipayIndirectActivityStatus;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.wosai.upay.job.Constants.BlueSeaConstant.FAIL;

/**
 * @Description: 支付宝间连活动拒绝处理
 * <AUTHOR>
 * @Date: 2021/11/22 4:50 下午
 */
@Component
@Slf4j
public class AlipayIndirectActivityRejectBiz implements AliMessageHandleService {
    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private RedisLock redisLock;

    private static final String STATUS = "alipay_indirect_activity_apply_status";

    @Override
    public String getMsgMethod() {
        return "ant.merchant.expand.indirect.activity.rejected";
    }

    @Override
    public void handleMessageBiz(String bizContent) {
        log.info("支付宝间连活动报名失败业务处理 : {}", bizContent);
        AlipayIndirectActivityStatus fail = JSONObject.parseObject(bizContent, AlipayIndirectActivityStatus.class);

        String key = STATUS + fail.getOrder_id();
        try {
            if (!redisLock.lock(key, key, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
                return;
            }
            //活动申请单Id
            String activityOrderId = fail.getOrder_id();
            //是否存在这个记录
            BlueSeaTask task = blueSeaTaskMapper.findTaskByActivityOrderId(activityOrderId);
            if (Objects.isNull(task)) {
                return;
            }
            //更改状态为报名失败
            String msg = "支付宝拒绝商户报名失败" + fail.getFail_reason();
            blueSeaBiz.updateStatus(task.getId(), FAIL, msg, null, null, null, null, 2);

        } finally {
            redisLock.unlock(key, key);
        }

    }
}
