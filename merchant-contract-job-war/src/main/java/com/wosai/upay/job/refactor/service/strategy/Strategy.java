package com.wosai.upay.job.refactor.service.strategy;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 策略
 *
 * @param <T> 策略类型枚举
 * @param <V> 策略类型值
 */
public interface Strategy<T extends ITextValueEnum<V>, V> {

    /**
     * 获取策略类型
     *
     * @return 策略类型
     */
    T getType();

    /**
     * 获取策略类型值
     *
     * @return 策略类型值
     */
    default V getTypeValue() {
        return getType().getValue();
    }
}
