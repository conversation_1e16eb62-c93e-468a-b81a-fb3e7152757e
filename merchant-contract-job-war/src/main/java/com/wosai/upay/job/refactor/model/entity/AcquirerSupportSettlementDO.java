package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 收单机构支持结算类型表表实体对象
 *
 * <AUTHOR>
 */
@TableName("acquirer_support_settlement")
@Data
public class AcquirerSupportSettlementDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 营业执照类型 对应merchant_business_license.type
     */
    @TableField(value = "business_license_type")
    private Integer businessLicenseType;
    /**
     * 法人对私支持类型   0-不支持 1-支持
     */
    @TableField(value = "legal_private_support_type")
    private Integer legalPrivateSupportType;
    /**
     * 非法人对私支持类型 0-不支持 1-支持
     */
    @TableField(value = "non_legal_private_support_type")
    private Integer nonLegalPrivateSupportType;
    /**
     * 普通对公支持类型   0-不支持 1-支持
     */
    @TableField(value = "common_public_support_type")
    private Integer commonPublicSupportType;
    /**
     * 其他对公支持类型   0-不支持 1-支持
     */
    @TableField(value = "other_public_support_type")
    private Integer otherPublicSupportType;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;


}

