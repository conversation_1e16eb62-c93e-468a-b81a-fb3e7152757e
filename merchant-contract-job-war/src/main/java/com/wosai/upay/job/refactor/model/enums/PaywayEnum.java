package com.wosai.upay.job.refactor.model.enums;


/**
 * 支付源枚举
 *
 * <AUTHOR>
 */
public enum PaywayEnum implements ITextValueEnum {

    /**
     * 收单机构
     */
    ACQUIRER(0, "收单机构"),

    /**
     * 支付宝
     */
    ALIPAY(2, "支付宝2.0"),

    /**
     * 微信
     */
    WEI_XIN(3, "微信"),

    /**
     * 数字人民币
     */
    DCEP(23, "数字人民币");

    PaywayEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }
    private final int value;
    private final String text;


    public String getText() {
        return this.text;
    }

    public int getValue() {
        return this.value;
    }
}
