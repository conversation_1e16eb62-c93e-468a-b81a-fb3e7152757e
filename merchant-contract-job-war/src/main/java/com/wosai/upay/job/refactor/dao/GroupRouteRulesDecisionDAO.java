package com.wosai.upay.job.refactor.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.upay.job.refactor.mapper.GroupRouteRulesDecisionMapper;
import com.wosai.upay.job.refactor.model.entity.GroupRouteRulesDecisionDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 进件规则决策表表数据库访问层 {@link GroupRouteRulesDecisionDO}
 * 对McRulesDecisionMapper层做出简单封装 {@link GroupRouteRulesDecisionMapper}
 *
 * <AUTHOR>
 */
@Repository
public class GroupRouteRulesDecisionDAO extends AbstractBaseDAO<GroupRouteRulesDecisionDO, GroupRouteRulesDecisionMapper> {

    @Autowired
    public GroupRouteRulesDecisionDAO(SqlSessionFactory sqlSessionFactory, GroupRouteRulesDecisionMapper groupRouteRulesDecisionMapper) {
        super(sqlSessionFactory, groupRouteRulesDecisionMapper);
    }


    /**
     * 获取最大id
     *
     * @return 最大id
     */
    public Optional<Long> getMaxId() {
        return Optional.ofNullable(entityMapper.selectMaxId());
    }

    /**
     * 根据id更新有效状态
     *
     * @param ids   id集合
     * @param status 状态
     * @return effect rows
     */
    public Integer updateValidStatusByIds(Set<Long> ids, Integer status) {
        if (CollUtil.isEmpty(ids) || Objects.isNull(status)) {
            return 0;
        }
        LambdaQueryWrapper<GroupRouteRulesDecisionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(GroupRouteRulesDecisionDO::getId, ids);
        GroupRouteRulesDecisionDO decisionDO = new GroupRouteRulesDecisionDO();
        decisionDO.setValidStatus(status);
        return entityMapper.update(decisionDO, lambdaQueryWrapper);
    }

    /**
     * 根据优先级获取进件路由规则决策
     *
     * @param priority 优先级
     * @return 进件路由规则决策
     */
    public List<GroupRouteRulesDecisionDO> listByPriority(Integer priority) {
        LambdaQueryWrapper<GroupRouteRulesDecisionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(GroupRouteRulesDecisionDO::getPriority, priority);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据id列表查询
     *
     * @param ids id列表
     * @return 进件路由规则决策
     */
    public List<GroupRouteRulesDecisionDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<GroupRouteRulesDecisionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .in(GroupRouteRulesDecisionDO::getId, ids);
        return entityMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 批量更新或者插入,id已存在更新,id不存在插入
     *
     * @param groupRouteRulesDecisionDOS 进件路由规则决策
     * @return effect rows
     */
    public Integer batchInsertOrUpdateById(List<GroupRouteRulesDecisionDO> groupRouteRulesDecisionDOS) {
        Integer effectRows = 0;
        if (CollectionUtils.isEmpty(groupRouteRulesDecisionDOS)) {
            return 0;
        }
        Set<Long> existedIdSets = listByIds(groupRouteRulesDecisionDOS.stream().map(GroupRouteRulesDecisionDO::getId)
                .collect(Collectors.toList())).stream().map(GroupRouteRulesDecisionDO::getId).collect(Collectors.toSet());
        Map<Boolean, List<GroupRouteRulesDecisionDO>> dataMap = groupRouteRulesDecisionDOS.stream().collect(Collectors.partitioningBy(t -> existedIdSets.contains(t.getId())));
        List<GroupRouteRulesDecisionDO> updateList = dataMap.get(Boolean.TRUE);
        List<GroupRouteRulesDecisionDO> insertList = dataMap.get(Boolean.FALSE);
        effectRows += batchOperation(updateList, GroupRouteRulesDecisionMapper::updateById);
        effectRows += batchOperation(insertList, GroupRouteRulesDecisionMapper::insert);
        return effectRows;
    }

    /**
     * 获取所有有效的进件路由规则决策
     *
     * @return 进件路由规则决策
     */
    public List<GroupRouteRulesDecisionDO> listAllValid() {
        LambdaQueryWrapper<GroupRouteRulesDecisionDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(GroupRouteRulesDecisionDO::getValidStatus, ValidStatusEnum.VALID.getValue());
        return entityMapper.selectList(lambdaQueryWrapper);
    }
}
