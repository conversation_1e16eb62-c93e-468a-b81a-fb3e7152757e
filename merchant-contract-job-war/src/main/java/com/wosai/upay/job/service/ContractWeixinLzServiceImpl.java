package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Created by lihebin on 2018/9/14.
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractWeixinLzServiceImpl implements ContractWeixinLzService {


    @Override
    public void changeLzParamToNormal(String merchantSn, String feeRate) {
        throw new ContractBizException("接口已废弃");
    }

    @Override
    public void changeNormalToLz(String merchantSn) {
        throw new ContractBizException("接口已废弃");
    }


}
