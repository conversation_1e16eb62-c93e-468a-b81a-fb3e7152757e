package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 进件报备规则组策略组合表表实体对象
 *
 * <AUTHOR>
 */
@TableName("group_combined_strategy")
@Data
public class GroupCombinedStrategyDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 策略类型 1-一主无备 2-一主一备 3-一主多备 4-一主一备+每日额度 5-负载比例
     */
    @TableField(value = "strategy_type")
    private Integer strategyType;
    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 有效状态 0-失效 1-生效
     */
    @TableField(value = "valid_status")
    private Integer validStatus;
    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;


}

