package com.wosai.upay.job.refactor.biz.audit;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionConfigReqDTO;
import com.wosai.upay.job.refactor.model.bo.BankTradeProtectionApplyExcelBO;
import com.wosai.upay.job.model.dto.request.BankTradeProtectionAuditApplyReqDTO;
import com.wosai.upay.job.refactor.model.entity.BankTradeProtectionMerchantConfigDO;
import com.wosai.upay.job.refactor.service.impl.BankTradeProtectionServiceImpl;
import com.wosai.upay.job.util.BatchChangeAcquireUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 银行保障交易审核申请
 *
 * <AUTHOR>
 * @date 2024/8/15 10:43
 */
@Component
@Slf4j
public class BankTradeProtectionAuditApply {

    @Resource
    private AopBiz aopBiz;

    @Autowired
    private MerchantService merchantService;

    @Resource(type = BankTradeProtectionServiceImpl.class)
    private BankTradeProtectionServiceImpl bankTradeProtectionService;

    @Value("${bank.cooperation.notice.app.devCode}")
    private String noticeMerchantDevCode;

    @Value("${close.trade.protection.notify.template_id}")
    private String noticeMerchantCloseTemplateId;

    @Value("${open.trade.protection.notify.template_id}")
    private String noticeMerchantOpenTemplateId;

    /**
     * 处理银行保障交易审核申请
     * 触发给商户发通知，让商户选择是否需要银行保障交易
     *
     * @param bankTradeProtectionAuditApplyReqDTO 银行保障交易审核申请
     */
    public void processApply(BankTradeProtectionAuditApplyReqDTO bankTradeProtectionAuditApplyReqDTO) {
        boolean auditApplyValid = isAuditApplyValid(bankTradeProtectionAuditApplyReqDTO);
        if (!auditApplyValid) {
            log.error("银行保障交易审核申请参数不合法, auditApplyBO:{}", bankTradeProtectionAuditApplyReqDTO);
            return;
        }
        Set<String> merchantSns = listApplyMerchantSns(bankTradeProtectionAuditApplyReqDTO);
        if (CollectionUtils.isEmpty(merchantSns)) {
            return;
        }
        Map<Boolean, Set<String>> merchantOpenGroupMap = bankTradeProtectionService.batchListByMerchantSns(merchantSns)
                .stream()
                .collect(Collectors.partitioningBy(BankTradeProtectionMerchantConfigDO::isOpen))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(BankTradeProtectionMerchantConfigDO::getMerchantSn).collect(Collectors.toSet())));
        String templateId = isTradeProtectionApplyClose(bankTradeProtectionAuditApplyReqDTO.getTradeProtectionType()) ? noticeMerchantCloseTemplateId : noticeMerchantOpenTemplateId;
        for (String merchantSn : merchantSns) {
            try {
                processEachMerchant(bankTradeProtectionAuditApplyReqDTO, merchantOpenGroupMap, merchantSn, templateId);
            } catch (Exception e) {
                log.error("商户银行交易保障开关配置错误,merchant:{}", merchantSn, e);
            }
        }
    }

    private void processEachMerchant(BankTradeProtectionAuditApplyReqDTO bankTradeProtectionAuditApplyReqDTO, Map<Boolean, Set<String>> merchantOpenGroupMap,
                                     String merchantSn, String templateId) {
        // 状态只要不是关闭，都是开启状态，即使表中没有记录（默认）
        if (isTradeProtectionApplyOpen(bankTradeProtectionAuditApplyReqDTO.getTradeProtectionType()) && !merchantOpenGroupMap.get(Boolean.FALSE).contains(merchantSn)) {
            log.warn("商户已经开启银行保障交易,不需要更新白名单及发通知, merchantSn:{}", merchantSn);
            return;
        }
        if (isTradeProtectionApplyClose(bankTradeProtectionAuditApplyReqDTO.getTradeProtectionType()) && (merchantOpenGroupMap.get(Boolean.FALSE).contains(merchantSn) )) {
            log.warn("商户已经关闭银行保障交易,不需要更新白名单及发通知, merchantSn:{}", merchantSn);
            return;
        }
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        if (Objects.isNull(merchant)) {
            log.error("商户不存在, merchantSn:{}", merchantSn);
            return;
        }
        if (updateProtectionStatus(isTradeProtectionApplyOpen(bankTradeProtectionAuditApplyReqDTO.getTradeProtectionType()), merchant.getId())) {
            sendNoticeToMerchant( merchant.getId(),  templateId);
        }
    }

    private boolean updateProtectionStatus(boolean isOpenTradeProtection, String merchantId) {
        Integer protectionStatus = isOpenTradeProtection ? BankTradeProtectionConfigReqDTO.RECOVER_PROTECTION : BankTradeProtectionConfigReqDTO.CLOSE_PROTECTION;
        BankTradeProtectionConfigReqDTO bankTradeProtectionConfigReqDTO = new BankTradeProtectionConfigReqDTO();
        bankTradeProtectionConfigReqDTO.setProtectionStatus(protectionStatus);
        bankTradeProtectionConfigReqDTO.setMerchantId(merchantId);
        return bankTradeProtectionService.updateTradeProtectionStatus(bankTradeProtectionConfigReqDTO);
    }


    private boolean isTradeProtectionApplyClose(String tradeProtectionType) {
        return StringUtils.equals(tradeProtectionType, BankTradeProtectionAuditApplyReqDTO.TRADE_PROTECTION_TYPE_CLOSE);
    }

    private boolean isTradeProtectionApplyOpen(String tradeProtectionType) {
        return StringUtils.equals(tradeProtectionType, BankTradeProtectionAuditApplyReqDTO.TRADE_PROTECTION_TYPE_OPEN);
    }

    private void sendNoticeToMerchant(String merchantId, String templateId) {
        aopBiz.sendNoticeToAdmin(merchantId, noticeMerchantDevCode, templateId, Collections.EMPTY_MAP);
    }

    private Set<String> listApplyMerchantSns(BankTradeProtectionAuditApplyReqDTO bankTradeProtectionAuditApplyReqDTO) {
        if (Objects.equals(bankTradeProtectionAuditApplyReqDTO.getApplyType(), BankTradeProtectionAuditApplyReqDTO.APPLY_TYPE_SINGLE)) {
            return Sets.newHashSet(bankTradeProtectionAuditApplyReqDTO.getMerchantSn());
        }
        List<BankTradeProtectionApplyExcelBO> excelInfos = Lists.newArrayList();
        for (String merchantSnFileOssUrl : bankTradeProtectionAuditApplyReqDTO.getMerchantSnFileOssUrls()) {
            if (StringUtils.isNotBlank(merchantSnFileOssUrl)) {
                try {
                    excelInfos.addAll(BatchChangeAcquireUtil.getExcelInfoList(merchantSnFileOssUrl, new BankTradeProtectionApplyExcelBO())) ;
                } catch (Exception e) {
                    log.error("获取商户信息失败, merchantSnFileOssUrl:{}", merchantSnFileOssUrl, e);
                }
            }
        }
        return excelInfos.stream().filter(t -> Objects.nonNull(t) && StringUtils.isNotBlank(t.getMerchantSn()))
                .map(BankTradeProtectionApplyExcelBO::getMerchantSn).collect(Collectors.toSet());
    }

    private boolean isAuditApplyValid(BankTradeProtectionAuditApplyReqDTO bankTradeProtectionAuditApplyReqDTO) {
        if (Objects.isNull(bankTradeProtectionAuditApplyReqDTO)) {
            return false;
        }
        return true;
    }
}
