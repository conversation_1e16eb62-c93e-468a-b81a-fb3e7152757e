package com.wosai.upay.job.refactor.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.model.dto.request.BankAutoChangeToThirdPartyReqDTO;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.bo.BankAutoChangeToThirdPartyTaskFormBO;
import com.wosai.upay.job.refactor.model.bo.BankErrorRepairSolutionMappingBO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.impl.BankTradeProtectionServiceImpl;
import com.wosai.upay.job.service.AcquirerServiceImpl;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 银行自动切三方任务
 *
 * <AUTHOR>
 * @date 2024/6/18 14:12
 */
@Service
@Slf4j
public class BankAutoChangeToThirdPartyTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private AcquirerServiceImpl acquirerService;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Resource
    private AopBiz aopBiz;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private TaskInstanceService taskInstanceService;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private McAcquirerDAO mcAcquirerDAO;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Value("${bank.cooperation.notice.app.devCode}")
    private String noticeTemplateDevCode;

    @Value("${bank.auto.change.third.notify.template_id}")
    private String noticeTemplateId;

    @Value("${bank_error_change_to_third_task_template_id}")
    private Long taskTemplateId;

    @Resource
    private IMerchantService iMerchantService;

    @Resource
    private OrganizationService organizationService;

    @Autowired
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private ContractStatusMapper contractStatusMapper;

    @Resource(type = BankTradeProtectionServiceImpl.class)
    private BankTradeProtectionServiceImpl bankTradeProtectionService;

    @Resource
    private BusinessLogBiz businessLogBiz;


    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY;
    }

    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        return executeProperty;
    }

    /**
     * 处理单个子任务
     * 需要覆盖子任务的状态：0-待处理 2-等待外部结果 3-重试 的处理逻辑
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 子任务执行结果
     */
    @Override
    public InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return handleApplyChangeTask(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            return handleWaitChangeTaskResult(mainTaskDO, subTaskDO);
        }
        throw new ContractBizException("不支持的任务状态");
    }


    private InternalScheduleSubTaskProcessResultBO handleApplyChangeTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
        String merchantSn = mainTaskDO.getMerchantSn();
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        if (indirectAcquirerList.contains(contractStatus.getAcquirer())) {
            log.warn("商户当前已在三方收单机构，不用发起自动切换任务: {}", merchantSn);
            return getUnableChangeResult(mainTaskDO, "商户当前已在三方收单机构:" + contractStatus.getAcquirer(), false);
        }
        List<InternalScheduleMainTaskDO> mainTaskDOList = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, getTaskType().getValue());
        long existedProcessingTask = mainTaskDOList.stream()
                .filter(t -> !Objects.equals(t.getId(), mainTaskDO.getId()))
                .filter(t -> !Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue())
                        && !Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue())).count();
        if (existedProcessingTask > 0) {
            log.warn("商户已存在正在进行的自动切回三方收单机构的任务,商户号: {}", merchantSn);
            return getUnableChangeResult(mainTaskDO, "商户已存在正在进行的自动切回三方收单机构的任务", false);
        }
        String unableChangeReason = filterRuleAndGetUnableChangeToThirdPartyReason(mainTaskDO);
        if (StringUtils.isNotBlank(unableChangeReason)) {
            return getUnableChangeResult(mainTaskDO, unableChangeReason, false);
        }
        String targetAcquirer = getAndSubmitChangeApplyToTargetAcquirer(merchantSn);
        if (StringUtils.isBlank(targetAcquirer)) {
            return getUnableChangeResult(mainTaskDO, "该商户未匹配到合适的三方收单机构", true);
        }
        Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getLastedByMerchantSnAndTargetAcquirer(merchantSn, targetAcquirer, currentTimestamp);
        if (!changeTaskOpt.isPresent()) {
            log.warn("没有生成对应切换收单机构的任务, merchantSn = {}, targetAcquirer = {}", merchantSn, targetAcquirer);
            return getUnableChangeResult(mainTaskDO, "生成切换收单机构任务失败", true);
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeTaskOpt.get();
        HashMap<String, String> contextMap = Maps.newHashMap();
        contextMap.put("changeId", String.valueOf(mcAcquirerChangeDO.getId()));
        contextMap.put("targetAcquirer", targetAcquirer);
        subTaskDO.setContext(JSON.toJSONString(contextMap));
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, "切换任务已经提交,等待切换结果");
    }

    private InternalScheduleSubTaskProcessResultBO getUnableChangeResult(InternalScheduleMainTaskDO mainTaskDO, String unableChangeReason, boolean sendWorkOrder) {
        if (sendWorkOrder) {
            sendUnableChangeWorkOrder(mainTaskDO, unableChangeReason);
        }
        InternalScheduleSubTaskProcessResultBO processResultBO = new InternalScheduleSubTaskProcessResultBO();
        processResultBO.setResult(unableChangeReason);
        processResultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
        return processResultBO;
    }

    private String getAndSubmitChangeApplyToTargetAcquirer(String merchantSn) {
        // 必须保证微信是授权的
        Set<String> existedAllProviderSet = merchantProviderParamsDAO.listByMerchantSnAndPayWay(merchantSn, PaywayEnum.WEIXIN.getValue()).stream()
                .filter(t -> Objects.equals(t.getAuthStatus(), AuthStatusEnum.YES.getValue()))
                .map(MerchantProviderParamsDO::getProvider).filter(Objects::nonNull).map(String::valueOf).collect(Collectors.toSet());
        // 间清机构
        Set<String> sortedAcquirerSet = Sets.newLinkedHashSet(Arrays.asList(AcquirerTypeEnum.LKL_V3.getValue(),
                AcquirerTypeEnum.LKL.getValue(), AcquirerTypeEnum.HAI_KE.getValue(), AcquirerTypeEnum.FU_YOU.getValue()));
        List<String> indirectAcquirerList = mcAcquirerDAO.getIndirectAcquirerList();
        Set<String> existedIndirectAcquirerSet = mcProviderDAO.listByProviders(existedAllProviderSet).stream()
                .map(McProviderDO::getAcquirer)
                .filter(indirectAcquirerList::contains)
                .collect(Collectors.toSet());
        Set<String> candidateAcquirer = Sets.newLinkedHashSet();
        for (String acquirer : sortedAcquirerSet) {
            if (existedIndirectAcquirerSet.contains(acquirer)) {
                candidateAcquirer.add(acquirer);
            }
        }
        if (CollectionUtils.isEmpty(candidateAcquirer)) {
            return null;
        }
        String targetAcquirer = "";
        for (String acquirer : candidateAcquirer) {
            try {
                CheckChangeAcquirerResp checkChangeAcquirerResp = acquirerService.checkChangeAcquirer(merchantSn, acquirer);
                if (!checkChangeAcquirerResp.isCan_change()) {
                    log.warn("商户号: {}, target acquirer: {}, 不能切换原因: {}", merchantSn, acquirer, checkChangeAcquirerResp.getMsg());
                    continue;
                }
                boolean applySuccess = acquirerService.applyChangeAcquirer(merchantSn, acquirer, true);
                if (!applySuccess) {
                    log.warn("商户号: {}, can not change to target acquirer: {},", merchantSn, acquirer);
                    continue;
                }
                targetAcquirer = acquirer;
                break;
            } catch (Exception e) {
                log.error("商户号: {}, 银行通道自动切换三方通道，获取可以切换的三方通道异常", merchantSn, e);
            }
            if (StringUtils.isNotBlank(targetAcquirer)) {
                break;
            }
        }
        return targetAcquirer;
    }

    private InternalScheduleSubTaskProcessResultBO handleWaitChangeTaskResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        JSONObject contextMap = JSON.parseObject(subTaskDO.getContext());
        String changeId = MapUtils.getString(contextMap, "changeId");
        String targetAcquirer = MapUtils.getString(contextMap, "targetAcquirer");
        Optional<McAcquirerChangeDO> changeOpt = mcAcquirerChangeDAO.getByPrimaryKey(Long.valueOf(changeId));
        if (!changeOpt.isPresent()) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "切换任务不存在, id = " + changeId);
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeOpt.get();
        if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.SUCCESS)) {
            sendChangeSuccessWorkOrderTask(mainTaskDO, targetAcquirer);
            notifyMerchantChangeSuccess(mainTaskDO, targetAcquirer);
            recordMerchantLog(mainTaskDO, mcAcquirerChangeDO.getSourceAcquirer(), targetAcquirer);
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, "切换成功,目标收单机构: " + targetAcquirer);
        } else if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.FAIL)) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "切换失败");
        }
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, "切换任务已经提交,等待切换结果");
    }

    private void recordMerchantLog(InternalScheduleMainTaskDO mainTaskDO, String bankAcquirer, String thirdAcquirer) {
        businessLogBiz.recordBankProtectionAutoChangeLog(merchantService.getMerchantBySn(mainTaskDO.getMerchantSn(), devCode).getId(),
                bankAcquirer, thirdAcquirer, true);
    }

    private void notifyMerchantChangeSuccess(InternalScheduleMainTaskDO mainTaskDO, String targetAcquirer) {
        try {
            aopBiz.sendNoticeToAdmin(merchantService.getMerchantBySn(mainTaskDO.getMerchantSn(), devCode).getId(),
                    noticeTemplateDevCode, noticeTemplateId, Collections.EMPTY_MAP);
        } catch (Exception e) {
            log.error("通知商户,银行通道自动切换三方通道成功异常, 商户sn: {}, 目标收单机构: {}", mainTaskDO.getMerchantSn(), targetAcquirer, e);
        }
    }

    private String getRepairSolution(String bankName, String errorCode) {
        List<BankErrorRepairSolutionMappingBO> mappingList = applicationApolloConfig.listBankErrorRepairSolutionMapping();
        Map<String, String> mappingMap = mappingList.stream().collect(Collectors.toMap(t -> t.getBankName() + "-" + t.getCode(),
                BankErrorRepairSolutionMappingBO::getSolution, (a, b) -> a));
        return MapUtils.getString(mappingMap, bankName + "-" + errorCode, "请联系银行处理");
    }


    private String filterRuleAndGetUnableChangeToThirdPartyReason(InternalScheduleMainTaskDO mainTaskDO) {
        BankAutoChangeToThirdPartyReqDTO changeReq = JSON.parseObject(mainTaskDO.getContext(), BankAutoChangeToThirdPartyReqDTO.class);
        // 银行限制
        List<String> enableAutoChangeBanks = applicationApolloConfig.listEnableAutoChangeToThirdBanks();
        Optional<McProviderDO> providerOpt = mcProviderDAO.getByProvider(changeReq.getProvider());
        if (!providerOpt.isPresent()) {
            return "交易渠道:" + changeReq.getProvider() + "不支持";
        }
        String acquirer = providerOpt.get().getAcquirer();
        String acquirerName = mcAcquirerDAO.getAcquirerName(acquirer);
        if (!CollectionUtils.containsAny(enableAutoChangeBanks, acquirer)) {
            return "该银行不支持自动切换到三方收单机构";
        }
        // 报错码限制
        List<BankErrorRepairSolutionMappingBO> mappingList = applicationApolloConfig.listBankErrorRepairSolutionMapping();
        Map<String, String> mappingMap = mappingList.stream().collect(Collectors.toMap(t -> t.getBankName() + "-" + t.getCode(),
                BankErrorRepairSolutionMappingBO::getSolution, (a, b) -> a));
        if (!mappingMap.containsKey(acquirerName + "-" + changeReq.getErrorCode())) {
            return "银行报错码:" + changeReq.getErrorCode() + "不支持";
        }
        // 市 组织 限制
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(changeReq.getMerchantSn(), devCode);
        String merchantCityName = merchantInfo.getCity();
        List<String> enableAutoChangeCityList = applicationApolloConfig.listEnableAutoChangeToThirdCitys();
        if (!enableAutoChangeCityList.contains("all") && !enableAutoChangeCityList.contains(merchantCityName)) {
            return "商户所在城市:" + merchantCityName + "不支持";
        }
        String merchantOrg = getMerchantOrganizationPath(merchantInfo);
        if (!validateMerchantOrg(merchantOrg)) {
            return "商户所在组织:" + merchantOrg + "不支持";
        }
        if (bankTradeProtectionService.isCloseBankTradeProtection(merchantInfo.getSn())) {
            return "商户已选择关闭银行保障交易";
        }
        return null;
    }

    private boolean validateMerchantOrg(String merchantOrg) {
        if (StringUtils.isBlank(merchantOrg)) {
            return false;
        }
        List<String> enableAutoChangeOrgList = applicationApolloConfig.listEnableAutoChangeToThirdOrgs();
        if (CollectionUtils.isEmpty(enableAutoChangeOrgList)) {
            return false;
        }
        for (String enableOrg : enableAutoChangeOrgList) {
            if (StringUtils.contains(merchantOrg, enableOrg)) {
                return  true;
            }
        }
        return false;
    }

    // name_path -> 直营/业务管理测试
    private String getMerchantOrganizationPath(MerchantInfo merchantBasicInfo) {
        String organizationId = BeanUtil.getPropString(iMerchantService.getMerchantBySn(merchantBasicInfo.getSn()), "organization_id");
        if (StringUtils.isBlank(organizationId)) {
            return StringUtils.EMPTY;
        }
        return MapUtils.getString(organizationService.getOrganization(organizationId), "name_path");
    }

    private void sendChangeSuccessWorkOrderTask(InternalScheduleMainTaskDO mainTaskDO, String targetAcquirer) {
        sendChangeWorkOrder(mainTaskDO, mcAcquirerDAO.getAcquirerName(targetAcquirer));
    }


    private void sendUnableChangeWorkOrder(InternalScheduleMainTaskDO mainTaskDO, String unableChangeReason) {
        sendChangeWorkOrder(mainTaskDO, "无 " + unableChangeReason);
    }

    public void  sendChangeWorkOrder(InternalScheduleMainTaskDO mainTaskDO, String  targetAcquirer) {
        BankAutoChangeToThirdPartyReqDTO changeReq = JSON.parseObject(mainTaskDO.getContext(), BankAutoChangeToThirdPartyReqDTO.class);
        TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
        dto.setOperator("SYSTEM");
        dto.setOperatorName("SYSTEM");
        dto.setPlatform("SYSTEM");
        BankAutoChangeToThirdPartyTaskFormBO diyParams = new BankAutoChangeToThirdPartyTaskFormBO();
        diyParams.setMerchantSn(mainTaskDO.getMerchantSn());
        merchantProviderParamsDAO.getBySnAndProviderAndPayWay(mainTaskDO.getMerchantSn(),
                        Integer.valueOf(changeReq.getProvider()), PaywayEnum.ACQUIRER.getValue())
                .map(MerchantProviderParamsDO::getProviderMerchantId).ifPresent(diyParams::setBankMerchantId);
        mcProviderDAO.getByProvider(changeReq.getProvider()).map(McProviderDO::getName).ifPresent(diyParams::setTradeFailBankName);
        diyParams.setPayWay(EnumUtils.ofNullable(PaywayEnum.class, Integer.valueOf(changeReq.getPayWay())).map(PaywayEnum::getText).orElse("未知支付源"));
        String failReason = StringUtils.isNotBlank(changeReq.getErrorCodeMeaning()) ? changeReq.getErrorCodeMeaning() : "未知错误码";
        diyParams.setTradeFailReason(changeReq.getErrorCode() + ": " + failReason);
        diyParams.setBankRepairSolution(getRepairSolution(diyParams.getTradeFailBankName(), changeReq.getErrorCode()));
        diyParams.setBankOriginalErrorMsg(changeReq.getAcquirerOriginalErrorMsg());
        diyParams.setChangeToTargetAcquirer(targetAcquirer);
        dto.setDiyParams(JSON.parseObject(JSON.toJSONString(diyParams)));
        dto.setTaskTemplateId(taskTemplateId);
        dto.setTaskObjectSn(mainTaskDO.getMerchantSn());
        BankAutoChangeToThirdPartyReqDTO reqDTO = JSON.parseObject(mainTaskDO.getContext(), BankAutoChangeToThirdPartyReqDTO.class);
        reqDTO.setTargetAcquirer(targetAcquirer);
        try {
            // 某些商户组织不支持派工，调用接口会抛出异常，这部分商户和产品沟通，不需要派工
            Map resultMap = taskInstanceService.startTaskForRpc(dto);
            String id = MapUtils.getString(resultMap, "id");
            reqDTO.setTaskInstanceId(id);
        } catch (Exception e) {
            log.warn("商户号: {}, 发起派工任务异常: {}, 请求参数: {}", mainTaskDO.getMerchantSn(), e.getMessage(), JSON.toJSONString(dto));
        }
        mainTaskDO.setContext(JSON.toJSONString(reqDTO));
    }


}
