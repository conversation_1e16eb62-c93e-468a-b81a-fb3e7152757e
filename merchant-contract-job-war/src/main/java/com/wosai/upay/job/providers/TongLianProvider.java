package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.ProviderTerminal;
import com.wosai.upay.job.service.ContractTaskServiceImpl;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.TongLianParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.TlTerminfoinDTO;
import com.wosai.upay.merchant.contract.model.terminal.UpdateTermInfoDTO;
import com.wosai.upay.merchant.contract.service.TongLianService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-09-06
 */
@Slf4j
@Component(ProviderUtil.TONG_LIAN_CHANNEL)
public class TongLianProvider extends AbstractProvider {

    @Autowired
    private TongLianService tongLianService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private StoreService storeService;

    @Override
    String getProviderBeanName() {
        return ProviderUtil.TONG_LIAN_CHANNEL;
    }

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        String acquirer = acquirerBiz.getMerchantAcquirer(merchantSn);
        // 非当前收单机构，更新不影响总状态
        int influPtask = AcquirerTypeEnum.TONG_LIAN.getValue().equals(acquirer) ? contractRule.getUpdateInfluPtask() : 0;

        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(influPtask)
                .setChannel(ProviderUtil.TONG_LIAN_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);

        Integer taskType = null;
        //银行账户变更
        if (ContractEvent.OPT_TYPE_MERCHANT_SETTLEMENT_ACCOUNTS == event.getEvent_type()) {
            if (contractRule.getPayway() == null || contractRule.getPayway().equals(PaywayEnum.ACQUIRER.getValue())) {
                Map requestParam = (Map) paramContext.get("cardRequestParam");
                if (!CollectionUtils.isEmpty(requestParam)) {
                    //银行卡管理服务发起的变更(merchant_bank_account_pre)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE;
                } else {
                    //dts订阅直接变更(merchant_bank_account)
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                }
            }
        } else if (ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION == event.getEvent_type()) {
            //更新基本信息
            taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
        } else if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type()) {
            String crmUpdate = BeanUtil.getPropString(paramContext, "crmUpdate");
            if (!StringUtils.isEmpty(crmUpdate)) {
                if ("0".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION;
                } else if ("1".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS;
                } else if ("2".equals(crmUpdate)) {
                    taskType = ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH;
                } else {
                    //do nothing
                }
            }

        } else if (ContractEvent.OPT_TYPE_UPDATE_BUSINESS_LICENSE == event.getEvent_type()) {
            if (PaywayEnum.ACQUIRER.getValue().equals(contractRule.getPayway())) {
                //更新营业执照
                taskType = ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE;
            }
        }

        if (taskType == null) {
            return null;
        }
        return subTask.setTask_type(taskType);
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        TongLianParam tongLianParam = buildParam(contractChannel, sub, TongLianParam.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            if (!tongLianService.verifyThreeElements(bankAccount)) {
                return ContractResponse.builder()
                        .code(460)
                        .message("三要素验证不通过")
                        .build();
            }
            if (needPayFor(contextParam, sub, contractTask)) {
                return null;
            }
            ContractResponse response = tongLianService.contractMerchant(contextParam, tongLianParam);
            String merchantId = BeanUtil.getPropString(contextParam, "merchant.id");
            updateClearProvider(merchantId, sub.getMerchant_sn());
            return response;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            return tongLianService.contractAlipayWithParams(contextParam, tongLianParam);
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            return tongLianService.contractWeixinWithParams(contextParam, tongLianParam);
        } else if (PaywayEnum.UNIONPAY.getValue().equals(payWay)) {
            return tongLianService.contractUnionpayWithParams(contextParam, tongLianParam);
        }
        return null;
    }

    protected static String hxBankName = "华夏银行";

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        TongLianParam tongLianParam = buildParam(contractChannel, sub, TongLianParam.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            if (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equals(contractTask.getType())) {
                Map bankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
                String bankName = MapUtils.getString(bankAccount, MerchantBankAccount.BANK_NAME);
                /**
                 * 银行业务开通成功以后需要将最新的银行卡同步到间连,此时可能由于三方数据不及时导致三要素校验不通过,所以此时不校验三要素,但是有的银行业务开通的时候没有银行卡,
                 * 所以又很难根据银行卡相关信息判断,因此只能根据设置默认银行卡的特征affect_sub_task_count=0和status=5来判断
                 * @see ContractTaskServiceImpl#createSuccessTask(java.lang.String)
                 */
                final Integer status = contractTask.getStatus();
                final Integer affectSubTaskCount = contractTask.getAffect_sub_task_count();
                if (hxBankName.equals(bankName) || (Objects.equals(status, TaskStatus.SUCCESS.getVal()) && Objects.equals(affectSubTaskCount, 0))) {
                    //华夏银行不做通联的三要素校验
                    //开通银行直连不需要做三要素校验,因此不需要操作
                } else if (!tongLianService.verifyThreeElements(bankAccount)) {
                    return ContractResponse.builder()
                            .code(460)
                            .message("三要素验证不通过")
                            .build();
                }
                if (needPayFor(contextParam, sub, contractTask)) {
                    return null;
                }
            }

            if (ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(sub.getTask_type()) ||
                    ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(sub.getTask_type())) {
                return tongLianService.updateMerchantBankAccount(contextParam, tongLianParam);
            } else if (ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(sub.getTask_type())) {
                //变更营业执照
                return tongLianService.updateMerchantBusinessLicense(contextParam, tongLianParam);
            } else {
                return tongLianService.updateMerchant(contextParam, tongLianParam);
            }


        } else if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
            return tongLianService.updateAlipayWithParams(contextParam, tongLianParam);
        } else if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            return tongLianService.updateWeixinWithParams(contextParam, tongLianParam);
        }
        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        TongLianParam tongLianParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, TongLianParam.class);
        return tongLianService.weixinSubdevConfig(tongLianParam, weixinConfig, true);
    }

    /**
     * 终端/子商户号绑定
     *
     * @param termInfoDTO
     * @param payWay
     * @param terminalSn
     * @return
     */
    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
            response = tongLianService.addAliTermInfo(termInfoDTO, tongLianParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_WX.getValue(), TongLianParam.class);
            response = tongLianService.addWxTermInfo(termInfoDTO, tongLianParam);
        }
        if (payWay == PaywayEnum.UNIONPAY.getValue()) {
            final TlTerminfoinDTO dto = new TlTerminfoinDTO();
            Map terminal = terminalService.getTerminalByTerminalSn(terminalSn);
            final String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
            BeanUtils.copyProperties(termInfoDTO,dto);
            dto.setTerminalId(terminalId);
            dto.setTerminalstate("1");
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_UP.getValue(), TongLianParam.class);
            response = tongLianService.terminfoin(dto,tongLianParam);
        }
        return response;
    }

    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
            response = tongLianService.LogOutAliTermInfo(dto, tongLianParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_WX.getValue(), TongLianParam.class);
            response = tongLianService.LogOutWxTermInfo(dto, tongLianParam);
        }
        if (payWay == PaywayEnum.UNIONPAY.getValue()) {
            final TlTerminfoinDTO tlTerminfoinDTO = new TlTerminfoinDTO();
            Map terminal = terminalService.getTerminalByTerminalSn(terminalSn);
            final String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
            BeanUtils.copyProperties(dto,tlTerminfoinDTO);
            tlTerminfoinDTO.setTerminalId(terminalId);
            tlTerminfoinDTO.setTerminalstate("2");
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_UP.getValue(), TongLianParam.class);
            response = tongLianService.terminfoin(tlTerminfoinDTO,tongLianParam);
        }
        return response;
    }

    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_ALI.getValue(), TongLianParam.class);
            response = tongLianService.updateAliTermInfo(dto, tongLianParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            TongLianParam tongLianParam = contractParamsBiz.buildContractParams(ChannelEnum.TONGLIAN_WX.getValue(), TongLianParam.class);
            response = tongLianService.updateWxTermInfo(dto, tongLianParam);
        }
        return response;
    }


    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal,Integer provider) {
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if(CollectionUtils.isEmpty(params)) {
            return;
        }
        //provider_terminal 不存在该门店的终端记录
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        if(Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbTerminal();
            try {
                sqbTerminalConnectionProviderTerminal(merchantSn,providerTerminalId,provider,vendorAppAppid,terminalSn,storeSn);
                log.info("终端绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}",providerTerminalId,storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e){
                log.error("终端绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{}",providerTerminalId,storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if(CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("终端绑定=>已经存在收钱吧终端Id:{},门店:{},交易参数:{}",providerTerminal.getProvider_terminal_id(),storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider,
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        terminalSn));
    }

    @Override
    public void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal,Integer provider) {
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //provider_terminal 不存在该门店的终端记录
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //无需解绑
        if(Objects.isNull(providerTerminal)) {
            return;
        }
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //判断是否需要插入解绑任务
        final List<MerchantProviderParams> needDeleteMerchantProviderParams = providerTerminalBiz.getNeedDeleteMerchantProviderParams(providerTerminal, params);

        needDeleteMerchantProviderParams.stream().forEach(
                        param -> providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                                param.getPay_merchant_id(),
                                provider,param.getPayway(),
                                ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType(),
                                providerTerminal.getProvider_terminal_id(),
                                providerTerminal.getStore_sn(),
                                terminalSn)
                );
    }



    @Override
    public void handleSqbStoreTerminal(String storeSn,String merchantSn,Integer provider) {
        //provider_terminal 是否存在该门店的终端记录
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if(CollectionUtils.isEmpty(params)) {
            return;
        }
        //不存在则绑定
        if(Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbStore();
            try {
                sqbStoreTerminalConnectionProviderTerminal(merchantSn,providerTerminalId,provider,storeSn);
                log.info("通联门店绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}",providerTerminalId,storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e){
                log.info("通联门店绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{},",providerTerminalId,storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(
                            merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if(CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("门店绑定=>已经存在终端Id:{},门店:{},交易参数:{}",providerTerminal.getProvider_terminal_id(),storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider,param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        null)
        );
    }

    @Override
    public void handleSqbMerchantProviderTerminal(String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if(Objects.isNull(providerTerminal)) {
            //不存在则绑定
            final String termNo = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
            try {
                merchantConnectionProviderTerminal(merchantSn, termNo, provider);
                log.info("通联商户绑定=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider);
            } catch (Exception e){
                log.error("通联商户绑定失败=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                            termNo,
                            null,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if(CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("通联商户绑定=>商户号:{},已经存在商户级别终端,新增子商户号,终端Id:{},交易参数:{}",merchantSn,providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        null,
                        null)
        );
    }


    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn,provider);
    }

    protected void merchantConnectionProviderTerminal(String merchantSn, String termNo, Integer provider){
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn,termNo, acquirerParams.getPay_merchant_id(), provider);
    }

    protected void sqbStoreTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, Integer provider, String storeSn){
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn,providerTerminalId, acquirerParams.getPay_merchant_id(), provider,storeSn);
    }

    protected void sqbTerminalConnectionProviderTerminal(String merchantSn, String providerTerminalId, Integer provider, String vendorAppAppid, String terminalSn, String storeSn){
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_TONGLIAN.getValue());
        providerTerminalBiz.sqbTerminalConnectionProviderTerminal(merchantSn,providerTerminalId, acquirerParams.getPay_merchant_id(), provider,vendorAppAppid,terminalSn,storeSn);
    }

}
