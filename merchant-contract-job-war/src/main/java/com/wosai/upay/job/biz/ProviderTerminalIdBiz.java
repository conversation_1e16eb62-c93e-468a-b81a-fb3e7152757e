package com.wosai.upay.job.biz;

import com.wosai.upay.core.service.SnGenerator;
import com.wosai.upay.job.enume.ProviderTerminalIdTypeEnum;
import com.wosai.upay.job.util.ScaleConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * @Description: 收单机构终端ID获取
 * <AUTHOR>
 * @Date: 2022/3/8 11:05 上午
 */
@Component
@Slf4j
public class ProviderTerminalIdBiz {
    @Autowired
    private SnGenerator snGenerator;

    @Autowired
    private Environment environment;


    /**
     * 生成终端ID  商户级别
     *
     * @return
     */
    public String getProviderTerminalIdByMerchantSn() {
        ProviderTerminalIdTypeEnum type = Arrays.asList(environment.getActiveProfiles()).contains("prod") ?
                ProviderTerminalIdTypeEnum.MERCHANT_PREFIX : ProviderTerminalIdTypeEnum.MERCHANT_TEST_PREFIX;
        return getProviderTerminalId(type.getCode());
    }

    /**
     * 生成终端ID  Haike
     *
     * @return
     */
    public String nextProviderTerminalId() {
        ProviderTerminalIdTypeEnum type = Arrays.asList(environment.getActiveProfiles()).contains("prod") ?
                ProviderTerminalIdTypeEnum.GENERAL_PREFIX : ProviderTerminalIdTypeEnum.GENERAL_TEST_PREFIX;
        String prefix = type.getCode();
        String nextTerminalId = ScaleConverterUtil.encode36(Long.parseLong(snGenerator.nextProviderTerminalId()));
        if (StringUtils.isNotBlank(prefix)) {
            return prefix + nextTerminalId;
        }
        return nextTerminalId;
    }


    /**
     * 生成终端ID  收钱吧终端级别
     * @return
     */
    public String getProviderTerminalIdBySqbTerminal() {
        ProviderTerminalIdTypeEnum type = Arrays.asList(environment.getActiveProfiles()).contains("prod") ?
                ProviderTerminalIdTypeEnum.TERMINAL_PREFIX : ProviderTerminalIdTypeEnum.TERMINAL_TEST_PREFIX;
        return getProviderTerminalId(type.getCode());
    }

    /**
     * 生成终端ID  收钱吧门店级别
     * @return
     */
    public String getProviderTerminalIdBySqbStore() {
        ProviderTerminalIdTypeEnum type = Arrays.asList(environment.getActiveProfiles()).contains("prod") ?
                ProviderTerminalIdTypeEnum.STORE_PREFIX : ProviderTerminalIdTypeEnum.STORE_TEST_PREFIX;
        return getProviderTerminalId(type.getCode());
    }


    /**
     * 获取收单机构终端ID
     * 自增ID 转62进制 生成6位数
     *
     * @param prefix 前缀
     * @return
     */
    private String getProviderTerminalId(String prefix) {
        String nextTerminalId = ScaleConverterUtil.encode(Long.parseLong(snGenerator.nextProviderTerminalId()));
        if (StringUtils.isNotBlank(prefix)) {
            return prefix + nextTerminalId;
        }
        return nextTerminalId;
    }


}
