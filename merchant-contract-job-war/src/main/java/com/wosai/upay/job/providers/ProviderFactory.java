package com.wosai.upay.job.providers;

import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/25
 */
@Component
@Slf4j
public class ProviderFactory {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private RuleContext ruleContext;

    public BasicProvider getProviderByRule(String contractRule) {
        return getProvider(ruleContext.getContractRule(contractRule).getProvider());
    }

    public BasicProvider getProviderByContractRule(ContractRule contractRule) {
        if (contractRule == null) {
            return null;
        }
        return getProvider(contractRule.getProvider());
    }

    public BasicProvider getProvider(String provider) {
        return getProviderByName(convertToBeanName(provider));
    }

    public BasicProvider getProviderByName(String providerName) {
        try {
            return applicationContext.getBean(providerName, BasicProvider.class);
        } catch (BeansException e) {
            log.error("get BasicProvider error", e);
        }
        return null;
    }

    public BasicProvider getProviderByRuleGroupId(String ruleGroupId) {
        ruleGroupId = ruleGroupId.replace("change2", "");
        if (McConstant.RULE_GROUP_LKLORG.equals(ruleGroupId)) {
            ruleGroupId = ProviderUtil.LKL_ORG_PROVIDER_CHANNEL;
        }
        if (McConstant.RULE_GROUP_TONGLIAN_V2_SD.equals(ruleGroupId)) {
            ruleGroupId = ProviderUtil.TONGLIAN_V2_CHANNEL;
        }
        return getProviderByName(ruleGroupId);
    }

    public BasicProvider getProviderByRuleGroupIdAcquirer(String ruleGroupId) {
        RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
        String acquirer = ruleGroup.getAcquirer();
        return getProviderByName(acquirer);
    }

    public String convertToBeanName(String provider) {
        Optional<McProviderDO> mcProvider = mcProviderDAO.getByProvider(provider);
        return mcProvider.map(mcProviderDO -> mcProviderDO.getBeanName() == null ? "unknown" : mcProviderDO.getBeanName()).orElse("unknown");
    }
}
