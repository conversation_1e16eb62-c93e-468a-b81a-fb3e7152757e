package com.wosai.upay.job.biz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.terminal.DataBusTerminal;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.request.CancelFeeRateRequest;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.helper.MyObjectMapper;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantAppConfig;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.model.request.LakalaMerchantConfigRequest;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.consumer.StoreCreateConsumer;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.providers.LklV3Provider;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.dao.DirectStatusDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.task.license.micro.LklV3UpdateTradeParams;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.service.MobilePosServiceImpl;
import com.wosai.upay.job.service.T9ServiceImpl;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.constant.ContractSubTaskTaskType;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.model.direct.GetDevParamReq;
import com.wosai.upay.qrcode.service.QrcodeService;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_FAIL;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_SUCCESS;

/**
 * @Description: lklv3终端写入
 * <AUTHOR>
 * @Date 2021/4/25 4:33 下午
 **/
@Slf4j
@Component
public class LklV3ShopTermBiz {

    @Autowired
    LklV3ShopTermMapper mapper;

    @Autowired
    StoreService storeService;

    @Autowired
    com.wosai.upay.core.service.StoreService cStoreService;

    @Autowired
    ContractSubTaskBiz contractSubTaskBiz;

    @Autowired
    TerminalService terminalService;

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;
    @Autowired
    MerchantService merchantService;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    LklV3Provider lklV3Provider;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    LklV3Service lklV3Service;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    SupportService supportService;
    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;

    @Autowired(required = false)
    StoreCreateConsumer storeCreateConsumer;

    @Autowired
    @Lazy
    MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    @Lazy
    private MobilePosServiceImpl mobilePosService;

    @Autowired
    private SubBizParamsBiz subBizParamsBiz;


    @Autowired
    private FeeRateService feeRateService;

    @Autowired
    private CrmEdgeService crmEdgeService;

    @Autowired
    private ForeignCardMapper foreignCardMapper;

    @Autowired
    private QrcodeService qrcodeService;

    @Autowired
    private CallBackService callBackService;

    @Value("${lklMobilePos.devCode}")
    private String lklMobilePosDevCode;

    @Resource(name = "jsonRedisTemplate")
    private RedisTemplate jsonRedisTemplate;

    @Resource
    protected DirectStatusDAO directStatusDAO;

    public Boolean addMer(String merchantSn, String storeSn, long subTaskId) {
        List<LklV3ShopTerm> lklV3ShopTerms = mapper.selectByMerchantSn(merchantSn);
        if (StringUtil.listEmpty(lklV3ShopTerms)) {
            LklV3ShopTerm update = new LklV3ShopTerm().setId(UUID.randomUUID().toString())
                    .setMerchantSn(merchantSn).setStoreSn(storeSn);
            update.setLklV3TermInfo(Arrays.asList(new LklV3Term().setDevSerialNo(storeSn).setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_WAIT_BIND)));
            mapper.insert(update);
            return true;
        }
        return false;
    }

    public Boolean addShop(String merchantSn, String storeSn, String merInnerNo, Long subTaskId) {
        LklV3ShopTerm lklV3ShopTerm = mapper.selectByStoreSnAndMerInnerNo(storeSn, merInnerNo);
        if (Objects.isNull(lklV3ShopTerm)) {
            LklV3ShopTerm update = new LklV3ShopTerm().setId(UUID.randomUUID().toString())
                    .setMerchantSn(merchantSn).setStoreSn(storeSn).setMerInnerNo(merInnerNo);
            update.setLklV3TermInfo(Arrays.asList(new LklV3Term().setDevSerialNo(storeSn).setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_WAIT_BIND)));
            mapper.insert(update);
            return true;
        }
        return false;
    }

    public Boolean addTerm(String storeSn, String terminalId, Long subTaskId) {
        LklV3ShopTerm lklV3ShopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.nonNull(lklV3ShopTerm)) {
            List<LklV3Term> lklV3TermInfo = lklV3ShopTerm.getLklV3TermInfo();
            lklV3TermInfo.add(new LklV3Term().setDevSerialNo(terminalId).setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_WAIT_BIND));
            lklV3ShopTerm.setLklV3TermInfo(lklV3TermInfo);
            mapper.updateByPrimaryKey(lklV3ShopTerm);
            return true;
        }
        return false;
    }

    /**
     * lklV3进件成功以后创建第一个lklV3门店
     *
     * @param shopId     门店ID
     * @param v3Term     门店终端信息
     * @param id         对应的子任务ID
     * @param merchantSn 商户号
     */
    public void createDefaultStore(String shopId, String merInnerNo, LklV3Term v3Term, Long id, String merchantSn) {
        final StoreInfo store = storeService.getStoreBySn(v3Term.getDevSerialNo(), null);
        if (Objects.isNull(store)) {
            return;
        }
        LklV3ShopTerm lklV3ShopTerm = mapper.selectByStoreSnAndMerInnerNo(v3Term.getDevSerialNo(), merInnerNo);
        if (Objects.isNull(lklV3ShopTerm)) {
            lklV3ShopTerm = mapper.selectByStoreSn(v3Term.getDevSerialNo());
        }
        if (Objects.isNull(lklV3ShopTerm)) {
            //不存在此终端
            LklV3Term term = new LklV3Term()
                    .setTermId(v3Term.getTermId()).setTermNo(v3Term.getTermNo()).setDevSerialNo(v3Term.getDevSerialNo())
                    .setProductCode(v3Term.getProductCode()).setBusiTypeName(v3Term.getBusiTypeName()).setProductName(v3Term.getProductName())
                    .setSubTaskId(id).setStatus(LklV3Term.TERM_SUCCESS);
            final LklV3ShopTerm v3ShopTerm = new LklV3ShopTerm();
            v3ShopTerm.setId(UUID.randomUUID().toString());
            v3ShopTerm.setLklV3TermInfo(Lists.newArrayList(term));
            v3ShopTerm.setMerchantSn(merchantSn);
            v3ShopTerm.setStoreSn(v3Term.getDevSerialNo());
            v3ShopTerm.setShopId(shopId);
            v3ShopTerm.setMerInnerNo(merInnerNo);
            mapper.insert(v3ShopTerm);
        } else {
            List<LklV3Term> lklV3Terms = lklV3ShopTerm.getLklV3TermInfo();
            LklV3Term ori = lklV3Terms.stream().filter(lklV3Term -> lklV3Term.getDevSerialNo().equalsIgnoreCase(v3Term.getDevSerialNo()) && lklV3Term.getSubTaskId().equals(id)).findAny().orElse(null);
            if (Objects.isNull(ori)) {
                //不存在此终端
                LklV3Term update = new LklV3Term()
                        .setTermId(v3Term.getTermId()).setTermNo(v3Term.getTermNo()).setDevSerialNo(v3Term.getDevSerialNo())
                        .setProductCode(v3Term.getProductCode()).setBusiTypeName(v3Term.getBusiTypeName()).setProductName(v3Term.getProductName())
                        .setSubTaskId(id).setStatus(LklV3Term.TERM_SUCCESS);
                lklV3Terms.add(update);
            } else {
                lklV3Terms.remove(ori);
                ori.setTermNo(v3Term.getTermNo()).setTermId(v3Term.getTermId()).setBusiTypeName(v3Term.getBusiTypeName()).setProductCode(v3Term.getProductCode()).setStatus(LklV3Term.TERM_SUCCESS).setProductName(v3Term.getProductName());
                lklV3Terms.add(ori);
            }
            LklV3ShopTerm update = new LklV3ShopTerm().setId(lklV3ShopTerm.getId()).setShopId(shopId);
            update.setLklV3TermInfo(lklV3Terms);
            if (WosaiStringUtils.isEmpty(lklV3ShopTerm.getMerInnerNo())) {
                update.setMerInnerNo(merInnerNo);
            }
            mapper.updateByPrimaryKey(update);
        }
    }

    public Boolean verifyShop(String shopId, LklV3Term term, Long subTaskId, String merInnerNo) {
        LklV3ShopTerm lklV3ShopTerm = mapper.selectByStoreSnAndMerInnerNo(term.getDevSerialNo(), merInnerNo);
        if (Objects.isNull(lklV3ShopTerm)) {
            return false;
        }
        List<LklV3Term> lklV3Terms = lklV3ShopTerm.getLklV3TermInfo();
        LklV3Term ori = lklV3Terms.stream().filter(lklV3Term -> lklV3Term.getDevSerialNo().equalsIgnoreCase(term.getDevSerialNo()) && lklV3Term.getSubTaskId().equals(subTaskId)).findAny().orElse(null);
        if (Objects.isNull(ori)) {
            //不存在此终端
            LklV3Term update = new LklV3Term()
                    .setTermId(term.getTermId()).setTermNo(term.getTermNo()).setDevSerialNo(term.getDevSerialNo())
                    .setProductCode(term.getProductCode()).setBusiTypeName(term.getBusiTypeName()).setProductName(term.getProductName())
                    .setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_SUCCESS);
            lklV3Terms.add(update);
        } else {
            lklV3Terms.remove(ori);
            ori.setTermNo(term.getTermNo()).setTermId(term.getTermId()).setBusiTypeName(term.getBusiTypeName()).setProductCode(term.getProductCode()).setStatus(LklV3Term.TERM_SUCCESS).setProductName(term.getProductName());
            lklV3Terms.add(ori);
        }
        LklV3ShopTerm update = new LklV3ShopTerm().setId(lklV3ShopTerm.getId()).setShopId(shopId);
        update.setLklV3TermInfo(lklV3Terms);
        mapper.updateByPrimaryKey(update);
        //如果是进件则先不绑定门店因为此时还没有微信和支付宝交易参数
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        final Integer taskType = contractSubTask.getTask_type();
        if (Objects.equals(taskType, ContractSubTaskTaskType.SUB_TASK_TASK_TYPE_CONTRACT)) {
            return true;
        }
        //终端绑定259文件改造
        CompletableFuture.runAsync(() -> lklV3Provider.handleSqbStoreTerminal(term.getDevSerialNo(), lklV3ShopTerm.getMerchantSn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> processMicroLicenseUpgradeCondition(subTaskId));
        return true;
    }

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    public void processMicroLicenseUpgradeCondition(Long subTaskId) {
        Optional<ContractSubTaskDO> subTaskOpt = contractSubTaskDAO.getByPrimaryKey(subTaskId);
        if (!subTaskOpt.isPresent()) {
            return;
        }
        Optional<ContractTaskDO> mainTaskOpt = contractTaskDAO.getByPrimaryKey(subTaskOpt.get().getPTaskId());
        if (!mainTaskOpt.isPresent()) {
            return;
        }
        ContractTaskDO contractTaskDO = mainTaskOpt.get();
        JSONObject contextMap = JSON.parseObject(contractTaskDO.getEventContext());
        if (!org.apache.commons.lang3.StringUtils.equals(
                MapUtils.getString(contextMap, LklV3UpdateTradeParams.MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_KEY),
                LklV3UpdateTradeParams.MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_VALUE
        )) {
            return;
        }
        log.info("商户{},  任务{}, 门店sn{}, 小微升级新增门店成功，开始对该门店下所有终端进件", contractTaskDO.getMerchantSn(), contractTaskDO.getId(), MapUtils.getString(contextMap, CommonModel.STORE_SN));
        String storeSn = MapUtils.getString(contextMap, CommonModel.STORE_SN);
        String merchantSn = contractTaskDO.getMerchantSn();
        insertTerminalContractTaskByStore(storeSn, merchantSn);
    }

    private void insertTerminalContractTaskByStore(String storeSn, String merchantSn) {
        // todo 一体化刷卡的不需要绑定和报备了
        List<Map> terminals = merchantBasicInfoBiz.listAllSqbTerminalsByStoreSn(storeSn);
        if (org.springframework.util.CollectionUtils.isEmpty(terminals)) {
            log.warn("商户{}, 门店sn{}, 门店下没有终端", merchantSn, storeSn);
            return;
        }
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn);
        for (Map terminal : terminals) {
            try {
                log.info("商户{},门店{},终端{}, 新增拉卡拉终端进件任务", merchantSn, storeSn, org.apache.commons.collections4.MapUtils.getString(terminal, "sn"));
                insertContractTerminalTask(terminal, merchantInfo);
            } catch (Exception e) {
                log.error("商户{},终端{},新增拉卡拉终端任务失败", merchantSn, org.apache.commons.collections4.MapUtils.getString(terminal, "sn"), e);
            }
        }
    }

    private void insertContractTerminalTask(Map terminal, MerchantInfo merchantInfo) {
        TerminalBasicInsertEvent event = new TerminalBasicInsertEvent();
        DataBusTerminal dataBusTerminal = new DataBusTerminal();
        dataBusTerminal.setType(org.apache.commons.collections4.MapUtils.getInteger(terminal, "type"));
        dataBusTerminal.setStoreId(org.apache.commons.collections4.MapUtils.getString(terminal, "store_id"));
        dataBusTerminal.setMerchantId(merchantInfo.getId());
        event.setTerminalId(org.apache.commons.collections4.MapUtils.getString(terminal, "id"));
        event.setData(dataBusTerminal);
        lklV3Provider.produceInsertTerminalTaskByRule(event, merchantInfo);
    }


    public Boolean verifyTerm(String shopId, LklV3Term term, Long subTaskId) {
        LklV3ShopTerm original = mapper.selectByShopId(shopId);
        if (Objects.isNull(original)) {
            return false;
        }
        List<LklV3Term> terms = original.getLklV3TermInfo();
        LklV3Term ori = terms.stream()
                .filter(x -> subTaskId.equals(x.getSubTaskId()) && term.getDevSerialNo().equalsIgnoreCase(x.getDevSerialNo()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(ori)) {
            // 数据库中不存在此终端信息
            Map terminal = terminalService.getTerminalByTerminalId(term.getDevSerialNo());
            StoreInfo store = storeService.getStoreById(MapUtils.getString(terminal, Terminal.STORE_ID), devCode);
            if (Objects.nonNull(store) && store.getSn().equalsIgnoreCase(original.getStoreSn())) {
                LklV3Term update = new LklV3Term()
                        .setTermId(term.getTermId()).setTermNo(term.getTermNo()).setDevSerialNo(term.getDevSerialNo())
                        .setProductCode(term.getProductCode()).setBusiTypeName(term.getBusiTypeName()).setProductName(term.getProductName())
                        .setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_SUCCESS);
                terms.add(update);
            } else {
                return false;
            }
        } else {
            terms.remove(ori);
            ori.setTermId(term.getTermId()).setTermNo(term.getTermNo()).setProductCode(term.getProductCode()).setBusiTypeName(term.getBusiTypeName()).setStatus(LklV3Term.TERM_SUCCESS).setProductName(term.getProductName());
            terms.add(ori);
        }
        LklV3ShopTerm updateShopTerm = new LklV3ShopTerm().setId(original.getId());
        updateShopTerm.setLklV3TermInfo(terms);
        mapper.updateByPrimaryKey(updateShopTerm);

        //终端绑定259文件改造
        final ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        final MerchantInfo merchant = merchantService.getMerchantBySn(subTask.getMerchant_sn(), null);
        Map terminal = terminalService.getTerminalByTerminalId(term.getDevSerialNo());
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //部分终端只需要报一次
        if (!applicationApolloConfig.getTerminalLevelVendorAppAppid().contains(vendorAppAppid)) {
            return true;
        }
        CompletableFuture.runAsync(() -> lklV3Provider.handleSqbTerminalBind(merchant, terminal, ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
        return true;
    }

    /**
     * 简易智能pos处理
     *
     * @param shopId
     * @param terms
     * @param subTaskId
     * @return
     */
    public Boolean verifySimpleSuperPos(String shopId, List<Map> terms, Long subTaskId) {
        LklV3ShopTerm original = mapper.selectByShopId(shopId);
        if (Objects.isNull(original)) {
            return false;
        }
        //本地数据终端
        List<LklV3Term> originalTerms = Optional.ofNullable(original.getLklV3TermInfo()).orElseGet(ArrayList::new);
        //拉卡拉返回终端数据
        final List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = JSONObject.parseArray(JSONObject.toJSONString(terms), QueryLklV3MerchantResponse.RespData.TermData.class);
        final QueryLklV3MerchantResponse.RespData.TermData termData = termDataList.get(0);
        final String devSerialNo = termData.getDevSerialNo();

        Map terminal = terminalService.getTerminalByTerminalId(devSerialNo);
        StoreInfo storeInfo = storeService.getStoreById(MapUtils.getString(terminal, Terminal.STORE_ID), devCode);
        //当前终端所在门店是否与本地记录是同一个门店
        final boolean present = Optional.ofNullable(storeInfo).map(store -> store.getSn()).filter(sn -> Objects.equals(sn, original.getStoreSn())).isPresent();
        if (!present) {
            return false;
        }
        //将拉卡拉返回的终端信息保存
        final List<LklV3Term> collect = originalTerms.stream().filter(x -> subTaskId.equals(x.getSubTaskId()) && devSerialNo.equalsIgnoreCase(x.getDevSerialNo())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            originalTerms.removeAll(collect);
        }
        //将拉卡拉返回的数据存入
        termDataList.stream().forEach(data -> {
            final LklV3Term lklV3Term = new LklV3Term();
            BeanUtils.copyProperties(data, lklV3Term);
            lklV3Term.setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_SUCCESS);
            originalTerms.add(lklV3Term);
        });
        LklV3ShopTerm updateShopTerm = new LklV3ShopTerm().setId(original.getId());
        updateShopTerm.setLklV3TermInfo(originalTerms);
        mapper.updateByPrimaryKey(updateShopTerm);

        //普通的终端还是需要 终端绑定259文件改造
        final String merchantSn = original.getMerchantSn();
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //部分终端只需要报一次
        if (!applicationApolloConfig.getTerminalLevelVendorAppAppid().contains(vendorAppAppid)) {
            return true;
        }
        CompletableFuture.runAsync(() -> lklV3Provider.handleSqbTerminalBind(merchant, terminal, ProviderEnum.PROVIDER_LAKALA_V3.getValue()));
        //TODO 保存银行卡交易参数 等待交易组提供
        //收钱吧终端Id
        final String terminalId = BeanUtil.getPropString(terminal, "id");
        //拉卡拉返回的用于刷银行卡的终端ID
        final Optional<QueryLklV3MerchantResponse.RespData.TermData> bankCardTerm = termDataList.stream().filter(term -> Objects.equals(term.getBusiTypeCode(), "BANK_CARD")).findFirst();
        final String termId = bankCardTerm.get().getTermId();
        final String termNo = bankCardTerm.get().getTermNo();
        if (StringUtils.isEmpty(termId)) {
            log.info("verifyA8PosTerm subTaskId:{},没有找到对应的拉卡拉终端ID", subTaskId);
            return true;
        }
        try {
            final Map terminalConfigParams = CollectionUtil.hashMap("terminal_id", terminalId, "payway", PaywayEnum.BANK_CARD.getValue(), "c2b_agent_name", "1034_21_false_true_0001", "c2b_status", MerchantConfig.STATUS_OPENED
                    , "provider", ProviderEnum.PROVIDER_LKL_OPEN.getValue(), "params", CollectionUtil.hashMap("lakala_open_trade_params", CollectionUtil.hashMap("term_id", termId, "term_no", termNo)));
            final Map map = tradeConfigService.updateLklOpenTerminalConfig(terminalConfigParams);
            log.info("updateLklOpenTerminalConfig result :{}", map);
            final String storeSn = storeInfo.getSn();
            final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
            //插入未绑定记录,并生成任务查询绑定状态
            providerTerminalBiz.initLklBankCardTermNoTask(merchant.getSn(), termNo, vendorAppAppid, storeSn, terminalSn);
            supportService.removeCachedParams(merchant.getSn());
        } catch (Exception exception) {
            log.error("updateLklOpenTerminalConfig  subTaskId:{},terminalId:{},exception:{}", subTaskId, terminalId, exception);
        }
        return true;
    }


    /**
     * 手机pos处理
     *
     * @param shopId
     * @param terms
     * @param subTaskId
     * @return
     */
    public Boolean verifyMobilePos(String shopId, List<Map> terms, Long subTaskId) {
        LklV3ShopTerm original = mapper.selectByShopId(shopId);
        if (Objects.isNull(original)) {
            return false;
        }

        List<LklV3Term> originalTerms = Optional.ofNullable(original.getLklV3TermInfo()).orElseGet(ArrayList::new);
        List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = parseTermDataList(terms);
        QueryLklV3MerchantResponse.RespData.TermData termData = termDataList.get(0);
        String devSerialNo = termData.getDevSerialNo();

        updateOriginalTerms(originalTerms, subTaskId, devSerialNo);
        saveTermDataList(originalTerms, termDataList, subTaskId);

        LklV3ShopTerm updateShopTerm = new LklV3ShopTerm().setId(original.getId());
        updateShopTerm.setLklV3TermInfo(originalTerms);
        mapper.updateByPrimaryKey(updateShopTerm);

        Optional<QueryLklV3MerchantResponse.RespData.TermData> mobilePosData = findMobilePosData(termDataList);
        if (!mobilePosData.isPresent() || StringUtils.isEmpty(mobilePosData.get().getTermId())) {
            log.info("verifyMobilePos subTaskId:{},没有找到对应的拉卡拉终端ID", subTaskId);
            return true;
        }

        try {
            afterOpenMobilePos(original, mobilePosData.get());
        } catch (Exception exception) {
            log.error("verifyMobilePos merchantAppConfig subTaskId:{},exception:{}", subTaskId, exception);
        }
        return true;
    }

    public List<QueryLklV3MerchantResponse.RespData.TermData> parseTermDataList(List<Map> terms) {
        return JSONObject.parseArray(JSONObject.toJSONString(terms), QueryLklV3MerchantResponse.RespData.TermData.class);
    }

    private void updateOriginalTerms(List<LklV3Term> originalTerms, Long subTaskId, String devSerialNo) {
        List<LklV3Term> collect = originalTerms.stream()
                .filter(x -> subTaskId.equals(x.getSubTaskId()) && devSerialNo.equalsIgnoreCase(x.getDevSerialNo()))
                .collect(Collectors.toList());
        if (!collect.isEmpty()) {
            originalTerms.removeAll(collect);
        }
    }

    private void saveTermDataList(List<LklV3Term> originalTerms, List<QueryLklV3MerchantResponse.RespData.TermData> termDataList, Long subTaskId) {
        termDataList.forEach(data -> {
            LklV3Term lklV3Term = new LklV3Term();
            BeanUtils.copyProperties(data, lklV3Term);
            lklV3Term.setSubTaskId(subTaskId).setStatus(LklV3Term.TERM_SUCCESS);
            originalTerms.add(lklV3Term);
        });
    }

    private Optional<QueryLklV3MerchantResponse.RespData.TermData> findMobilePosData(List<QueryLklV3MerchantResponse.RespData.TermData> termDataList) {
        return termDataList.stream()
                .filter(term -> StrUtil.equals(term.getProductCode(), MobilePosServiceImpl.PRODUCT_CODE, Boolean.TRUE) &&
                        StrUtil.equals(term.getBusiTypeCode(), MobilePosServiceImpl.BUSI_TYPE_CODE, Boolean.TRUE))
                .findFirst();
    }

    private void afterOpenMobilePos(LklV3ShopTerm original, QueryLklV3MerchantResponse.RespData.TermData mobilePosData) {
        String termId = mobilePosData.getTermId();
        String termNo = mobilePosData.getTermNo();
        String merchantSn = original.getMerchantSn();
        MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        //在crm中将记录变更为成功
        mobilePosService.createOrUpdateBizOpenInfo(merchantSn, AppInfoModel.STATUS_SUCCESS, null);

        Map mobilePosMap = Optional.ofNullable(tradeConfigService.getMerchantAppConfigByMerchantIdAndPaywayAndApp(merchant.getId(), PaywayEnum.BANK_CARD.getValue(), subBizParamsBiz.getMobilePosAppId()))
                .orElseGet(HashMap::new);
        Map params = MapUtils.getMap(mobilePosMap, MerchantAppConfig.PARAMS);

        //修改merchantAppConfig
        if (StringUtils.isEmpty(BeanUtil.getPropString(mobilePosMap, DaoConstants.ID))) {
            createMerchantAppConfig(merchant, termId, termNo);
        } else {
            updateMerchantAppConfig(mobilePosMap, params, termId, termNo);
        }

        log.info("verifyMobilePos merchantAppConfig 参数写入成功 merchantSn:{}", merchantSn);
        supportService.removeCachedParams(merchant.getSn());
        //设置套餐
        applyFeeRate(merchantSn);
        //写入sub_biz_params中
        subBizParamsBiz.updateSubBizParams(merchantSn, subBizParamsBiz.getMobilePosAppId(), ProviderEnum.PROVIDER_LAKALA_V3.getValue(), new MerchantProviderParams());
        //手机POS置为成功
        final ForeignCard foreignCard = foreignCardMapper.selectByMerchantSnAndCode(merchantSn, lklMobilePosDevCode);
        if (Objects.nonNull(foreignCard)) {
            foreignCardMapper.updateByPrimaryKeySelective(new ForeignCard().setId(foreignCard.getId()).setStatus(ForeignCard.STATUS_SUCCESS));
        }
        //手机POS开通成功也认为是一种外卡开通成功
        String key = String.format(T9ServiceImpl.FOREIGN_CARD_OPEN_FLAG, merchantSn);
        jsonRedisTemplate.opsForValue().set(key, Boolean.TRUE, T9ServiceImpl.EXPIRATION_DURATION, T9ServiceImpl.EXPIRATION_TIME_UNIT);
    }

    /**
     * 重新写入merchant_app_config,sub_biz_params和费率
     *
     * @param shopId
     * @param shopTermList
     */
    public void reSetMobilePosParams(String shopId, List<Map> shopTermList) {
        LklV3ShopTerm original = mapper.selectByShopId(shopId);
        if (Objects.isNull(original)) {
            return;
        }
        final List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = parseTermDataList(shopTermList);

        Optional<QueryLklV3MerchantResponse.RespData.TermData> mobilePosData = findMobilePosData(termDataList);
        if (!mobilePosData.isPresent() || StringUtils.isEmpty(mobilePosData.get().getTermId())) {
            log.info("reImportAppConfig shopId:{} 没有找到对应的拉卡拉终端ID", shopId);
            return;
        }
        //写入收钱吧相关参数
        afterOpenMobilePos(original, mobilePosData.get());
    }

    private void createMerchantAppConfig(MerchantInfo merchant, String termId, String termNo) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchant.getSn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        Map<String, Object> merchantAppConfig = CollectionUtil.hashMap(
                MerchantAppConfig.MERCHANT_ID, merchant.getId(),
                MerchantAppConfig.PAYWAY, PaywayEnum.BANK_CARD.getValue(),
                MerchantAppConfig.APP_ID, subBizParamsBiz.getMobilePosAppId(),
                MerchantAppConfig.B2C_STATUS, 0,
                MerchantAppConfig.C2B_STATUS, 1,
                MerchantAppConfig.C2B_FEE_RATE, "0",
                MerchantAppConfig.C2B_AGENT_NAME, "1034_21_false_true_0001",
                MerchantAppConfig.WAP_STATUS, 0,
                MerchantAppConfig.MINI_STATUS, 0,
                MerchantAppConfig.APP_STATUS, 0,
                MerchantAppConfig.H5_STATUS, 0,
                MerchantAppConfig.PROVIDER, ProviderEnum.PROVIDER_LKL_OPEN.getValue(),
                MerchantAppConfig.PARAMS, CollectionUtil.hashMap("lakala_open_trade_params",
                        CollectionUtil.hashMap("merc_id", acquirerParams.getProvider_merchant_id(), "term_id", termId, "term_no", termNo))
        );
        tradeConfigService.createMerchantAppConfig(merchantAppConfig);
    }

    private void updateMerchantAppConfig(Map mobilePosMap, Map params, String termId, String termNo) {
        Map<String, Object> newAppConfig = new HashMap<>();
        Map<String, Object> newParams = new HashMap<>(params);
        newAppConfig.put(DaoConstants.ID, BeanUtil.getPropString(mobilePosMap, DaoConstants.ID));
        BeanUtil.setNestedProperty(newParams, "lakala_open_trade_params.term_id", termId);
        BeanUtil.setNestedProperty(newParams, "lakala_open_trade_params.term_no", termNo);
        newAppConfig.put(MerchantAppConfig.PARAMS, newParams);
        tradeConfigService.updateMerchantAppConfig(newAppConfig);
    }

    private void applyFeeRate(String merchantSn) {
        Map<String, String> applyFeeRateMap = CollectionUtil.hashMap(
                String.valueOf(PaywayEnum.BANK_CARD.getValue()), "0"
        );
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setTradeComboId(applicationApolloConfig.getMobilePosTradeComboId())
                .setAuditSn("手机POS开通")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(applyFeeRateMap);
        try {
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
        } catch (Exception e) {
            log.error("手机POS开通设置费率merchantSn:{}", merchantSn, e);
        }
    }


    public Boolean unbindTerm(String storeSn, String terminalId) {
        LklV3ShopTerm original = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(original)) {
            return false;
        }
        List<LklV3Term> terms = original.getLklV3TermInfo();
        final Map terminal = terminalService.getTerminal(terminalId);
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //简易智能新pos返回两个终端号两个终端在本地的记录都要变成解绑成功
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        Boolean simpleSuperPos = simpleSuperPosList.contains(vendorAppAppid);
        if (simpleSuperPos) {
            final List<LklV3Term> simpleTermList = terms.stream().filter(term -> terminalId.equalsIgnoreCase(term.getDevSerialNo())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(simpleTermList)) {
                return false;
            }
            terms.remove(simpleTermList);
            simpleTermList.stream().forEach(a8term -> a8term.setStatus(LklV3Term.TERM_UNBIND));
            terms.addAll(simpleTermList);
        } else {
            LklV3Term updateTerm = terms.stream().filter(term -> terminalId.equalsIgnoreCase(term.getDevSerialNo())).findAny().orElseGet(null);
            if (Objects.isNull(updateTerm) || !updateTerm.getDevSerialNo().equalsIgnoreCase(terminalId)) {
                return false;
            }
            terms.remove(updateTerm);
            updateTerm.setStatus(LklV3Term.TERM_UNBIND);
            terms.add(updateTerm);
        }
        LklV3ShopTerm update = new LklV3ShopTerm().setId(original.getId());
        update.setLklV3TermInfo(terms);
        mapper.updateByPrimaryKey(update);

        final String merchantSn = original.getMerchantSn();
        final MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        lklV3Provider.handleSqbTerminalUnBind(merchantInfo, terminal, ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        //非简易智能pos不用删除银行卡交易参数
        if (!simpleSuperPos) {
            return true;
        }
        final Map terminalConfigParams = CollectionUtil.hashMap(
                "terminal_id", terminalId,
                "payway", PaywayEnum.BANK_CARD.getValue(),
                "b2c_status", MerchantConfig.STATUS_CLOSED,
                "c2b_status", MerchantConfig.STATUS_CLOSED,
                "c2b_fee_rate", null,
                "c2b_agent_name", null,
                "wap_status", MerchantConfig.STATUS_CLOSED,
                "mini_status", MerchantConfig.STATUS_CLOSED,
                "app_status", MerchantConfig.STATUS_CLOSED,
                "h5_status", MerchantConfig.STATUS_CLOSED,
                "provider", ProviderEnum.PROVIDER_LKL_OPEN.getValue(),
                "params", Maps.newHashMap()
        );
        tradeConfigService.updateLklOpenTerminalConfig(terminalConfigParams);
        supportService.removeCachedParams(merchantSn);
        return true;
    }

    /**
     * 获取lkl终端号
     *
     * @param storeSn
     * @param terminalId
     * @return
     */
    public String getTermNo(String storeSn, String terminalId) {
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(shopTerm)) {
            return null;
        }
        List<LklV3Term> terms = shopTerm.getLklV3TermInfo();
        LklV3Term term = terms.stream().filter(x -> terminalId.equals(x.getDevSerialNo())).limit(1).collect(Collectors.toList()).get(0);
        if (Objects.isNull(term)) {
            return null;
        }
        return term.getTermNo();
    }

    public Map findFirstStore(String merchantId) {
        ListResult list = cStoreService.getStoreListByMerchantId(merchantId,
                new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC))),
                Maps.newHashMap()
        );
        if (Objects.nonNull(list) && list.getRecords().size() > 0) {
            return list.getRecords().get(0);
        } else {
            return null;
        }
    }

    /**
     * 获取商户入网任务的状态
     *
     * @param merchantSn
     * @return null 不存在依赖，即已入网成功
     * not null 存在依赖，返回contractId, 即入网的subtaskId
     */
    public Tuple2<Integer, Long> merchantContract(String merchantSn) {
        ContractSubTask acquirerSubTask = contractSubTaskBiz.getAcquirerSubTask(merchantSn, AcquirerTypeEnum.LKL_V3.getValue());
        // 存量商户入网时候channel是lkl
        if (acquirerSubTask == null) {
            acquirerSubTask = contractSubTaskBiz.getAcquirerSubTask(merchantSn, AcquirerTypeEnum.LKL.getValue());
        }
        if (acquirerSubTask == null) {
            return null;
        }
        if (Objects.equals(TaskStatus.SUCCESS.getVal(), acquirerSubTask.getStatus())) {
            return new Tuple2<>(1, acquirerSubTask.getId());
        }
        return new Tuple2<>(0, acquirerSubTask.getId());
    }

    /**
     * 查看门店状态
     *
     * @param storeSn 门店号码
     * @return null 没有门店记录
     * Integer 1->已绑定成功； 0->暂未绑定成功
     * Long 绑定任务的subTaskId
     */
    public Tuple2<Integer, Long> shopContract(String storeSn) {
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(shopTerm)) {
            return null;
        }
        LklV3Term lklV3Term = shopTerm.getLklV3TermInfo().stream()
                .filter(x -> storeSn.equalsIgnoreCase(x.getDevSerialNo()))
                .findAny()
                .orElse(null);
        if (Objects.isNull(lklV3Term)) {
            return null;
        }
        final Long subTaskId = lklV3Term.getSubTaskId();
        if (!StringUtils.isEmpty(shopTerm.getShopId())) {
            return new Tuple2<>(1, subTaskId);
        }
        //对于历史数据门店联系电话不符合拉卡拉格式会有问题
        //所以对于历史调用增网的时候直接报错"三代进件增终失败(网点联系人手机号格式校验失败[028****8888]"则需要重新调度起来
        final Optional<ContractSubTaskDO> contractSubTaskDO = contractSubTaskDAO.getByPrimaryKey(subTaskId);
        if (contractSubTaskDO.isPresent()
                && Objects.equals(contractSubTaskDO.get().getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_FAIL.getValue())
                && contractSubTaskDO.get().getResponseBody().contains("手机号格式校验失败")) {
            final ContractSubTaskDO subTaskDO = contractSubTaskDO.get();
            final Long taskId = subTaskDO.getPTaskId();
            contractTaskMapper.updateStatusById(ContractTaskProcessStatusEnum.WAIT_PROCESS.getValue(), taskId);
            contractSubTaskMapper.updateStatusById(ContractSubTaskProcessStatusEnum.WAIT_PROCESS.getValue(), subTaskId);
            return new Tuple2<>(0, subTaskId);
        }
        //实时LklV3查询一下
        final List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = getLklV3Term(shopTerm.getMerchantSn());
        if (CollectionUtils.isEmpty(termDataList)) {
            return new Tuple2<>(0, subTaskId);
        }
        //已经审核通过
        final QueryLklV3MerchantResponse.RespData.TermData term = termDataList.stream().filter(termData -> Objects.equals(termData.getDevSerialNo(), storeSn))
                .findAny().orElseGet(QueryLklV3MerchantResponse.RespData.TermData::new);
        if (StringUtils.isEmpty(term.getShopId())) {
            return new Tuple2<>(0, subTaskId);
        }
        final List<LklV3Term> newTermList = shopTerm.getLklV3TermInfo().stream().map(shop -> {
            if (Objects.equals(shop.getDevSerialNo(), term.getDevSerialNo())) {
                BeanUtils.copyProperties(term, shop);
            }
            return shop;
        }).collect(Collectors.toList());
        //修改本地的信息
        shopTerm.setLklV3TermInfo(newTermList);
        shopTerm.setShopId(term.getShopId());
        mapper.updateByPrimaryKey(shopTerm);
        return new Tuple2<>(1, subTaskId);
    }


    /**
     * 根据商户号实时查询门店审核状态
     *
     * @param merchantSn
     * @return
     */
    public List<QueryLklV3MerchantResponse.RespData.TermData> getLklV3Term(String merchantSn) {
        //lklV3商户号
        final String payMerchantId = Optional.ofNullable(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_LAKALA_V3.getValue())).map(MerchantProviderParams::getPay_merchant_id)
                .orElse(null);
        //还没有进件成功
        if (StringUtils.isEmpty(payMerchantId)) {
            return Lists.newArrayList();
        }
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        final Map map = lklV3Service.queryMerchant(payMerchantId, null, lklV3Param);
        final QueryLklV3MerchantResponse lklV3MerchantResponse = JSONObject.parseObject(JSONObject.toJSONString(map), QueryLklV3MerchantResponse.class);
        final List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = Optional.ofNullable(lklV3MerchantResponse)
                .map(response -> lklV3MerchantResponse.getRespData())
                .map(respData -> respData.getTermData()).orElseGet(ArrayList::new);
        return termDataList;
    }

    /**
     * 查看终端状态
     *
     * @param storeSn
     * @param terminalId
     * @return
     */
    public LklV3Term getTermInfo(String storeSn, String terminalId) {
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(shopTerm)) {
            return null;
        }
        List<LklV3Term> lklV3TermInfo = shopTerm.getLklV3TermInfo();
        return lklV3TermInfo.stream().filter(x -> terminalId.equalsIgnoreCase(x.getDevSerialNo())).findAny().orElse(null);
    }

    /**
     * 获取该商户下所有终端
     *
     * @param merchantSn
     * @return
     */
    public List<LklV3Term> getUnbindLklV3Term(String merchantSn) {
        List<LklV3ShopTerm> lklV3ShopTerms = mapper.selectByMerchantSn(merchantSn);
        if (Objects.isNull(lklV3ShopTerms)) {
            return null;
        }
        ArrayList<LklV3Term> unbindTerm = new ArrayList<>();
        lklV3ShopTerms.forEach(lklV3ShopTerm -> {
            List<LklV3Term> lklV3Terms = lklV3ShopTerm.getLklV3TermInfo();
            List<LklV3Term> filter = lklV3Terms.stream().filter(lklV3Term -> LklV3Term.TERM_UNBIND.equalsIgnoreCase(lklV3Term.getStatus())).collect(Collectors.toList());
            if (!StringUtil.listEmpty(filter)) {
                unbindTerm.addAll(filter);
            }
        });
        return unbindTerm;
    }

    public String getShopId(String storeSn) {
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.nonNull(shopTerm)) {
            return shopTerm.getShopId();
        }
        return null;
    }


    public MutablePair<String, List<LklV3Term>> getShopTerm(String storeSn) {
        final MutablePair<String, List<LklV3Term>> pair = new MutablePair<>();
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        if (Objects.isNull(shopTerm)) {
            return null;
        }
        pair.setLeft(shopTerm.getShopId());
        pair.setRight(shopTerm.getLklV3TermInfo());
        return pair;
    }

    /**
     * 获取银行卡激活码信息
     *
     * @param terminalSn
     * @return
     */
    public LklV3BankTerm getBankCardTermData(String terminalSn) {
        LklV3BankTerm posTerm = new LklV3BankTerm();
        Map terminal = terminalService.getTerminalByTerminalSn(terminalSn);
        final String terminalId = MapUtils.getString(terminal, DaoConstants.ID);
        if (StringUtils.isEmpty(terminalId)) {
            return posTerm;
        }
        StoreInfo store = storeService.getStoreById(MapUtils.getString(terminal, Terminal.STORE_ID), devCode);
        //当前是不是在lklV3
        final String merchantId = store.getMerchant_id();
        final MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
        List<AcquirerMerchantDto> acquirerList = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件拉卡拉
        boolean match = acquirerList.parallelStream().anyMatch(info -> info.getAcquirer().contains(AcquirerTypeEnum.LKL_V3.getValue()));
        if (!match) {
            log.info("getBankCardTermData商户:{},没有成功进件拉卡拉", merchantId);
            return posTerm;
        }
        posTerm = getTermActiveInfo(terminal, terminalId, store, merchant);
        return posTerm;
    }

    @Nullable
    public LklV3BankTerm getTermActiveInfo(Map terminal, String terminalId, StoreInfo store, MerchantInfo merchant) {
        LklV3BankTerm posTerm = new LklV3BankTerm();
        final List<LklV3Term> v3TermList = Optional.ofNullable(selectLklV3ShopTermByStoreSn(store.getSn()))
                .map(v3ShopTerm -> v3ShopTerm.getLklV3TermInfo()).orElseGet(ArrayList::new);
        final Optional<LklV3Term> bankTerm = v3TermList.stream().filter(term ->
                Objects.equals(term.getDevSerialNo(), terminalId) && Objects.equals(term.getBusiTypeCode(), "BANK_CARD")
        ).findFirst();
        //存在
        if (bankTerm.isPresent()) {
            final LklV3Term term = bankTerm.get();
            BeanUtils.copyProperties(term, posTerm);
            return posTerm;
        }
        //重新同步拉卡拉门店这个逻辑里会判断存在就不开通
        storeCreateConsumer.syncLklStore(merchant.getSn(), store.getSn());
        //重新绑定终端
        lklV3ShopTermBiz.bindLklTerminal(terminal, new MyObjectMapper().convertValue(merchant, Map.class));
        return posTerm;
    }


    /**
     * 拉卡拉银行卡费率
     *
     * @param bankcardFee
     * @return
     */
    public List<Map> getLklBankFeeList(Map bankcardFee) {
        final List<Map> feeRatesV3 = Lists.newArrayList();
        //贷记卡
        final Map creditMap = MapUtils.getMap(bankcardFee, "credit");
        final String creditFee = BeanUtil.getPropString(creditMap, "fee");
        //借记卡
        final Map debitMap = MapUtils.getMap(bankcardFee, "debit");
        final String debitFee = BeanUtil.getPropString(debitMap, "fee");
        final String debitMaxFee = BeanUtil.getPropString(debitMap, "max");
        //银联借记卡	300
        final Map lklV3DebitFee = CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "300", LakalaConstant.FEERATE_PCT, debitFee);
        //*************** 代表手续费无封顶
        if (org.apache.commons.lang3.StringUtils.isNotBlank(debitMaxFee) && !Objects.equals("***************", debitMaxFee)) {
            lklV3DebitFee.put("feeUpperAmtPcnt", debitMaxFee);
        }
        feeRatesV3.add(lklV3DebitFee);
        //银联贷记卡	301
        feeRatesV3.add(CollectionUtil.hashMap(LakalaConstant.FEERATE_TYPECODE, "301", LakalaConstant.FEERATE_PCT, creditFee));
        return feeRatesV3;
    }


    public CommonResult queryLklTermTask(String deviceFingerprint) {
        final Map terminal = terminalService.getTerminalByDeviceFingerprint(deviceFingerprint);
        final String storeId = MapUtils.getString(terminal, Terminal.STORE_ID);
        final StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final Tuple2<Integer, Long> shopContract = shopContract(storeSn);
        LklV3ShopTerm shopTerm = selectLklV3ShopTermByStoreSn(storeSn);
        final Long subTaskId;
        //门店创建失败
        if (Objects.nonNull(shopContract) && Objects.equals(shopContract.get_1(), 0)) {
            subTaskId = shopTerm.getLklV3TermInfo().stream().filter(x -> storeSn.equalsIgnoreCase(x.getDevSerialNo())).findAny().map(ter -> ter.getSubTaskId()).orElse(0L);
        } else {
            //创建终端失败
            subTaskId = Optional.ofNullable(shopTerm).map(term -> term.getLklV3TermInfo()).orElseGet(ArrayList::new)
                    .stream().filter(x -> MapUtils.getString(terminal, DaoConstants.ID).equalsIgnoreCase(x.getDevSerialNo()))
                    .findAny().map(ter -> ter.getSubTaskId()).orElse(0L);
        }
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(Optional.ofNullable(contractSubTask).orElseGet(ContractSubTask::new).getP_task_id());
        final boolean present = Optional.ofNullable(contractTask).filter(task -> Objects.equals(task.getStatus(), 5)).isPresent();
        if (present) {
            return new CommonResult(CommonResult.SUCCESS, "终端报备拉卡拉成功");
        }
        final String failReason = Optional.ofNullable(contractTask).filter(x -> Objects.equals(BeanUtil.getPropString(x.getEventContext(), "store_sn"), storeSn))
                .map(fail -> BeanUtil.getPropString(JSONObject.parseObject(fail.getResult(), Map.class), "result")).orElse("未知错误");
        //TODO 文案转义
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager("crm_msg", failReason, ErrorCodeManageBiz.PLATFORM_POS_ERROR_MESSAGE);
        return new CommonResult(CommonResult.BIZ_FAIL, errorInfo.getMsg());
    }

    /**
     * 关闭手机业务
     *
     * @param merchantSn
     */
    public void closeMobilePos(String merchantSn) {
        final MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn, null);
        final String mobilePosAppId = subBizParamsBiz.getMobilePosAppId();
        //删除本地记录
        subBizParamsBiz.deleteParamsByTradeAppId(merchantSn, mobilePosAppId);
        //删除手机POS记录
        final ForeignCard foreignCard = Optional.ofNullable(foreignCardMapper.selectSuccessByMerchantSnAndCode(merchantSn, lklMobilePosDevCode))
                .orElseGet(ForeignCard::new);
        if (Objects.nonNull(foreignCard.getId())) {
            foreignCardMapper.deleteByPrimaryKey(foreignCard.getId());
        }
        directStatusDAO.deleteByMerchantSnAndDevCode(merchantSn, lklMobilePosDevCode);
        //不存在已经删除了,所以不需要取消
        List<Map<String, Object>> appConfigList = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchant.getId(), mobilePosAppId);
        if (CollectionUtils.isEmpty(appConfigList)) {
            return;
        }
        //取消套餐
        CancelFeeRateRequest rateRequest = new CancelFeeRateRequest();
        rateRequest.setMerchantSn(merchantSn);
        rateRequest.setTradeComboId(applicationApolloConfig.getMobilePosTradeComboId());
        rateRequest.setAuditSn("关闭手机POS业务取消套餐");
        feeRateService.cancelFeeRate(rateRequest);
        //取消手机业务业务交易参数
        appConfigList.stream().forEach(appConfig -> tradeConfigService.deleteMerchantAppConfig(BeanUtil.getPropString(appConfig, "id")));
        //删除缓存
        supportService.removeCachedParams(merchantSn);
    }


    /**
     * 创建终端绑定任务
     *
     * @param terminal
     * @param merchant
     */
    public void bindLklTerminal(Map terminal, Map merchant) {
        final String merchantId = BeanUtil.getPropString(terminal, Terminal.MERCHANT_ID);
        final String terminalId = BeanUtil.getPropString(terminal, DaoConstants.ID);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        List<AcquirerMerchantDto> acquirerList = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件拉卡拉
        boolean match = acquirerList.parallelStream().anyMatch(info -> info.getAcquirer().contains(AcquirerTypeEnum.LKL_V3.getValue()));
        if (!match) {
            log.info("bindLklTerminal商户:{},没有成功进件拉卡拉", merchantSn);
            return;
        }
        String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, devCode);
        final String storeSn = store.getSn();
        Tuple2<Integer, Long> shopContract = shopContract(storeSn);
        if (Objects.isNull(shopContract) || !Objects.equals(shopContract.get_1(), 1)) {
            log.info("bindLklTerminal merchantSn:{} storeSn:{},terminalId:{},对应拉卡拉网点不存在", merchantSn, storeSn, terminalId);
            return;
        }
        LklV3Term termInfo = getTermInfo(storeSn, terminalId);
        if (Objects.nonNull(termInfo) && Objects.equals(termInfo.getStatus(), LklV3Term.TERM_SUCCESS)) {
            log.info("bindLklTerminal storeSn:{},terminalId:{},对应的终端已经绑定成功", storeSn, terminalId);
            return;
        }
        //增终任务已经存在,等待审核
        if (Objects.nonNull(termInfo) && Objects.equals(termInfo.getStatus(), LklV3Term.TERM_WAIT_BIND)) {
            log.info("bindLklTerminal storeSn:{},terminalId:{},对应的终端正在审核", storeSn, terminalId);
            return;
        }

        Long auditId = (Long) BeanUtil.getProperty(terminal, "auditId", null);

        Map context = CollectionUtil.hashMap(
                CommonModel.STORE_SN, storeSn,
                "terminalId", terminalId,
                ParamContextBiz.MERCHANT_FEE_RATES, lklV3Provider.getFeeRate(merchantId)
        );
        if (Objects.nonNull(auditId)) {
            context.put("auditId", auditId);
        }
        //终端校验
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        //简易智能新pos
        List<String> simpleSuperPosList = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        Boolean simpleSuperPos = simpleSuperPosList.contains(vendorAppAppid);
        if (simpleSuperPos) {
            //产品说绑定一定在银行卡业务开通以后,所以可以拿到绑定银行卡pos的费率
            final ApiRequestParam param = new ApiRequestParam();
            final GetDevParamReq devParamReq = new GetDevParamReq();
            devParamReq.setDevCode("GF7GNKL9O48N");
            devParamReq.setMerchantId(merchantId);
            param.setBodyParams(devParamReq);
            final Map devParam = crmEdgeService.getDevParam(param);
            final Map feeMap = (Map) devParam.get("feeMap");
            log.info("商户号merchantSn:{},终端Id:{},绑定拉卡拉银行卡终端费率:{}", merchantSn, terminalId, JSONObject.toJSONString(feeMap));
            if (!CollectionUtils.isEmpty(feeMap)) {
                //银行卡费率
                final List<Map> bankFeeList = getLklBankFeeList(feeMap);
                //原有费率
                final List<Map> merchantFeeRates = (List<Map>) context.get(ParamContextBiz.MERCHANT_FEE_RATES);
                merchantFeeRates.addAll(bankFeeList);
                context.put(ParamContextBiz.MERCHANT_FEE_RATES, merchantFeeRates);
            }
        }
        ContractTask task = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME))
                .setStatus(0)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                .setAffect_status_success_task_count(0)
                .setAffect_sub_task_count(1)
                .setEvent_context(JSON.toJSONString(context));
        contractTaskBiz.insert(task);
        ContractSubTask subTask = new ContractSubTask()
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_TERM)
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setMerchant_sn(merchantSn)
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setSchedule_dep_task_id(shopContract.get_2())
                .setSchedule_status(shopContract.get_1())
                .setP_task_id(task.getId());
        contractSubTaskMapper.insert(subTask);
        addTerm(storeSn, terminalId, subTask.getId());
    }

    /**
     * 根据门店号查询终端报备信息：默认查询自己收单机构商户号的
     *
     * @param storeSn 门店号
     * @return 终端报备信息
     */
    public LklV3ShopTerm selectLklV3ShopTermByStoreSn(String storeSn) {
        List<LklV3ShopTerm> lklV3ShopTerms = mapper.selectListByStoreSn(storeSn);
        if (WosaiCollectionUtils.isEmpty(lklV3ShopTerms)) {
            return null;
        }
        if (lklV3ShopTerms.size() == 1) {
            return lklV3ShopTerms.get(0);
        }
        StoreInfo storeInfo = storeService.getStoreBySn(storeSn, null);
        MerchantInfo merchantInfo = merchantService.getMerchantById(storeInfo.getMerchant_id(), null);
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantInfo.getSn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
        return lklV3ShopTerms.stream().filter(lklV3ShopTerm -> Objects.equals(lklV3ShopTerm.getMerInnerNo(), acquirerParams.getPay_merchant_id())).findFirst().orElse(null);
    }

    /**
     * 根据门店编号删除拉卡拉V3门店终端信息
     *
     * @param sn 门店编号，用于标识特定的门店终端
     */
    public void deleteLklV3ShopTerm(String sn) {
        // 根据门店编号查询拉卡拉V3门店终端信息
        final LklV3ShopTerm lklV3ShopTerm = selectLklV3ShopTermByStoreSn(sn);
        // 如果查询结果非空，即存在对应的门店终端信息
        if (Objects.nonNull(lklV3ShopTerm)) {
            // 记录删除操作的日志信息，包括门店号和ID
            log.info("deleteLklV3ShopTerm门店号:{},id{},拉卡拉审核失败,删除拉卡拉门店信息", sn, lklV3ShopTerm.getId());
            // 通过主键删除拉卡拉V3门店终端信息
            mapper.deleteByPrimaryKey(lklV3ShopTerm.getId());
        }
    }

    /**
     * apple pay 外卡终端处理
     *
     * @param shopId
     * @param terms
     * @param subTask
     * @return
     */
    public Boolean verifyApplePayTerm(String shopId, List<Map> terms, ContractSubTask subTask, String merCupNo, Long auditId) {
        LklV3ShopTerm original = mapper.selectByShopId(shopId);
        if (Objects.isNull(original)) {
            return false;
        }

        MerchantInfo merchant = merchantService.getMerchantBySn(subTask.getMerchant_sn(), null);
        Long subTaskId = subTask.getId();

        List<LklV3Term> originalTerms = Optional.ofNullable(original.getLklV3TermInfo()).orElseGet(ArrayList::new);
        List<QueryLklV3MerchantResponse.RespData.TermData> termDataList = parseTermDataList(terms);
        QueryLklV3MerchantResponse.RespData.TermData termData = termDataList.get(0);
        String devSerialNo = termData.getDevSerialNo();

        updateOriginalTerms(originalTerms, subTaskId, devSerialNo);
        saveTermDataList(originalTerms, termDataList, subTaskId);

        LklV3ShopTerm updateShopTerm = new LklV3ShopTerm().setId(original.getId());
        updateShopTerm.setLklV3TermInfo(originalTerms);
        mapper.updateByPrimaryKey(updateShopTerm);

        Optional<QueryLklV3MerchantResponse.RespData.TermData> applePayTerm = termDataList.stream()
                .filter(term -> StrUtil.equals(term.getProductCode(), "B2B_CASHIER_DESK_ONLINE", Boolean.TRUE) &&
                        StrUtil.equals(term.getBusiTypeCode(), "ONLINE_WILD_CARD", Boolean.TRUE))
                .findFirst();

        if (!applePayTerm.isPresent() || StringUtils.isEmpty(applePayTerm.get().getTermNo())) {
            log.info("verifyApplePayTerm subTaskId {} 没有找到对应的拉卡拉终端ID", subTaskId);
            return true;
        }

        try {
            // 保存交易参数
            LakalaMerchantConfigRequest request = new LakalaMerchantConfigRequest();
            request.setMerchantId(merchant.getId());
            request.setPayway(7);
            request.setAgentName("1034_7_*_false_false_0004");
            request.setProviderMchId(merCupNo);
            request.setProviderTerminalNo(applePayTerm.get().getTermNo());
            tradeConfigService.updateLakalaApplePayParams(request);

            // 打标签方便门店码展示
            qrcodeService.saveMerchantTag(CollectionUtil.hashMap(
                    "merchant_id", merchant.getId(),
                    "key", "TAG_APPLE_PAY",
                    "value", JSON.toJSONString(CollectionUtil.hashMap(
                            "enable", true,
                            "provider", "lkl"
                    ))
            ));

            if (Objects.nonNull(auditId)) {
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(Long.valueOf(auditId))
                        .message("开通成功")
                        .resultType(AUDIT_EXECUTE_SUCCESS)
                        .build();
                callBackService.addComment(callBackBean);
            }

        } catch (Exception exception) {
            log.error("verifyApplePayTerm merchantAppConfig subTaskId {}", subTaskId, exception);
            if (Objects.nonNull(auditId)) {
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(Long.valueOf(auditId))
                        .message("开通失败 " + exception.getMessage())
                        .resultType(AUDIT_EXECUTE_FAIL)
                        .build();
                callBackService.addComment(callBackBean);
            }
        }
        return true;
    }
}