package com.wosai.upay.job.refactor.biz.acquirer.lklv3.model;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;


/**
 * lklV3进件结果
 *
 * <AUTHOR>
 * @date 2024/9/9 15:44
 */
@Data
public class LklV3ContractResultDTO {

    public final static Integer CONTRACT_NOT_FINISHED = 1;

    public final static Integer CONTRACT_FAILED = 2;

    public final static Integer CONTRACT_SUCCESS = 3;

    private Map<String, Object> requestMap;

    private Map<String, Object> responseMap;

    private String acquirerMerchantId;

    private String unionMerchantId;

    private String termId;

    private String shopId;


    public boolean isFinished() {
        return !Objects.equals(getContractResultStatus(), CONTRACT_NOT_FINISHED);
    }

    public boolean isNotFinished() {
        return Objects.equals(getContractResultStatus(), CONTRACT_NOT_FINISHED);
    }

    // todo 这里应该校验商户号和终端号信息不能为空
    public boolean isContractSuccess() {
        return Objects.equals(getContractResultStatus(), CONTRACT_SUCCESS);
    }

    public boolean isContractFailed() {
        return isFinished() && Objects.equals(getContractResultStatus(), CONTRACT_FAILED);
    }

    public Integer getContractResultStatus() {
        if (Objects.isNull(responseMap)) {
            return CONTRACT_NOT_FINISHED;
        }
        if (!StringUtils.equals("000000", MapUtils.getString(responseMap, LakalaConstant.RETCODE))) {
            return CONTRACT_NOT_FINISHED;
        }
        String contractStatus = StringUtils.isBlank(BeanUtil.getPropString(responseMap, "data.contractStatus"))
                ? BeanUtil.getPropString(responseMap, "respData.contractStatus")
                : BeanUtil.getPropString(responseMap, "data.contractStatus");
        if (com.wosai.upay.job.service.LakalaCallBackServiceImpl.NO_COMMIT.equalsIgnoreCase(contractStatus)
                || com.wosai.upay.job.service.LakalaCallBackServiceImpl.MANUAL_AUDIT.equalsIgnoreCase(contractStatus)
                || com.wosai.upay.job.service.LakalaCallBackServiceImpl.COMMIT.equalsIgnoreCase(contractStatus)
                || com.wosai.upay.job.service.LakalaCallBackServiceImpl.REVIEW_ING.equalsIgnoreCase(contractStatus)) {
            return CONTRACT_NOT_FINISHED;
        } else if (com.wosai.upay.job.service.LakalaCallBackServiceImpl.COMMIT_FAIL.equalsIgnoreCase(contractStatus)
                || com.wosai.upay.job.service.LakalaCallBackServiceImpl.INNER_CHECK_REJECTED.equalsIgnoreCase(contractStatus)) {
            return CONTRACT_FAILED;

        } else if (com.wosai.upay.job.service.LakalaCallBackServiceImpl.WAIT_FOR_CONTRACT.equalsIgnoreCase(contractStatus)) {
            return CONTRACT_SUCCESS;
        }
        return CONTRACT_NOT_FINISHED;
    }


}
