package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wosai.upay.job.refactor.mapper.ContractSubTaskExtMapper;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskExtDO;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * 子任务扩展表数据库访问层 {@link ContractSubTaskExtDO}
 * 对ContractSubTaskMapper层做出简单封装 {@link ContractSubTaskExtMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ContractSubTaskExtDAO extends AbstractBaseDAO<ContractSubTaskExtDO, ContractSubTaskExtMapper> {

    public ContractSubTaskExtDAO(SqlSessionFactory sqlSessionFactory, ContractSubTaskExtMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据筛选条件,获取子任务信息
     *
     * @param subTaskId 子任务编号
     * @return 子任务
     */
    public Optional<ContractSubTaskExtDO> getBySubTaskId(Long subTaskId) {
        LambdaQueryWrapper<ContractSubTaskExtDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .eq(ContractSubTaskExtDO::getSubTaskId, subTaskId);
        return super.selectOne(lambdaQueryWrapper);
    }


}
