package com.wosai.upay.job.handlers;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 国通入网
 *
 * <AUTHOR>
 * @date 2025-02-06 10:56:36
 */
@Component
@Order(90)
@Slf4j
public class GuotongContractSubTaskHandler extends AbstractSubTaskHandler {

    @Autowired
    RuleContext ruleContext;


    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByName(subTask.getChannel());
        ContractResponse response = provider.processTaskByRule(task,
                ruleContext.getContractRule(subTask.getContract_rule()).getContractChannel(),
                subTask);
        if (Objects.isNull(response)) {
            log.info("merchantSn {} subTask {} channelName {} processTask return null", task.getMerchant_sn(), subTask.getId(), subTask.getChannel());
            return;
        }
        handleResult(response, subTask);
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        if (ChannelEnum.GUOTONG.getValue().equalsIgnoreCase(subTask.getChannel())
                && (Objects.isNull(subTask.getPayway()) || Objects.equals(subTask.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                && (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE.equals(subTask.getTask_type()))
        ) {
            return true;
        }
        return false;
    }

}