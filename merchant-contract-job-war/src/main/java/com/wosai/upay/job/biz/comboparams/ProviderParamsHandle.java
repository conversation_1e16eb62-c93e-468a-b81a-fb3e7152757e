package com.wosai.upay.job.biz.comboparams;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description:不同通道对应配置params
 * <AUTHOR>
 * Date 2020/6/3 4:17 下午
 **/
public abstract class ProviderParamsHandle {

    @Autowired
    TradeConfigService tradeConfigService;


    public static String SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY = "provider_trade_params_key";


    /**
     * @return
     * <AUTHOR>
     * @Description:能否处理这个merchantConfigParams
     * @time 5:18 下午
     **/
    protected abstract boolean accept(MerchantConfigParams merchantConfigParams);

    /**
     * @return
     * <AUTHOR>
     * @Description:处理参数
     * @time 5:19 下午
     **/
    protected Map handle(MerchantConfigParams configParams) {
        Map merchantConfig = getMerchantConfig(configParams.getMerchantId(), configParams.getPayWay());
        Map updateInfo = CollectionUtil.hashMap(
                DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID),
                MerchantConfig.PROVIDER, configParams.getProvider()
        );
        //装配更新字段
        decorateUpdateInfo(updateInfo, merchantConfig, configParams);
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName(SYSTEM_CONFIG_NAME_PROVIDER_TRADE_PARAMS_KEY);
        String paramsKey = BeanUtil.getPropString(providerTradeParamsKey, configParams.getProvider() + "");
        //清空设置到大商户下面的子商户号配置 留待删除
        Map params = (Map) merchantConfig.get(MerchantConfig.PARAMS);
        removeMethodICannotAbandon(params, providerTradeParamsKey, updateInfo);
        Map merchantConfigTradeParams = getConfigParams(configParams);
        if (merchantConfigTradeParams == null) {
            throw new CommonPubBizException("构建交易参数失败");
        }
        params.put(paramsKey, merchantConfigTradeParams);
        updateInfo.put(MerchantConfig.PARAMS, params);
        return updateInfo;
    }


    private void decorateUpdateInfo(Map updateInfo, Map source, MerchantConfigParams configParams) {
        setAgentColumns(updateInfo, configParams.getAgentName(), configParams.getAgentColumns());
        setFeeRate(updateInfo, configParams.getFee(), configParams.getFeeColumns());
        Map params = (Map) source.get(MerchantConfig.PARAMS);
        if (Objects.isNull(params)) {
            params = new HashMap();
        }
        setHuabeiStatus(params, configParams.getPayWay(), configParams.getHuaBeiStatus());
        source.put(MerchantConfig.PARAMS, params);
    }


    /**
     * @return
     * <AUTHOR>
     * @Description:支付宝花呗默认开启
     * @time 5:58 下午
     **/
    private void setHuabeiStatus(Map params, int payWay, int huaBeiStatus) {
        if (payWay == PaywayEnum.ALIPAY.getValue() && MerchantConfig.STATUS_OPENED == huaBeiStatus) {
            params.put(TransactionParam.ALIPAY_HUABEI_STATUS, MerchantConfig.STATUS_OPENED);
        }
    }

    protected abstract Map getConfigParams(MerchantConfigParams configParams);


    /**
     * 查询商户当前在用的交易参数配置
     */
    private Map getMerchantConfig(String merchantId, int payWay) {
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, payWay);
        if (merchantConfig == null) {
            merchantConfig = initMerchantConfig(merchantId, payWay);
        }
        return merchantConfig;
    }


    /**
     * 设置费率值
     **/
    private void setFeeRate(Map updateInfo, String fee, List<String> feeColumns) {
        if (StringUtils.isEmpty(fee)) {
            return;
        }
        feeColumns.forEach(f -> {
            updateInfo.put(f, fee);
        });
    }


    /**
     * 一个可能会被废弃的方法
     */
    private void removeMethodICannotAbandon(Map params, Map<String, Object> providerTradeParamsKey, Map updateInfo) {
        if (!CollectionUtils.isEmpty(params)) {
            if (!CollectionUtils.isEmpty(providerTradeParamsKey)) {
                for (String providerKey : providerTradeParamsKey.keySet()) {
                    //lakala 比较特殊，它还负责结算
                    if (!providerKey.equals(ProviderEnum.PROVIDER_LAKALA.getValue() + "")) {
                        params.remove(providerTradeParamsKey.get(providerKey));
                    }
                }
            }
            updateInfo.put(MerchantConfig.PARAMS, params);
        }
    }


    /**
     * @return
     * <AUTHOR>
     * @Description:设置需要更新agent_name COL列名
     * @time 3:37 下午
     **/
    private void setAgentColumns(Map update, String agentName, List<String> agentColumns) {
        for (String agentColumn : agentColumns) {
            update.put(agentColumn, agentName);
        }
    }


    /**
     * @return
     * <AUTHOR>
     * @Description:创建merchant_config
     * @time 3:39 下午
     **/
    private Map initMerchantConfig(String merchantId, int payway) {
        Map merchantConfig = getCreateMerchantConfig(merchantId, payway);
        return tradeConfigService.createMerchantConfig(merchantConfig);
    }


    private Map getCreateMerchantConfig(String merchantId, int payway) {
        return CollectionUtil.hashMap(
                MerchantConfig.PAYWAY, payway,
                MerchantConfig.MERCHANT_ID, merchantId,
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.PARAMS, new HashMap<>()
        );
    }

}
