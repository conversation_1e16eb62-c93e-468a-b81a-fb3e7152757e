package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.WeixinDirectApply;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WeixinDirectApplyMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WeixinDirectApply record);

    int insertSelective(WeixinDirectApply record);

    WeixinDirectApply selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WeixinDirectApply record);

    int updateByPrimaryKeyWithBLOBs(WeixinDirectApply record);

    int updateByPrimaryKey(WeixinDirectApply record);

    WeixinDirectApply selectProcessApplyByMerchantSn(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode);

    WeixinDirectApply selectLatestApplyByMerchantSn(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode);

    WeixinDirectApply selectLatestFailedApplyByMerchantSn(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode, @Param("submitType") Integer submitType);

    List<WeixinDirectApply> selectFailedApplyByMerchantSn(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode);

    List<WeixinDirectApply> getAppliesByPriorityAndStatus(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("statuses") List<Integer> statuses, @Param("limit") Integer limit);

    int submitResult(@Param("id") Long id, @Param("request") String requestBody, @Param("response") String responseBody, @Param("result") String result, @Param("status") Integer status);

    void updateStatus(@Param("id") Long id, @Param("status") Integer status, @Param("message") Object o);

    WeixinDirectApply selectApplyByTaskId(long taskId);
}