package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;

import java.util.List;

public interface SelfOpenCcbDecpMapper {
    int deleteByPrimaryKey(Long id);

    int insert(SelfOpenCcbDecp record);

    int insertSelective(SelfOpenCcbDecp record);

    SelfOpenCcbDecp selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SelfOpenCcbDecp record);

    int updateByPrimaryKeyWithBLOBs(SelfOpenCcbDecp record);

    int updateByPrimaryKey(SelfOpenCcbDecp record);

    SelfOpenCcbDecp selectByMerchantSn(String merchantSn);

    void cancelSelfOpenCcbDecp(Long id, long mtime);

    List<SelfOpenCcbDecp> selectProcessByMtime(long mtime);

    void updateOpenStatusById(Long id, int openStatus);

    List<SelfOpenCcbDecp> selectSuccessByMtime(long start, long end);
}