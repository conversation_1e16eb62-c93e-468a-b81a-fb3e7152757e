package com.wosai.upay.job.refactor.event;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.union.UnionWxMchInfo;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.ComposeService;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.wosai.upay.job.model.PayParamsModel.PROVIDER_HAIKE;

/**
 * 海科银联商户号要等云闪付报备成功后才返回，所以要给AT补更新up_merchant_id
 *
 * <AUTHOR>
 * @date 2025/5/22
 */
@Component
@Slf4j
public class HaikeUnionPayContractSuccessEventListener implements ApplicationListener<HaikeUnionPayContractSuccessEvent> {

    private static final List<Integer> AT_PAYWAYS = Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue());

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private ComposeService composeService;

    @Autowired
    private NewUnionService newUnionService;

    @Override
    public void onApplicationEvent(HaikeUnionPayContractSuccessEvent event) {
        merchantProviderParamsDAO.listByMerchantSn(event.getMerchantSn())
                .stream()
                .filter(p -> Objects.equals(p.getProvider(), PROVIDER_HAIKE) && AT_PAYWAYS.contains(p.getPayway()))
                .forEach(param -> {
                    if (Objects.equals(PaywayEnum.ALIPAY.getValue(), param.getPayway())) {
                        handleAliPay(param);
                    } else if (Objects.equals(PaywayEnum.WEIXIN.getValue(), param.getPayway())) {
                        handleWeixin(param);
                    }
                });


    }

    private void handleAliPay(MerchantProviderParamsDO param) {
        CompletableFuture.runAsync(() -> {
                    try {
                        Map mchInfo = newUnionService.queryAlySubMch(param.getPayMerchantId());
                        String upMerchantId = BeanUtil.getPropString(mchInfo, "up_merchant_id");
                        if (WosaiStringUtils.isEmpty(upMerchantId)) {
                            ContractResponse response = blueSeaService.updateAliMchInfoBySubMchId(param.getPayMerchantId(), new HashMap());
                            log.info("{} {} 更新成功 {}", param.getMerchantSn(), param.getPayMerchantId(), response.getMessage());
                        }
                    } catch (Exception e) {
                        log.warn("{} {} 更新失败", param.getMerchantSn(), param.getPayMerchantId(), e);
                    }
                }
        );
    }

    private void handleWeixin(MerchantProviderParamsDO param) {
        CompletableFuture.runAsync(() -> {
                    try {
                        UnionWxMchInfo unionWxMchInfo = newUnionService.queryUnionWxMchInfo(param.getPayMerchantId());
                        if (WosaiStringUtils.isEmpty(unionWxMchInfo.getUp_merchant_id())) {
                            ContractResponse response = composeService.updateWxMchInfoBySubMchId(param.getPayMerchantId(), new HashMap());
                            log.info("{} {} 更新成功 {}", param.getMerchantSn(), param.getPayMerchantId(), response.getMessage());
                        }
                    } catch (Exception e) {
                        log.warn("{} {} 更新失败", param.getMerchantSn(), param.getPayMerchantId(), e);
                    }
                }
        );
    }

}
