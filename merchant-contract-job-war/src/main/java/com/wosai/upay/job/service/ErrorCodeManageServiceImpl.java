package com.wosai.upay.job.service;

import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.response.ErrorInfoPromptTextRspDTO;
import com.wosai.upay.scene.service.activity.response.SceneConfigRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 错误码管理服务
 *
 * <AUTHOR>
 * @date 2024/7/22 11:32
 */
@AutoJsonRpcServiceImpl
@Service
public class ErrorCodeManageServiceImpl implements ErrorCodeManageService{

    @Resource
    private ErrorCodeManageBiz errorCodeManageBiz;

    /**
     * 根据原始错误信息获取对接平台的提示文案
     *
     * @param viewEndpoint 文案要被哪端(crm,spa,app,消费者端)看到
     * @param originalMsg  原始错误信息
     * @param platform     原始错误信息来自哪一个对接平台
     * @return 提示文案
     */
    @Override
    public ErrorInfo getPromptMessage(String viewEndpoint, String originalMsg, String platform) {
        return errorCodeManageBiz.getPromptMessageFromErrorCodeManager(viewEndpoint, originalMsg, platform);
    }

    /**
     * 根据原始错误信息获取对接平台的提示文案
     *
     * @param originalMsg 原始错误信息
     * @param platform    原始错误信息来自哪一个对接平台
     * @return 提示文案列表
     */
    @Override
    public List<ErrorInfoPromptTextRspDTO> getAllEndPointPromptMessage(String originalMsg, String platform) {
        SceneConfigRecord sceneConfigRecord = errorCodeManageBiz.getSceneConfigRecord(originalMsg, platform);
        if (Objects.isNull(sceneConfigRecord)) {
            return Collections.emptyList();
        }
        List<ErrorInfoPromptTextRspDTO> rspList = Lists.newArrayList();
        rspList.add(new ErrorInfoPromptTextRspDTO(ErrorMsgViewEndpointTypeEnum.SPA, sceneConfigRecord.getSpaTipsZh()));
        rspList.add(new ErrorInfoPromptTextRspDTO(ErrorMsgViewEndpointTypeEnum.CRM, sceneConfigRecord.getCrmTipsZh()));
        rspList.add(new ErrorInfoPromptTextRspDTO(ErrorMsgViewEndpointTypeEnum.APP, sceneConfigRecord.getToBTipsZh()));
        rspList.add(new ErrorInfoPromptTextRspDTO(ErrorMsgViewEndpointTypeEnum.CONSUMER, sceneConfigRecord.getToCTipsZh()));
        return rspList;
    }

}
