package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MerchantProviderParamsExtMapper {

    int insertSelective(MerchantProviderParamsExt record);

    MerchantProviderParamsExt selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MerchantProviderParamsExt record);

    @Select("select ext_field_1 from merchant_provider_params_ext where param_id=#{paramId} and type=#{type} order by create_at desc limit 1")
    String getField1ByParamId(@Param("paramId") String paramId, @Param("type") int type);

    @Select("select * from merchant_provider_params_ext where param_id=#{paramId} and type=#{type} order by create_at desc limit 1")
    MerchantProviderParamsExt getByParamId(@Param("paramId") String paramId, @Param("type") int type);

    @Select("select * from merchant_provider_params_ext where param_id=#{paramId} and type = 4")
    MerchantProviderParamsExt getUnionOpen(@Param("paramId") String paramId);

}