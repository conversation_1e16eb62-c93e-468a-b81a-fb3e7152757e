package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.*;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import lombok.Data;

/**
 * 内部调度任务子表表实体对象
 *
 * <AUTHOR>
 */
@TableName("internal_schedule_sub_task")
@Data
public class InternalScheduleSubTaskDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 主任务id
     */
    @TableField(value = "main_task_id")
    private Long mainTaskId;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 子任务类型
     */
    @TableField(value = "task_type")
    private String taskType;
    /**
     * 任务类型
     */
    @TableField(value = "type")
    @Deprecated
    private Integer type;
    /**
     * 状态标识
     */
    @TableField(value = "status_mark")
    private String statusMark;
    /**
     * 任务状态 0-待处理 1-处理中 2-等待外部结果 3-重试 4-处理失败 5-处理成功
     * 默认0
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 优先级
     */
    @TableField(value = "priority")
    private Integer priority;
    /**
     * 是否影响主任务状态 0-否 1-是 默认 1
     */
    @TableField(value = "affect_main_task_status")
    private Integer affectMainTaskStatus;
    /**
     * 依赖的子任务id(依赖的任务执行成功方可调度)
     */
    @TableField(value = "depend_on_sub_task_id")
    private Long dependOnSubTaskId;
    /**
     * 任务上下文信息,json类型,每个type对应的json格式固定
     */
    @TableField(value = "context")
    private String context;
    /**
     * 请求报文
     */
    @TableField(value = "request_message")
    private String requestMessage;
    /**
     * 返回报文
     */
    @TableField(value = "response_message")
    private String responseMessage;
    /**
     * 处理结果信息
     */
    @TableField(value = "result")
    private String result;
    /**
     * 备注信息
     */
    @TableField(value = "remark")
    private String remark;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 结算通道
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 是否可被调度 0-不可调度 1-可调度
     */
    @TableField(value = "enable_scheduled_status")
    private Integer enableScheduledStatus;
    /**
     * 失败是否重试 0-不重试 1-重试
     */
    @TableField(value = "enable_retry_status")
    private Integer enableRetryStatus;
    /**
     * 最大重试次数
     */
    @TableField(value = "max_retry_num")
    private Integer maxRetryNum;
    /**
     * 已经重试次数
     */
    @TableField(value = "already_retry_num")
    private Integer alreadyRetryNum;
    /**
     * 版本号
     */
    @TableField(value = "version")
    private Integer version;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Timestamp mtime;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Timestamp ctime;

    public boolean isProcessSuccess() {
        return Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue());
    }

    public boolean isProcessFail() {
        return Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue());
    }

    /**
     * 是否待处理
     *
     * @return true-待处理
     */
    public boolean isWaitProcess() {
        return Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
    }

    /**
     * 是否等待外部结果中
     *
     * @return true-等待外部结果中
     */
    public boolean isWaitExternalResult() {
        return Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT.getValue());
    }

    public boolean isTaskCanNotBeHandle() {
        return Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS.getValue())
                || Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue())
                || Objects.equals(this.status, InternalScheduleSubTaskStatusEnum.BEING_PROCESSING.getValue());
    }

    public void updateStatusFailed(String result) {
        this.status = InternalScheduleSubTaskStatusEnum.PROCESS_FAIL.getValue();
        this.result = result;
    }

    public void setResult(String result) {
        int maxLength = 200;
        if (Objects.isNull(result)) {
            this.result = null;
            return;
        }
        if (result.length() > maxLength) {
            this.result = result.substring(0, maxLength);
            return;
        }
        this.result = result;
    }

}

