package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.app.dto.V2.UcUserInfo;
import com.wosai.app.service.v2.UcUserAccountService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.mapper.CcbDecpMerchantMapper;
import com.wosai.upay.job.mapper.OpenCcbDecpMapper;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.CcbDecpMerchant;
import com.wosai.upay.job.model.DO.OpenCcbDecp;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import com.wosai.upay.job.model.DecpParamResp;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.OpenCcbDecpConstant;
import com.wosai.upay.merchant.contract.model.ccb.request.OpenCcbDecpReq;
import com.wosai.upay.merchant.contract.model.provider.CcbParam;
import com.wosai.upay.merchant.contract.service.CcbService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.util.*;

import static javax.management.timer.Timer.ONE_DAY;

/**
 * <AUTHOR>
 * @date 2021/12/17
 */
@Slf4j
@Component
public class SelfOpenCcbDecpBiz {

    @Autowired
    private CcbService ccbService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private UcUserAccountService ucUserAccountService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService licenseService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private CcbDecpMerchantMapper ccbDecpMerchantMapper;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Autowired
    private OpenCcbDecpMapper openCcbDecpMapper;

    @Autowired
    private Environment environment;

    @Value("${mail-gateway}")
    private String mailGateway;

    private static final String SEND_DECP_MAIL = "send_decp_mail";

    public DecpParamResp getOpenDecpParam(String merchantId, String ucUserId) {
        DecpParamResp decpParamResp = new DecpParamResp();
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        // 商户名
        decpParamResp.setMerchantName(BeanUtil.getPropString(merchant, Merchant.NAME));
        // 商户经营名
        decpParamResp.setBusinessName(BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME));
        // 客户姓名
        decpParamResp.setHolder(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER));
        // 证件号
        decpParamResp.setIdNumber(getDesensitizeIdentity(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY)));
        SelfOpenCcbDecp selfOpenCcbDecp = selfOpenCcbDecpMapper.selectByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
        // 获取
        if (selfOpenCcbDecp != null) {
            OpenCcbDecpReq openCcbDecpReq = JSON.parseObject(selfOpenCcbDecp.getRequest_body(), OpenCcbDecpReq.class);
            // 请求参数不是空，并且不是待开通(可能异名换卡了) 获取请求建行的手机号设置进去 否则取商户信息的联系人手机号
            if (openCcbDecpReq != null && selfOpenCcbDecp.getOpen_status() != SelfOpenCcbDecp.WAIT_OPEN_STATUS) {
                decpParamResp.setPhoneNumber(openCcbDecpReq.getMrchCtcPsnMblPhNo());
            } else {
                UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(ucUserId);
                decpParamResp.setPhoneNumber(ucUserInfo.getCellphone());
            }
        } else {
            UcUserInfo ucUserInfo = ucUserAccountService.getUcUserById(ucUserId);
            decpParamResp.setPhoneNumber(ucUserInfo.getCellphone());
        }
        return decpParamResp;
    }

    public DecpParamResp getSuccessDecpParam(String merchantId) {
        DecpParamResp decpParamResp = new DecpParamResp();
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        // 商户名
        decpParamResp.setMerchantName(BeanUtil.getPropString(merchant, Merchant.NAME));
        // 商户经营名
        decpParamResp.setBusinessName(BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME));
        Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);

        // 兼容逻辑
        List<String> ccbDecpOldIds = applicationApolloConfig.getCcbDecpOldIds();
        if (WosaiCollectionUtils.isNotEmpty(ccbDecpOldIds) && ccbDecpOldIds.contains(merchantId)) {
            CcbDecpMerchant ccbDecpMerchant = ccbDecpMerchantMapper.selectActivatedByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
            Map map = JSON.parseObject(ccbDecpMerchant.getResponse_body(), Map.class);
            Map dataInfo = WosaiMapUtils.getMap(map, "dataInfo");
            OpenCcbDecpReq openCcbDecpReq = JSON.parseObject(ccbDecpMerchant.getRequest_body(), OpenCcbDecpReq.class);

            // 钱包id
            decpParamResp.setWalletId(BeanUtil.getPropString(dataInfo, "Cst_AccNo"));
            // 客户姓名
            decpParamResp.setHolder(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER));
            // 证件号
            decpParamResp.setIdNumber(getDesensitizeIdentity(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY)));
            // 手机号
            decpParamResp.setPhoneNumber(openCcbDecpReq.getMrchCtcPsnMblPhNo());
            return decpParamResp;
        }
        // 新逻辑
        SelfOpenCcbDecp selfOpenCcbDecp = selfOpenCcbDecpMapper.selectByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
        OpenCcbDecpReq openCcbDecpReq = JSON.parseObject(selfOpenCcbDecp.getRequest_body(), OpenCcbDecpReq.class);
        // 钱包id
        decpParamResp.setWalletId(openCcbDecpReq.getCstAccNo());
        // 客户姓名
        decpParamResp.setHolder(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER));
        // 证件号
        decpParamResp.setIdNumber(getDesensitizeIdentity(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY)));
        // 手机号
        decpParamResp.setPhoneNumber(openCcbDecpReq.getMrchCtcPsnMblPhNo());
        return decpParamResp;
    }


    public ContractResponse openCcbDecp(String merchantId, String phoneNumber, String walletNumber) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        SelfOpenCcbDecp selfOpenCcbDecp = selfOpenCcbDecpMapper.selectByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
        if (selfOpenCcbDecp != null) {
            // 如果已经是开通成功或者是审核中，就直接返回开通成功
            if (selfOpenCcbDecp.getOpen_status() == SelfOpenCcbDecp.SUCCESS_OPEN_STATUS || selfOpenCcbDecp.getOpen_status() == SelfOpenCcbDecp.PROCESS_OPEN_STATUS) {
                return new ContractResponse().setSuccess(true);
            }
            // 如果开通失败或者待开通，就去开通
            return doOpenDecpByIdNumber(merchant, phoneNumber, walletNumber, selfOpenCcbDecp);
        }
        // 没有开通过，就去开通
        return doOpenDecpByIdNumber(merchant, phoneNumber, walletNumber, selfOpenCcbDecp);
    }

    /**
     * 判断用这个证件号批量开通的商户有没有成功的
     *
     * @param merchant        商户信息
     * @param phoneNumber     手机号
     * @param walletNumber    钱包号
     * @param selfOpenCcbDecp 开通信息
     * @return 开通结果
     */
    private ContractResponse doOpenDecpByIdNumber(Map merchant, String phoneNumber, String walletNumber, SelfOpenCcbDecp selfOpenCcbDecp) {
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        Map ccbDecpCity = applicationApolloConfig.getCcbDecpCity();
        // 获取机构号
        String dpBkInNo = WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(ccbDecpCity, BeanUtil.getPropString(merchant, Merchant.CITY))) ?
                BeanUtil.getPropString(ccbDecpCity, BeanUtil.getPropString(merchant, Merchant.CITY)) : BeanUtil.getPropString(ccbDecpCity, BeanUtil.getPropString(merchant, Merchant.PROVINCE));
        // 用这个渠道的密钥和机构号
        CcbParam ccbParam = contractParamsBiz.buildContractParams("ccb-1026-23-B0002", CcbParam.class);
        // 获取上下文调用接口开通
        Map contextParam = getParamContextByMerchantSnForCcbDecp(merchant);
        Map<String, Object> dynamicParam = new HashMap<>(3);
        dynamicParam.put("DpBkInNo", dpBkInNo);
        dynamicParam.put("Mrch_CtcPsn_MblPh_No", phoneNumber);
        dynamicParam.put("Cntrprt_MblPh_No", phoneNumber);
        dynamicParam.put("Rctly_Auto_Py_Ind", "0");
        dynamicParam.put("Cst_AccNo", walletNumber);
        dynamicParam.put("Ar_Acc_TpCd", "2");
        dynamicParam.put("IdCst_AccNo_Nm", BeanUtil.getPropString(contextParam, "bankAccount.holder"));
        com.wosai.upay.merchant.contract.model.ContractResponse response = ccbService.selfOpenCcbDecp(contextParam, ccbParam, dynamicParam);
        // 处理开通结果
        return handleSelfOpenCcbDecpResult(selfOpenCcbDecp, merchantSn, BeanUtil.getPropString(merchant, DaoConstants.ID), response);
    }

    /**
     * 处理开通结果
     * @param selfOpenCcbDecp  开通数据
     * @param merchantSn       商户号
     * @param merchantId       商户id
     * @param response         开通返回结果
     * @return 开通结果
     */
    private ContractResponse handleSelfOpenCcbDecpResult(SelfOpenCcbDecp selfOpenCcbDecp, String merchantSn, String merchantId, com.wosai.upay.merchant.contract.model.ContractResponse response) {
        int status;
        if (OpenCcbDecpConstant.SUCCESS_MESSAGE.equals(response.getMessage())) {
            status = SelfOpenCcbDecp.PROCESS_OPEN_STATUS;
        } else {
            status = SelfOpenCcbDecp.FAIL_OPEN_STATUS;
        }
        long decpId = BeanUtil.getPropLong(response.getResponseParam(), OpenCcbDecpConstant.DECP_ID);
        if (selfOpenCcbDecp != null) {
            SelfOpenCcbDecp update = new SelfOpenCcbDecp();
            update.setId(selfOpenCcbDecp.getId());
            update.setOpen_status(status);
            update.setDecp_id(decpId);
            update.setRequest_body(MapUtils.isEmpty(response.getRequestParam()) ? null : JSON.toJSONString(response.getRequestParam()));
            update.setResult(response.getMessage());
            update.setMtime(System.currentTimeMillis());
            selfOpenCcbDecpMapper.updateByPrimaryKeySelective(update);
        } else {
            SelfOpenCcbDecp save = new SelfOpenCcbDecp();
            save.setMerchant_sn(merchantSn);
            save.setOpen_status(status);
            save.setDecp_id(decpId);
            save.setRequest_body(MapUtils.isEmpty(response.getRequestParam()) ? null : JSON.toJSONString(response.getRequestParam()));
            save.setResult(response.getMessage());
            save.setCtime(System.currentTimeMillis());
            save.setMtime(System.currentTimeMillis());
            selfOpenCcbDecpMapper.insertSelective(save);
        }
        if (status == SelfOpenCcbDecp.PROCESS_OPEN_STATUS) {
            setTradeParamsAndUpdateCcbDecpMerchant(merchantSn, merchantId, getCcbTradeParamsFromSelfOpenCcbDecp(decpId));
            return new ContractResponse().setSuccess(true);
        } else {
            return new ContractResponse().setSuccess(false).setMsg(response.getMessage());
        }
    }

    /**
     * 设置交易参数 && 将id对应数据的商户改为已激活 提交过 设置关联商户号
     *
     * @param merchantSn      商户号
     * @param merchantId      商户id
     */
    private void setTradeParamsAndUpdateCcbDecpMerchant(String merchantSn, String merchantId, Map ccbTradeParams) {
        Map decpConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        Map updateDecpConfig = CollectionUtil.hashMap(
                MerchantConfig.PAYWAY, PaywayEnum.DCEP.getValue(),
                MerchantConfig.MERCHANT_ID, merchantId,
                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                MerchantConfig.B2C_AGENT_NAME, "1026_*_*_false_true_0002",
                MerchantConfig.B2C_FEE_RATE, "0.0",
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_CCB.getValue()
        );
        if (MapUtils.isEmpty(decpConfig)) {
            updateDecpConfig.put(MerchantConfig.PARAMS, CollectionUtil.hashMap("ccb_trade_params", ccbTradeParams));
            tradeConfigService.createMerchantConfig(updateDecpConfig);
        } else {
            Map params = MapUtils.getMap(decpConfig, MerchantConfig.PARAMS);
            if (params == null) {
                params = new HashMap(1);
            }
            params.put("ccb_trade_params", ccbTradeParams);
            updateDecpConfig.put(MerchantConfig.PARAMS, params);
            updateDecpConfig.put(com.wosai.upay.common.dao.DaoConstants.ID, BeanUtil.getPropString(decpConfig, com.wosai.upay.common.dao.DaoConstants.ID));
            tradeConfigService.updateMerchantConfig(updateDecpConfig);
        }
        supportService.removeCachedParams(merchantSn);
    }

    /**
     * 取消该商户的数币交易权限
     *
     * @param merchantId 商户id
     * @return 是否取消成功
     */
    public ContractResponse cancelCcbDecp(String merchantId) {
        Map decpConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        // 建行的数币
        if (WosaiMapUtils.isNotEmpty(decpConfig) && SelfOpenCcbDecp.DECP_AGENTS.contains(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))) {
            // 取消该payway交易权限
            Map updateDecpConfig = CollectionUtil.hashMap(
                    MerchantConfig.PAYWAY, PaywayEnum.DCEP.getValue(),
                    MerchantConfig.MERCHANT_ID, merchantId,
                    MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_CLOSED
            );
            updateDecpConfig.put(com.wosai.upay.common.dao.DaoConstants.ID, BeanUtil.getPropString(decpConfig, com.wosai.upay.common.dao.DaoConstants.ID));
            tradeConfigService.updateMerchantConfig(updateDecpConfig);

            Map merchant = merchantService.getMerchantByMerchantId(merchantId);
            supportService.removeCachedParams(BeanUtil.getPropString(merchant, Merchant.SN));

            // 如果有批量开通记录，将该记录改为未提交 未激活
            SelfOpenCcbDecp selfOpenCcbDecp = selfOpenCcbDecpMapper.selectByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
            if (selfOpenCcbDecp != null) {
                selfOpenCcbDecpMapper.cancelSelfOpenCcbDecp(selfOpenCcbDecp.getId(), System.currentTimeMillis());
            }
        }
        return new ContractResponse().setSuccess(true);
    }

    /**
     * 证件号的脱敏处理
     *
     * @param identity 证件号
     * @return 脱敏后的结果
     */
    private String getDesensitizeIdentity(String identity) {
        if (WosaiStringUtils.isEmpty(identity)) {
            return identity;
        }
        //获取当前的证件号码的长度
        int certificateNoLength = identity.length();
        StringBuilder star = new StringBuilder();
        //获取*的长度，因为前三位和最后四位不需要脱敏，所以长度-7
        for (int i = 0; i < certificateNoLength - 7; i++) {
            star.append("*");
        }
        //取最后四位
        //总长度-（总长度-(总长度-需要脱敏的位数)-开头不要需要脱敏的长度）
        //这个获取的就时末尾不需要脱敏的位数，因为证件号长度不确定，所以需要动态获取
        int four = certificateNoLength - (certificateNoLength - (certificateNoLength - 7) - 3);
        return identity.substring(0, 3) + star + identity.substring(four, certificateNoLength);
    }

    /**
     * 从open_ccb_decp中获取交易参数等信息
     *
     * @return
     */
    private Map getCcbTradeParamsFromSelfOpenCcbDecp(long id) {
        OpenCcbDecp openCcbDecp = openCcbDecpMapper.selectByPrimaryKey(id);
        Map responseBody = JSON.parseObject(openCcbDecp.getResponse_body(), Map.class);
        Map dataInfo = MapUtils.getMap(responseBody, "dataInfo");
        return CollectionUtil.hashMap(
                "merchant_id", BeanUtil.getPropString(dataInfo, "Mrch_ID"),
                "pos_id", BeanUtil.getPropString(dataInfo, "Tmnl_Cd"),
                "terminal_id", BeanUtil.getPropString(dataInfo, "EdCrdMchn_Tmnl_Idr_CD"),
                "terminal_no", BeanUtil.getPropString(dataInfo, "POS_ID"),
                "branch_id", BeanUtil.getPropString(dataInfo, "BRANCHID"));

    }

    private Map<String, Object> getParamContextByMerchantSnForCcbDecp(Map merchant) {
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map<String, Object> contextParam = new HashMap<>(3);
        //商户信息
        contextParam.put("merchant", merchant);
        // 银行卡信息
        Map bankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        contextParam.put("bankAccount", bankAccount);
        // 营业执照信息
        Map<String, Object> license = licenseService.getBusinessLicenseByMerchantId(merchantId);
        contextParam.put("merchantBusinessLicense", license);

        // 判断是否是非法人个人结算 如果是则将营业执照类型改为0, 按照小微商户去送数据
        if (BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE) > BusinessLicenseTypeEnum.MICRO.getValue()
                && WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER))
                && !BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER).equals(BeanUtil.getPropString(bankAccount, MerchantBankAccount.IDENTITY))) {
            license.put(MerchantBusinessLicence.NUMBER, null);
            license.put(MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        }
        return contextParam;
    }

    @Scheduled(cron = "0 3 5 * * ?")
    public void sendSelfOpenCcbDecpMail() {
        if (!redisLock.lock(SEND_DECP_MAIL, SEND_DECP_MAIL, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return;
        }
        try {
            // 测试环境不发
            if (!ArrayUtils.isEmpty(environment.getActiveProfiles()) && !Objects.equals(environment.getActiveProfiles()[0], "prod")) {
                return;
            }
            RestTemplate restTemplate = new RestTemplate();
            List<Map> data = buildData();
            Context context = new Context();
            context.setVariable("allData", data);
            String content = templateEngine.process("ccbDecpTemplate", context);
            Map request = CollectionUtil.hashMap(
                    "id", 267,
                    "content", content
            );
            Map response = restTemplate.postForObject(mailGateway, request, Map.class);
            if (0 == BeanUtil.getPropInt(response, "errcode")) {
                log.info("SelfOpenCcbDecp sendMail success");
            } else {
                log.info("SelfOpenCcbDecp sendMail fail : {}", JSON.toJSONString(response));
            }

        } catch (Exception e) {
            log.error("SelfOpenCcbDecp sendMail error", e);
        }
    }

    private List<Map> buildData() {
        // 获取昨天的开始时间
        long dayStart = WosaiDateTimeUtils.getDayStart(System.currentTimeMillis()) - ONE_DAY;
        List<SelfOpenCcbDecp> selfOpenCcbDecps = selfOpenCcbDecpMapper.selectSuccessByMtime(dayStart, dayStart + ONE_DAY);
        String date = WosaiDateTimeUtils.longToString(dayStart).substring(0, 10);
        List<Map> allData = new ArrayList<>();
        for (SelfOpenCcbDecp selfOpenCcbDecp : selfOpenCcbDecps) {
            Map merchant = merchantService.getMerchantBySn(selfOpenCcbDecp.getMerchant_sn());
            Map requestBody = JSON.parseObject(selfOpenCcbDecp.getRequest_body(), Map.class);
            OpenCcbDecp openCcbDecp = openCcbDecpMapper.selectByPrimaryKey(selfOpenCcbDecp.getDecp_id());
            String mrchId = BeanUtil.getPropString(JSON.parseObject(openCcbDecp.getResponse_body(), Map.class), "dataInfo.Mrch_ID");
            String name = BeanUtil.getPropString(requestBody, "LgPsRprNm");
            String address = BeanUtil.getPropString(requestBody, "Store_Adr");
            String cstAccNo = BeanUtil.getPropString(requestBody, "Cst_AccNo");
            Map data = CollectionUtil.hashMap(
                    "merchant_sn", selfOpenCcbDecp.getMerchant_sn(),
                    "mrch_id", mrchId,
                    "cst_accno",cstAccNo,
                    "name", name,
                    "date", date,
                    "city", BeanUtil.getPropString(merchant, Merchant.CITY),
                    "address", address
            );
            allData.add(data);
        }
        return allData;
    }
}
