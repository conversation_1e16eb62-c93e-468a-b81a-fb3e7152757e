package com.wosai.upay.job.refactor.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.kafka.PhotoSyncToAcquirerSuccessDTO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.service.ErrorCodeManageService;
import com.wosai.upay.job.service.task.TrackContractTaskResultTaskService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 追踪进件任务结果任务
 *
 * <AUTHOR>
 * @date 2024/11/11 11:19
 */
@Component
@Slf4j
public class TrackContractTaskResultTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private InterScheduleTaskServiceImpl interactionTaskService;

    @Resource
    private ContractTaskDAO contractTaskDAO;


    @Resource
    private ErrorCodeManageService errorCodeManageService;

    @Resource
    private ContractRelatedMappingConfigService contractRelatedMappingConfigService;

    @Autowired
    private CallBackService callBackService;

    @Resource(name = "aliKafkaTemplate")
    private KafkaTemplate<String, Object> aliKafkaTemplate;

    public static final String TOPIC = "events_CUA_photo_sync_acquirer_success";

    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.TRACK_CONTRACT_RESULT;
    }

    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        executeProperty.setSupportParallel(true);
        return executeProperty;
    }

    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess() || subTaskDO.isWaitExternalResult()) {
            return echoBackToAudit(mainTaskDO, subTaskDO);
        }
        return InternalScheduleSubTaskProcessResultBO.fail("未知状态");
    }

    private InternalScheduleSubTaskProcessResultBO echoBackToAudit(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String context = subTaskDO.getContext();
        if (StringUtils.isBlank(context)) {
            return InternalScheduleSubTaskProcessResultBO.fail("context为空");
        }
        SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(context, SubTaskContextBOInner.class);
        Long contractTaskId = subTaskContextBOInner.getContractTaskId();
        Optional<ContractTaskDO> contractTaskOpt = contractTaskDAO.getByPrimaryKey(contractTaskId);
        if (!contractTaskOpt.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("进件任务为空");
        }
        ContractTaskDO contractTaskDO = contractTaskOpt.get();
        if (!contractTaskDO.processFail() && !contractTaskDO.processSuccess()) {
            return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待进件完成");
        }
        if (contractTaskDO.processSuccess()) {
            if (StringUtils.isNotBlank(subTaskContextBOInner.getAuditId())) {
                syncToAuditWhenContractSuccess(subTaskDO.getTaskType(), subTaskContextBOInner.getAuditId(), subTaskContextBOInner.getTemplateId());
            }
            sendContractSuccessResultToKafka(subTaskDO, contractRelatedMappingConfigService.getAcquirerByRuleGroupId(contractTaskDO.getRuleGroupId()));
            return InternalScheduleSubTaskProcessResultBO.success("进件成功");
        }
        // fail
        if (StringUtils.isNotBlank(subTaskContextBOInner.getAuditId())) {
            syncToAuditWhenContractFail(contractTaskDO, subTaskContextBOInner.getAuditId(), subTaskContextBOInner.getTemplateId());
        }
        return InternalScheduleSubTaskProcessResultBO.success("进件失败");

    }

    private void syncToAuditWhenContractSuccess(String taskType, String auditId, Long templateId) {
        String message = Objects.equals(TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_CORPORATE_RECEIPT_PHOTO_AUDIT, Integer.valueOf(taskType))
                ? "对公账户凭证同步富友成功" : "公示网截图同步富友成功";
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(auditId)).templateId(templateId).message(message).resultType(1)
                .build();
        try {
            callBackService.addComment(callBackBean);
        } catch (Exception e) {
            log.error("回写审批备注失败, auditId:{}", auditId, e);
        }
    }

    private void sendContractSuccessResultToKafka(InternalScheduleSubTaskDO subTaskDO, String acquirer) {
        if (CollectionUtils.containsAny(
                Sets.newHashSet(TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_CORPORATE_RECEIPT_PHOTO_AUDIT
                        , TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_ANNOUNCEMENT_WEBSITE_PHOTO_AUDIT,
                        TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_UPDATE_FIRST_STORE), Integer.valueOf(subTaskDO.getTaskType()))) {
            PhotoSyncToAcquirerSuccessDTO photoSyncToAcquirerSuccessDTO = new PhotoSyncToAcquirerSuccessDTO();
            photoSyncToAcquirerSuccessDTO.setMerchantSn(subTaskDO.getMerchantSn());
            photoSyncToAcquirerSuccessDTO.setAcquirer(acquirer);
            photoSyncToAcquirerSuccessDTO.setType(Integer.valueOf(subTaskDO.getTaskType()));
            aliKafkaTemplate.send(TOPIC, photoSyncToAcquirerSuccessDTO);
        }
    }

    private void syncToAuditWhenContractFail(ContractTaskDO contractTaskDO, String auditId, Long templateId) {
        String contractTaskFailReason = getContractTaskFailReason(contractTaskDO);
        String acquirer = contractRelatedMappingConfigService.getAcquirerByRuleGroupId(contractTaskDO.getRuleGroupId());
        ErrorInfo promptMessage = errorCodeManageService.getPromptMessage(ErrorMsgViewEndpointTypeEnum.CRM.getValue(), contractTaskFailReason, acquirer);
        String sceneReason = Objects.nonNull(promptMessage) && StringUtils.isNotBlank(promptMessage.getMsg()) ? promptMessage.getMsg() : contractTaskFailReason;
        CallBackBean callBackBean = CallBackBean.builder()
                .auditId(Long.valueOf(auditId)).templateId(templateId).message(sceneReason).resultType(2)
                .build();
        callBackService.addComment(callBackBean);
    }

    /**
     * 获取任务失败原因
     *
     * @param contractTaskDO 任务
     * @return 失败原因
     */
    public String getContractTaskFailReason(ContractTaskDO contractTaskDO) {
        String result = contractTaskDO.getResult();
        if (StringUtils.isBlank(result)) {
            return "";
        }
        if (result.startsWith("{") && result.endsWith("}")) {
            JSONObject resultMap = JSON.parseObject(result);
            String contextResult = MapUtils.getString(resultMap, "result");
            if (StringUtils.isNotBlank(contextResult)) {
                return contextResult;
            }
            String message =  MapUtils.getString(resultMap, "message");
            if (StringUtils.isNotBlank(message)) {
                return message;
            }
        }
        return result;
    }

    /**
     * 新增进件任务结果回显审批任务
     *
     * @param merchantSn     商户号
     * @param auditId        审批id
     * @param contractTaskId 进件任务id
     */
    public void insertTask(int businessSceneType, String merchantSn, String auditId, Long contractTaskId, Long templateId) {
        Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> tasksMap = Maps.newHashMap();
        tasksMap.put(buildMainTask(merchantSn), Lists.newArrayList(buildSubTask(businessSceneType, merchantSn, auditId, contractTaskId, templateId)));
        interactionTaskService.batchInsertTasks(tasksMap);
    }

    private InternalScheduleSubTaskDO buildSubTask(int businessSceneType, String merchantSn, String auditId, Long contractTaskId, Long templateId) {
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setType(getTaskType().getValue());
        subTaskDO.setTaskType(String.valueOf(businessSceneType));
        subTaskDO.setMerchantSn(merchantSn);
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        subTaskDO.setPriority(1);
        subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        subTaskDO.setContext(JSON.toJSONString(new SubTaskContextBOInner(auditId, contractTaskId, templateId)));
        return subTaskDO;
    }

    private InternalScheduleMainTaskDO buildMainTask(String merchantSn) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setMerchantSn(merchantSn);
        internalScheduleMainTaskDO.setType(getTaskType().getValue());
        internalScheduleMainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue());
        internalScheduleMainTaskDO.setAffectStatusSubTaskNum(1);
        internalScheduleMainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        return internalScheduleMainTaskDO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskContextBOInner {
        private String auditId;

        private Long contractTaskId;

        private Long templateId;

    }
}
