package com.wosai.upay.job.refactor.task;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.shouqianba.cua.utils.object.EnumUtils;
import com.shouqianba.cua.utils.object.StringExtensionUtils;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.externalservice.mail.MailClient;
import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 银行自动切三方通知定时任务
 *
 * <AUTHOR>
 * @date 2024/6/20 18:38
 */
@Component
@Slf4j
public class BankAutoChangeThirdNotifyTask {

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Resource
    private MailClient mailClient;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    private static final String[] EXCEL_HEADERS = {"商户号", "任务类型", "任务上下文", "任务结果", "任务详细结果", "任务开始时间"};

    private static final int QUERY_TASK_DAYS_SPAN = -90;

    /**
     * 发送银行自动切三方任务邮件
     */
    public void sendBankAutoChangeThirdTaskEmail() {
        List<InternalScheduleMainTaskDO> threeMonthMainTaskDOS = listThreeMonthMainTasks();
        List<Long> mainIds = threeMonthMainTaskDOS.stream().map(InternalScheduleMainTaskDO::getId).collect(Collectors.toList());
        Map<Long, InternalScheduleSubTaskDO> subTaskDOMap = getSubTaskDOMap(mainIds);
        byte[] excel = buildExcel(threeMonthMainTaskDOS, subTaskDOMap);
        sendEmail(excel);
    }

    @NotNull
    private Map<Long, InternalScheduleSubTaskDO> getSubTaskDOMap(List<Long> mainIds) {
        Map<Long, InternalScheduleSubTaskDO> subTaskDOMap = Maps.newHashMapWithExpectedSize(mainIds.size());
        for (List<Long> splitMainIds : Lists.partition(mainIds, 200)) {
            Map<Long, InternalScheduleSubTaskDO> subtaskMap = internalScheduleSubTaskDAO.listByMainIds(splitMainIds).stream()
                    .filter(t -> Objects.equals(t.getAffectMainTaskStatus(), AffectPrimaryTaskStatusEnum.YES.getValue()))
                    .collect(ExtCollectors.toMap(InternalScheduleSubTaskDO::getMainTaskId, Function.identity(), (k1, k2) -> k1));
            subTaskDOMap.putAll(subtaskMap);
        }
        return subTaskDOMap;
    }

    private void sendEmail(byte[] excel) {
        List<Long> mailIds = applicationApolloConfig.listBankAutoChangeThirdNotifyMailIds();
        if (CollectionUtils.isEmpty(mailIds)) {
            return;
        }
        for (Long mailId : mailIds) {
            try {
                doSendEmailToMailId(excel, mailId);
            } catch (Exception e) {
                log.error("send email to mailId {} error", mailId, e);
            }
        }
    }

    private void doSendEmailToMailId(byte[] excel, Long mailId) {
        MailSendReq mailSendReq = new MailSendReq();
        mailSendReq.setId(mailId);
        mailSendReq.setTo("<EMAIL>,<EMAIL>");  // just for test
        if (ArrayUtils.isEmpty(excel)) {
            mailSendReq.setContent("<p>三个月内没有银行自动切回三方或者三方切回的任务</p>");
        } else {
            mailSendReq.setContent("<p>三个月银行自动切回三方或者三方切回的任务，详情请查看附件</p>");
            HashMap<String, Object> map = Maps.newHashMap();
            map.put("name", DateExtensionUtils.format(new Timestamp(System.currentTimeMillis()), DateExtensionUtils.FORMAT_DATE_NUM) + "银行自动回切任务数据" + ".xlsx");
            map.put("data", new BASE64Encoder().encode(excel));
            mailSendReq.setAttachments(Collections.singletonList(map));
        }
        mailClient.sendEmail(mailSendReq);
    }

    private List<InternalScheduleMainTaskDO> listThreeMonthMainTasks() {
        Timestamp threeMonthBefore = DateExtensionUtils.addDays(new Timestamp(System.currentTimeMillis()), QUERY_TASK_DAYS_SPAN);
        List<InternalScheduleMainTaskDO> bankToThirdTask =  internalScheduleMainTaskDAO.listByGeUpdateTimeAndType(threeMonthBefore, InternalScheduleTaskTypeEnum.BANK_EXCEPTION_CHANGE_TO_THIRD_PARTY.getValue());
        List<InternalScheduleMainTaskDO> thirdToBankTask = internalScheduleMainTaskDAO.listByGeUpdateTimeAndType(threeMonthBefore, InternalScheduleTaskTypeEnum.BANK_RECOVER_CHANGE_TO_ORIGINAL.getValue());
        List<InternalScheduleMainTaskDO> mainTaskDOS = Lists.newArrayListWithCapacity(bankToThirdTask.size() + thirdToBankTask.size());
        mainTaskDOS.addAll(bankToThirdTask);
        mainTaskDOS.addAll(thirdToBankTask);
        return mainTaskDOS;
    }


    private byte[] buildExcel(List<InternalScheduleMainTaskDO> mainTaskDOS, Map<Long, InternalScheduleSubTaskDO> subTaskDOMap) {
        if (CollectionUtils.isEmpty(mainTaskDOS)) {
            return ArrayUtils.EMPTY_BYTE_ARRAY;
        }
        List<String[]> excelLines = Lists.newArrayListWithCapacity(mainTaskDOS.size());
        excelLines.add(EXCEL_HEADERS);
        mainTaskDOS.stream().sorted(Comparator.comparing(InternalScheduleMainTaskDO::getMtime)).forEach(mainTaskDO -> {
            InternalScheduleSubTaskDO subTaskDO = subTaskDOMap.get(mainTaskDO.getId());
            String[] line = new String[6];
            line[0] = mainTaskDO.getMerchantSn();
            line[1] = EnumUtils.ofNullable(InternalScheduleTaskTypeEnum.class, mainTaskDO.getType()).map(InternalScheduleTaskTypeEnum::getText).orElse("未知类型");
            line[2] = StringExtensionUtils.emptyToDefault(mainTaskDO.getContext(), StringUtils.EMPTY);
            line[3] = StringExtensionUtils.emptyToDefault(mainTaskDO.getResult(), StringUtils.EMPTY);
            line[4] = Objects.isNull(subTaskDO) ? StringUtils.EMPTY : StringExtensionUtils.emptyToDefault(subTaskDO.getResult(), StringUtils.EMPTY);
            line[5] = mainTaskDO.getMtime().toString();
            excelLines.add(line);
        });
        return ExcelUtil.writeExcel("回切任务数据", excelLines);
    }


}
