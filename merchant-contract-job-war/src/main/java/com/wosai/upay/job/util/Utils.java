package com.wosai.upay.job.util;

import com.google.common.base.Preconditions;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.exception.MpayException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

/**
 * Created by xuchmao on 17/6/19.
 */
public class Utils {
    public static final Logger logger = LoggerFactory.getLogger(Utils.class);
    private static final DateFormat dateFormatCommon = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");


    public static String formatDate(String dateString) throws ParseException {
        return dateFormatCommon.format(dateFormat.parse(dateString));
    }


    /**
     * 从省市区名称中提取行政单位之外的有效信息
     *
     * @param name
     * @return
     */
    public static String getDistrictShortName(String name) {
        if (StringUtil.empty(name)) {
            return name;
        }
        if (name.endsWith("内蒙古自治区")) {
            return name.replace("自治区", "");
        }
        if (name.endsWith("维吾尔自治区")) {
            return name.replace("维吾尔自治区", "");
        }
        if (name.endsWith("壮族自治区")) {
            return name.replace("壮族自治区", "");
        }
        if (name.endsWith("壮族自治区")) {
            return name.replace("壮族自治区", "");
        }
        if (name.endsWith("回族自治区")) {
            return name.replace("回族自治区", "");
        }
        if (name.endsWith("回族自治区")) {
            return name.replace("回族自治区", "");
        }
        if (name.endsWith("自治区")) {
            return name.replace("自治区", "");
        }
        if (name.endsWith("自治州")) {
            return name.replace("自治州", "");
        }
        if (name.endsWith("自治县")) {
            return name.replace("自治县", "");
        }
        if (name.endsWith("省")) {
            return name.replace("省", "");
        }
        if (name.endsWith("市")) {
            return name.replace("市", "");
        }
        if (name.endsWith("地区")) {
            return name.replace("地区", "");
        }
        if (name.endsWith("区")) {
            return name.replace("区", "");
        }
        if (name.endsWith("县")) {
            return name.replace("县", "");
        }
        return name;
    }

    public static CloseableHttpClient createSSLClientDefault() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                //信任所有
                public boolean isTrusted(X509Certificate[] chain,
                                         String authType) {
                    return true;
                }
            }).build();
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (KeyManagementException e) {
            logger.error("", e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("", e);
        } catch (KeyStoreException e) {
            logger.error("", e);
        }
        return HttpClients.createDefault();
    }

    public static String substring(String str, int maxLength) {
        if (str != null && str.length() > maxLength) {
            return str.substring(0, maxLength - 1);
        }
        return str;
    }

    public static String randomParam() {
        return (new Random().nextInt(899) + 100) + "";
    }

    private static final DateFormat datetimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取当前时间  yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getDateTimeString() {
        return datetimeFormat.format(new Date());
    }


    public static SSLContext getSSLContext() throws MpayException {
        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new X509TrustManager() {

                @Override
                public void checkClientTrusted(X509Certificate[] arg0, String arg1) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] arg0, String arg1) {
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[]{};
                }

            }}, new java.security.SecureRandom());
        } catch (Exception e) {
            throw new MpayException("获取证书失败", e);
        }
        return sc;
    }


    public static String randomParma() {
        return (new Random().nextInt(899) + 100) + "";
    }

    /**
     * 判断是否有效的查询条件
     * @param query
     * @return
     */
    public static boolean isValidQuery(Map query) {
        if (query == null || query.isEmpty()) {
            return false;
        }
        return !query.values().stream().allMatch(Objects::isNull);
    }

    public static  boolean isNullOrZero(Long applyId) {
        return applyId == null || Objects.equals(applyId, 0L);
    }


    public static boolean compareProvince(String districtCodeOne ,String districtCodeTwo) {
        try {
            Preconditions.checkArgument(StringUtils.length(districtCodeOne) == 6, "Invalid district code");
            Preconditions.checkArgument(StringUtils.length(districtCodeTwo) == 6, "Invalid district code");
            return districtCodeOne.substring(0,2).equals(districtCodeTwo.substring(0,2));
        }  catch (Exception e) {
            throw  new IllegalArgumentException("行政区划码格式错误");
        }
    }

}
