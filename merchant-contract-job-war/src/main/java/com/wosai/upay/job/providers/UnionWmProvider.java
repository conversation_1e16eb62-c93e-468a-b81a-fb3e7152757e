package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.GrayMerchantSnBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.*;
import com.wosai.upay.merchant.contract.model.terminal.*;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by hzq on 19/4/2.
 */
@Component(ProviderUtil.UNION_WM_PROVIDER_CHANNEL)
public class UnionWmProvider extends AbstractProvider {
    private final static Logger log = LoggerFactory.getLogger(UnionWmProvider.class);

    @Autowired
    private NewUnionService newUnionService;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    private GrayMerchantSnBiz grayMerchantSnBiz;
    @Autowired
    private LklV3Service lklV3Service;
    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        ContractSubTask subTask = new ContractSubTask()
                .setMerchant_sn(merchantSn)
                .setStatus_influ_p_task(contractRule.getUpdateInfluPtask())
                .setChannel(ProviderUtil.UNION_WM_PROVIDER_CHANNEL)
                .setChange_config(0)
                .setDefault_channel(contractRule.getDefault())
                .setPayway(contractRule.getPayway())
                .setContract_id(contractRule.getChannelNo())
                .setContract_rule(contractRule.getRule())
                .setRule_group_id(event.getRule_group_id())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE)
                .setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue())
                .setRetry(0);


        Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
        if (org.springframework.util.CollectionUtils.isEmpty(eventMsg)) {
            log.error("UnionWm channelNo {} 更新事件 {} eventMsg为空", contractRule.getChannelNo(), event.getId());
            return null;
        }
        if (WosaiMapUtils.getBooleanValue(eventMsg, ContractEvent.FORCE_UPDATE)) {
            subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
            return subTask;
        }
        List<String> changeFileds = (List) eventMsg.get("msg");
        if (!ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(event.getEvent_type()) && CollectionUtils.isEmpty(changeFileds)) {
            log.error("UnionWm channelNo {} 更新事件 {} msg为空", contractRule.getChannelNo(), event.getId());
            return null;
        }
        if (ProviderUtil.getPayWayUpdate(contractRule.getPayway(), event.getEvent_type(), changeFileds)) {
            subTask.setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE);
            return subTask;
        }

        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface(contractTask.getMerchant_sn());
        // 拉卡拉接口
        if (newLklInterface) {
            LklV3Param lklV3Param = buildParam(contractChannel, sub, LklV3Param.class);
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                return lklV3Service.contractAlipayWithParams(contextParam, lklV3Param);
            }
            if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                LklV3WechatParam lklV3WechatParam = getLklV3WechatParam(contractChannel,sub);
                return lklV3Service.contractWechat(contextParam, lklV3WechatParam);
            }
        } else {
            //银联接口
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                UnionAlipayParam alipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
                return newUnionService.contractAlipayWithParams(contextParam, alipayParam);
            }
            if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                UnionWeixinParam weixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
                return newUnionService.contractWeixinWithParams(contextParam, weixinParam);
            }
        }
        return null;
    }

    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Integer payWay = sub.getPayway();
        boolean newLklInterface = grayMerchantSnBiz.newLklInterface(contractTask.getMerchant_sn());
        if (newLklInterface) {
            LklV3Param lklV3Param = buildParam(contractChannel, sub, LklV3Param.class);
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                return lklV3Service.updateAliPayWithParams(contextParam, lklV3Param);
            }
            if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                LklV3WechatParam lklV3WechatParam = getLklV3WechatParam(contractChannel,sub);
                return lklV3Service.updateWechat(contextParam, lklV3WechatParam);
            }
        } else {
            if (PaywayEnum.ALIPAY.getValue().equals(payWay)) {
                UnionAlipayParam alipayParam = buildParam(contractChannel, sub, UnionAlipayParam.class);
                return newUnionService.updateAlipayWithParams(contextParam,alipayParam);
            }
            if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
                UnionWeixinParam weixinParam = buildParam(contractChannel, sub, UnionWeixinParam.class);
                return newUnionService.updateWechatWithParams(contextParam, weixinParam);
            }
        }
        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        return unionService.weixinSubdevConfignNew(weixinConfig);
    }
    /**
     * 终端/子商户号绑定
     *
     * @param termInfoDTO
     * @param payWay
     * @param terminalSn
     * @return
     */
    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO termInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.addAliTermInfo(termInfoDTO, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(termInfoDTO.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.addWxTermInfo(termInfoDTO, weixinParam);
        }
        return response;
    }

    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.LogOutAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.LogOutWxTermInfo(dto, weixinParam);
        }
        return response;
    }

    @Override
    public ContractResponse updateTerminal(UpdateTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.updateAliTermInfo(dto, alipayParam);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.updateWxTermInfo(dto, weixinParam);
        }
        return response;
    }


    @Override
    public ContractResponse updateTerminalWithCustom(UpdateTermInfoDTO dto,
                                                     int payWay,
                                                     String terminalSn,
                                                     AliTermInfoRequest customAliTermInfo,
                                                     WxTermInfoRequest wxCustomInfo) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            UnionAlipayParam alipayParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionAlipayParam.class);
            response = newUnionService.updateAliTermInfoWihCustom(dto, alipayParam,customAliTermInfo);
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            UnionWeixinParam weixinParam = contractParamsBiz.buildContractParamsByPayMchId(dto.getSubMchId(), UnionWeixinParam.class);
            response = newUnionService.updateWxTermInfoWithCustom(dto, weixinParam,wxCustomInfo);
        }
        return response;
    }


    private LklV3WechatParam getLklV3WechatParam(ContractChannel contractChannel, ContractSubTask sub) {
        WanmaWeixinParam wanMa = buildParam(contractChannel, sub, WanmaWeixinParam.class);
        return grayMerchantSnBiz.getLklV3WechatParam(wanMa);
    }
}
