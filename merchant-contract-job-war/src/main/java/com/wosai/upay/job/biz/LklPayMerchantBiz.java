package com.wosai.upay.job.biz;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import facade.ICustomerRelationValidateFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 拉卡拉组织商户业务处理
 * <AUTHOR>
 * @Date: 2022/2/13 8:01 下午
 */
@Component
@Slf4j
public class LklPayMerchantBiz {
    @Autowired
    private MerchantService merchantService;

    @Autowired
    private ICustomerRelationValidateFacade customerRelationValidateFacade;

    @Autowired
    private LklV3Service lklV3Service;

    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    @Qualifier("lklMerchantRelationThreadPoolTaskExecutor")
    private ThreadPoolTaskExecutor lklMerchantRelationThreadPoolTaskExecutor;

    /**
     * 是否为拉卡拉组织商户
     *
     * @param merchantSn
     * @param merchantId
     * @return
     */
    public boolean isLklPayMerchant(String merchantSn, String merchantId) {
        if (StringUtils.isBlank(merchantId)) {
            Map merchant = merchantService.getMerchantBySn(merchantSn);
            if (merchant == null) {
                throw new CommonInvalidParameterException("商户不存在");
            }
            merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);
        }
        //查询是否为拉卡拉组织商户
        return customerRelationValidateFacade.isLakalaPayMerchant(merchantId);
    }

    /**
     * 是否为拉卡拉组织商户
     * @param merchantSn 商户sn
     * @param merchantId 商户id
     * @param organizationId 组织id
     * @return true:是lkl组织
     */
    public boolean isLklPayMerchant(String merchantSn, String merchantId, String organizationId) {
        if (WosaiStringUtils.isNotEmpty(organizationId)) {
            return customerRelationValidateFacade.isLakalaPayOrg(organizationId);
        }
        return isLklPayMerchant(merchantSn, merchantId);
    }

    static Retryer RETRYER;

    static {
        RETRYER = RetryerBuilder.newBuilder()
                //抛出异常进行重试
                .retryIfException()
                //重试策略，固定时间间隔 间隔较长
                .withWaitStrategy(WaitStrategies.fixedWait(5, TimeUnit.SECONDS))
                //重试次数
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
    }

    /**
     * 联合品牌标识上送
     *
     * @param merchantSn
     * @param merInnerNo
     */
    public void createMerRelation(String merchantSn, String merInnerNo) {
        lklMerchantRelationThreadPoolTaskExecutor.submit(() -> {
            try {
                RETRYER.call(() -> {
                    if (isLklPayMerchant(merchantSn, null)) {
                        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
                        ContractResponse response = lklV3Service.createMerRelation(merInnerNo, lklV3Param);
                        if (!response.isSuccess()) {
                            String msg = String.format("拉卡拉联合品牌标识上送 异常 %s %s", merchantSn, merInnerNo);
                            log.error(msg);
                            throw new RuntimeException(msg);
                        }
                    }
                    return null;
                });
            } catch (Exception e) {
                log.error("createMerRelation error {} {}", merchantSn, merInnerNo, e);
            }

        });


    }


}
