package com.wosai.upay.job.externalservice.paybusiness;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.externalservice.paybusiness.model.PayCombosQueryReq;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.model.OpenPayComboReq;
import com.wosai.upay.service.CrmEdgeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vo.ApiRequestParam;
import vo.UserVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
@Component
public class PayBusinessOpenClient {

    @Autowired
    private CrmEdgeService crmEdgeService;

    public List<Map> queryPayCombos(PayCombosQueryReq req) {
        ApiRequestParam<OpenPayComboReq> comboReq = new ApiRequestParam<>();
        UserVo userVo = new UserVo();
        userVo.setOrganizationId(req.getOrganizationId());
        comboReq.setUser(userVo);
        OpenPayComboReq openPayComboReq = new OpenPayComboReq()
                .setPlatform("crm_app")
                .setIndustryId(req.getIndustryId());
        comboReq.setBodyParams(openPayComboReq);

        // 获取商户当前应该配置的套餐
        return crmEdgeService.getPayCombos(comboReq);
    }
}
