package com.wosai.upay.job.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.model.MerchantParamReq;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.union.UnionWxMchInfo;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.ComposeService;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/2/26
 */
@Component
@Slf4j
public class Fill259InfoBiz {

    @Autowired
    private NewUnionService newUnionService;

    @Autowired
    private ComposeService composeService;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantProviderParamsBiz paramsBiz;


    public void fill259Info(String subMchId) {
        List<MerchantProviderParamsDto> merchantProviderParams = paramsBiz.getMerchantProviderParams(new MerchantParamReq().setPay_merchant_id(subMchId));
        if (WosaiCollectionUtils.isEmpty(merchantProviderParams)) {
            log.info("{} 子商户不存在", subMchId);
            return;
        }
        String merchantSn = merchantProviderParams.get(0).getMerchant_sn();
        if (subMchId.startsWith("2088")) {
            handleAli(merchantSn, subMchId, getMerchantBankNumber(merchantSn));
        } else {
            handleWx(merchantSn, subMchId, getMerchantBankNumber(merchantSn));
        }
    }

    private String getMerchantBankNumber(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        return BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.NUMBER, "");
    }


    private void handleWx(String merchantSn, String wxMchId, String bankNumber) {
        UnionWxMchInfo mchInfo = newUnionService.queryUnionWxMchInfo(wxMchId);
        if (!isWxNeedUpdate(mchInfo, bankNumber)) {
            log.info("{} {} 不需要处理", merchantSn, wxMchId);
            return;
        }
        ContractResponse response = composeService.updateWxMchInfoBySubMchId(wxMchId, CollectionUtil.hashMap(
                "merchant_shortname", null,
                "service_phone", null
        ));
        if (response.isSuccess()) {
            log.info("{} {} 更新成功", merchantSn, wxMchId);
        } else {
            log.info("{} {} 更新失败 {}", merchantSn, wxMchId, response.getMessage());
        }
    }

    private boolean isWxNeedUpdate(UnionWxMchInfo unionWxMchInfo, String bankNumber) {
        if (WosaiStringUtils.isEmptyAny(
                unionWxMchInfo.getService_codes(),
                unionWxMchInfo.getBusiness_license_type(),
                unionWxMchInfo.getBusiness_license(),
                unionWxMchInfo.getUp_merchant_id()
        )
        ) {
            return true;
        }
        if (Objects.isNull(unionWxMchInfo.getBankcard_info())) {
            return true;
        }
        if (!Objects.equals(bankNumber, unionWxMchInfo.getBankcard_info().getCardNo())) {
            return true;
        }
        if (Objects.isNull(unionWxMchInfo.getAddress_info())) {
            return true;
        }
        return false;
    }

    private void handleAli(String merchantSn, String aliMchId, String bankNumber) {
        Map mchInfo = newUnionService.queryAlySubMch(aliMchId);
        if (WosaiMapUtils.isEmpty(mchInfo)) {
            log.info("{} {} 未查到用户信息", merchantSn, aliMchId);
            return;
        }
        if (!isAliNeedUpdate(mchInfo, bankNumber)) {
            log.info("{} {} 不需要处理", merchantSn, aliMchId);
            return;
        }
        ContractResponse response = blueSeaService.updateAliMchInfoBySubMchId(aliMchId, new HashMap());
        if (response.isSuccess()) {
            log.info("{} {} 更新成功", merchantSn, aliMchId);
        } else {
            log.info("{} {} 更新失败 {}", merchantSn, aliMchId, response.getMessage());
        }
    }

    private boolean isAliNeedUpdate(Map mchInfo, String bankNumber) {
        if (WosaiStringUtils.isEmpty(BeanUtil.getPropString(mchInfo, "up_merchant_id"))) {
            return true;
        }
        if (WosaiStringUtils.isEmpty(BeanUtil.getPropString(mchInfo, "business_license")) &&
                WosaiStringUtils.isEmpty(BeanUtil.getPropString(mchInfo, "contact_info.0.id_card_no"))) {
            return true;
        }

        String cardNo = BeanUtil.getPropString(mchInfo, "bankcard_info.0.card_no");
        if (!Objects.equals(bankNumber, cardNo)) {
            return true;
        }

        return false;
    }
}
