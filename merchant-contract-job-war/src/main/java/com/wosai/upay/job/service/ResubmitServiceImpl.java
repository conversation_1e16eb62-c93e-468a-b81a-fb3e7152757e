package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.databus.event.merchant.contract.MerchantContractOpinionEvent;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.util.ProviderUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;

import java.util.*;


/**
 * @Author: jerry
 * @date: 2019/4/8 19:01
 * @Description:回调服务实现
 */

@Service
@AutoJsonRpcServiceImpl
public class ResubmitServiceImpl implements ResubmitService {

    private static Integer CONTRACT_STATUS_FAIL = 6;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private BankCardServiceImpl bankCardService;
    @Autowired
    DataBusBiz dataBusBiz;
    @Autowired
    BankDirectApplyMapper bankDirectApplyMapper;

    private final static Logger log = LoggerFactory.getLogger(ResubmitServiceImpl.class);

    private final static List<String> SUPPORT_CHANGE_CARD_CHANNELS = Lists.newArrayList(
            ProviderUtil.LKL_PROVIDER_CHANNEL,
            ProviderUtil.LKL_V3_PROVIDER_CHANNEL,
            ProviderUtil.TONG_LIAN_CHANNEL,
            ProviderUtil.TONGLIAN_V2_CHANNEL
    );

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map reSubmitByTaskId(long taskId) {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        if (ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType()) && contractTaskBiz.checkNewVersionWechatAuthTask(contractTask)) {
            return res(400, "新版本实名认证不支持重新提交");
        }
        if (contractTask == null) {
            return res(400, "任务不存在");
        }
        if (!CONTRACT_STATUS_FAIL.equals(contractTask.getStatus())) {
            return res(400, "非失败任务");
        }
//        if (BANK_LIST.contains(contractTask.getRule_group_id())) {
//            return res(400, "银行业务只能通过客户端操作");
//        }
        String merchantSn = contractTask.getMerchant_sn();
        List<ContractTask> pendingTasks = contractTaskMapper.selectTaskTodoByMerchantSn(merchantSn);
        if (pendingTasks.size() > 0) {
            return res(400, "该商户下有正在处理的任务暂时无法重新提交！");
        }
        ContractTask lastContract = Optional.ofNullable(contractTaskMapper.getCanReSubmitTask(merchantSn))
                .orElseGet(ContractTask::new);
        if (!Objects.equals(taskId, lastContract.getId())) {
            return res(400, "非最新进件失败记录无法重新提交");
        }
        Map result = JSON.parseObject(contractTask.getResult(), Map.class);
        if (ProviderUtil.PAY_FOR_CHANNEL.equals(result.get("channel"))) {
            return res(400, "代付失败无法重新提交");
        }
        if (ProviderUtil.WECHAT_AUTH.equals(result.get("channel"))) {
            return res(400, "微信实名认证失败不允许重新提交");
        }
        List<ContractSubTask> contractSubTaskList = contractSubTaskMapper.selectByPTaskIdAndStatus(contractTask.getId(), TaskStatus.FAIL.getVal());
        if (CollectionUtils.isEmpty(contractSubTaskList)) {
            return res(400, "该任务下无失败子任务，请等待子任务处理回调");
        }
        log.info(" merchantSn {} resubmit taskId {}", merchantSn, contractTask.getId());
        for (ContractSubTask contractSubTask : contractSubTaskList) {
            ContractSubTask update = new ContractSubTask().setId(contractSubTask.getId()).setStatus(TaskStatus.PENDING.getVal()).setPriority(new Date());
            if (WosaiStringUtils.isNotEmpty(contractSubTask.getContract_id())) {
                update.setContract_id("");
            }
            if (SUPPORT_CHANGE_CARD_CHANNELS.contains(contractSubTask.getChannel()) &&
                    ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(contractSubTask.getTask_type())) {
                Map context = JSON.parseObject(contractTask.getEvent_context(), Map.class);
                if (!CollectionUtils.isEmpty(context)) {
                    Map cardRequest = (Map) context.get("cardRequestParam");
                    log.info("merchantSn {} subtaskId {} card {}", merchantSn, contractSubTask.getId(), BeanUtil.getPropString(cardRequest, com.wosai.data.dao.DaoConstants.ID));
                    bankCardService.resubmitChangePreInProgressVerify(cardRequest);
                }
            }
            contractSubTaskMapper.updateByPrimaryKey(update);
        }

        contractTaskBiz.update(new ContractTask().setId(contractTask.getId()).setStatus(0).setResult(JSON.toJSONString(result))).setPriority(new Date());
        //银行业务处理bank_direct_apply表
        BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(taskId);
        if (Objects.nonNull(apply) && Objects.equals(apply.getStatus(), BankDirectApplyConstant.Status.FAIL)) {
            BankDirectApply updateApply = new BankDirectApply();
            updateApply.setId(apply.getId());
            updateApply.setStatus(BankDirectApplyConstant.Status.APPLYING);
            updateApply.setProcess_status(BankDirectApplyConstant.ProcessStatus.CONTRACT_APPLYING);
            bankDirectApplyMapper.updateByPrimaryKeySelective(updateApply);
        }
        return res(200, "提交成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map editOpnion(long taskId, String msg) {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
        if (contractTask == null) {
            return res(400, "任务不存在");
        }
        if (contractTask.getStatus().equals(TaskStatus.SUCCESS.getVal())) {
            return res(400, "任务已成功无法编辑处理意见");
        }
        if (!ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType()) && !ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType())) {
            return res(400, "非新增商户入网或微信商家认证无法编辑");
        }
        ContractTask last = contractTaskMapper.getBySnAndType(contractTask.getMerchant_sn(), contractTask.getType());
        if (last.getId() != taskId) {
            return res(400, "非最新记录无法编辑处理意见");
        }

        ContractTask update = new ContractTask().setId(taskId).setEvent_msg(msg);
//        contractTaskMapper.updateByPrimaryKey(update);
        // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
        contractTaskBiz.update(update);
        insertEvent(contractTask, msg);

        return res(200, "编辑成功");
    }

    private void insertEvent(ContractTask contractTask, String msg) {
        Integer status = contractTask.getStatus();
        Map context = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Map merchant = (Map) context.get(ParamContextBiz.KEY_MERCHANT);
        MerchantContractOpinionEvent event = new MerchantContractOpinionEvent();
        TaskStatus taskStatus = TaskStatus.toStatus(status);
        if (taskStatus == TaskStatus.PROGRESSING) {
            event.setStatus(MerchantContractOpinionEvent.CONTRACT_PROGRESS);
        }
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType()) && taskStatus == TaskStatus.FAIL) {
            event.setStatus(MerchantContractOpinionEvent.CONTRACT_FAIL);
        }
        if (taskStatus == TaskStatus.WAIT_FOR_AUTH) {
            event.setStatus(MerchantContractOpinionEvent.WECHAT_AUTH_PROGRESS);
        }
        if (ProviderUtil.CONTRACT_TYPE_AUTH.equals(contractTask.getType()) && taskStatus == TaskStatus.FAIL) {
            event.setStatus(MerchantContractOpinionEvent.WECHAT_AUTH_FAIL);
        }
        event.setMsg(msg).setTimestamp(System.currentTimeMillis());
        event.setMerchantSn(contractTask.getMerchant_sn());
        event.setMerchantId(BeanUtil.getPropString(merchant, DaoConstants.ID));
        dataBusBiz.insertOpinionEvent(event);
    }


    private Map res(int i, String s) {
        Map result = Maps.newHashMap();
        result.put("code", i);
        result.put("message", s);
        return result;
    }
}
