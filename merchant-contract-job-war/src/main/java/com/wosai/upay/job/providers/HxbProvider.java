package com.wosai.upay.job.providers;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.sales.core.model.User;
import com.wosai.sales.core.service.UserService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.bankDirect.CcbDirectBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.HxbStatusEnum;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.helper.DateHelper;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.refactor.model.constant.ContractTaskEventContextPropertyConstant;
import com.wosai.upay.job.refactor.model.constant.RequestHxRelatedConstant;
import com.wosai.upay.job.refactor.service.localcache.IndustryMappingLocalCacheService;
import com.wosai.upay.job.refactor.utils.MapUtils;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.HXParam;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import com.wosai.upay.merchant.contract.model.terminal.LogOutTermInfoDTO;
import com.wosai.upay.merchant.contract.service.HXService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * @Description: 华夏银行处理类
 * <AUTHOR>
 * @Date 2021/4/6 15:35
 */
@Component(ProviderUtil.HXB_CHANNEL)
@Slf4j
@Transactional
public class HxbProvider extends AbstractProvider {

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;
    @Autowired
    HXService hxService;
    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Lazy
    @Autowired
    CcbDirectBiz ccbDirectBiz;

    @Value("${hxb_crm_template_code}")
    public String hxbCrmTemplateCode;

    @Value("${hxb_crm_dev_code}")
    public String hxbCrmDevCode;

    @Value("${hxb_app_template_code}")
    public String hxbAppTemplateCode;

    @Value("${hxb_app_dev_code}")
    public String hxbAppDevCode;

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    UserService userService;

    @Resource
    private IndustryMappingLocalCacheService industryMappingLocalCacheService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        // 华夏暂时不支持更新
        return null;
    }

    @Override
    public ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask contractSubTask) {
        Integer payWay = contractSubTask.getPayway();
        Map<String, String> contextParam = buildHxRequestContextParam(contractTask);
        HXParam hxParam = buildHxRequestParam(contractChannel, contractSubTask);
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            ContractResponse contractResponse;
            if (Objects.equals(payWay, PaywayEnum.ACQUIRER.getValue())) {
                contractResponse = hxService.contractMerchant(contextParam, hxParam);
                if (contractResponse.isSuccess()) {
                    final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    // 已提交待银行审核
                    ccbDirectBiz.recordViewProcess(apply, 10, new Date());
                }
            } else {
                // 其他支付类型：微信，支付宝、云闪付
                contractResponse = hxService.contractMerchantOtherPayWay(contractSubTask, hxParam);
                if (contractResponse.isSuccess() && payWay.equals(PaywayEnum.WEIXIN.getValue())) {
                    // 银行审核通过后,进入商户待实名认证状态时,在crm中通知销售,告知微信实名认证方法
                    final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
                    notifyCrmAndMerchant(crmUserId, contractTask);
                }
            }
            return contractResponse;
        }
        return null;
    }


    private Map<String, String> buildHxRequestContextParam(ContractTask contractTask) {
        if (Objects.isNull(contractTask) || org.apache.commons.lang3.StringUtils.isBlank(contractTask.getEvent_context())) {
            return Collections.emptyMap();
        }
        // 为了兼容原有接口(map转来转去...)
        Map resContextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        Map<String, String> contextParam = JSON.parseObject(contractTask.getEvent_context(), new TypeReference<Map<String, String>>() {
        });
        MapUtils.ifPresentAndValid(contextParam, ContractTaskEventContextPropertyConstant.MERCHANT_KEY,
                org.apache.commons.lang3.StringUtils::isNotBlank,
                merchantJson -> {
                    String industryId = BeanUtil.getPropString(JSON.parseObject(merchantJson, new TypeReference<Map<String, String>>() {
                            }),
                            ContractTaskEventContextPropertyConstant.INDUSTRY_ID_KEY);
                    industryMappingLocalCacheService.getIndustryMappingByIndustryId(industryId)
                            .ifPresent(industryCodeV2DTO ->
                                    resContextParam.put(RequestHxRelatedConstant.E_CNY_INDUSTRY_CODE_KEY, industryCodeV2DTO.getECnyCode()));
                });
        return resContextParam;
    }


    private HXParam buildHxRequestParam(ContractChannel contractChannel, ContractSubTask sub) {
        return super.buildParam(contractChannel, sub, HXParam.class);
    }


    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        // 华夏不支持更新操作
        return null;
    }

    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        HXParam hxParam = contractParamsBiz.buildContractParamsByParams(merchantProviderParams, HXParam.class);
        return hxService.wechatSubDevConfig(weixinConfig, hxParam);
    }

    @Override
    protected ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        ContractResponse response = hxService.queryContractStatusByContractId(contractSubTask, buildHxbParam(contractSubTask));
        if (response.isSystemFail()) {
            ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
            if (DateHelper.isOverOneMonth(contractTask.getCreate_at())) {
                response.setCode(460).setMessage("任务等待时长超过一个月，中止任务");
            }
        }
        return response;
    }

    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        return doHandleContractStatus(contractSubTask, response);
    }

    @Override
    public ContractResponse boundTerminal(AddTermInfoDTO addTermInfoDTO, int payWay, String terminalSn) {
        ContractResponse response = null;
        String redisKey = addTermInfoDTO.getDeviceId();
        if (redisTemplate.hasKey(redisKey)) {
            ThreadUtil.sleep(applicationApolloConfig.getHxBindTaskDelay());
        }
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_ALI.getValue(), HXParam.class);
            response = hxService.addTermInfo(addTermInfoDTO, hxParam, PaywayEnum.ALIPAY.getValue());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_WX.getValue(), HXParam.class);
            response = hxService.addTermInfo(addTermInfoDTO, hxParam, PaywayEnum.WEIXIN.getValue());
        }
        if (payWay == PaywayEnum.UNIONPAY.getValue()) {
            //华夏后续不会传这个虚拟终端,且调用华夏接口的时候并不需要传支付源子商户号,所以这里设置一个任意值避免@NotBlank注解拦截
            if (StringUtils.isEmpty(addTermInfoDTO.getSubMchId())) {
                addTermInfoDTO.setSubMchId("unionPay");
            }
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_UP.getValue(), HXParam.class);
            response = hxService.addTermInfo(addTermInfoDTO, hxParam, PaywayEnum.UNIONPAY.getValue());
        }
        if (!redisTemplate.hasKey(redisKey)) {
            //添加key
            redisTemplate.opsForValue().set(redisKey, redisKey, 5L, TimeUnit.SECONDS);
        }
        return response;
    }

    @Override
    public ContractResponse unbindTerminal(LogOutTermInfoDTO dto, int payWay, String terminalSn) {
        ContractResponse response = null;
        if (payWay == PaywayEnum.ALIPAY.getValue()) {
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_ALI.getValue(), HXParam.class);
            response = hxService.LogOutTermInfo(dto, hxParam, PaywayEnum.ALIPAY.getValue());
        }
        if (payWay == PaywayEnum.WEIXIN.getValue()) {
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_WX.getValue(), HXParam.class);
            response = hxService.LogOutTermInfo(dto, hxParam, PaywayEnum.WEIXIN.getValue());
        }
        if (payWay == PaywayEnum.UNIONPAY.getValue()) {
            //华夏后续不会传这个虚拟终端,且调用华夏接口的时候并不需要传支付源子商户号,所以这里设置一个任意值避免@NotBlank注解拦截
            if (StringUtils.isEmpty(dto.getSubMchId())) {
                dto.setSubMchId("unionPay");
            }
            HXParam hxParam = contractParamsBiz.buildContractParams(ChannelEnum.HX_UP.getValue(), HXParam.class);
            response = hxService.LogOutTermInfo(dto, hxParam, PaywayEnum.UNIONPAY.getValue());
        }
        return response;
    }

    /**
     * 查询的状态做处理
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        //失败 修改状态
        if (response.isBusinessFail()) {
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(response.getMessage());
        } else if (response.isSystemFail()) {
            //可重试异常 不做任何处理
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            //返回值
            Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
            Map<String, Object> callbackMsg = response.getResponseParam();
            //银行流程状态 1待提交 2处理中 3已完成 5 退回
            final Integer bpmStatus = BeanUtil.getPropInt(callbackMsg, "bpmStatus");
            final String processTaskName = BeanUtil.getPropString(callbackMsg, "processTaskName");
            final String originResult = contractSubTask.getResult();
            final String message = HxbStatusEnum.getMessage(bpmStatus);
            //获取银行直连申请任务
            final Long pTaskId = contractSubTask.getP_task_id();
            final BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(pTaskId);
            final ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(pTaskId);
            final Map eventContext = contractTask.getEventContext();
            Map<String, Object> license = (Map) eventContext.get("merchantBusinessLicense");
            final Integer type = BeanUtil.getPropInt(license, "type");
            updateSubAndParentTaskResult(contractSubTask.getId(), originResult, message + ":" + processTaskName);
            if (Objects.equals(bpmStatus, 5)) {
                //华夏退回
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(message + ":" + processTaskName);
            }
            if (!Objects.equals(bpmStatus, 3)) {
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage(message + ":" + processTaskName);
            }
            //银行已审核,待商户微信实名认证
            ccbDirectBiz.recordViewProcess(apply, Lists.newArrayList(0, 1).contains(type) ? 40 : 41, new Date());
            return new HandleQueryStatusResp().setSuccess(true)
                    .setMessage(message + ":" + processTaskName);
        }
    }

    /**
     * <AUTHOR>
     * @Description: 在crm和收钱吧app中通知销售, 告知微信实名认证方法
     * @time 17:13
     */
    public void notifyCrmAndMerchant(String userId, ContractTask contractTask) {
        try {
            String merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(Maps.newHashMap(), "hxb-1028-3");
            final String imageUrl = replaceHttp(merchantChannelCode);
            //获取商户信息
            final String merchantSn = contractTask.getMerchant_sn();
            final Map merchant = merchantService.getMerchantBySn(merchantSn);
            if (!StringUtils.isEmpty(userId)) {
                //crm
                final ClientSideNoticeSendModel crmSendModel = new ClientSideNoticeSendModel();
                //产品开发标识
                crmSendModel.setDevCode(hxbCrmDevCode);
                //短信通知模板标识
                crmSendModel.setTemplateCode(hxbCrmTemplateCode);
                //账户Id
                crmSendModel.setAccountId(userId);
                //需要传的数据
                crmSendModel.setData(CollectionUtil.hashMap(
                        "merchant", WosaiMapUtils.getString(merchant, Merchant.NAME),
                        "text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",华夏入网审核已通过,请联系商户完成微信实名认证",
                        "text2", "请商户通过微信扫描华夏服务商授权码,完成微信实名认证",
                        "qrCodeImg", imageUrl));
                crmSendModel.setClientSides(Lists.newArrayList("TERMINALCRM"));
                //请求时间戳
                crmSendModel.setTimestamp(System.currentTimeMillis());
                log.info("华夏给crm发送通知参数{},商户号:{}", JSONObject.toJSONString(crmSendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.send(crmSendModel);
            }
            //给商户超管角色发送通知（收钱吧APP)
            final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            //生成二维码和文案
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(hxbAppDevCode);
                sendModel.setTemplateCode(hxbAppTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                //需要传的数据
                sendModel.setData(CollectionUtil.hashMap("text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",华夏活动审核通过,请用微信扫描下方华夏服务商授权码,完成微信实名认证",
                        "text2", "认证通过后,方可参加活动",
                        "qrCodeImg", imageUrl));
                log.info("华夏给收钱吧app发送通知参数{},商户号:{}", JSONObject.toJSONString(sendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }
        } catch (Exception e) {
            log.error("华夏通知发送通知异常id:{},异常信息{}", contractTask.getId(), e);
        }
    }


    public HXParam buildHxbParam(ContractSubTask contractSubTask) {
        final ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        return buildParam(contractChannel, contractSubTask, HXParam.class);
    }

    /**
     * <AUTHOR>
     * @Description: 同时更新sub_task和task中的result
     * @time 17:24
     */
    public void updateSubAndParentTaskResult(Long id, String originResult, String targetResult) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(id);
        final Long pTaskId = contractSubTask.getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        //机器人推送 包含字段有：城市，商户号，推广人|手机号，商户进件状态
        dingTalkRemind(task, targetResult);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        //只有影响主任务的时候才会去更新主任务的中的result
        if (Objects.equals(contractSubTask.getStatus_influ_p_task(), 1)) {
            final ContractTask contractTask = new ContractTask();
            contractTask.setId(pTaskId);
            contractTask.setResult(targetResult);
            contractTask.setPriority(task.getPriority());
            contractTaskMapper.updateByPrimaryKey(contractTask);
        }
    }

    public static String replaceHttp(String url) {
        if (com.wosai.mpay.util.StringUtils.isEmpty(url)) {
            return url;
        }
        if (url.startsWith("http:")) {
            url = url.replace("http:", "https:");
        }
        try {
            return URLEncoder.encode(url, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("replaceHttp EncodingException :{}", e);
        }
        return url;
    }

    /**
     * @param task
     * @param targetResult
     * <AUTHOR>
     * @Description: 城市，商户号，推广人|手机号，商户进件状态 异步发送钉钉提醒
     * @time 16:46
     */
    private void dingTalkRemind(ContractTask task, String targetResult) {
        try {
            final Map merchant = merchantService.getMerchantByMerchantSn(task.getMerchant_sn());
            //城市
            final String city = BeanUtil.getPropString(merchant, Merchant.CITY);
            //商户号
            final String merchantSn = task.getMerchant_sn();
            //推广人|手机号
            final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(task.getId());
            final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
            final Map user = userService.getUser(crmUserId);
            final String cellphone = BeanUtil.getPropString(user, User.CELLPHONE);
            final String linkman = BeanUtil.getPropString(user, User.LINKMAN);
            final String join = Joiner.on("|").skipNulls().join(Lists.newArrayList(linkman, cellphone));
            final String message = String.format("华夏进件状态更新提醒 %s, %s, %s, %s", city, merchantSn, join, targetResult);
            log.info("华夏状态更新提醒:{}", message);
            chatBotUtil.sendMessageToHxbChatBot(message);
        } catch (Exception exception) {
            log.error("华夏状态更新提醒异常:{}", exception);
        }
    }

    @Override
    public void handleSqbTerminalBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HXB.getValue());
        //provider_terminal 不存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        if (Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbTerminal();
            try {
                providerTerminalBiz.sqbTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), provider, vendorAppAppid, terminalSn, storeSn);
                log.info("终端绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.error("终端绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            terminalSn));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("终端绑定=>已经存在收钱吧终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider,
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        terminalSn));
    }


    @Override
    public void handleSqbStoreTerminal(String storeSn, String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HXB.getValue());
        //provider_terminal 是否存在该门店的终端记录
        final ProviderTerminal providerTerminal = providerTerminalBiz.existStoreProviderTerminal(provider, storeSn, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (CollectionUtils.isEmpty(params)) {
            return;
        }
        //不存在则绑定
        if (Objects.isNull(providerTerminal)) {
            final String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdBySqbStore();
            try {
                providerTerminalBiz.sqbStoreTerminalConnectionProviderTerminal(merchantSn, providerTerminalId, acquirerParams.getPay_merchant_id(), provider, storeSn);
                log.info("华夏门店绑定=>新创建终端Id:{},门店:{},交易参数:{},provider:{}", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.info("华夏门店绑定失败=>新创建终端Id:{},门店:{},交易参数:{},provider:{},", providerTerminalId, storeSn, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(
                            merchantSn,
                            param.getPay_merchant_id(),
                            provider,
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                            providerTerminalId,
                            storeSn,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("门店绑定=>已经存在终端Id:{},门店:{},交易参数:{}", providerTerminal.getProvider_terminal_id(), storeSn, JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider, param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BIND_STORE_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        storeSn,
                        null)
        );
    }


    @Override
    public void handleSqbTerminalUnBind(MerchantInfo merchant, Map terminal, Integer provider) {
        final String storeId = BeanUtil.getPropString(terminal, Terminal.STORE_ID);
        StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        final String merchantSn = merchant.getSn();
        final String terminalSn = BeanUtil.getPropString(terminal, Terminal.SN);
        //provider_terminal 不存在该门店的终端记录
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HXB.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existProviderTerminal(provider, storeSn, merchantSn, terminalSn, acquirerParams.getPay_merchant_id());
        //无需解绑
        if (Objects.isNull(providerTerminal)) {
            return;
        }
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        //判断是否需要插入解绑任务
        final List<MerchantProviderParams> needDeleteMerchantProviderParams = providerTerminalBiz.getNeedDeleteMerchantProviderParams(providerTerminal, params);

        needDeleteMerchantProviderParams.stream().forEach(
                param -> providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        provider, param.getPayway(),
                        ProviderTerminalTaskTypeEnum.UNBIND_TERMINAL_ALL_SUB_MCH.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        providerTerminal.getStore_sn(),
                        terminalSn)
        );
    }

    @Override
    public void handleSqbMerchantProviderTerminal(String merchantSn, Integer provider) {
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_HXB.getValue());
        final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(provider, merchantSn, acquirerParams.getPay_merchant_id());
        //该通道下所有间连子商户号
        List<MerchantProviderParams> params = getMerchantProviderParamsByProvider(provider, merchantSn);
        if (Objects.isNull(providerTerminal)) {
            //不存在则绑定
            final String termNo = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
            try {
                providerTerminalBiz.merchantConnectionProviderTerminal(merchantSn, termNo, acquirerParams.getPay_merchant_id(), provider);
                log.info("华夏商户绑定=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider);
            } catch (Exception e) {
                log.error("华夏商户绑定失败=>商户号:{},不存在商户级别终端,开始重新绑定,终端Id:{},交易参数:{},provider:{}", merchantSn, termNo, JSONObject.toJSONString(params), provider, e);
                return;
            }
            params.stream().forEach(param ->
                    providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                            param.getPay_merchant_id(),
                            param.getProvider(),
                            param.getPayway(),
                            ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                            termNo,
                            null,
                            null));
            return;
        }
        //判断是否需要插入绑定任务
        final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, params);
        //已经绑定了不用处理
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return;
        }
        log.info("华夏商户绑定=>商户号:{},已经存在商户级别终端,新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
        //未绑定的子商户号插入task
        merchantProviderParams.stream().forEach(param ->
                providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                        param.getPay_merchant_id(),
                        param.getProvider(),
                        param.getPayway(),
                        ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                        providerTerminal.getProvider_terminal_id(),
                        null,
                        null)
        );
    }

    @Override
    public void createProviderTerminal(String merchantSn, Integer provider) {
        doCreateProviderTerminal(merchantSn, provider);
    }


}

