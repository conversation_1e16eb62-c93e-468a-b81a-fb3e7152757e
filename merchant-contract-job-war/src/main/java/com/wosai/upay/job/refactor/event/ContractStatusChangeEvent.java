package com.wosai.upay.job.refactor.event;

import com.wosai.databus.event.merchant.contract.MerchantContractStatusChangeEvent;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 进件状态变更的事件
 * <AUTHOR>
 * @date 2024/9/12
 */
@Getter
public class ContractStatusChangeEvent extends ApplicationEvent {

    public ContractStatusChangeEvent(MerchantContractStatusChangeEvent event) {
        super(event);
    }

    @Override
    public MerchantContractStatusChangeEvent getSource() {
        return (MerchantContractStatusChangeEvent) super.getSource();
    }


}
