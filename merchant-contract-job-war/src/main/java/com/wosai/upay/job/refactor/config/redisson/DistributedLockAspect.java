package com.wosai.upay.job.refactor.config.redisson;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 分布式锁切面类
 *
 * <AUTHOR>
 * @date 2023/9/13 10:02
 */
@Aspect
@Component
@Slf4j
public class DistributedLockAspect {

    @Resource
    private RedissonClient redissonClient;


    /**
     * 加锁
     *
     * @param joinPoint       切点
     * @param distributedLock 加锁注解
     * @return 方法返回值 or null
     * @throws Throwable 异常
     */
    @Around("@annotation(distributedLock)")
    public Object executeWithLock(ProceedingJoinPoint joinPoint, DistributedLock distributedLock) throws Throwable {
        String lockKey = distributedLock.key();
        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;
        try {
            int executeNum = 0;
            do {
                isLocked = distributedLock.autoLease()
                        ? lock.tryLock(distributedLock.waitTime(), distributedLock.timeUnit())
                        : lock.tryLock(distributedLock.waitTime(), distributedLock.leaseTime(), distributedLock.timeUnit());
                if (isLocked) {
                    return joinPoint.proceed();
                }
                executeNum++;
            } while (executeNum <= distributedLock.retryTimes());
            if (distributedLock.throwExceptionOnFailure()) {
                throw new LockFailedException("failed to acquire lock for key: " + lockKey);
            } else {
                distributedLock.lockFailedHandler().newInstance().accept(lockKey);
                return null;
            }
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    log.error("failed to release lock for key: {}", lockKey, e);
                }
            }
        }
    }
}
