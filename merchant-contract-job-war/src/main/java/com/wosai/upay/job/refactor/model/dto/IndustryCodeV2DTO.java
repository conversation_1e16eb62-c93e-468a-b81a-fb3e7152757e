package com.wosai.upay.job.refactor.model.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * industry_code_v2表DTO对象
 *
 * <AUTHOR>
 */
@Data
public class IndustryCodeV2DTO {


    @ApiModelProperty("主键id")
    private String id;


    @ApiModelProperty("行业id")
    private String industryId;


    @ApiModelProperty("银联对应行业编号(微信对私)")
    private String unionCodeWeixinPrivate;


    @ApiModelProperty("银联对应行业编号(微信对公)")
    private String unionCodeWeixinPublic;


    @ApiModelProperty("网联对应行业编号(微信对私)")
    private String nuccCodeWeixinPrivate;


    @ApiModelProperty("网联对应行业编号(微信对公)")
    private String nuccCodeWeixinPublic;


    @ApiModelProperty("银联对应行业编号(支付宝对私)")
    private String unionCodeAlipayPrivate;


    @ApiModelProperty("银联对应行业编号(支付宝对公)")
    private String unionCodeAlipayPublic;


    @ApiModelProperty("网联对应行业编号(支付宝对私)")
    private String nuccCodeAlipayPrivate;


    @ApiModelProperty("网联对应行业编号(支付宝对公)")
    private String nuccCodeAlipayPublic;


    @ApiModelProperty("银联对应行业编号(翼支付对公)")
    private String nuccCodeBestpayPublic;


    @ApiModelProperty("银联对应行业编号(翼支付对私)")
    private String nuccCodeBestpayPrivate;


    @ApiModelProperty("翼支付对应行业编号")
    private String nuccCodeBestpay;


    @ApiModelProperty("拉卡拉对应行业编号")
    private String lakalaCode;


    @ApiModelProperty("通联对应行业编号")
    private String tlCode;


    @ApiModelProperty("")
    private String version;


    @ApiModelProperty("万码支付宝")
    private String wmAly;


    @ApiModelProperty("万码微信对私")
    private String wmWeixinPrivate;


    @ApiModelProperty("万码微信对公")
    private String wmWeixinPublic;


    @ApiModelProperty("银联开放平台")
    private String unionOpenCode;


    @ApiModelProperty("直连微信")
    private String directConnectWeixinCode;


    @ApiModelProperty("万码支付宝MCC")
    private String wmAlyMcc;


    @ApiModelProperty("数字人民币对应行业编号")
    private String eCnyCode;
}
