package com.wosai.upay.job.constant;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
public class ContractRuleConstants {

    /**
     * 切换收单机构报备规则组的特征，包含change2
     */
    public static final String CHANGE_ACQUIRER_RULE_GROUP_FEATURE = "change2";


    /**
     * 小微升级重新入网包含microUpgrade2
     */
    public static final String RULE_GROUP_MICROUPGRADE = "microUpgrade2";

    /**
     * 切换到lklV3的报备规则组
     */
    public static final String CHANGE_TO_LKLORG_RULE_GROUP = "change2lklorg";

    /**
     * 切换到通联收银宝的报备规则组
     */
    public static final String CHANGE_TO_TONGLIANV2_RULE_GROUP = "change2tonglianV2";
    /**
     * 切换到山东 通联收银宝的报备规则组
     */
    public static final String CHANGE_TO_TONGLIANV2_SD_RULE_GROUP = "change2tonglianV2-sd";
    /**
     * 切换到通联的报备规则组
     */
    public static final String CHANGE_TO_TONGLIAN_RULE_GROUP = "change2tonglian";

    /**
     * 切换到银商的报备规则组
     */
    public static final String CHANGE_TO_UMS_RULE_GROUP = "change2ums";

    /**
     * 切换到邮储的报备规则组
     */
    public static final String CHANGE_TO_PSBC_RULE_GROUP = "change2Psbc";

    /**
     * 切换到建行的报备规则组
     */
    public static final String CHANGE_TO_CCB_RULE_GROUP = "change2Ccb";

    /**
     * 切换到广发的报备规则组
     */
    public static final String CHANGE_TO_CGB_RULE_GROUP = "change2cgb";

    /**
     * 切换到华夏的报备规则组
     */
    public static final String CHANGE_TO_HXB_RULE_GROUP = "change2Hxb";

    /**
     * 切换到泸州的报备规则组
     */
    public static final String CHANGE_TO_LZB_RULE_GROUP = "change2Lzb";

    /**
     * 切换到工商的报备规则组
     */
    public static final String CHANGE_TO_ICBC_RULE_GROUP = "change2Icbc";

    /**
     * 切换到平安的报备规则组
     */
    public static final String CHANGE_TO_PAB_RULE_GROUP = "change2Pab";

    /**
     * 切换到浙江泰隆银行规则组
     */
    public static final String CHANGE_TO_ZJTLCB_RULE_GROUP = "change2Zjtlcb";

    /**
     * 切换到拉卡拉的报备规则组
     */
    public static final String CHANGE_TO_LKL_RULE_GROUP = "change2lkl";

    /**
     * 切换到富友的报备规则组
     */
    public static final String CHANGE_TO_FUYOU_RULE_GROUP = "change2fuyou";

    /**
     * 切换到海科的报备规则组
     */
    public static final String CHANGE_TO_HAIKE_RULE_GROUP = "change2haike";
    /**
     * 切换到国通的报备规则组
     */
    public static final String CHANGE_TO_GUOTONG_RULE_GROUP = "change2guotong";


    public static final String CHANGE_TO_CMBC_RULE_GROUP = "change2cmbc";

    /**
     * lklorg 进件规则的特性
     */
    public static final String LKL_ORG_CONTRACT_RULE_FEATURE = "lkl-1033";

    /**
     * 建行普通微信报备规则
     */
    public static final String CCB_NORMAL_WEIXIN_RULE = "ccb-1026-3";

    /**
     * 广发普通微信报备规则
     */
    public static final String CGB_NORMAL_WEIXIN_RULE = "cgb-1024-3";

    /**
     * 邮储普通微信报备规则
     */
    public static final String PSBC_NORMAL_WEIXIN_RULE = "psbc-1023-3";

    /**
     * 海科普通微信报备规则
     */
    public static final String HAIKE_NORMAL_WEIXIN_RULE = "haike-1037-3-176174614";
    /**
     * 海科普通支付宝报备规则
     */
    public static final String HAIKE_NORMAL_ALI_RULE = "haike-1037-2-2088011691288213";

    /**
     * 通联普通支付宝报备规则
     */
    public static final String TONGLIAN_NORMAL_ALI_RULE = "tonglian-1020-2";

    /**
     * 通联普通微信报备规则
     */
    public static final String TONGLIAN_NORMAL_WEIXIN_RULE = "tonglian-1020-3";

    /**
     * 拉卡拉普通微信报备规则
     */
    public static final String LKL_NORMAL_WEIXIN_RULE = "lkl-1016-3-32631798";

    /**
     * 拉卡拉组织普通微信报备规则
     */
    public static final String LKL_ORG_NORMAL_WEIXIN_RULE = "lkl-1033-3-32631798";

    /**
     * 拉卡拉组织普通支付宝报备规则
     */
    public static final String LKL_ORG_NORMAL_ALI_RULE = "lkl-1033-2-2088011691288213";

    /**
     * 富友普通微信报备规则
     */
    public static final String FUYOU_NORMAL_WEIXIN_RULE = "fuyou-1038-3";

    /**
     * 富友普通云闪付报备规则
     */
    public static final String FUYOU_NORMA_UNION_PAY_RULE = "fuyou-1038-17";

    /**
     * 华夏银行普通微信报备规则
     */
    public static final String HXB_NORMAL_WEIXIN_RULE = "hxb-1028-3";

    public static final String LZB_NORMAL_WEIXIN_RULE = "lzb-1049-3";

    /**
     * 工商银行普通微信报备规则
     */
    public static final String ICBC_NORMAL_WEIXIN_RULE = "icbc-1030-3";

    /**
     * 平安银行普通微信报备规则
     */
    public static final String PAB_NORMAL_WEIXIN_RULE = "pab-1040-3";
    /**
     * 银商普通微信报备规则
     */
    public static final String UMS_NORMAL_WEIXIN_RULE = "ums-1018-3";
    /**
     * 浙江泰隆银行普通微信报备规则
     */
    public static final String ZJTLCB_NORMAL_WEIXIN_RULE = "zjtlcb-1043-3";
    /**
     * 国通星驿普通微信报备规则
     */
    public static final String GUOTONG_NORMAL_WEIXIN_RULE = "guotong-1048-3";

    public static final String GUOTONG_NORMAL_UNIONPAY_RULE = "guotong-1048-17";

    public static final String UMB_NORMAL_WEIXIN_RULE = "lzb-1050-3";

    public static final String CHANGE_TO_UMB_RULE_GROUP = "change2Umb";



}
