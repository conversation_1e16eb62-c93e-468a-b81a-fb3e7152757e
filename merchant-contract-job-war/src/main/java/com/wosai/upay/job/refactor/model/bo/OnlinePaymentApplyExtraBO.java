package com.wosai.upay.job.refactor.model.bo;

import lombok.Data;

/**
 * open_online_payment_apply中的extra存储的信息
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@Data
public class OnlinePaymentApplyExtraBO {

    /**
     * 微信子商户号信息
     */
    private OnlinePaymentApplyWeixinBO weixin;
    /**
     * 支付宝子商户号信息
     */
    private OnlinePaymentApplyAliBO ali;
    /**
     * 费率套餐信息
     */
    private OnlinePaymentApplyComboBO combo;

    /**
     * 此参数只有在申请单是成功的时候有意义
     * 申请单是否生效中，默认是true。线上参数和跨城参数都是使用中的
     * 当关闭跨城收款之后，变为false。线上参数和跨城参数都被删除了
     */
    private Boolean effect = true;
}
