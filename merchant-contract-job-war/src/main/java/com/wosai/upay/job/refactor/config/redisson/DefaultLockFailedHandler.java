package com.wosai.upay.job.refactor.config.redisson;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.function.Consumer;

/**
 * 默认的加锁失败处理逻辑
 *
 * <AUTHOR>
 * @date 2023/9/13 10:19
 */
public class DefaultLockFailedHandler implements Consumer<String> {

    private static final Logger logger = LoggerFactory.getLogger(DefaultLockFailedHandler.class);

    @Override
    public void accept(String lockKey) {
        logger.warn("lock failed, key = {}", lockKey);
    }
}
