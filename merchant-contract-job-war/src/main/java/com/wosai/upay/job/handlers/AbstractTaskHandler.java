package com.wosai.upay.job.handlers;

import com.wosai.middleware.hera.toolkit.metrics.Timed;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021-04-08
 */
@Slf4j
public abstract class AbstractTaskHandler<R> implements Handler<ContractTask, R> {

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Override
    @Timed(value = "TaskHandle")
    public R handle(ContractTask task) throws Exception {
        try {
            AbstractTaskHandler<R> handler = (AbstractTaskHandler<R>) AopContext.currentProxy();
            task = contractTaskMapper.selectByPrimaryKey(task.getId());
            return handler.doHandle(task);
        } catch (Exception e) {
            log.error("handleTask error", e);
            chatBotUtil.sendMessageToContractWarnChatBot("merchant: " + task.getMerchant_sn() + "  task_id: " + task.getId() + "  error: " + ExceptionUtil.getThrowableMsg(e));
            handleError(task, e);
        }
        return null;
    }

    /**
     * 处理异常
     *
     * @param task
     * @param e
     * @throws Exception
     */
    protected abstract void handleError(ContractTask task, Exception e) throws Exception;

    /**
     * 具体处理逻辑
     * <p>
     * 子类如果需要事务控制，需要加上  @Transactional
     *
     * @param task
     * @return
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public abstract R doHandle(ContractTask task) throws Exception;
}
