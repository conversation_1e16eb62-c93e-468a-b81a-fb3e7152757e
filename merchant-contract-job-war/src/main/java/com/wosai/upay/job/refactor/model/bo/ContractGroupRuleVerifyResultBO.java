package com.wosai.upay.job.refactor.model.bo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 进件通道规则校验结果
 *
 * <AUTHOR>
 * @date 2024/3/6 09:38
 */
@Data
@AllArgsConstructor
public class ContractGroupRuleVerifyResultBO {

    /**
     * constructor
     *
     * @param checkPass 是否校验通过
     */
    public ContractGroupRuleVerifyResultBO(boolean checkPass) {
        this.checkPass = checkPass;
    }

    /**
     * 校验结果 true-通过 false-不通过
     */
    private boolean checkPass;

    /**
     * 提示信息
     */
    private String message;

    /**
     * 是否校验未通过
     *
     * @return 校验结果
     */
    public boolean isCheckFail() {
        return !checkPass;
    }

    /**
     * 校验通过
     *
     * @return 校验结果
     */
    public static ContractGroupRuleVerifyResultBO success() {
        return new ContractGroupRuleVerifyResultBO(true);
    }

    /**
     * 校验不通过
     *
     * @param message 提示信息
     * @return 校验结果
     */
    public static ContractGroupRuleVerifyResultBO fail(String message) {
        return new ContractGroupRuleVerifyResultBO(false, message);
    }

}
