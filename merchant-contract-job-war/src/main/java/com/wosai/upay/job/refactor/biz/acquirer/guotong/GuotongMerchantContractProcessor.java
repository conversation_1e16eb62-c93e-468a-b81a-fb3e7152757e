package com.wosai.upay.job.refactor.biz.acquirer.guotong;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.guotong.BaseGuotongResponseModel;
import com.wosai.upay.job.model.guotong.GuotongMerchantInfoModel;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.guotong.request.GuotongMerchantInfoQueryRequest;
import com.wosai.upay.merchant.contract.model.provider.GuotongParam;
import com.wosai.upay.merchant.contract.service.GuotongService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

/**
 * 国通商户进件处理
 *
 * <AUTHOR>
 * @date 2025/1/24
 */
@Slf4j
@Component
public class GuotongMerchantContractProcessor {

    @Resource
    private GuotongService guotongService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    /**
     * 获取国通商户信息
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public Optional<GuotongMerchantInfoModel> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        try {
            GuotongParam guotongParam = contractParamsBiz.buildContractParams(ChannelEnum.GUOTONG.getValue(), GuotongParam.class);
            MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_GUOTONG.getValue());
            if (Objects.isNull(acquirerParams)) {
                return Optional.empty();
            }
            GuotongMerchantInfoQueryRequest request = new GuotongMerchantInfoQueryRequest();
            request.setAcquirerMerchantId(acquirerParams.getPay_merchant_id());
            ContractResponse contractResponse = guotongService.queryMerchant(request, guotongParam);
            if (contractResponse == null || !contractResponse.isSuccess() || contractResponse.getResponseParam() == null) {
                return Optional.empty();
            }
            BaseGuotongResponseModel<GuotongMerchantInfoModel> guotongMerchantInfoQueryResponse = JSON.parseObject(JSON.toJSONString(contractResponse.getResponseParam()), new TypeReference<BaseGuotongResponseModel<GuotongMerchantInfoModel>>() {
            });
            if (!guotongMerchantInfoQueryResponse.isSuccess()) {
                return Optional.empty();
            }
            return Optional.of(guotongMerchantInfoQueryResponse.getData());
        } catch (Exception e) {
            log.error("获取国通商户信息异常, merchantSn:{}", merchantSn, e);
            return Optional.empty();
        }
    }

    /**
     * 获取国通商户银行卡号
     *
     * @param merchantSn 商户号
     * @return 银行卡号
     */
    public Optional<String> getBankAccountNo(String merchantSn) {
        Optional<GuotongMerchantInfoModel> merchantInfo = getMerchantInfo(merchantSn);
        return merchantInfo.map(GuotongMerchantInfoModel::getAccountNo);
    }

}
