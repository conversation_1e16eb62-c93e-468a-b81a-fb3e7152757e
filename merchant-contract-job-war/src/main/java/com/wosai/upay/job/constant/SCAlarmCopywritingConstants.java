package com.wosai.upay.job.constant;

/**
 * 收钱吧结算卡开户行清算行号不支持报错文案常量类
 */
public class SCAlarmCopywritingConstants {

    /**
     * 富友报错文案
     */
    public static final String FY_ALARM_COPYWRITING = "参数格式错误:联行号未找到";

    /**
     * 海科报错文案
     */
    public static final String HK_ALARM_COPYWRITING = "结算卡开户行清算行行号不支持";

    /**
     * 拉卡拉报错文案
     */
    public static final String LKL_ALARM_COPYWRITING_ERROR = "开户行校验不通过，该开户行号在行名行号表中不存在";

    /**O
     * 操作流程
     */
    public static final String ALARM_OPERATION_PROCESS = "请将信息反馈至收单机构，添加开户行清算行号";
}
