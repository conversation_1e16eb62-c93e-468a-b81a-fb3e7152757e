package com.wosai.upay.job.web;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.service.AppRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by hzq on 20/2/5.
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class AppRpcServiceImpl implements AppRpcService {

    @Autowired
    AcquirerService acquirerService;

    @Autowired
    MerchantService merchantService;

    @Override
    public String acquirer(Map<String, Object> param) {
        try {
            String merchantId = BeanUtil.getPropString(param, CommonModel.MERCHANT_ID);
            Map merchant = merchantService.getMerchantByMerchantId(merchantId);
            String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            if (!StringUtil.empty(merchantSn)) {
                return acquirerService.getMerchantAcquirer(merchantSn);
            }
        } catch (Exception e) {
            log.error("error get acquirer", e);
            return AcquirerTypeEnum.LKL.getValue();
        }
        return AcquirerTypeEnum.LKL.getValue();
    }

    @Override
    public Map getAcquirerConfig(Map param) {
        String merchantId = BeanUtil.getPropString(param, CommonModel.MERCHANT_ID);
        if(WosaiStringUtils.isEmpty(merchantId)) {
            throw new CommonPubBizException("merchant_id不能为空");
        }
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, "sn");
        if (WosaiStringUtils.isEmpty(merchantSn)) {
            throw new CommonPubBizException("商户不存在");
        }
        String acquirer = acquirerService.getMerchantAcquirer(merchantSn);
        if (WosaiStringUtils.isEmpty(acquirer)) {
            throw new CommonPubBizException("未查询到商户收单机构");
        }

        Map config = getMap("acquirer-map", "{}");
        Map result = WosaiMapUtils.getMap(config, acquirer);
        if (WosaiMapUtils.isEmpty(result)) {
            throw new CommonPubBizException("收单机构配置不存在");
        }

        return result;
    }

    private static Config appBaseConfig = ConfigService.getConfig("app.base");

    private ObjectMapper objectMapper = new ObjectMapper();
    private Map getMap(String key, String defaultParams) {
        try {
            return objectMapper.readValue(appBaseConfig.getProperty(key, defaultParams), Map.class);
        } catch (IOException e) {
            log.error("Apollo getMap:{},{},{}", key, defaultParams, e);
            return new HashMap();
        }
    }
}
