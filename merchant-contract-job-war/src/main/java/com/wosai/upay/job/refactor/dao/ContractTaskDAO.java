package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ContractTaskDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * 总任务表表数据库访问层 {@link ContractTaskDO}
 * 对ContractTaskMapper层做出简单封装 {@link ContractTaskDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ContractTaskDAO extends AbstractBaseDAO<ContractTaskDO, ContractTaskDynamicMapper> {

    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    public ContractTaskDAO(SqlSessionFactory sqlSessionFactory, ContractTaskDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据任务type和priority查询指定数量任务
     *
     * @param type     任务类型
     * @param status   任务状态
     * @param priority 优先级时间
     * @param limit    限制数量
     * @return 任务列表
     */
    public List<ContractTaskDO> listContractTasksWithLimit(String type, Timestamp priority, Integer status, Integer limit) {
        if (Objects.isNull(limit) || Objects.isNull(priority) || StringUtils.isBlank(type)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ContractTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractTaskDO::getType, type)
                .eq(Objects.nonNull(status), ContractTaskDO::getStatus, status)
                .ge(ContractTaskDO::getPriority, priority)
                .le(ContractTaskDO::getPriority, new Timestamp(System.currentTimeMillis()))
                .orderByAsc(ContractTaskDO::getPriority)
                .last("limit " + limit);
        return entityMapper.selectList(queryWrapper);
    }

    /**
     * 批量更新任务(主任务和子任务)
     *
     * @param contractTaskDO     主任务
     * @param contractSubTaskDOS 子任务列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateTasks(ContractTaskDO contractTaskDO, List<ContractSubTaskDO> contractSubTaskDOS) {
        contractTaskDO.setUpdateAt(null);
        contractSubTaskDOS.forEach(t -> t.setUpdateAt(null));
        updateByPrimaryKeySelective(contractTaskDO);
        contractSubTaskDAO.batchUpdateByIdSelective(contractSubTaskDOS);
    }

    /**
     * 批量插入任务(主任务和对应子任务)
     *
     * @param contractTaskDO     主任务
     * @param contractSubTaskDOS 子任务列表
     */
    @Transactional(rollbackFor = Exception.class)
    public Long batchInsertTasks(ContractTaskDO contractTaskDO, List<ContractSubTaskDO> contractSubTaskDOS) {
        insertOne(contractTaskDO);
        contractSubTaskDOS.forEach(t -> t.setPTaskId(contractTaskDO.getId()));
        contractSubTaskDAO.batchInsert(contractSubTaskDOS);
        return contractTaskDO.getId();
    }

    /**
     * 根据商户号和任务类型获取最新一条任务(按照创建时间)
     *
     * @param merchantSn 商户号
     * @param taskType   任务类型
     * @return 任务列表
     */
    public Optional<ContractTaskDO> getLastedTaskBySnAndType(String merchantSn, String taskType) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        LambdaQueryWrapper<ContractTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractTaskDO::getMerchantSn, merchantSn)
                .eq(StringUtils.isNotBlank(taskType), ContractTaskDO::getType, taskType);
        List<ContractTaskDO> contractTaskDOS = entityMapper.selectList(queryWrapper);
        return contractTaskDOS.stream().max((o1, o2) -> {
            if (o1.getCreateAt().getTime() == o2.getCreateAt().getTime()) {
                return 0;
            }
            return o1.getCreateAt().getTime() - o2.getCreateAt().getTime() > 0 ? 1 : -1;
        });
    }

    /**
     * 根据任务id更新任务状态,忽略更新时间
     *
     * @param contractTaskDO 任务
     * @return 更新数量
     */
    public Integer updateByIdIgnoreUpdateTime(ContractTaskDO contractTaskDO) {
        if (Objects.isNull(contractTaskDO)) {
            return 0;
        }
        contractTaskDO.setUpdateAt(null);
        return updateByPrimaryKeySelective(contractTaskDO);
    }

    /**
     * 删除失败的任务和子任务
     *
     * @param merchantSn
     * @param taskType
     */
    public void batchDeleteFailTaskAndSubTask(String merchantSn, String taskType) {
        if (StringUtils.isBlank(merchantSn)) {
            return;
        }
        LambdaQueryWrapper<ContractTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractTaskDO::getMerchantSn, merchantSn)
                .eq(StringUtils.isNotBlank(taskType), ContractTaskDO::getType, taskType)
                .eq(ContractTaskDO::getStatus, TaskStatus.FAIL.getVal());
        List<ContractTaskDO> contractTaskDOS = entityMapper.selectList(queryWrapper);
        if (WosaiCollectionUtils.isEmpty(contractTaskDOS)) {
            return;
        }
        List<Long> allTaskIds = contractTaskDOS.stream()
                .map(ContractTaskDO::getId)
                .collect(Collectors.toList());

        Lists.partition(allTaskIds, 100).forEach(taskIds -> {
            entityMapper.deleteBatchIds(taskIds);
            contractSubTaskDAO.batchDeleteSubTaskByPTaskIds(taskIds);
        });
    }
}
