package com.wosai.upay.job.biz;

import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.refactor.dao.TaskMutexConfigDAO;
import com.wosai.upay.job.refactor.model.enums.MutexTypeCategoryEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 统一校验：event生成task或者task执行时判断是否有互斥的任务正在进行
 *
 * <AUTHOR>
 * @date 2023/12/21
 */
@Component
public class MutexTaskCheckBiz {

    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private TaskMutexConfigDAO taskMutexConfigDAO;

    /**
     * 判断是否有互斥任务正在进行
     *
     * @param merchantSn  商户号
     * @param taskType    任务类型
     * @param ruleGroupId 报备规则组，不同的报备规则组不互斥
     * @return false没有互斥任务  true有互斥任务正在进行
     */
    public boolean checkMutexTaskProcessingForTask(String merchantSn, String taskType, String ruleGroupId) {
        return checkMutexTaskProcessing(merchantSn, taskType, MutexTypeCategoryEnum.TASK_TYPE, ruleGroupId);
    }

    public boolean checkMutexTaskProcessingForEvent(String merchantSn, Integer eventType, String ruleGroupId) {
        return checkMutexTaskProcessing(merchantSn, String.valueOf(eventType), MutexTypeCategoryEnum.EVENT_TYPE, ruleGroupId);
    }

    private boolean checkMutexTaskProcessing(String merchantSn, String typeIdentifier, MutexTypeCategoryEnum categoryEnum, String ruleGroupId) {
        Set<String> mutexTypes = taskMutexConfigDAO.getMutexTypesByTaskType(typeIdentifier, categoryEnum);
        if (WosaiCollectionUtils.isEmpty(mutexTypes)) {
            return false;
        }
        List<ContractTask> contractTasks = contractTaskMapper.selectProcessingMutexTasks(merchantSn, mutexTypes);
        if (WosaiCollectionUtils.isEmpty(contractTasks)) {
            return false;
        }
        for (ContractTask contractTask : contractTasks) {
            if (Objects.equals(ruleGroupId, contractTask.getRule_group_id())) {
                return true;
            }
        }
        return false;
    }
}
