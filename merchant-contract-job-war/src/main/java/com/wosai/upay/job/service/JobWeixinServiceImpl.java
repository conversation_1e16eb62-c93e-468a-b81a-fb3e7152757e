package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.validation.NotEmpty;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.SettlementIdConfig;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Description: WeixinServiceImpl
 * <AUTHOR>
 * @Date 2021/11/4 2:38 下午
 **/

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class JobWeixinServiceImpl implements JobWeixinService {

    @Autowired
    WechatAuthBiz wechatAuthBiz;

    @Autowired
    ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Override
    public String getSettlementId(String merchantSn) {
        try {
            return wechatAuthBiz.getMerchantNameAndSettlementId(merchantSn).getSettlementId();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public String getSettlementId(String merchantName, String industryId, Integer type) {
        try {
            return wechatAuthBiz.getSettlementId(industryId, type, merchantName);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public SettlementIdConfig getSettlementConfig(String industryId) {
        try {
            Map settlementConfig = wechatAuthBiz.getSettlementConfig(industryId);
            if (WosaiMapUtils.isNotEmpty(settlementConfig)) {
                return JSON.parseObject(JSON.toJSONString(settlementConfig), SettlementIdConfig.class);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public WxMchInfo getWxMchInfoBySubMchId(String subMchId) {
        return composeAcquirerBiz.getWxMchInfo(merchantProviderParamsMapper.getByPayMerchantId(subMchId));
    }


    @Override
    public WxMchInfo getWxMchNameBySubMchId(@NotEmpty(message = "微信商户号不能为空") String subMchId) {
        MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(subMchId);
        if (StringUtils.isNotBlank(params.getMerchant_name()) && StringUtils.isNotBlank(params.getWx_settlement_id())) {
            return new WxMchInfo()
                    .setMchInfo(
                            new MchInfo()
                                    .setMch_id(subMchId)
                                    .setBusiness(params.getWx_settlement_id())
                                    .setMerchant_name(params.getMerchant_name())
                                    .setMerchant_shortname(params.getMerchant_name()
                                    )
                    );
        } else {
            WxMchInfo wxMchInfo = composeAcquirerBiz.getWxMchInfo(params);
            merchantProviderParamsMapper.updateMerchantNameAndSettlementIdById(params.getId(), wxMchInfo.getMchInfo().getMerchant_name(), wxMchInfo.getMchInfo().getBusiness());
            return wxMchInfo;
        }
    }
}