package com.wosai.upay.job.refactor.model.enums.crm;

import com.wosai.upay.job.refactor.model.enums.ITextValueEnum;

/**
 * crm申请单审批状态枚举
 *
 * <AUTHOR>
 */
public enum CrmApplyFormAuditStatusEnum implements ITextValueEnum {

    /**
     * 待提交
     */
    WAIT_SUBMIT(0, "待提交"),

    /**
     * 审核中
     */
    AUDITING(1, "审核中"),

    /**
     * 审核失败
     */
    AUDIT_FAIL(2, "审核失败"),

    /**
     * 审核成功
     */
    AUDIT_SUCCESS(3, "审核成功");

    private final int value;
    private final String text;

    CrmApplyFormAuditStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
