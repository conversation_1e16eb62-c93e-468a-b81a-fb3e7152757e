package com.wosai.upay.job.refactor.event;

import com.wosai.upay.job.model.ContractTask;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 收单机构切换成功的事件
 * <AUTHOR>
 * @date 2024/7/10
 */
@Getter
public class ContractTaskEvent extends ApplicationEvent {

    /**
     * 商户号
     */
    private final ContractTask contractTask;

    public ContractTaskEvent(Object source) {
        super(source);
        this.contractTask = (ContractTask) source;
    }

}
