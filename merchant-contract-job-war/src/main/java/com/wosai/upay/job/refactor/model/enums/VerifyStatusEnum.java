package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 验证状态枚举
 *
 * <AUTHOR>
 */
public enum VerifyStatusEnum implements ITextValueEnum<Integer> {

    VERIFYING(1, "验证中"),

    SUCCESS(2, "成功"),

    FAIL(3, "失败");


    private final Integer value;
    private final String text;

    VerifyStatusEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
