package com.wosai.upay.job.refactor.task;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.BnsMchDAO;
import com.wosai.upay.job.refactor.dao.InactiveBnsMerchantDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.SubBizParamsDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.bo.SubBizParamsExtraBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * bns不活跃商户清理 30天未发生交易
 * 流程：
 *      1. mysql.bns_mch -> 数仓
 *      2. 数仓定时任务统计非活跃商户
 *      3. 数仓非活跃商户 -> mysql.inactive_bns_merchant
 *      4. 根据inactive_bns_merchant构建任务
 *      4. xxl-job调度处理
 *
 * <AUTHOR>
 * @date 2024/11/20 10:54
 */
@Component
@Slf4j
public class BnsInActiveMerchantCleanTask extends AbstractInternalScheduleTaskHandleTemplate{

    @Resource
    private BnsMchDAO bnsMchDao;

    @Resource
    private InactiveBnsMerchantDAO inactiveBnsMchDAO;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    /**
     * 初始化清理任务
     */
    public void initCleanTask() {
        List<InactiveBnsMerchantDO> inactiveBnsMerchantDOS;
        long beginId = 0;
        do {
            inactiveBnsMerchantDOS = inactiveBnsMchDAO.listByBeginId(beginId, 200);
            if (CollectionUtils.isEmpty(inactiveBnsMerchantDOS)) {
                break;
            }
            Map<String, InternalScheduleMainTaskDO> existedTaskMap = internalScheduleMainTaskDAO.listByTypeAndMerchantSns(InternalScheduleTaskTypeEnum.BNS_INACTIVE_MERCHANT_CLEAN,
                            inactiveBnsMerchantDOS.stream().map(InactiveBnsMerchantDO::getMerchantSn).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(InternalScheduleMainTaskDO::getMerchantSn, Function.identity(), (k1, k2) -> k1));
            Map<String, BnsMchDO> bnsMchDOMap = bnsMchDao.listByMerchantSns(inactiveBnsMerchantDOS.stream().map(InactiveBnsMerchantDO::getMerchantSn).collect(Collectors.toList()))
                    .stream().collect(Collectors.toMap(BnsMchDO::getMerchantSn, Function.identity(), ((k1, k2) -> k1.getStatus() >= k2.getStatus() ? k1 : k2)));
            for (InactiveBnsMerchantDO inActiveBnsMerchant : inactiveBnsMerchantDOS) {
                if (!bnsMchDOMap.containsKey(inActiveBnsMerchant.getMerchantSn()) ||
                        bnsMchDOMap.get(inActiveBnsMerchant.getMerchantSn()).isBoundFail()
                        || existedTaskMap.containsKey(inActiveBnsMerchant.getMerchantSn())) {
                    continue;
                }
                String merchantSn = inActiveBnsMerchant.getMerchantSn();
                Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> tasksMap = Maps.newHashMap();
                tasksMap.put(buildMainTask(merchantSn), Lists.newArrayList(buildSubTask(merchantSn)));
                interScheduleTaskService.batchInsertTasks(tasksMap);
            }
            beginId = inactiveBnsMerchantDOS.stream().map(InactiveBnsMerchantDO::getId).max(Long::compareTo).orElse(0L);
        } while (CollectionUtils.isNotEmpty(inactiveBnsMerchantDOS) && beginId != 0);
    }

    private InternalScheduleSubTaskDO buildSubTask(String merchantSn) {
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setType(getTaskType().getValue());
        subTaskDO.setTaskType("不活跃的bns商户清除交易参数");
        subTaskDO.setMerchantSn(merchantSn);
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        subTaskDO.setPriority(1);
        subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        return subTaskDO;
    }

    private InternalScheduleMainTaskDO buildMainTask(String merchantSn) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setMerchantSn(merchantSn);
        internalScheduleMainTaskDO.setType(getTaskType().getValue());
        internalScheduleMainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue());
        internalScheduleMainTaskDO.setAffectStatusSubTaskNum(1);
        internalScheduleMainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        return internalScheduleMainTaskDO;
    }


    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.BNS_INACTIVE_MERCHANT_CLEAN;
    }

    /**
     * 获取任务执行属性taskExecuteProperty
     */
    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO propertyBO = new ScheduleTaskExecutePropertyBO();
        propertyBO.setSupportParallel(true);
        propertyBO.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_TASKS);
        return propertyBO;
    }


    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return doCleanMerchantParams(mainTaskDO, subTaskDO);
        }
        return InternalScheduleSubTaskProcessResultBO.fail("未知状态");
    }

    private InternalScheduleSubTaskProcessResultBO doCleanMerchantParams(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String merchantSn = mainTaskDO.getMerchantSn();
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantTradeParamsBiz.listParamsByMerchantSn(merchantSn);
        Map<String, BnsMchDO> bnsMchDOMap = bnsMchDao.listByMerchantSns(Lists.newArrayList(merchantSn)).stream()
                .filter(BnsMchDO::isBound).collect(Collectors.toMap(BnsMchDO::getSubMchId, Function.identity(), (k1, k2) -> k1));
        // 如果商户在用的参数包含了绑定的bns商户，则删除该商户的参数
        if (MapUtils.isEmpty(bnsMchDOMap)) {
            return InternalScheduleSubTaskProcessResultBO.success("bns_mch无对应绑定成功记录");
        }
        List<MerchantProviderParamsDO> existedBnsParams = merchantProviderParamsDOS.stream()
                .filter(t -> bnsMchDOMap.containsKey(t.getPayMerchantId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existedBnsParams)) {
            return InternalScheduleSubTaskProcessResultBO.success("商户在用交易参数无bns微信参数");
        }
        merchantProviderParamsDAO.logicDeleteByIds(existedBnsParams.stream().map(MerchantProviderParamsDO::getId).collect(Collectors.toList()));
        List<MerchantProviderParamsDO> inUseExistedBnsParams = existedBnsParams.stream().filter(t -> Objects.equals(t.getStatus(), UseStatusEnum.IN_USE.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(inUseExistedBnsParams)) {
            merchantTradeParamsBiz.deletePaySideMerchantConfigParams(merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId(), PaywayEnum.WEIXIN.getValue(), ProviderEnum.PROVIDER_LKLORG.getValue());
        }
        deleteSubBizParams(merchantSn, existedBnsParams);
        return InternalScheduleSubTaskProcessResultBO.success("清除交易参数成功");
    }

    private void deleteSubBizParams(String merchantSn, List<MerchantProviderParamsDO> existedWxBnsParams) {
        if (CollectionUtils.isEmpty(existedWxBnsParams)) {
            return;
        }
        Set<String> existedBnsParamsIds = existedWxBnsParams.stream().map(MerchantProviderParamsDO::getId).collect(Collectors.toSet());
        List<SubBizParamsDO> subBizParamsDOS = subBizParamsDAO.listByMerchantSnAndProvider(merchantSn, String.valueOf(ProviderEnum.PROVIDER_LAKALA_V3.getValue()))
                .stream()
                .filter(t -> !StringUtils.equals(t.getTradeAppId(), "1"))
                .filter(t -> CollectionUtils.containsAny(existedBnsParamsIds, listSubBizParamsExtraParamProviderIds(t.getExtra(), ProviderEnum.PROVIDER_LAKALA_V3.getValue()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subBizParamsDOS)) {
            return;
        }
        String merchantId = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn).getId();
        for (SubBizParamsDO subBizParamsDO : subBizParamsDOS) {
            List<String> paramsIds = listSubBizParamsExtraParamProviderIds(subBizParamsDO.getExtra(), ProviderEnum.PROVIDER_LAKALA_V3.getValue())
                    .stream().filter(existedBnsParamsIds::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(paramsIds)) {
                continue;
            }
            removeBnsWxParams(subBizParamsDO, paramsIds);
            deletePayTradeAppConfigWxParams(merchantId, subBizParamsDO, paramsIds);
        }
    }

    private void deletePayTradeAppConfigWxParams(String merchantId, SubBizParamsDO subBizParamsDO, List<String> paramsIds) {
        merchantTradeParamsBiz.deletePaySideMerchantAppConfigParams(merchantId, subBizParamsDO.getTradeAppId(),
                PaywayEnum.WEIXIN.getValue(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
    }

    private void removeBnsWxParams(SubBizParamsDO subBizParamsDO, List<String> paramsIds) {
        String extra = subBizParamsDO.getExtra();
        if (StringUtils.isEmpty(extra)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(extra);
        List<String> paramsIdList =(List<String>)MapUtils.getObject(jsonObject, subBizParamsDO.getProvider().toString());
        if (CollectionUtils.isEmpty(paramsIdList)) {
            return;
        }
        paramsIdList.removeAll(paramsIds);
        subBizParamsDO.setExtra(jsonObject.toJSONString());
        subBizParamsDAO.updateByPrimaryKeySelective(subBizParamsDO);
    }

    private List<String> listSubBizParamsExtraParamProviderIds(String extra, Integer provider) {
        if (StringUtils.isEmpty(extra)) {
            return Collections.emptyList();
        }
        try {
            JSONObject jsonObject = JSON.parseObject(extra);
            return jsonObject.getJSONArray(provider.toString()).toJavaList(String.class);
        } catch (Exception e) {
            log.error("listSubBizParamsExtraParamProviderIds error extra:{},provider:{}", extra, provider, e);
            return Collections.emptyList();
        }
    }

    @Resource
    private SubBizParamsDAO subBizParamsDAO;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskContextBOInner {
        private String payMerchantId;
    }

}
