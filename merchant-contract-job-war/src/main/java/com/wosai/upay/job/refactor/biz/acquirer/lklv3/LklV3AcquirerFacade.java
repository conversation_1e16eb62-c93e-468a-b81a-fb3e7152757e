package com.wosai.upay.job.refactor.biz.acquirer.lklv3;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.acquirer.LklV3AcquirerBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.LklV3Term;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;

import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.task.license.entity.BankAccountDTO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.lklV3.MerAccountResp;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


/**
 * lklV3商户信息处理
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
@Slf4j
public class LklV3AcquirerFacade extends AbstractAcquirerHandler {

    @Resource
    private LklV3MerchantInfoProcessor lklV3MerchantInfoProcessor;

    @Resource(name = "lklV3-biz")
    private LklV3AcquirerBiz lklV3AcquirerBiz;

    @Resource
    private LklV3MerchantContractProcessor lklV3MerchantContractProcessor;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private LklV3Service lklV3Service;

    public static final String LKL_MERCHANT_INFO_LICENSE_NUMBER_KEY = "merBlis";


    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.LKL_V3;
    }

    @Override
    public Optional<String> getDefaultContractRuleGroupId() {
        return Optional.of(McConstant.RULE_GROUP_LKLORG);
    }

    @Override
    public String getMicroUpdageDefaultContractRuleGroupId() {
        return McConstant.RULE_GROUP_MICROUPGRADE_LKLORG;
    }

    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> lklV3MerchantInfoProcessor.getMerchantBankAccountInfo(merchantSn).map(MerAccountResp::getAcctNo).orElse(null),
                (sqbBankCardNo, acquirerBankCardNo) -> lklV3MerchantInfoProcessor.compareBankCardNos(sqbBankCardNo, acquirerBankCardNo)
        );
    }

    /**
     * 获取商户所在收单机构的商户状态
     * todo 待重构 本次来不及
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        try {
            return lklV3AcquirerBiz.getAcquirerMchStatus(merchantSn) ? AcquirerMerchantStatusEnum.NORMAL : AcquirerMerchantStatusEnum.CLOSE;
        } catch (Exception e) {
            log.error("获取拉卡拉商户状态异常", e);
            return AcquirerMerchantStatusEnum.CLOSE;
        }
    }

    /**
     * 商户向收单机构报备
     * 仅仅向收单机构做报备，不涉及其他业务逻辑校验。返回报文也不会落库。调用方需要自行处理
     *
     * @param merchantSn 商户号
     * @return 报备结果 todo 待重构 本次来不及
     */
    @Override
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn) {
        return lklV3MerchantContractProcessor.contractToAcquirer(merchantSn);
    }

    /**
     * 商户向收单机构报备
     *
     * @param merchantSn   商户号
     * @param contextParam 上下文参数
     * @return 报备结果
     */
    @Override
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn, Map<String, Object> contextParam) {
        return lklV3MerchantContractProcessor.contractToAcquirer(merchantSn, contextParam);
    }

    /**
     * 查询商户报备结果
     *
     * @param contractId 回调id
     */
    public LklV3ContractResultDTO queryContractResult(String contractId) {
        return lklV3MerchantContractProcessor.queryContractResult(contractId);
    }

    /**
     * 根据contractId，查询拉卡拉任务结果
     *
     * @param contractId 回调id
     */
    public LklV3ContractResultDTO queryTaskResultByContractId(String contractId) {
        return lklV3MerchantContractProcessor.queryTaskResultByContractId(contractId);
    }

    /**
     * 在收单机构侧是否小微商户
     *
     * @param merchantSn 商户号
     * @return 是否是小微 true-是
     */
    public boolean isMicroOnAcquirerSide(String merchantSn) {
        Optional<String> payMerchantIdOpt = merchantTradeParamsBiz.getAcquirerMerchantId(merchantSn, getTypeValue());
        if (!payMerchantIdOpt.isPresent()) {
            log.error("该商户没有对应收单机构的交易参数, merchantSn:{}, acquirer:{}", merchantSn, getTypeValue());
            throw new ContractBizException("该商户没有对应收单机构的交易参数");
        }
        final LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        final Map lklV3MerchantResponse = lklV3Service.queryMerchant(payMerchantIdOpt.get(), null, lklV3Param);
        if (MapUtils.isEmpty(lklV3MerchantResponse) || !lklV3MerchantResponse.containsKey("respData")) {
            throw new ContractBizException("拉卡拉商户查询结果为空");
        }
        Map<String, ?> respDataMap = MapUtils.getMap(lklV3MerchantResponse, "respData");
        if (StringUtils.isNotBlank(MapUtils.getString(respDataMap, LKL_MERCHANT_INFO_LICENSE_NUMBER_KEY))) {
            return false;
        }
        return true;
    }

    @Autowired
    private MerchantService merchantService;

    @Override
    public CuaCommonResultDTO canChangeToOtherAcquirer(String merchantSn) {
        // 如果是授权对公结算，不支持切通道
        Map merchantMap = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchantMap)) {
            return CuaCommonResultDTO.fail("商户不存在");
        }
        Map account = merchantService.getMerchantBankAccountByMerchantId(MapUtils.getString(merchantMap, DaoConstants.ID));
        if (MapUtils.isEmpty(account)
                || Objects.equals(MapUtils.getInteger(account, MerchantBankAccount.TYPE), BankAccountTypeEnum.PERSONAL.getValue())) {
            return CuaCommonResultDTO.success();
        }
        if (MapUtils.isEmpty(MapUtils.getMap(account, MerchantBankAccount.EXTRA))
                || !MapUtils.getMap(account, MerchantBankAccount.EXTRA).containsKey(BankAccountDTO.SETTLEMENT_ACCOUNT_TYPE_KEY)) {
            return CuaCommonResultDTO.success();
        }
        Integer settlementAccountType = MapUtils.getInteger(MapUtils.getMap(account, MerchantBankAccount.EXTRA), BankAccountDTO.SETTLEMENT_ACCOUNT_TYPE_KEY);
        if (Objects.equals(settlementAccountType, BankAccountDTO.AUTHORIZED_CORPORATE)) {
            return CuaCommonResultDTO.fail("拉卡拉通道结算账户类型授权对公，不支持切换通道");
        }
        return CuaCommonResultDTO.success();
    }


    @Override
    public MerchantAcquireInfoBO getAcquireInfoFromContractSubTask(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        // 查找拉卡拉入网任务
        ContractSubTaskDO lklSubTask = findSubTask(subTaskDOS, PaywayEnum.ACQUIRER.getValue(), "拉卡拉入网任务", pTaskId);
        String responseBody = lklSubTask.getResponseBody();

        Optional<Map> callbackMsgOpt = ConvertUtil.castToExpectedList(BeanUtil.getNestedProperty(responseBody, "callback_msg"), Map.class).stream().findFirst();
        if (!callbackMsgOpt.isPresent()) {
            throw new ContractBizException("拉卡拉机构进件子任务返回结果callback_msg为空, subTask id = " + lklSubTask.getId());
        }
        Map dataMap = MapUtils.getMap(callbackMsgOpt.get(), "data");
        if (MapUtils.isEmpty(dataMap)) {
            dataMap = MapUtils.getMap(callbackMsgOpt.get(), "respData");
        }
        final MerchantAcquireInfoBO merchantAcquireInfoBO = new MerchantAcquireInfoBO();
        List<Map> terms = (List) MapUtils.getObject(dataMap, "termDatas");
        final String requestBody = lklSubTask.getRequestBody();
        if (!org.springframework.util.StringUtils.isEmpty(requestBody)) {
            final Map requestMap = JSONObject.parseObject(requestBody, Map.class);
            Optional.ofNullable(requestMap)
                    .filter(map -> map.containsKey(LakalaConstant.TERMDATA) || map.containsKey("devSerialNo"))
                    .map(map -> {
                        //收钱吧终端主键
                        String devSerialNo;
                        devSerialNo = (String) BeanUtil.getNestedProperty(map, "termData.devSerialNo");
                        if (!org.springframework.util.StringUtils.isEmpty(devSerialNo)) {
                            return devSerialNo;
                        }
                        //收钱吧门店号
                        return BeanUtil.getPropString(map, "devSerialNo");
                    })
                    .ifPresent(devSerialNo -> {
                        terms.stream()
                                .filter(term -> !term.containsKey("devSerialNo") && !StringUtils.isEmpty(devSerialNo))
                                .forEach(term -> term.put("devSerialNo", devSerialNo));
                    });
        }
        Map term = terms.get(0);
        LklV3Term v3Term = JSON.parseObject(JSON.toJSONString(term), LklV3Term.class);
        // 设置拉卡拉相关信息
        merchantAcquireInfoBO.setUnionNo(getNestedProperty(dataMap, "merCupNo"));
        merchantAcquireInfoBO.setAcquireMerchantId(getNestedProperty(dataMap, "merInnerNo"));
        merchantAcquireInfoBO.setLklShopId(MapUtils.getString(term, LakalaConstant.SHOPID));
        merchantAcquireInfoBO.setLklTermNo(v3Term.getTermNo());
        merchantAcquireInfoBO.setLklV3Term(v3Term);
        // 查找微信入网任务
        ContractSubTaskDO wxSubTask = findSubTask(subTaskDOS, PaywayEnum.WEIXIN.getValue(), "微信报备任务", pTaskId);
        merchantAcquireInfoBO.setWxNo(getNestedProperty(JSONObject.parseObject(wxSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));
        merchantAcquireInfoBO.setWxContractRule(wxSubTask.getContractRule());
        // 查找支付宝入网任务
        ContractSubTaskDO aliSubTask = findSubTask(subTaskDOS, PaywayEnum.ALIPAY.getValue(), "支付宝报备任务", pTaskId);
        merchantAcquireInfoBO.setAliNo(getNestedProperty(JSONObject.parseObject(aliSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));

        return merchantAcquireInfoBO;
    }


    /**
     * 查找指定支付方式的成功子任务
     */
    protected ContractSubTaskDO findSubTask(List<ContractSubTaskDO> subTaskDOS, Integer payway, String taskName, Long pTaskId) throws ContractBizException {
        Optional<ContractSubTaskDO> optionalSubTask = subTaskDOS.stream()
                .filter(r -> {
                    if(Objects.equals(PaywayEnum.ACQUIRER.getValue(), payway)) {
                        return  Objects.equals(r.getPayway(), payway)
                                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                                && Objects.equals(r.getContractRule(), "lklV3");
                    }else {
                        return  Objects.equals(r.getPayway(), payway)
                                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                                && Lists.newArrayList("lkl-1033-2-2088011691288213","lkl-1033-3-32631798").contains(r.getContractRule());
                    }


                })
                .findFirst();

        if (!optionalSubTask.isPresent()) {
            log.warn("pTaskId{} 没有找到对应的{}", pTaskId, taskName);
            throw new ContractBizException("没有找到对应的" + taskName);
        }

        return optionalSubTask.get();
    }

}
