package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 子业务交易参数记录表表实体对象
 *
 * <AUTHOR>
 */
@TableName("sub_biz_params")
@Data
public class SubBizParamsDO {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 业务方
     */
    @TableField(value = "trade_app_id")
    private String tradeAppId;
    /**
     * 交易通道服务提供方
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 支付源子商户号
     */
    @TableField(value = "extra")
    private String extra;
    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Timestamp createAt;
    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    private Timestamp updateAt;
    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Integer deleted;


}

