package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@Accessors(chain = true)
public class AliDirectApply {
    private Long id;

    private String merchant_sn;

    private Long task_id;

    private String batch_no;

    private Integer status;

    private String sign_url;

    private String result;

    private String user_id;

    private Date create_at;

    private Date priority;

    private Date update_at;

    private String request_body;

    private String response_body;

    private String form_body;
}