package com.wosai.upay.job.refactor.task.rotational;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ContractSubTaskProcessStatusEnum;
import com.shouqianba.cua.enums.contract.FuYouBusinessAuditModifyStatusEnum;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 富友营业执照人工审核任务
 * 1. 双账户新接口
 * 2. 轮询任务结果
 * 3. 接口入参报文赋值
 *
 * <AUTHOR>
 * @date 2025/4/23 15:24
 */
@Component
public class FuYouBusinessLicenseManualAuditSubTaskProcessor implements RotationalSubTaskProcessor {

    public static final String  PROVIDER_MERCHANT_ID = "providerMerchantId";

    @Autowired
    private FuyouService fuyouService;

    @Override
    public RotationalSubTaskTypeEnum getSubTaskType() {
        return RotationalSubTaskTypeEnum.FU_YOU_LICENSE_MANUAL_AUDIT;
    }

    @Override
    public InternalScheduleSubTaskProcessResultBO handleRotationalSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        String context = mainTaskDO.getContext();
        RotationalTaskContext rotationalTaskContext = JSON.parseObject(context, RotationalTaskContext.class);
        String rotationId = rotationalTaskContext.getRotationId();
        Object providerMerchantId = rotationalTaskContext.getParamContextValueByKey("providerMerchantId");
        if (Objects.isNull(providerMerchantId)) {
            return InternalScheduleSubTaskProcessResultBO.fail("providerMerchantId为空");
        }
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        ContractResponse contractResponse = fuyouService.queryBusinessAuditStatus(mainTaskDO.getMerchantSn(), (String)providerMerchantId, rotationId, "BS");
        resultBO.setRequestMsg(JSON.toJSONString(contractResponse.getRequestParam()));
        resultBO.setResponseMsg(JSON.toJSONString(contractResponse.getResponseParam()));
        Map<String, Object> responseParam = contractResponse.getResponseParam();
        String modifyStatus = MapUtils.getString(responseParam, "modify_st");
        String modifyDealMsg = MapUtils.getString(responseParam, "modify_deal_msg");
        if (contractResponse.isSuccess()) {
            if (FuYouBusinessAuditModifyStatusEnum.PROCESSED.getValue().equals(modifyStatus)) {
                resultBO.setResult("审核通过");
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS);
            } else if (FuYouBusinessAuditModifyStatusEnum.REFUSED.getValue().equals(modifyStatus)) {
                resultBO.setResult(modifyDealMsg);
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
            } else {
                resultBO.setResult("等待富友人工审核完成");
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT);
            }
        } else {
            resultBO.setResult(contractResponse.getMessage());
            resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
        }
        return resultBO;
    }
}
