package com.wosai.upay.job.model.DO;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class McBatchTask {
    private Integer id;

    private String operator_id;

    private String operator_name;
    // fine TODO   枚举
    private Integer type;

    private Date effect_time;

    private String task_apply_log_id;

    private Date create_at;

    private Date update_at;

    private Integer status;
    private String payload;

    private String result;

}