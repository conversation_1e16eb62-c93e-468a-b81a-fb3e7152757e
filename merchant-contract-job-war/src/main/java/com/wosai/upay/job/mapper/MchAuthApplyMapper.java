package com.wosai.upay.job.mapper;


import com.wosai.upay.job.model.MchAuthApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MchAuthApplyMapper {


    MchAuthApply selectByPrimaryKey(Long id);

    int insertSelective(MchAuthApply record);

    int updateByPrimaryKeySelective(MchAuthApply record);

    MchAuthApply getAuthApplyByTypeAndAuthNumAndStatus(@Param("type") int type, @Param("auth_num") String authNum, @Param("statuses") List<Integer> integers);

    List<MchAuthApply> getAuthApplyByAuthNumAndStatus(@Param("auth_num") String authNum, @Param("status") List<Integer> status);

    /**
     * 创建时间查询 (提交申请单)
     */
    List<MchAuthApply> getAppliesByCreateAtAndStatus(@Param("priority") String priority, @Param("statuses") List<Integer> statuses, @Param("limit") Integer limit);

    /**
     * 按Priority查询 (同步申请单状态)
     */
    List<MchAuthApply> getAppliesByPriorityAndStatus(@Param("start") String start, @Param("end") String end, @Param("statuses") List<Integer> statuses, @Param("limit") Integer limit);

    /**
     * 提交时间查询 (同步审核状态)
     */
    List<MchAuthApply> getAppliesByCommitAtAndStatus(@Param("commit_at") String commitAt, @Param("statuses") List<Integer> statuses, @Param("limit") Integer limit);


    int submitResult(@Param("id") Long id, @Param("request") String requestBody, @Param("response") String responseBody, @Param("result") String result, @Param("status") Integer status);

    int updateStatusByIdAndPreStatus(@Param("id") Long id, @Param("result_status") Integer result_status, @Param("pre_status") Integer pre_status, @Param("result") String result);

    int updateQrcode(@Param("id") Long applyId, @Param("qrcode_data_pre") String qrCodePre, @Param("qrcode_data_after") String qrCodeAfter);

    int updateFieldValueById(@Param("id") Long id, @Param("value") String value, @Param("field") String field);

    int updateCancelStatus(@Param("id") Long applyId, @Param("cancel_status") Integer cancelStatus);

    int updatePayMerchantId(@Param("id") Long id, @Param("pay_merchant_id") String payMerchantId);

    List<MchAuthApply> allNeedCancelTask(@Param("createAt") String createAt);

    MchAuthApply selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 查询小微商户待提交实名认证的申请单
     */
    MchAuthApply selectMicroWait(@Param("merchantSn") String merchantSn);

    MchAuthApply selectAuthApplyByMerchantSnAndStatus(@Param("merchantSn") String merchantSn, @Param("statuses") List<Integer> integers);

    /**
     * 超时申请单
     * @return
     */
//    List<MchAuthApply> getTimeOutApply();
}