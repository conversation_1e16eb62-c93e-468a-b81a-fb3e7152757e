package com.wosai.upay.job.config;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Maps;
import com.wosai.common.utils.WosaiMapUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ApolloConfig {

    private static final Config CONFIG = ConfigService.getAppConfig();
    public static String getNewBlueSeaAppId() {
        return CONFIG.getProperty("alipay-newbluesea-appid", "2015102000490218");
    }

    public static String getNewBlueSeaPrivateKey() {
        return CONFIG.getProperty("alipay-newbluesea-private-key",
                "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMRwMRyLCIPHg/17PyH+urOvvqbutubkeBbhz6BQ40jE+gth5A/QDyW8BzmuATY/cKDZRaw9xfMTNe9EkaTJz4jzBzEa61VMy9Bw2/ntCOfXzSCuJwDTxknMEK2b0O0VCMSR3lD2XVyf19LxdFrt2PpPSqIwKOCtp8bdeRfDThItAgMBAAECgYEAtcmBmYGM4q77ldO9jA845lqoW8GkH66k98AHLbxNaaVoJKp1rLCFpA0KvrUpx/MTnrcTRB9ylc1cZ02UXuSoGdvCRSzQyGJVSoJsyzEH/M3a1swNJ704LgoDVMYwVKixpuC++vGoo+OcDZMI340rR1LbnaVYjPVtfVlpJ0e91L0CQQDuV0RcHMDjUPdLVbpYUKe4MwR4+5rW7dH7/pmwrYVXBFOxJ1whZXv6c8EajGl8KWEcgO9cAbZNSe0yNu2rjzETAkEA0v4i2qYe2ipSCq1xKnaoHObs8yHM+o3NLo+BiTKhjZe1o/zT+zdypT9kfrKcyHqipg4y/cvwljDm1JwMQ2lXvwJBAMagsZa2W8XVnxIIctDQ/sWStuKAhM0jy0DgMIM+SQZ406qqq6wlYEocF80hQXO5JHZVuaUKxDvrJSMZBb9ZCUMCQFxu94gAdM2w8qY427X1q6qVxEKzkSBHFReLyPz5EGt+hhXkgl2xKemY/wa+aw8tIqpK2C31tV2m6MlF3918ffMCQQCrRsxb2vxJbIFITY1fesOqCDK2KVveAng8eGZ5mZYpkZPpUYNUZhhRklu6t7umcNUsc97nJ06eSaZrd8n+vOM5");
    }

    public static String getNewBlueSeaPublicKey() {
        return CONFIG.getProperty("alipay-newbluesea-public-key",
                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDI6d306Q8fIfCOaTXyiUeJHkrIvYISRcc73s3vF1ZT7XN8RNPwJxo8pWaJMmvyTn9N4HQ632qJBVHf8sxHi/fEsraprwCtzvzQETrNRwVxLO5jVmRGi60j8Ue1efIlzPXV9je9mkjzOmdssymZkh2QhUrCmZYI/FCEa3/cNMW0QIDAQAB");
    }

    public static String getNewBlueSignType() {
        return CONFIG.getProperty("alipay-newbluesea-sign-type", "RSA");
    }

    /** 是否开启新蓝海活动 默认开启
    * @Author: zhmh
    * @Description:
    * @time: 11:57 2020/12/7
    */
    public static Boolean getNewBlueSeaActivityStatus() {
        return CONFIG.getBooleanProperty("new_blue_sea_activity_status", Boolean.TRUE);
    }

    /**
     * 是否开启微信活动 默认开启
     */
    public static Boolean getWechatActivityStatus() {
        return CONFIG.getBooleanProperty("wechat_activity_status", Boolean.TRUE);
    }
    /**
     * 获取所有异常返回信息
     * @return
     */
    public static Map<String, Map<String, String>> getList() {
        final List<Map<String,String>>  list = JSONObject.parseObject(CONFIG.getProperty("new_blue_sea_error_message", "new_blue_sea_error_message"), List.class);
        final Map<String, Map<String, String>> code = list.stream().collect(Collectors.toMap(x -> x.get("message"), x -> x, (val1, val2) -> val1));
        return code;
    }

    /**
     * 获取银行支付的支付方式
     * @return
     */
    public static Map<String, List<Integer>> getBankSupportPayWay() {
        final Map<String,List<Integer>> payWayMap = JSONObject.parseObject(CONFIG.getProperty("bank_support_payWay", "bank_support_payWay"), Map.class);
        return payWayMap;
    }

    /**
     * 邮储转义后的提示
     * @return
     */
    public static Map<String,List<Map<String, String>>> getBankDirectTips() {
        final Map<String,List<Map<String, String>>> tips = JSONObject.parseObject(CONFIG.getProperty("bank_direct_tips", "bank_direct_tips"), Map.class);
        return tips;
    }

    /**
     * 获取批量报备最大值
     * @return
     */
    public static Integer getBatchThreshold() {
        final Integer batchThreshold = CONFIG.getIntProperty("batch_threshold", 3000);
        return batchThreshold;
    }

    /**
     * crm审核中状态展示
     * @return
     */
    public static List<Map<String, String>> getBankViewProcess(String acquire) {
        final Map<String,List<Map<String, String>>> viewProcess = JSONObject.parseObject(CONFIG.getProperty("bank_view_process", JSONObject.toJSONString(Maps.newHashMap())), Map.class);
        return viewProcess.get(acquire);
    }

    /**
     * 获取特殊底价入网富友行业信息
     * @param industryId
     * @return
     */
    public static Map<String, String> getNetFuYouSpecChNlTypeAndBusinessCodeAndChannelNoMapping(String industryId) {
        Map allMapping = JSONObject.parseObject(CONFIG.getProperty("fuyou_spec_chnl_type_and_business_code_and_channel_no_mapping", "{}"), Map.class);
        Map mapping = WosaiMapUtils.getMap(allMapping, "net");
        return WosaiMapUtils.getMap(mapping, industryId);
    }

    public static boolean getFuYouSpecialIndustryApplySwitch() {
        return CONFIG.getBooleanProperty("fuyou_special_industry_apply_switch", true);
    }

    public static String getSpecifiedFuYouNormalIndustry() {
        return CONFIG.getProperty("fu_you_specified_normal_industry_id", "747f3e28-312d-11e6-aebb-ecf4bbdee2f0");
    }

    public static Long getFyUnionDelay() {
        return CONFIG.getLongProperty("fy_union_delay", 5000L);
    }

}