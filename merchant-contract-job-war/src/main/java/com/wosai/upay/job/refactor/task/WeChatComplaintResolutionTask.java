package com.wosai.upay.job.refactor.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.AffectPrimaryTaskStatusEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.contract.ScheduleStatusEnum;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.job.model.ReContract;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.BnsMchDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.WxComplaintRecordDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.WxComplaintRecordDO;
import com.wosai.upay.job.refactor.model.enums.*;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.service.MerchantProviderParamsServiceImpl;
import com.wosai.upay.job.service.ReContractService;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Date;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信投诉处理任务
 * 目前处理bns历史遗留问题，被投诉的商户，如果存在bns列表，需要对微信切参数或者重新报备（幂等处理）
 *
 * <AUTHOR>
 * @date 2024/10/22 10:31
 */
@Service
@Slf4j
public class WeChatComplaintResolutionTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private WxComplaintRecordDAO wxComplaintRecordDAO;

    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private BnsMchDAO bnsMchDAO;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private MerchantProviderParamsServiceImpl merchantProviderParamsService;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ReContractService reContractService;

    @Autowired
    private TaskInstanceService taskInstanceService;

    @Resource
    private ChatBotUtil chatBotUtil;

    @Value("${wx_complaint_task_template_id}")
    private Long taskTemplateId;

    private static final Integer DEFAULT_BATCH_NUM = 200;


    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.WX_COMPLAINT_RESOLUTION;
    }

    /**
     * 获取任务执行属性taskExecuteProperty
     */
    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO propertyBO = new ScheduleTaskExecutePropertyBO();
        propertyBO.setSupportParallel(true);
        propertyBO.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        return propertyBO;
    }

    /**
     * 新增任务根据创建时间是今天的投诉记录
     * 每天凌晨三点半调度一次
     */
    public void initTasks() {
        List<WxComplaintRecordDO> wxComplaintRecordDOs;
        long beginId = 0L;
        String todayStr = LocalDate.now().format(DateTimeFormatter.ofPattern(DateExtensionUtils.FORMAT_DATE_SIMPLE));
        Date todayDate;
        try {
            todayDate = DateExtensionUtils.parseDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateExtensionUtils.FORMAT_DATE_SIMPLE)));
        } catch (ParseException e) {
            log.error("解析日期失败, 日期:{}", todayStr, e);
            return;
        }
        List<String> alreadyAddTaskPayMerchantIds = Lists.newArrayList();
        do {
            wxComplaintRecordDOs = wxComplaintRecordDAO.listByDateAndProviderAndId(todayDate, ProviderEnum.PROVIDER_LKLORG.getValue(), beginId, DEFAULT_BATCH_NUM);
            if (CollectionUtils.isEmpty(wxComplaintRecordDOs)) {
                break;
            }
            addTasksByComplaintRecords(wxComplaintRecordDOs, alreadyAddTaskPayMerchantIds);
            beginId = wxComplaintRecordDOs.stream().map(WxComplaintRecordDO::getId).max(Long::compareTo).orElse(0L);
        } while (CollectionUtils.isNotEmpty(wxComplaintRecordDOs) && beginId != 0L);
    }

    private void addTasksByComplaintRecords(List<WxComplaintRecordDO> wxComplaintRecordDOs, List<String> alreadyAddTaskPayMerchantIds) {
        if (CollectionUtils.isEmpty(wxComplaintRecordDOs)) {
            return;
        }
        Set<String> merchantSns = wxComplaintRecordDOs.stream().map(WxComplaintRecordDO::getMerchantSn).collect(Collectors.toSet());
        // 这里失败的也不会重新生成任务了
        Set<String> existedNotFailTasks = internalScheduleMainTaskDAO
                .listByTypeAndMerchantSns(InternalScheduleTaskTypeEnum.WX_COMPLAINT_RESOLUTION,merchantSns).stream()
                .map(internalScheduleMainTaskDO -> {
                    String context = internalScheduleMainTaskDO.getContext();
                    MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(context, MainTaskContextBOInner.class);
                    return mainTaskContextBOInner.getPayMerchantId();
                })
                .collect(Collectors.toSet());
        List<WxComplaintRecordDO> needBuildTasksRecords = wxComplaintRecordDOs.stream()
                .filter(t -> !alreadyAddTaskPayMerchantIds.contains(t.getPayMerchantId()) && !existedNotFailTasks.contains(t.getPayMerchantId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needBuildTasksRecords)) {
            return;
        }
        alreadyAddTaskPayMerchantIds.addAll(needBuildTasksRecords.stream().map(WxComplaintRecordDO::getPayMerchantId).collect(Collectors.toList()));
        Map<InternalScheduleMainTaskDO, List<InternalScheduleSubTaskDO>> tasksMap = Maps.newHashMapWithExpectedSize(needBuildTasksRecords.size());
        for (WxComplaintRecordDO needBuildTasksRecord : needBuildTasksRecords) {
            try {
                tasksMap.put(buildMainTask(needBuildTasksRecord), Lists.newArrayList(buildSubTask(needBuildTasksRecord)));
            } catch (Exception e) {
                log.error("构建微信投诉单解决任务失败, 商户号:{}, 投诉记录id:{}", needBuildTasksRecord.getMerchantSn(), needBuildTasksRecord.getId(), e);
            }
        }
        interScheduleTaskService.batchInsertTasks(tasksMap);
    }

    private InternalScheduleSubTaskDO buildSubTask(WxComplaintRecordDO needBuildTasksRecord) {
        InternalScheduleSubTaskDO subTaskDO = new InternalScheduleSubTaskDO();
        subTaskDO.setType(getTaskType().getValue());
        subTaskDO.setTaskType("切微信参数");
        subTaskDO.setMerchantSn(needBuildTasksRecord.getMerchantSn());
        subTaskDO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue());
        subTaskDO.setPriority(1);
        subTaskDO.setAffectMainTaskStatus(AffectPrimaryTaskStatusEnum.YES.getValue());
        subTaskDO.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        subTaskDO.setProvider(ProviderEnum.PROVIDER_LKLORG.getValue());
        subTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        return subTaskDO;
    }

    private InternalScheduleMainTaskDO buildMainTask(WxComplaintRecordDO needBuildTasksRecord) {
        InternalScheduleMainTaskDO internalScheduleMainTaskDO = new InternalScheduleMainTaskDO();
        internalScheduleMainTaskDO.setMerchantSn(needBuildTasksRecord.getMerchantSn());
        internalScheduleMainTaskDO.setType(getTaskType().getValue());
        internalScheduleMainTaskDO.setStatus(InternalScheduleMainTaskStatusEnum.WAIT_PROCESS.getValue());
        internalScheduleMainTaskDO.setAffectStatusSubTaskNum(1);
        internalScheduleMainTaskDO.setContext(JSON.toJSONString(new MainTaskContextBOInner(needBuildTasksRecord.getPayMerchantId())));
        internalScheduleMainTaskDO.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        internalScheduleMainTaskDO.setProvider(ProviderEnum.PROVIDER_LKLORG.getValue());
        internalScheduleMainTaskDO.setEnableScheduledStatus(ScheduleStatusEnum.CAN.getValue());
        internalScheduleMainTaskDO.setContext(JSON.toJSONString(new SubTaskContextBOInner(needBuildTasksRecord.getPayMerchantId())));
        return internalScheduleMainTaskDO;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MainTaskContextBOInner {
        private String payMerchantId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubTaskContextBOInner {
        private String payMerchantId;
    }


    /**
     * 处理单个子任务逻辑
     * 子类需要完善不同状态的处理逻辑，如：0-待处理 2-等待外部结果 3-重试
     * 子类需要根据业务场景，决定是否需要重试
     * 同时该方法执行出现异常也会默认重试 配置：{@link ScheduleTaskExecutePropertyBO} retryIfException
     * 返回 status = InternalScheduleSubTaskStatusEnum.RETRY 会尝试重试
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 子任务执行结果
     */
    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return changeWeChatParams(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            //return getReContract(mainTaskDO, subTaskDO);
        }
        return InternalScheduleSubTaskProcessResultBO.fail("未知状态");
    }



    private InternalScheduleSubTaskProcessResultBO changeWeChatParams(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        MainTaskContextBOInner mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), MainTaskContextBOInner.class);
        boolean bond = bnsMchDAO.isPayMerchantIdBond(mainTaskContextBOInner.getPayMerchantId());
        if (!bond) {
            return InternalScheduleSubTaskProcessResultBO.success("子商户号未被绑定，无需处理");
        }
        Map<String, MerchantProviderParamsDO> existedWxParamMap = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn())
                .stream()
                .filter(t -> Objects.equals(t.getProvider(), ProviderEnum.PROVIDER_LKLORG.getValue()) && Objects.equals(t.getPayway(), PaywayEnum.WEI_XIN.getValue()))
                .collect(Collectors.toMap(MerchantProviderParamsDO::getPayMerchantId, t -> t, (a, b) -> a));
        if (!existedWxParamMap.containsKey(mainTaskContextBOInner.getPayMerchantId())) {
            return InternalScheduleSubTaskProcessResultBO.success("子商户号已不再使用");
        }
        boolean success = changeExistedWxParams(mainTaskDO, existedWxParamMap, mainTaskContextBOInner);
        if (!success) {
            log.info("微信投诉单处理切换参数,商户现有微信参数不满足，需要重新报备, 主任务id:{}, 商户号:{}, 被投诉子商户号:{}", mainTaskDO.getId(), mainTaskDO.getMerchantSn(),
                    mainTaskContextBOInner.getPayMerchantId());
            reContractWxParams(mainTaskDO, existedWxParamMap, mainTaskContextBOInner);
        }
        // 产品要求，无论切换是否成功，都需要删除原交易参数，然后派工
        logicDeleteOriginalWxParams(existedWxParamMap.get(mainTaskContextBOInner.getPayMerchantId()));
        sendWorkOrder(mainTaskDO);
        return InternalScheduleSubTaskProcessResultBO.success("处理成功");
    }

    private void sendWorkOrder(InternalScheduleMainTaskDO mainTaskDO) {
        TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
        dto.setOperator("SYSTEM");
        dto.setOperatorName("SYSTEM");
        dto.setPlatform("SYSTEM");
        dto.setTaskTemplateId(taskTemplateId);
        dto.setTaskObjectSn(mainTaskDO.getMerchantSn());
        try {
            Map map = taskInstanceService.startTaskForRpc(dto);
            log.info("发起派工任务成功: 商户号: {},  派工结果: {}", mainTaskDO.getMerchantSn(), JSON.toJSONString(map));
        } catch (Exception e) {
            log.warn("商户号: {}, 发起派工任务异常: {}, 请求参数: {}", mainTaskDO.getMerchantSn(), e.getMessage(), JSON.toJSONString(dto));
            chatBotUtil.sendMessageToContractWarnChatBot("微信投诉单处理派工失败" + " 商户号: " + mainTaskDO.getMerchantSn()
                    + " 发起派工任务异常: " + e.getMessage());
        }
    }



    private void logicDeleteOriginalWxParams(MerchantProviderParamsDO merchantProviderParamsDO) {
        merchantProviderParamsDAO.logicDeleteByIds(Lists.newArrayList(merchantProviderParamsDO.getId()));
    }

    private boolean changeExistedWxParams(InternalScheduleMainTaskDO mainTaskDO, Map<String, MerchantProviderParamsDO> existedWxParamMap, MainTaskContextBOInner mainTaskContextBOInner) {
        for (MerchantProviderParamsDO existedWxParam : existedWxParamMap.values()) {
            if (StringUtils.equals(existedWxParam.getPayMerchantId(), mainTaskContextBOInner.getPayMerchantId())) {
                continue;
            }
            try {
                merchantProviderParamsService.setDefaultMerchantProviderParams(existedWxParam.getId(), null, "消费者投诉，更换微信子商户号");
                return true;
            } catch (Exception e) {
                log.error("微信投诉单处理切换参数失败, 主任务id:{}, 商户号:{}, 目标子商户号:{}", mainTaskDO.getId(), mainTaskDO.getMerchantSn(), existedWxParam.getPayMerchantId(), e);
            }
        }
        return false;
    }

    private boolean reContractWxParams(InternalScheduleMainTaskDO mainTaskDO, Map<String, MerchantProviderParamsDO> existedWxParamMap, MainTaskContextBOInner mainTaskContextBOInner) {
        ReContract reContract = new ReContract();
        reContract.setMerchant_sn(mainTaskDO.getMerchantSn());
        reContract.setRule(existedWxParamMap.get(mainTaskContextBOInner.getPayMerchantId()).getContractRule());
        reContract.setRemark("消费者投诉，重新报备微信子商户号");
        try {
            reContractService.reContract(reContract);
            // 获取刚刚重新报备的参数，设置为默认
            Optional<MerchantProviderParamsDO> newParamsOpt = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn())
                    .stream()
                    .filter(t -> Objects.equals(t.getProvider(), ProviderEnum.PROVIDER_LKLORG.getValue())
                            && Objects.equals(t.getPayway(), PaywayEnum.WEI_XIN.getValue())
                            && !existedWxParamMap.containsKey(t.getId()))
                    .max(Comparator.comparing(MerchantProviderParamsDO::getCtime));
            if (!newParamsOpt.isPresent()) {
                log.error("微信投诉单处理重新报备失败,未正确生成微信的交易参数, 主任务id:{}, 商户号:{}, 被投诉子商户号:{}", mainTaskDO.getId(), mainTaskDO.getMerchantSn(), mainTaskContextBOInner.getPayMerchantId());
                return false;
            }
            merchantProviderParamsService.setDefaultMerchantProviderParams(newParamsOpt.get().getId(), null, "消费者投诉，更换微信子商户号");
            return true;
        } catch (Exception e) {
            log.error("微信投诉单处理重新报备失败, 主任务id:{}, 商户号:{}, 被投诉子商户号:{}", mainTaskDO.getId(), mainTaskDO.getMerchantSn(), mainTaskContextBOInner.getPayMerchantId(), e);
            return false;
        }
    }


}
