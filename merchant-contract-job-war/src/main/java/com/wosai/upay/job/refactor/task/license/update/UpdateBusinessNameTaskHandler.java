package com.wosai.upay.job.refactor.task.license.update;

import com.google.common.collect.Maps;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;

/**
 * 营业执照认证-更新经营名称
 *
 * <AUTHOR>
 * @date 2025/2/17 15:04
 */
@Slf4j
@Component
public class UpdateBusinessNameTaskHandler {


    @Resource
    private SelfHelpNetInEventService selfHelpNetInEventService;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    public void handleUpdateBusinessNameUpdate(String merchantId, String newBusinessName) {
        if (StringUtils.isBlank(newBusinessName)) {
            log.warn("经营名称为空, merchantId:{}", merchantId);
            return;
        }
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoById(merchantId);
        if (merchantInfo == null) {
            log.warn("商户不存在, merchantId:{}", merchantId);
            return;
        }
        String merchantSn = merchantInfo.getSn();
        try {
            HashMap<String, Object> tableMap = Maps.newHashMap();
            tableMap.put(ConstantsEvent.EVENT_TYPE_TABLE_NAME, "merchant");
            HashMap<String, Object> keyValueMap = Maps.newHashMap();
            keyValueMap.put("business_name", newBusinessName);
            keyValueMap.put("remark", "更新营业执照变更经营名称");
            selfHelpNetInEventService.saveSelfHelpNetInEvent(
                    merchantSn,
                    ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION,
                    tableMap,
                    Arrays.asList("business_name", "remark"),
                    keyValueMap);
        } catch (Exception e) {
            log.warn("更新营业执照变更经营名称失败, merchantSn:{}", newBusinessName, e);
        }
    }
}
