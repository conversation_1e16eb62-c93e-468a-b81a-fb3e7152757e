package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 进件规则详情连接类型枚举
 *
 * <AUTHOR>
 */
public enum RuleDetailConnectionTypeEnum implements ITextValueEnum<String> {

    AND("AND", "等于"),

    OR("OR", "或者");


    private final String value;
    private final String text;

    RuleDetailConnectionTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
