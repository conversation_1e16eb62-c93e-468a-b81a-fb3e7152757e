package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.GroupCombinedStrategyDetailMapper;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;

import java.util.*;


/**
 * 进件报备规则组策略组合detail表表数据库访问层 {@link GroupCombinedStrategyDetailDO}
 * 对GroupCombinedStrategyDetailMapper层做出简单封装 {@link GroupCombinedStrategyDetailMapper}
 *
 * <AUTHOR>
 */
@Repository
public class GroupCombinedStrategyDetailDAO extends AbstractBaseDAO<GroupCombinedStrategyDetailDO, GroupCombinedStrategyDetailMapper>{

    public GroupCombinedStrategyDetailDAO(SqlSessionFactory sqlSessionFactory, GroupCombinedStrategyDetailMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据进件报备规则组策略组合id获取策略组合detail
     *
     * @param strategyId 策略组合id
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listByStrategyId(Long strategyId) {
        if (Objects.isNull(strategyId)) {
            return Collections.emptyList();
        }
        return entityMapper
                .selectList(new LambdaQueryWrapper<GroupCombinedStrategyDetailDO>().eq(GroupCombinedStrategyDetailDO::getGroupStrategyId, strategyId));
    }

    /**
     * 根据进件报备规则组策略组合id列表获取策略组合detail
     *
     * @param strategyIds 策略组合id列表
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listByStrategyIds(Collection<Long> strategyIds) {
        if (CollectionUtils.isEmpty(strategyIds)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<GroupCombinedStrategyDetailDO>()
                .in(GroupCombinedStrategyDetailDO::getGroupStrategyId, strategyIds));
    }

    /**
     * 根据进件报备规则组策略组合id列表和规则组id获取策略组合detail
     *
     * @param strategyIds 策略组合id列表
     * @param groupIds    规则组id列表
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listValidByStrategyIdsAndGroupIds(List<Long> strategyIds, List<String> groupIds) {
        if (CollectionUtils.isEmpty(strategyIds) || CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        return entityMapper.selectList(new LambdaQueryWrapper<GroupCombinedStrategyDetailDO>()
                        .eq(GroupCombinedStrategyDetailDO::getValidStatus, ValidStatusEnum.VALID.getValue())
                        .in(GroupCombinedStrategyDetailDO::getGroupStrategyId, strategyIds)
                        .in(GroupCombinedStrategyDetailDO::getGroupId, groupIds));
    }

    /**
     * 根据收单机构获取策略组合detail
     *
     * @param acquirer 收单机构
     * @return 策略组合detail列表
     */
    public List<GroupCombinedStrategyDetailDO> listByAcquirer(String acquirer) {
        if (StringUtils.isBlank(acquirer)) {
            return Collections.emptyList();
        }
        return entityMapper
                .selectList(new LambdaQueryWrapper<GroupCombinedStrategyDetailDO>()
                        .eq(GroupCombinedStrategyDetailDO::getAcquirer, acquirer));
    }
}
