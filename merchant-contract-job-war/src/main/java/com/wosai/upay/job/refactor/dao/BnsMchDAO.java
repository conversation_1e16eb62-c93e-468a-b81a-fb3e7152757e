package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.BnsMchMapper;
import com.wosai.upay.job.refactor.model.entity.BnsMchDO;

import java.util.Collections;
import java.util.List;


/**
 * bns关联表表数据库访问层 {@link BnsMchDO}
 * 对BnsMchMapper层做出简单封装 {@link BnsMchMapper}
 *
 * <AUTHOR>
 */
@Repository
public class BnsMchDAO extends AbstractBaseDAO<BnsMchDO, BnsMchMapper> {


    public BnsMchDAO(SqlSessionFactory sqlSessionFactory, BnsMchMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 子商户号是否被绑定
     *
     * @param payMerchantId 子商户号
     * @return 是否被绑定
     */
    public boolean isPayMerchantIdBond(String payMerchantId) {
        LambdaQueryWrapper<BnsMchDO> query = new LambdaQueryWrapper<>();
        query.eq(BnsMchDO::getSubMchId, payMerchantId);
        query.eq(BnsMchDO::getStatus, BnsMchDO.STATUS_SUCCESS);
        query.last("limit 1");
        return entityMapper.selectCount(query) > 0;
    }

    /**
     * 根据主键起始id获取数据
     *
     * @param beginId   起始id
     * @param batchSize 批量大小
     * @return data
     */
    public List<BnsMchDO> listByBeginId(Integer beginId, Integer batchSize) {
        LambdaQueryWrapper<BnsMchDO> query = new LambdaQueryWrapper<>();
        query.gt(BnsMchDO::getId, beginId).orderByAsc(BnsMchDO::getId);
        query.last("limit " + batchSize);
        return entityMapper.selectList(query);
    }

    /**
     * 根据商户号获取数据
     *
     * @param merchantSns 商户号
     * @return data
     */
    public List<BnsMchDO> listByMerchantSns(List<String> merchantSns) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<BnsMchDO> query = new LambdaQueryWrapper<>();
        query.in(BnsMchDO::getMerchantSn, merchantSns);
        return entityMapper.selectList(query);
    }
}
