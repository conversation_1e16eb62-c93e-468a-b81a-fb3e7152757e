package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.service.MerchantEnrolService;
import com.wosai.upay.bank.model.enume.AccountApplyStatus;
import com.wosai.upay.bank.model.verify.AccountApply;
import com.wosai.upay.bank.model.verify.AccountApplyResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.PayForResultBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.SnowFlakeIdGenerator;
import com.wosai.upay.remit.exception.RemitException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: jerry
 * @date: 2019/4/15 11:24
 * @Description:代付交易处理
 */
@Component(ProviderUtil.PAY_FOR_CHANNEL)
public class PayForProvider {
    private final static Logger log = LoggerFactory.getLogger(PayForProvider.class);


    @Autowired
    private PayForTaskMapper payForTaskMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private TaskResultService taskResultService;

    @Autowired
    private AccountVerifyService accountVerifyService;
    @Autowired
    private MerchantEnrolService merchantEnrolService;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private PayForResultBiz payForResultBiz;
    @Autowired
    private DataBusBiz dataBusBiz;
    @Autowired
    private ContractTaskBiz contractTaskBiz;


    public PayForTask produceTask(Map<String, Object> contextParam, ContractSubTask contractSubTask) {
        String merchantSn = contractSubTask.getMerchant_sn();
        Map merchantBankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        //对公灰度
        if (!getRule(merchantBankAccount, merchantSn)) {
            return null;
        }
        int hashCode = getHash(merchantBankAccount);
        Long depTaskId = contractSubTask.getSchedule_dep_task_id();
        Integer schedule = contractSubTask.getSchedule_status();
        Integer statusInfluPTask = contractSubTask.getStatus_influ_p_task();
        //因为 如果是不影响task状态的subTask生成了payForTask, 会导致 task 状态一直卡在3.
        if (depTaskId == 0 && schedule == 1 && statusInfluPTask == 1) {
            PayForTask payForTask = payForTaskMapper.selectByPrimaryKey(depTaskId);
            if (payForTask == null) {
                payForTask = new PayForTask()
                        .setContext_param(JSON.toJSONString(contextParam)).setMerchant_sn(merchantSn).setStatus(PayForTask.PEND_STATUS)
                        .setHash_req(hashCode).setSub_task_id(contractSubTask.getId());
                payForTaskMapper.insertSelective(payForTask);
                return payForTask;
            }
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void processTask(PayForTask payForTask) {
        AccountApply apply = new AccountApply();
        try {
            Map contextParam = JSON.parseObject(payForTask.getContext_param(), Map.class);
            Map merchantBankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            Map merchant = (Map) contextParam.get(ParamContextBiz.KEY_MERCHANT);
            int type = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.TYPE);
            String holderName = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.HOLDER);
            String cardNo = (String) merchantBankAccount.get(MerchantBankAccountPre.NUMBER);
            String bankName = (String) merchantBankAccount.get(MerchantBankAccountPre.BANK_NAME);
            Integer idType = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE);
            String branchName = BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.BRANCH_NAME);
            String requestFlowNo = String.valueOf(SnowFlakeIdGenerator.getInstance().nextId());
            apply.setAccount_type(type)
                    .setBusiness_id(requestFlowNo)
                    .setBank_holder(holderName)
                    .setPlat_form(ProviderUtil.PLAT_FORM)
                    .setPrivate_direct_pay_for(true)
                    .setHolder_type(idType)
                    .setBank_number(cardNo)
                    .setBank_name(bankName)
                    .setBranch_name(branchName)
                    .setBank_address(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.CITY));
            payForTask.setRequest_param(JSON.toJSONString(apply));

            AccountApplyResp accountApplyResp = accountVerifyService.apply(apply);
            payForTask.setRequest_flow_no(accountApplyResp.getBusiness_id()).setSubmit_remit_order_id(accountApplyResp.getBusiness_id());
            payForTask.setResponse(JSON.toJSONString(accountApplyResp));

            ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
            if (accountApplyResp.getSyns() && accountApplyResp.getLegal()) {
                payForTaskMapper.updateByPrimaryKeySelective(payForTask);
                payForResultBiz.successHandle(contractSubTask, payForTask.getId());
                return;
            }
            if (accountApplyResp.getNeed_verify()) {
                payForTask.setStatus(PayForTask.VERIFY_STATUS);
                //插入待验证事件
                if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
                    dataBusBiz.insertPayForEvent(payForTask.getMerchant_sn(), BeanUtil.getPropString(merchant, DaoConstants.ID), ContractStatus.STATUS_WAIT_VERIFY);
                }
            } else {
                payForTask.setStatus(PayForTask.PROGRESS_STATUS);
                if (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(contractSubTask.getTask_type())) {
                    dataBusBiz.insertPayForEvent(payForTask.getMerchant_sn(), BeanUtil.getPropString(merchant, DaoConstants.ID), ContractStatus.STATUS_WAIT_FOR_PAY);
                }
            }
            payForTaskMapper.updateByPrimaryKeySelective(payForTask);


            //更新代付result
            updateTaskResult(payForTask, accountApplyResp.getNeed_verify());
        } catch (RemitException | CommonPubBizException e1) {
            log.error("remitException merchantSn {} request {} message {}", payForTask.getMerchant_sn(), apply, e1.getMessage());
            // RemitException 业务异常不需要重试
            payForTask.setStatus(PayForTask.FAIL_STATUS).setResponse(e1.getMessage());
            ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
            Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
            if (CollectionUtils.isEmpty(resp)) {
                resp = Maps.newHashMap();
            }
            resp.put(ProviderUtil.PAY_FOR_CHANNEL, "代付校验不通过" + e1.getMessage());
            contractSubTask.setResult(JSON.toJSONString(resp)).setResponse_body(JSON.toJSONString(resp));

            ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
            //文案提示处理--》
            Map result = ScheduleUtil.getPayForTip(contractTask, AccountApplyStatus.PAYMENT_FAIL.getStatus());
            contractSubTaskMapper.updateByPrimaryKey(
                    new ContractSubTask()
                            .setId(contractSubTask.getId())
                            .setResult(contractSubTask.getResult())
                            .setStatus(TaskStatus.FAIL.getVal())
            );
            payForTaskMapper.updateByPrimaryKeySelective(payForTask);
            taskResultService.changeStatusAndResultV2(contractTask.getId(), contractSubTask.getId(), TaskStatus.FAIL.getVal(), JSON.toJSONString(result), false);
        } catch (Throwable t) {
            log.error("{} 提交账户核验失败", payForTask.getMerchant_sn(), t);
            ContractSubTask sub = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
            ContractTask task = contractTaskMapper.selectByPrimaryKey(sub.getP_task_id());
            Map result = JSON.parseObject(task.getResult(), Map.class);
            if (CollectionUtils.isEmpty(result)) {
                result = new HashMap();
            }
            result.put("channel", ProviderUtil.PAY_FOR_CHANNEL);
            result.put("message", "收钱吧代付系统异常");
//            contractTaskMapper.updateByPrimaryKey(new ContractTask().setId(task.getId()).setResult(JSON.toJSONString(result)));
            // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
            contractTaskBiz.update(new ContractTask().setId(task.getId()).setResult(JSON.toJSONString(result)));
        }

    }

    private void updateTaskResult(PayForTask payForTask, boolean needVerify) {
        ContractSubTask sub = contractSubTaskMapper.selectByPrimaryKey(payForTask.getSub_task_id());
        ContractTask task = contractTaskMapper.selectByPrimaryKey(sub.getP_task_id());
        Map contextParam = JSON.parseObject(task.getEvent_context(), Map.class);
        Map taskResult = JSON.parseObject(task.getResult(), Map.class);
        //这一段单纯是为了文案提示的转义  《--
        Map merchantBankAccount = (Map) contextParam.get(ParamContextBiz.KEY_BANK_ACCOUNT);
        int type = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.TYPE);
        int idType = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.ID_TYPE);
        String message = "代付验证中";
        if (type == BankAccountTypeEnum.PERSONAL.getValue() && idType != IdentificationTypeEnum.PRC_ID_CARD.getValue()) {
            message = ScheduleUtil.TIPS_PAY_FOR_PROCESS_PRIVATE_FOREIGN;
        } else if (type == BankAccountTypeEnum.PUBLIC.getValue()) {
            if (needVerify) {
                message = ScheduleUtil.TIPS_PAY_FOR_VERIFY_PUBLIC;
            } else {
                message = ScheduleUtil.TIPS_PAY_FOR_PROCESS_PUBLIC;
            }
        }
        //--》
        if (taskResult == null) {
            taskResult = CollectionUtil.hashMap("channel", ProviderUtil.PAY_FOR_CHANNEL, "message", message);
        } else {
            taskResult.put("channel", ProviderUtil.PAY_FOR_CHANNEL);
            taskResult.put("message", message);
        }
//        contractTaskMapper.updateByPrimaryKey(new ContractTask().setId(task.getId()).setResult(JSON.toJSONString(taskResult)));
        // TODO 更新contract_task表status,并根据type和status判断是否发送消息到神策
        contractTaskBiz.update(new ContractTask().setId(task.getId()).setResult(JSON.toJSONString(taskResult)));
    }


    private int getHash(Map data) {
        String holderName = (String) data.get(MerchantBankAccountPre.HOLDER);
        String cardNo = (String) data.get(MerchantBankAccountPre.NUMBER);

        StringBuilder stringBuilder = new StringBuilder()
                .append(holderName).append(cardNo);
        return stringBuilder.toString().hashCode();
    }


    private boolean getRule(Map merchantBankAccount, String merchantSn) {
        int type = BeanUtil.getPropInt(merchantBankAccount, MerchantBankAccount.TYPE);
        if (type != BankAccountTypeEnum.PUBLIC.getValue()) {
            return true;
        }
        boolean newRule, apoloConfig;

        apoloConfig = applicationApolloConfig.getPublicPayForRule();
        try {
            newRule = merchantEnrolService.invokeNewEnrolRule(merchantSn);
        } catch (Throwable t) {
            log.error("{} 获取商户灰度规则失败", merchantSn, t);
            return false;
        }
        return newRule && apoloConfig;
    }
}
