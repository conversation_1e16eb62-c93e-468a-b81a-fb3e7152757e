package com.wosai.upay.job.refactor.biz.acquirer.haike;

import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Optional;

/**
 * haike商户信息处理
 *
 * <AUTHOR>
 * @date 2024/7/25 16:52
 */
@Component
@Slf4j
public class HaikeMerchantInfoProcessor {

    @Resource(type = HaikeService.class)
    private HaikeService haikeService;

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    /**
     * 获取海科商户信息
     * todo map -> java bean (后期统一重构）
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public Optional<Map<String, Object>> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        try {
            Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
            HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
            final ContractResponse contractResponse = haikeService.queryMerchant(contextParam, haikeParam);
            if (contractResponse == null || !contractResponse.isSuccess() || contractResponse.getResponseParam() == null) {
                return Optional.empty();
            }
            return Optional.of(contractResponse.getResponseParam());
        } catch (Exception e) {
            log.error("获取海科商户信息异常, merchantSn:{}", merchantSn, e);
            return Optional.empty();
        }
    }

    /**
     * 获取海科商户银行卡号
     *
     * @param merchantSn 商户号
     * @return 银行卡号
     */
    public Optional<String> getBankAccountNo(String merchantSn) {
        Optional<Map<String, Object>> merchantInfo = getMerchantInfo(merchantSn);
        return merchantInfo.map(stringObjectMap -> BeanUtil.getPropString(stringObjectMap, "bankcard_data.acc_no"));
    }
}
