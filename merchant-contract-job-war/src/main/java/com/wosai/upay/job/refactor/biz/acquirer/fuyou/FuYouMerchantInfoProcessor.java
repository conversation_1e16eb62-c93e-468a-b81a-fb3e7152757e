package com.wosai.upay.job.refactor.biz.acquirer.fuyou;

import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 富友商户信息处理
 *
 * <AUTHOR>
 * @date 2024/7/25 11:30
 */
@Component
@Slf4j
public class FuYouMerchantInfoProcessor {

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource(type = FuyouService.class)
    private FuyouService fuyouService;

    @Resource
    private ParamContextBiz paramContextBiz;

    @Resource
    private MerchantService mcMerchantService;

    public static final String ACCOUNT_LIST_KEY = "mchntAcntInfList";

    public static final String ACCOUNT_KEY = "acntNo";

    /**
     * 获取富友商户信息
     * todo map -> java bean (后期统一重构）
     *
     * @param merchantSn 商户号
     * @return 商户信息
     */
    public Optional<Map<String, Object>> getMerchantInfo(String merchantSn) {
        if (StringUtils.isBlank(merchantSn)) {
            return Optional.empty();
        }
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        try {
            ContractResponse contractResponse = fuyouService.queryMerchant(merchantSn, fuyouParam);
            if (Objects.isNull(contractResponse) || !contractResponse.isSuccess() || Objects.isNull(contractResponse.getResponseParam())) {
                log.error("获取富友商户信息失败, merchantSn:{}", merchantSn);
                return Optional.empty();
            }
            return Optional.of(contractResponse.getResponseParam());
        } catch (Exception e) {
            log.error("获取富友商户信息异常, merchantSn:{}", merchantSn, e);
            return Optional.empty();
        }
    }

    /**
     * 获取商户银行账户信息
     *
     * @param merchantSn 商户号
     * @return 商户银行账户信息
     */
    public List<String> getMerchantBankAccounts(String merchantSn) {
        Optional<Map<String, Object>> accountOpt = getMerchantInfo(merchantSn);
        if (!accountOpt.isPresent()) {
            return Collections.emptyList();
        }
        try {
            List<Map> accountsMap = (List<Map>) MapUtils.getObject(accountOpt.get(), ACCOUNT_LIST_KEY, Collections.emptyList());
            List<String> bankNoList = new ArrayList<>();
            for (Map account : accountsMap) {
                bankNoList.add(MapUtils.getString(account, ACCOUNT_KEY));
            }
            return bankNoList;
        } catch (Exception e) {
            log.error("获取商户银行账户信息异常, merchantSn:{}", merchantSn, e);
            return Collections.emptyList();
        }

    }

    /**
     * 检查费率是否一致
     *
     * @param merchantSn
     * @return
     */
    public boolean isFeeRateConsistent(String merchantSn) {
        MerchantInfo merchant = mcMerchantService.getMerchantBySn(merchantSn, null);
        return fuyouService.isFeeRateConsistent(merchantSn, paramContextBiz.getSqbFeeRates(merchant.getId()));
    }


}
