package com.wosai.upay.job.refactor.biz.acquirer.fuyou;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.IdentificationTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 富有收单机构门面服务
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
@Slf4j
public class FuYouAcquirerFacade
        extends AbstractAcquirerHandler
        implements FuYouUniqueAbility {

    @Resource
    private FuYouOpenDayZeroProcessor fuYouOpenDayZeroProcessor;

    @Resource
    private FuYouContractProcessor fuYouContractProcessor;

    @Resource
    private FuYouMerchantInfoProcessor fuYouMerchantInfoProcessor;

    public static final String FU_YOU_CERTIFICATE_KEY = "fuyou_certificate";

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private com.wosai.upay.core.service.MerchantService coreBMerchantService;

    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;

    public static final Integer FU_YOU_MERCHANT_STATUS_CLOSE = 0;

    public static final Integer FU_YOU_MERCHANT_STATUS_OPEN = 1;

    public static final String FU_YOU_MERCHANT_STATUS_KEY = "insSt";


    @Override
    public Optional<String> getDefaultContractRuleGroupId() {
        return Optional.of(McConstant.RULE_GROUP_FUYOU);
    }

    @Override
    public String getMicroUpdageDefaultContractRuleGroupId() {
        return McConstant.RULE_GROUP_MICROUPGRADE_FUYOU;
    }

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.FU_YOU;
    }


    @Override
    public boolean isBankCardConsistentWithSqb(String merchantSn) {
        return acquirerCommonTemplate.checkBankCardConsistentWithSqb(
                getTypeValue(), merchantSn,
                t -> StringUtils.join(fuYouMerchantInfoProcessor.getMerchantBankAccounts(merchantSn), ","),
                (sqbBankCardNo, acquirerBankCardNo) -> StringUtils.contains(acquirerBankCardNo, sqbBankCardNo)
        );
    }

    @Override
    public boolean isFeeRateConsistentWithSqb(String merchantSn) {
        return fuYouMerchantInfoProcessor.isFeeRateConsistent(merchantSn);
    }

    @Override
    public void openDayZero(ContractTaskDO contractTaskDO, boolean force) {
        fuYouOpenDayZeroProcessor.openDayZero(contractTaskDO, force);
    }

    @Override
    public String queryOpenDayZeroResult(ContractTaskDO contractTaskDO, boolean force) {
        return fuYouOpenDayZeroProcessor.queryOpenDayZeroResult(contractTaskDO, force);
    }

    @Override
    public String  getContractSettleTypeByMerchantSn(String merchantSn) {
        return fuYouContractProcessor.getContractSettleTypeByMerchantSn(merchantSn);
    }


    /**
     * 收单机构特殊校验
     *
     * @param merchantFeatureBO 商户特征
     * @return 校验结果
     */
    @Override
    protected ContractGroupRuleVerifyResultBO satisfactionToAcquirerSpecificCheck(MerchantFeatureBO merchantFeatureBO) {
        // 企业对私 需要满足有对公账户凭证
        if (StringUtils.equals(merchantFeatureBO.getBankAccountType(), BankAccountTypeEnum.PERSONAL.getValue().toString())
                && StringUtils.isNotBlank(merchantFeatureBO.getMerchantSn())
                && Objects.nonNull(merchantFeatureBO.getExtraFeature())
                && Objects.nonNull(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo())
                && Objects.equals(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo().getType(), BusinessLicenseTypeEnum.ENTERPRISE.getValue())) {
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantFeatureBO.getMerchantSn(), null);
            if (Objects.isNull(merchant) || StringUtils.isBlank(MapUtils.getString(merchant.getExtra(), FU_YOU_CERTIFICATE_KEY))) {
                return ContractGroupRuleVerifyResultBO.fail("请先完成「富友企业对私商户补充对公凭证」申请");
            }
        }
        // 法人证件为执行事务合伙人，不可以切过来
        if (Objects.nonNull(merchantFeatureBO.getExtraFeature())
                && Objects.nonNull(merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo())) {
            Integer legalPersonIdType = merchantFeatureBO.getExtraFeature().getMerchantBusinessLicenseInfo().getLegal_person_id_type();
            if (Objects.equals(legalPersonIdType, IdentificationTypeEnum.EXECUTIVE_PARTNER.getValue())) {
                return ContractGroupRuleVerifyResultBO.fail("富友不支持执行事务合伙人法人证件类型");
            }
        }
        return new ContractGroupRuleVerifyResultBO(true);
    }

    /**
     * 获取商户所在收单机构的商户状态
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    @Override
    public AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        Optional<Map<String, Object>> merchantInfo = fuYouMerchantInfoProcessor.getMerchantInfo(merchantSn);
        if (!merchantInfo.isPresent() || MapUtils.isEmpty(merchantInfo.get())) {
            return AcquirerMerchantStatusEnum.CLOSE;
        }
        // 如果取不到默认给 NORMAL
        return Objects.equals(MapUtils.getInteger(merchantInfo.get(), FU_YOU_MERCHANT_STATUS_KEY, FU_YOU_MERCHANT_STATUS_OPEN), FU_YOU_MERCHANT_STATUS_CLOSE)
                ? AcquirerMerchantStatusEnum.CLOSE : AcquirerMerchantStatusEnum.NORMAL;
    }

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 是否可以营业执照和结算账户一起变更
     *
     * @param merchantSn                   商户号
     * @param newAccountType               新的账户类型
     * @param newAccountNumber             新的账户号
     * @param originalDefaultAccountType   原来的默认账户类型
     * @param originalDefaultAccountNumber 原来的默认账户号
     * @return true-是可以一起变更，false-不可以一起变更
     */
    public boolean enableUpdateLicenseAndAccountTogether(String merchantSn,
                                                         Integer newAccountType,
                                                         String newAccountNumber,Integer originalDefaultAccountType, String originalDefaultAccountNumber) {
        if (!applicationApolloConfig.getFuYouEnableUpdateLicenseAndAccountTogether()) {
            return false;
        }
        String merchantId = merchantService.getMerchantBySn(merchantSn, null).getId();
        MerchantBusinessLicenseInfo license = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        // 双账户不支持
        if (Objects.equals(license.getType(), BusinessLicenseTypeEnum.ENTERPRISE.getValue())
                && Objects.equals(newAccountType, BankAccountTypeEnum.PERSONAL.getValue())) {
            return false;
        }
        // 账户类型变更不支持
        if (!Objects.equals(newAccountType, originalDefaultAccountType)) {
            return false;
        }
        if (Objects.equals(newAccountType, BankAccountTypeEnum.PUBLIC.getValue())  && StringUtils.equals(newAccountNumber, originalDefaultAccountNumber)) {
            log.info("isLicenseTaskNeedExtraChangeAccountTask 富友对公结算修改营业执照和结算账户,不需要额外的结算任务变更任务,走单接口一次性变更, merchantSn:{}", merchantSn);
            return true;
        }
        if (Objects.equals(newAccountType, BankAccountTypeEnum.PERSONAL.getValue()) && StringUtils.equals(newAccountNumber, originalDefaultAccountNumber)) {
            log.info("isLicenseTaskNeedExtraChangeAccountTask 富友对私结算修改营业执照和结算账户,不需要额外的结算任务变更任务,走单接口一次性变更, merchantSn:{}", merchantSn);
            return true;
        }
        return false;
    }


    @Override
    public MerchantAcquireInfoBO getAcquireInfoFromContractSubTask(Long pTaskId) {
        final List<ContractSubTaskDO> subTaskDOS = contractSubTaskDAO.listByPTaskId(pTaskId);
        // 查找富友入网任务
        ContractSubTaskDO fuyouSubTask = findSubTask(subTaskDOS, PaywayEnum.ACQUIRER.getValue(), "富友入网任务", pTaskId);
        String responseBody = fuyouSubTask.getResponseBody();
        if (Objects.isNull(responseBody)) {
            throw new ContractBizException("富友机构进件子任务返回结果callback_msg为空, subTask id = " + fuyouSubTask.getId());
        }

        final MerchantAcquireInfoBO merchantAcquireInfoBO = new MerchantAcquireInfoBO();

        // 设置富友相关信息
        merchantAcquireInfoBO.setAcquireMerchantId(getNestedProperty(JSONObject.parseObject(responseBody,Map.class), "responseParam.fy_mchnt_cd"));
        // 查找云闪付入网任务
        ContractSubTaskDO unionSubTask = findSubTask(subTaskDOS, PaywayEnum.UNIONPAY.getValue(), "云闪付报备任务", pTaskId);
        if(Objects.nonNull(unionSubTask.getResponseBody())) {
            //银联商户号
            merchantAcquireInfoBO.setUnionNo(getNestedProperty(JSONObject.parseObject(unionSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));
        }

        // 查找微信入网任务
        ContractSubTaskDO wxSubTask = findSubTask(subTaskDOS, PaywayEnum.WEIXIN.getValue(), "微信报备任务", pTaskId);
        merchantAcquireInfoBO.setWxNo(getNestedProperty(JSONObject.parseObject(wxSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));
        merchantAcquireInfoBO.setWxContractRule(wxSubTask.getContractRule());
        // 查找支付宝入网任务
        ContractSubTaskDO aliSubTask = findSubTask(subTaskDOS, PaywayEnum.ALIPAY.getValue(), "支付宝报备任务", pTaskId);
        merchantAcquireInfoBO.setAliNo(getNestedProperty(JSONObject.parseObject(aliSubTask.getResponseBody(),Map.class), "responseParam.sub_mch_id"));

        return merchantAcquireInfoBO;
    }


    public  List<String> fyContractRuleList = Lists.newArrayList("fuyou-1038-3","fuyou-1038-2","fuyou-1038-17");

    /**
     * 查找指定支付方式的成功子任务
     */
    protected ContractSubTaskDO findSubTask(List<ContractSubTaskDO> subTaskDOS, Integer payway, String taskName, Long pTaskId) throws ContractBizException {
        Optional<ContractSubTaskDO> optionalSubTask = subTaskDOS.stream()
                .filter(r -> {
                    if(Objects.equals(PaywayEnum.ACQUIRER.getValue(), payway)) {
                       return  Objects.equals(r.getPayway(), payway)
                               && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                               && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                               && Objects.equals(r.getContractRule(), "fuyou");
                    }else {
                        return  Objects.equals(r.getPayway(), payway)
                                && Objects.equals(r.getStatus(), ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())
                                && Objects.equals(r.getTaskType(), ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                                && fyContractRuleList.contains(r.getContractRule());
                    }


                })
                .findFirst();

        if (!optionalSubTask.isPresent()) {
            log.warn("pTaskId{} 没有找到对应的{}", pTaskId, taskName);
            throw new ContractBizException("没有找到对应的" + taskName);
        }

        return optionalSubTask.get();
    }


}
