package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 商户所在收单机构信息表表实体对象
 *
 * <AUTHOR>
 */
@TableName("merchant_acquirer_info")
@Data
public class MerchantAcquirerInfoDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 收单机构商户id
     */
    @TableField(value = "acquirer_merchant_id")
    private String acquirerMerchantId;
    /**
     * D0业务开通状态 0-未开通 1-等待开通, 2-开通中 3-开通成功 4-开通失败
     */
    @TableField(value = "day_zero_open_status")
    private Integer dayZeroOpenStatus;
    /**
     * 收单机构侧商户信息(json格式固定)
     */
    @TableField(value = "merchant_info")
    private String merchantInfo;
    /**
     * 收单机构侧营业执照信息(json格式固定)
     */
    @TableField(value = "business_license_info")
    private String businessLicenseInfo;
    /**
     * 收单机构侧银行卡信息(json格式固定)
     */
    @TableField(value = "bank_card_info")
    private String bankCardInfo;
    /**
     * 收单机构侧商户(进件)状态 0-失败 1-成功
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 更新时间
     */
    @TableField(value = "mtime")
    private Timestamp mtime;
    /**
     * 创建时间
     */
    @TableField(value = "ctime")
    private Timestamp ctime;


}

