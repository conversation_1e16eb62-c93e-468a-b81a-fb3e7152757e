package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.InternalScheduleMainTaskMapper;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;


/**
 * 内部调度任务主表表数据库访问层 {@link InternalScheduleMainTaskDO}
 * 对InternalScheduleMainTaskMapper层做出简单封装 {@link InternalScheduleMainTaskMapper}
 *
 * <AUTHOR>
 */
@Repository
public class InternalScheduleMainTaskDAO extends AbstractBaseDAO<InternalScheduleMainTaskDO, InternalScheduleMainTaskMapper> {

    public InternalScheduleMainTaskDAO(SqlSessionFactory sqlSessionFactory, InternalScheduleMainTaskMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    /**
     * 根据主键更新,null字段不参与更新
     *
     * @param entity 待更新对象
     */
    @Override
    public Integer updateByPrimaryKeySelective(InternalScheduleMainTaskDO entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        entity.setMtime(null);
        entity.setCtime(null);
        return super.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据id大于startId的主任务列表
     *
     * @param type    任务类型
     * @param status  任务状态
     * @param startId 起始id
     * @param taskNum 任务数量
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listByTypeAndStatusGeIdWithLimit(Integer type, Integer status, Long startId, Integer taskNum) {
        LambdaUpdateWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(InternalScheduleMainTaskDO::getType, type);
        wrapper.eq(InternalScheduleMainTaskDO::getStatus, status);
        wrapper.ge(InternalScheduleMainTaskDO::getId, startId);
        wrapper.last("limit " + taskNum);
        wrapper.orderByAsc(InternalScheduleMainTaskDO::getId);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据任务类型和任务状态,按照上次调度时间排序,获取主任务列表
     *
     * @param type    任务类型
     * @param status  任务状态
     * @param taskNum 任务数量
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listByTypeAndStatusOrderByLastScheduledTimeWithLimit(Integer type, Integer status, Integer taskNum) {
        LambdaUpdateWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(InternalScheduleMainTaskDO::getType, type);
        wrapper.eq(InternalScheduleMainTaskDO::getStatus, status);
        wrapper.orderByAsc(InternalScheduleMainTaskDO::getLastScheduledTime);
        wrapper.last("limit " + taskNum);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 批量更新主任务状态
     *
     * @param ids    主任务id列表
     * @param status 状态
     * @return 更新条数
     */
    public Integer batchUpdateStatusByIds(List<Long> ids, Integer status) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        LambdaUpdateWrapper<InternalScheduleMainTaskDO> updateWrapper = new LambdaUpdateWrapper<InternalScheduleMainTaskDO>()
                .in(InternalScheduleMainTaskDO::getId, ids)
                .set(InternalScheduleMainTaskDO::getStatus, status);
        return entityMapper.update(null, updateWrapper);
    }

    /**
     * 批量更新任务
     *
     * @param mainTaskDOS 主任务列表
     * @param subTaskDOS  子任务列表
     * @return 插入条数
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateTasks(List<InternalScheduleMainTaskDO> mainTaskDOS, List<InternalScheduleSubTaskDO> subTaskDOS) {
        int updateRows = 0;
        if (CollectionUtils.isNotEmpty(mainTaskDOS)) {
            mainTaskDOS.forEach(mainTaskDO -> {
                mainTaskDO.setCtime(null);
                mainTaskDO.setMtime(null);
            });
            updateRows += super.batchUpdateByIdSelective(mainTaskDOS);
        }
        if (CollectionUtils.isNotEmpty(subTaskDOS)) {
            subTaskDOS.forEach(subTaskDO -> {
                subTaskDO.setCtime(null);
                subTaskDO.setMtime(null);
            });
            updateRows += internalScheduleSubTaskDAO.batchUpdateByIdSelective(subTaskDOS);
        }
        return updateRows;
    }

    /**
     * 根据id列表批量获取任务
     *
     * @param ids 主任务id列表
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InternalScheduleMainTaskDO::getId, ids);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据商户号和任务类型获取主任务列表
     *
     * @param merchantSn 商户号
     * @param type       任务类型
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listBySnAndType(String merchantSn, Integer type) {
        if (StringUtils.isBlank(merchantSn)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InternalScheduleMainTaskDO::getMerchantSn, merchantSn);
        wrapper.eq(InternalScheduleMainTaskDO::getType, type);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据商户号和任务类型列表获取主任务列表
     *
     * @param merchantSn 商户号
     * @param types      任务类型列表
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listBySnAndTypes(String merchantSn, Collection<Integer> types) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(types)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InternalScheduleMainTaskDO::getMerchantSn, merchantSn);
        wrapper.in(InternalScheduleMainTaskDO::getType, types);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据商户号和任务类型获取主任务列表数量
     *
     * @param merchantSn 商户号
     * @param types      任务类型列表
     * @return 主任务列表
     */
    public Long countBySnAndTypeWithStatusNotIn(String merchantSn, List<Integer> types, Collection<Integer> status) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(types)) {
            return 0L;
        }
        LambdaQueryWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InternalScheduleMainTaskDO::getMerchantSn, merchantSn);
        wrapper.in(InternalScheduleMainTaskDO::getType, types);
        wrapper.notIn(CollectionUtils.isNotEmpty(status), InternalScheduleMainTaskDO::getStatus, status);
        return entityMapper.selectCount(wrapper);
    }

    /**
     * 大于创建时间和根据类型，获取任务
     *
     * @param beginUpdateTime 更新时间
     * @param type            任务类型
     * @return 主任务
     */
    public List<InternalScheduleMainTaskDO> listByGeUpdateTimeAndType(Timestamp beginUpdateTime, Integer type) {
        LambdaQueryWrapper<InternalScheduleMainTaskDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(InternalScheduleMainTaskDO::getMtime, beginUpdateTime);
        wrapper.eq(InternalScheduleMainTaskDO::getType, type);
        return entityMapper.selectList(wrapper);
    }

    /**
     * 更新主任务及新增其子任务
     *
     * @param mainTask           主任务
     * @param scheduleSubTaskDOS 子任务列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateMainTaskAndInsertSubTasks(InternalScheduleMainTaskDO mainTask, List<InternalScheduleSubTaskDO> scheduleSubTaskDOS) {
        updateByPrimaryKeySelective(mainTask);
        internalScheduleSubTaskDAO.batchInsert(scheduleSubTaskDOS);
    }

    /**
     * 根据任务类型和商户号列表获取主任务列表
     *
     * @param internalScheduleTaskTypeEnum 任务类型
     * @param merchantSns                  商户号列表
     * @return 主任务列表
     */
    public List<InternalScheduleMainTaskDO> listByTypeAndMerchantSns(InternalScheduleTaskTypeEnum internalScheduleTaskTypeEnum, Collection<String> merchantSns) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InternalScheduleMainTaskDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InternalScheduleMainTaskDO::getType, internalScheduleTaskTypeEnum.getValue());
        queryWrapper.in(InternalScheduleMainTaskDO::getMerchantSn, merchantSns);
        return entityMapper.selectList(queryWrapper);
    }
}
