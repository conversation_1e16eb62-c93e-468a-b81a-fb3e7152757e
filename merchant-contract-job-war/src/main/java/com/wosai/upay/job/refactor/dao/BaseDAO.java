package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;

/**
 * 基础DAO
 *
 * @param <M> 实体类型
 * @param <T> mapper类型
 */
public interface BaseDAO<M, T extends BaseMapper<M>> {


    /**
     * 获取所有的实体对象
     * 注: 该方法不适用于大数据量的查询,且已经加了1000条的限制
     *
     * @return 实体对象列表
     */
    List<M> listAll();

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 实体对象
     */
    Optional<M> getByPrimaryKey(Serializable id);

    /**
     * 根据条件查询,返回一个
     *
     * @param queryWrapper 查询条件
     * @return 实体对象
     */
    Optional<M> selectOne(LambdaQueryWrapper<M> queryWrapper);

    /**
     * 根据主键更新,null字段不参与更新
     *
     * @param entity 待更新对象
     */
    Integer updateByPrimaryKeySelective(M entity);

    /**
     * 根据主键id删除
     *
     * @param id 主键id
     */
    Integer deleteByPrimaryKey(Serializable id);

    /**
     * 新增一条记录
     *
     * @param entity 实体对象
     */
    Integer insertOne(M entity);

    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return 删除条数
     */
    Integer batchDeleteByPrimaryKeys(Collection<? extends Serializable> ids);

    /**
     * 批量新增
     *
     * @param dataList 数据列表
     * @return 新增条数
     */
    Integer batchInsert(List<M> dataList);

    /**
     * 批量根据主键更新,null字段不参与更新
     *
     * @param dataList 数据列表
     * @return 更新条数
     */
     Integer batchUpdateByIdSelective(List<M> dataList);

    /**
     * 批量更新
     *
     * @param dataList  数据列表
     * @param operation 更新方式
     * @return 更新条数
     */
     Integer batchUpdate(List<M> dataList, BiConsumer<T, M> operation);
}