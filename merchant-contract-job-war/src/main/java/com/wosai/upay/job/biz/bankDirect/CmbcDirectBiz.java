package com.wosai.upay.job.biz.bankDirect;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.paramContext.FeeRateUtil;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.BankDirectApplyRefEnum;
import com.wosai.upay.job.enume.BankDirectApplyViewStatusEnum;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.model.psbc.BankDirectReq;
import com.wosai.upay.job.service.ContractTaskResultServiceImpl;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Component
public class CmbcDirectBiz extends AbstractBankDirectApplyBiz{

    /**
     * 银行客户经理门头合影照
     */
    public static final String ACCOUNT_MANAGER_STOREFRONT_PHOTO = "accountManagerStorefrontPhoto";

    /**
     * 分润模式
     */
    public static final String PROFIT_SHARING_MODE = "profitSharingMode";

    /**
     * 签约民生分行
     */
    public static final String SIGNED_MINSHENG_BRANCH = "signedMinshengBranch";

    /**
     * 民生客户经理编号
     */
    public static final String MINSHENG_ACCOUNT_MANAGER_ID = "minshengAccountManagerId";


    /**
     * 收单机构代码
     */
    public static final String ACQINSIDCD = "acqInsIdCd";

    /**
     * 签约机构
     */
    public static final String CONTRACT_BRANCH = "contractBranch";






    private static final List<String> customeFields = Lists.newArrayList(
            ACCOUNT_MANAGER_STOREFRONT_PHOTO,
            PROFIT_SHARING_MODE,
            SIGNED_MINSHENG_BRANCH,
            MINSHENG_ACCOUNT_MANAGER_ID
    )
;


    @Value("${cmbc_dev_code}")
    public String cmbcDevCode;

    @Autowired
    private ContractTaskResultServiceImpl contractTaskResultService;


    @Override
    protected String chooseGroupByContextAndDevCode(Map<String, Object> context, String devCode) {
        //分润模式和group_id相关
        final String profitSharingMode = MapUtils.getString(context, PROFIT_SHARING_MODE);
        if(StringUtils.isEmpty(profitSharingMode)) {
            throw new ContractBizException("分润模式不能为空");
        }

        AtomicReference<String> mode = new AtomicReference<>("");
        final Map<String, String> profitSharingModeInfo = applicationApolloConfig.getCmbcProfitSharingModeInfo();
        profitSharingModeInfo.forEach((k,v) -> {
            if(Objects.equals(profitSharingMode,v)) {
                mode.set(k);
            }
        });
        if(Objects.isNull(mode.get())) {
           throw new ContractBizException("分润模式不存在");
        }
        return String.format("cmbc-%s",mode.get());
    }

    @Override
    protected Map<String, Object> completeContext(Map<String, Object> paramContext, BankDirectReq bankDirectReq) {
        final Map formBody = JSONObject.parseObject(bankDirectReq.getForm_body(), Map.class);
        //放入费率
        final List config = JSONObject.parseObject(BeanUtil.getPropString(formBody, "merchant_config"), List.class);
        paramContext.put("cmbc_feeRate", config);
        //放入套餐
        paramContext.put("trade_combo_id", BeanUtil.getPropString(formBody, "trade_combo_id"));
        //放入业务标识
        paramContext.put("dev_code",bankDirectReq.getDev_code());
        //crm页面采集信息
        customeFields.forEach(field -> {
            final String customeField = BeanUtil.getPropString(formBody, field);
            if(Objects.equals(field,PROFIT_SHARING_MODE)) {
                final String profitSharingModeCode = MapUtils.getString(applicationApolloConfig.getCmbcProfitSharingModeInfo(),customeField);
                paramContext.put(PROFIT_SHARING_MODE, profitSharingModeCode);
            }else if(Objects.equals(field,SIGNED_MINSHENG_BRANCH)) {
                final Map<String, String> contractBranchMap = (Map<String, String>) MapUtils.getMap(applicationApolloConfig.getCmbcContractBranchInfo(), customeField);
                paramContext.put(ACQINSIDCD, MapUtils.getString(contractBranchMap,  ACQINSIDCD));
                paramContext.put(CONTRACT_BRANCH, MapUtils.getString(contractBranchMap,  CONTRACT_BRANCH));
            } else {
                paramContext.put(field,BeanUtil.getPropString(formBody, field));
            }
        });



        //放入spa可以识别的费率信息
        final List<Map> list = ((List<Map>) config).stream().map(x -> {
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.WEIXIN.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.WECHAT_PAY_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.ALIPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.ALIPAY_WALLET_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            if (BeanUtil.getPropInt(x, "payway") == PaywayEnum.UNIONPAY.getValue()) {
                return CollectionUtil.hashMap(
                        LakalaBusinessFileds.WECHAT_TYPE, LakalaConstant.UNIONPAY_WALLET_DEBIT_FEE,
                        LakalaBusinessFileds.WECHAT_RATE, FeeRateUtil.formatFeeRate(BeanUtil.getPropString(x, "rate"))
                );
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        paramContext.putIfAbsent(ParamContextBiz.MERCHANT_FEE_RATES,list);
        return paramContext;
    }

    @Override
    protected Integer getBankRef(String dev_code) {
        return BankDirectApplyRefEnum.CMBC.getValue();
    }

    @Override
    protected String getAcquire() {
        return AcquirerTypeEnum.CMBC.getValue();
    }

    @Override
    protected Integer getProvider() {
        return ProviderEnum.PROVIDER_CMBC.getValue();
    }

    @Override
    public String getDevCode() {
        return cmbcDevCode;
    }

    @Override
    public List<ViewProcess> initViewProcess(String merchantSn) {
        final List<ViewProcess> viewProcesses = preViewProcess(getAcquire());
        if(CollectionUtils.isEmpty(viewProcesses)) {
            return Lists.newArrayList();
        }
        //设置微信图片地址链接
        viewProcesses.stream().forEach(x-> {
            if(Objects.equals(x.getExtra(),Boolean.TRUE)) {
                final String imageUrl = getWXImageUrl(getAcquire(), getProvider());
                x.setExtraMessage(imageUrl);
            }
        });
        return viewProcesses;
    }


    @Override
    protected void postHandleViewProcess(BankDirectApply bankDirectApply, List<ViewProcess> processes) {
        if (Objects.isNull(bankDirectApply.getTask_id()) || bankDirectApply.getProcess_status() != 10) {
            return;
        }
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(bankDirectApply.getTask_id());
        if (Objects.isNull(contractTask)) {
            return;
        }
        processes.forEach(e -> {
            if (Objects.equals(BankDirectApplyViewStatusEnum.DISTRIBUTING.getValue(), e.getViewStatus())) {
                e.setLatestBankReviewStatusDesc(contractTask.getResult());
            }
        });
    }

    @Override
    public BankDirectApply populateBean(BankDirectReq bankDirectReq, Long taskId) {
        final BankDirectApply bankDirectApply = new BankDirectApply();
        bankDirectApply.setMerchant_sn(bankDirectReq.getMerchant_sn());
        bankDirectApply.setTask_id(taskId);
        bankDirectApply.setDev_code(bankDirectReq.getDev_code());
        Integer bank_ref = getBankRef(bankDirectReq.getDev_code());
        bankDirectApply.setBank_ref(bank_ref);
        bankDirectApply.setStatus(BankDirectApplyConstant.Status.PENDING);
        bankDirectApply.setResult("已提交请等待");
        bankDirectApply.setForm_body(bankDirectReq.getForm_body());
        final Map map = CollectionUtil.hashMap(BankDirectApplyConstant.Extra.ACQUIRE, getAcquire(), BankDirectApplyConstant.Extra.PROVIDER, getProvider());
        //建行批量导入
        String formBody = bankDirectReq.getForm_body();
        Map context = JSONObject.parseObject(formBody, Map.class);
        String from = BeanUtil.getPropString(context, "from");
        if (Objects.equals(from, "batch_import")) {
            map.put(BankDirectApplyConstant.Extra.DATA_FROM, "batch_import");
        }
        //不需要校验支付宝状态
        map.putIfAbsent(BankDirectApplyConstant.Extra.CHECK_ALI_AUTH, false);
        bankDirectApply.setExtra(JSONObject.toJSONString(map));
        return bankDirectApply;
    }
}
