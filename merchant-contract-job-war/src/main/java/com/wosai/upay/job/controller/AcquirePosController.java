package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfo;
import com.wosai.upay.job.model.dto.acquirePos.PosActiveInfoDTO;
import com.wosai.upay.job.service.T9Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2024/2/28 09:26
 */
@RestController
@RequestMapping("/t9_pos")
@Slf4j
@Validated
public class AcquirePosController {
    @Autowired
    private T9Service t9Service;
    @PostMapping("/getActiveInfo")
    public PosActiveInfo posActiveNo(@Valid PosActiveInfoDTO dto){
        log.info("getActiveInfo param:{}",JSONObject.toJSONString(dto));
        PosActiveInfo posActiveInfo = t9Service.getPosActiveInfo(dto);
        log.info("getActiveInfo param:{} result:{}", JSONObject.toJSONString(dto),JSONObject.toJSONString(posActiveInfo));
        return posActiveInfo;
    }
}
