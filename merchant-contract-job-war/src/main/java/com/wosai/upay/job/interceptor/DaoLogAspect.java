package com.wosai.upay.job.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * 只记录 DAO 的日志
 * Service 层的日志通过 TracingInterceptor 记录
 * Created by lihebin on 29/01/2018.
 */
@Aspect
@Component
@Order(1)
@Slf4j
public class DaoLogAspect {

    //申明一个切点 里面是 execution表达式
    @Pointcut("@annotation(DaoLog)")
    private void daoAspect() {

    }

    //请求method前打印内容
    @Before(value = "daoAspect()")
    public void daoBefore(JoinPoint joinPoint) {
        log.info("{} args: {}", joinPoint.getSignature().getName(), Arrays.toString(joinPoint.getArgs()));
    }


}
