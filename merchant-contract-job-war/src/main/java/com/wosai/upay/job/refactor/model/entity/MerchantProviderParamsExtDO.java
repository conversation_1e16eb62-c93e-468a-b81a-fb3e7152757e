package com.wosai.upay.job.refactor.model.entity;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.merchant.contract.model.UnionOpenResp;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.Objects;


/**
 * 商户报备获取的银行交易参数存储表(new)表实体对象
 *
 * <AUTHOR>
 */
@TableName("merchant_provider_params_ext")
@Data
public class MerchantProviderParamsExtDO {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "param_id")
    private String paramId;

    @TableField(value = "type")
    private Integer type;

    @TableField(value = "version")
    private Long version;

    @TableField(value = "extra")
    private String extra;
    /**
     * 支付宝 升级M3 表示商户等级
     **/
    @TableField(value = "ext_field_1")
    private String extField1;
    /**
     * 支付宝升级m3 微信开通点金计划 均代表支付源商户号
     */
    @TableField(value = "ext_field_2")
    private String extField2;

    @TableField(value = "create_at")
    private Date createAt;

    @TableField(value = "update_at")
    private Date updateAt;


    /**
     * 微信点金计划
     **/
    public static final int WEIXIN_GOLD_TYPE = 3;
    /**
     * 支付宝M3
     */
    public static final int ALY_M3_TYPE = 2;

    /**
     * 银联开放平台
     */
    public static final int UNION_OPEN_TYPE = 4;

    /**
     * 云闪付开通状态
     */
    public static final int UNION_PAY = 5;

    public static final String UNION_PAY_SUCCESS = "SUCCESS";
    public static final String UNION_PAY_FAIL = "FAIL";
    public static final String UNION_PAY_DELETE = "DELETE";

    public boolean isUnionAudited(){
        return this.extField2 != null && !UnionOpenResp.SYSTEM_ERROR.equalsIgnoreCase(this.extField2) &&
                !UnionOpenResp.SYSTEM_FAIL.equalsIgnoreCase(this.extField2) && !UnionOpenResp.AUDIT_FAIL.equalsIgnoreCase(this.extField2) &&
                !"0".equalsIgnoreCase(this.extField2);
    }

    public boolean isUnionPaySuccess() {
        return Objects.equals(UNION_PAY_SUCCESS, extField1);
    }

    public String getFailMessage() {
        Map map = JSON.parseObject(extra, Map.class);
        return WosaiMapUtils.getString(map, "message");
    }
}

