package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description: 支付宝的通用service
 * <AUTHOR>
 * @Date 2023/11/17
 **/
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class HaikeServiceImpl implements HaikeOperateService {

    @Autowired
    private HaikeService haikeService;

    @Override
    public ContractResponse syncTerminalAndMerchantByPayMerchantId(String payMerchantId) {
        return haikeService.syncTerminalAndMerchant(payMerchantId);
    }
}
