package com.wosai.upay.job.refactor.model.bo;

import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import lombok.Data;

import java.util.concurrent.ExecutorService;

/**
 * 内部调度任务执行配置
 *
 * <AUTHOR>
 * @date 2024/6/17 13:44
 */
@Data
public class ScheduleTaskExecutePropertyBO {

    /**
     * 批量获取任务的方式类型 必填
     */
    private BatchGetScheduleTaskPatternTypeEnum batchGetPatternType;

    /**
     * 是否支持并发执行 默认false
     * true - 支持并发执行，false - 不支持并发执行
     */
    private boolean supportParallel = false;

    /**
     * 是否支持异步执行 默认false
     * true - 支持异步执行，false - 不支持异步执行
     */
    private boolean supportAsyncExecute = false;


    /**
     * 子任务执行异常是否重试 默认false
     * true-重试，false-不重试
     */
    private boolean retryIfException = false;

    /**
     * 线程池
     * 如果任务比较耗时，需要自定义线程池进行隔离
     */
    private ExecutorService threadPool = McJobThreadPoolFactory.getInstance();

}
