package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProviderTerminalTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insertSelective(ProviderTerminalTask record);

    ProviderTerminalTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ProviderTerminalTask record);

    /**
     * 根据类型查询未处理 处理中的任务
     * @param priority
     * @param type
     * @param limit
     * @return
     */
    List<Long> selectByPriorityAndType(@Param("priority")String priority, @Param("type") Integer type, @Param("limit") int limit);

    /**
     * 根据类型查询未处理 处理中的任务 查询出商户号
     * @param priority
     * @param type
     * @param limit
     * @return
     */
    List<ProviderTerminalTask> selectMerchantSnByPriorityAndType(@Param("priority")String priority, @Param("currentTime") String currentTime,@Param("type") Integer type, @Param("limit") int limit);


    /**
     * 根据类型查询 对应任务
     * @return
     */
    List<ProviderTerminalTask> selectByCondition(@Param("providerTerminalTask") ProviderTerminalTask providerTerminalTask);
}