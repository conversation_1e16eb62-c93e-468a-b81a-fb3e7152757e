package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * industry_code_v2表实体对象
 *
 * <AUTHOR>
 */
@Data
@TableName("industry_code_v2")
@Accessors(chain = true)
public class IndustryCodeV2DO {
    /**
     * 主键id
     */
    @TableId
    private String id;

    @TableField(value = "industry_id")
    private String industryId;
    /**
     * 银联对应行业编号(微信对私)
     */
    @TableField(value = "union_code_weixin_private")
    private String unionCodeWeixinPrivate;
    /**
     * 银联对应行业编号(微信对公)
     */
    @TableField(value = "union_code_weixin_public")
    private String unionCodeWeixinPublic;
    /**
     * 网联对应行业编号(微信对私)
     */
    @TableField(value = "nucc_code_weixin_private")
    private String nuccCodeWeixinPrivate;
    /**
     * 网联对应行业编号(微信对公)
     */
    @TableField(value = "nucc_code_weixin_public")
    private String nuccCodeWeixinPublic;
    /**
     * 银联对应行业编号(支付宝对私)
     */
    @TableField(value = "union_code_alipay_private")
    private String unionCodeAlipayPrivate;
    /**
     * 银联对应行业编号(支付宝对公)
     */
    @TableField(value = "union_code_alipay_public")
    private String unionCodeAlipayPublic;
    /**
     * 网联对应行业编号(支付宝对私)
     */
    @TableField(value = "nucc_code_alipay_private")
    private String nuccCodeAlipayPrivate;
    /**
     * 网联对应行业编号(支付宝对公)
     */
    @TableField(value = "nucc_code_alipay_public")
    private String nuccCodeAlipayPublic;
    /**
     * 银联对应行业编号(翼支付对公)
     */
    @TableField(value = "nucc_code_bestpay_public")
    private String nuccCodeBestpayPublic;
    /**
     * 银联对应行业编号(翼支付对私)
     */
    @TableField(value = "nucc_code_bestpay_private")
    private String nuccCodeBestpayPrivate;
    /**
     * 翼支付对应行业编号
     */
    @TableField(value = "nucc_code_bestpay")
    private String nuccCodeBestpay;
    /**
     * 拉卡拉对应行业编号
     */
    @TableField(value = "lakala_code")
    private String lakalaCode;
    /**
     * 通联对应行业编号
     */
    @TableField(value = "tl_code")
    private String tlCode;

    @TableField(value = "version")
    private String version;
    /**
     * 万码支付宝
     */
    @TableField(value = "wm_aly")
    private String wmAly;
    /**
     * 万码微信对私
     */
    @TableField(value = "wm_weixin_private")
    private String wmWeixinPrivate;
    /**
     * 万码微信对公
     */
    @TableField(value = "wm_weixin_public")
    private String wmWeixinPublic;
    /**
     * 银联开放平台
     */
    @TableField(value = "union_open_code")
    private String unionOpenCode;
    /**
     * 直连微信
     */
    @TableField(value = "direct_connect_weixin_code")
    private String directConnectWeixinCode;
    /**
     * 万码支付宝MCC
     */
    @TableField(value = "wm_aly_mcc")
    private String wmAlyMcc;
    /**
     * 数字人民币对应行业编号
     */
    @TableField(value = "e_cny_code")
    private String eCnyCode;
}

