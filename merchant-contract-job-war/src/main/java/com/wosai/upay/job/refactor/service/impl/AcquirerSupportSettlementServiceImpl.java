package com.wosai.upay.job.refactor.service.impl;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.utils.object.BeanCopyUtils;
import com.wosai.upay.job.model.dto.request.AcquirerSupportSettlementRepDTO;
import com.wosai.upay.job.model.dto.response.AcquirerSupportSettlementRspDTO;
import com.wosai.upay.job.refactor.dao.AcquirerSupportSettlementDAO;
import com.wosai.upay.job.refactor.model.entity.AcquirerSupportSettlementDO;
import com.wosai.upay.job.service.AcquirerSupportSettlementService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 收单机构支持的账户结算类型服务
 *
 * <AUTHOR>
 * @date 2024/4/3 10:01
 */
@AutoJsonRpcServiceImpl
@Service
public class AcquirerSupportSettlementServiceImpl implements AcquirerSupportSettlementService {

    @Resource
    private AcquirerSupportSettlementDAO acquirerSupportSettlementDAO;

    /**
     * 根据营业执照类型获取收单机构支持的账户结算类型
     *
     * @param licenseType 营业执照类型
     * @return 收单机构支持的账户结算类型
     */
    @Override
    public List<AcquirerSupportSettlementRspDTO> listByLicenseType(Integer licenseType) {
        return BeanCopyUtils.copyList(acquirerSupportSettlementDAO.listByLicenseType(licenseType), AcquirerSupportSettlementRspDTO.class);
    }

    /**
     * 更新收单机构支持结算类型
     *
     * @param acquirerSupportSettlementRepDTOs 收单机构支持结算类型请求dto
     * @return effect rows
     */
    @Override
    public Integer updateAcquirerSupportSettlement(List<AcquirerSupportSettlementRepDTO> acquirerSupportSettlementRepDTOs) {
        if (CollectionUtils.isEmpty(acquirerSupportSettlementRepDTOs)) {
            return 0;
        }
        return acquirerSupportSettlementDAO.batchUpdateByIdSelective(BeanCopyUtils.copyList(acquirerSupportSettlementRepDTOs, AcquirerSupportSettlementDO.class));
    }
}
