package com.wosai.upay.job.refactor.task.license.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 营业执照认证V3主任务上下文
 *
 * <AUTHOR>
 * @date 2025/07/01 15:45
 */
@Data
@AllArgsConstructor
public class BusinessLicenseCertificationV3MainTaskContext {

    public BusinessLicenseCertificationV3MainTaskContext(BusinessLicenseAuditApplyDTO auditApplyDTO) {
        this.auditApplyDTO = auditApplyDTO;
    }

    public BusinessLicenseCertificationV3MainTaskContext() {
    }

    private BusinessLicenseAuditApplyDTO auditApplyDTO;
    private Boolean microUpgrade;
    private Boolean showTaskOnSpa;
    private String oldInUseAcquirer;
    private String oldInUseAcquirerMerchantId;
    private String newAcquirer;
    private String newAcquirerMerchantId;
    /**
     * 是否修改结算账户名称
     */
    private Boolean needUpdateBusinessName;
    /**
     * 是否需要更新银行卡信息
     */
    private Boolean needUpdateBankAccount;
    /**
     * 是否需要插入代付任务
     */
    private Boolean needInsertPayForTask;
    /**
     * 银行卡主表的卡号
     */
    private String originalDefaultAccountNumber;

    /**
     * 默认卡的校验状态，待回滚使用
     */
    private Integer originalDefaultAccountVerifyStatus;

    private Integer originalDefaultPreAccountVerifyStatus;

    /**
     * 1-更新默认卡
     * 2-非默认卡切默认卡
     * 3-新增一张默认卡
     */
    private Integer changeBankAccountCardType;

    private String originalDefaultAccountHolder;

    private Integer originalDefaultAccountType;

    private String originalDefaultAccountIdentity;

    private Boolean alreadySyncWaitMerchantVerifyAmount;
    private List<String> reContractAcquirers;
    private List<String> successReContractAcquirers;
    private List<String> successReContractAndUpdateParamsAcquirers;
    private Map<String, Object> bankAccount;
    private Map<String, Object> merchant;
    private Map<String, Object> bankInfo;
    private Map<String, Object> merchantBusinessLicense;

    /**
     * 当前正在使用收单机构进件ID(只有V3版本才会出现这个值)
     */
    private Long mainContractTaskId;


    /**
     * 当前正在使用收单机构进件ID(只有V3版本才会出现这个值)
     */
    private List<String> withdrawList;





    /**
     * 微信认证状态
     */
    private Boolean wxAuthStatus;

    /**
     * 支付宝认证状态
     */
    private Boolean aliAuthStatus;


    public List<String> getReContractAcquirers() {
        return Objects.isNull(reContractAcquirers) ? new ArrayList<>() : new ArrayList<>(reContractAcquirers);
    }

    public List<String> getSuccessReContractAcquirers() {
        return Objects.isNull(successReContractAcquirers) ? new ArrayList<>() : new ArrayList<>(successReContractAcquirers);
    }

    public List<String> getSuccessReContractAndUpdateParamsAcquirers() {
        return Objects.isNull(successReContractAndUpdateParamsAcquirers) ? new ArrayList<>() : new ArrayList<>(successReContractAndUpdateParamsAcquirers);
    }

    public boolean getShowTaskOnSpa() {
        return showTaskOnSpa;
    }

    public void setShowTaskOnSpa(boolean showTaskOnSpa) {
        this.showTaskOnSpa = showTaskOnSpa;
    }
}
