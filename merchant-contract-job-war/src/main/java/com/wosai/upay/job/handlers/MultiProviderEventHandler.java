package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.UpdateMerchantReq;
import com.wosai.mc.model.resp.StotreExtInfoAndPictures;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreExtService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.model.gaoDe.GetPoiDetailByPoiReq;
import com.wosai.sales.model.gaoDe.PoiDetail;
import com.wosai.sales.service.goDe.GaoDeService;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/11/25
 */
@Component
@Slf4j
@Order(99)
public class MultiProviderEventHandler extends AbstractMultiProviderEventHandler<Void>{

    @Autowired
    private BankCardServiceImpl bankCardService;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private ParamContextBiz paramContextBiz;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private DataBusBiz dataBusBiz;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MultiProviderContractEventMapper multiProviderEventMapper;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private GaoDeService gaoDeService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private StoreExtService mcStoreExtService;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private CommonEventHandler commonEventHandler;


    @Override
    protected void handleError(MultiProviderContractEvent event, Exception e) throws Exception {
        if (e instanceof ContextParamException) {
            log.error("multiProviderContractEvent {} merchantSn {} get contextParamException", event.getId(), event.getMerchant_sn(), e);
            event.setStatus(MultiProviderContractEvent.STATUS_BIZ_FAIL);
            event.setResult(JSON.toJSONString(MapUtil.hashMap("className:", e.getClass().getName(), "cause", e.getMessage())));
            multiProviderEventMapper.updateByPrimaryKeySelective(event);
            String msg = String.format("merchant %s ContextParamException message: %s", event.getMerchant_sn(), e.getMessage());
            chatBotUtil.sendMessageToContractWarnChatBot(msg);
        } else {
            log.error("multiProviderContractEvent {} merchantSn {} get unkownException", event.getId(), event.getMerchant_sn(), e);
            chatBotUtil.sendMessageToContractWarnChatBot("商户" + event.getMerchant_sn() + "事件" + event.getId() + "处理异常" + e.getMessage());
            // 如果在 createNetInTask最后一步 dataBusBiz出现了异常，contract_task会回滚，但是 event里面的主任务和次任务 ID还都在，如果更新进表会找不到对应的任务而报错
            MultiProviderContractEvent update = new MultiProviderContractEvent();
            if (event.getVersion() > 4) {
                update.setStatus(6).setResult(e.getMessage());
            }
            update.setVersion(event.getVersion() + 1);
            multiProviderEventMapper.updateByPrimaryKeySelective(update);
        }
    }

    @Override
    public Void doHandle(MultiProviderContractEvent event) throws Exception {
        // 1. 如果有进行中的multi_event则不执行
        List<MultiProviderContractEvent> notFinishedEvents = multiProviderEventMapper.selectNotFinishedEventsByMerchantSn(event.getMerchant_sn());
        if (WosaiCollectionUtils.isNotEmpty(notFinishedEvents)) {
            return null;
        }

        // 2. 获取上下文,不包含费率信息，下面根据rule_group_id去填充  因为lklV3格式和别的不一样哦
        Map<String, Object> paramContext = paramContextBiz.getNetInParamContextByMerchantSn(event.getMerchant_sn());
        checkMerchantDistrict(paramContext);

        // 3. 判断是命中了黑名单，直接创建失败的任务
        Map eventMsg = JSON.parseObject(event.getEvent_msg());
        String memo = BeanUtil.getPropString(eventMsg, "source.fail_memo");
        int eventType = BeanUtil.getPropInt(eventMsg, "eventType");
        if (WosaiStringUtils.isNotEmpty(memo)) {
            createFailTask(paramContext, event, memo);
            return null;
        }
        boolean pass = checkPhotos(paramContext);
        if (!pass) {
            pauseMultiProviderEvent(event);
            dataBusBiz.sendProcessEvent(ContractStatus.STATUS_PROCESS, event.getMerchant_sn(), null);
            return null;
        }
        // 4. 处理event_type = 9这种情况
        if (eventType == ContractEvent.OPT_TYPE_NET_CRM_UPDATE) {
            createCrmUpdateTask(event, paramContext);
            return null;
        }
        // 5. 创建两个进件task,并且更新MultiProviderContractEvent
        createNetInTask(event, paramContext, ContractEvent.OPT_TYPE_NET_IN);
        return null;
    }


    private boolean checkPhotos(Map<String, Object> paramContext) {
        String merchantId = BeanUtil.getPropString(paramContext, "merchant.id");
        StotreExtInfoAndPictures latestStorePictures = mcStoreExtService.findLastStoreExtAndPicturesByMerchantId(merchantId);
        if (Objects.isNull(latestStorePictures)) {
            return false;
        }
        String brandPhoto = Objects.nonNull(latestStorePictures.getBrandPhoto()) ? latestStorePictures.getBrandPhoto().getUrl() : "";
        String indoorMaterialPhoto = Objects.nonNull(latestStorePictures.getIndoorMaterialPhoto()) ? latestStorePictures.getIndoorMaterialPhoto().getUrl() : "";
        String outdoorMaterialPhoto = Objects.nonNull(latestStorePictures.getOutdoorMaterialPhoto()) ? latestStorePictures.getOutdoorMaterialPhoto().getUrl() : "";
        if (WosaiStringUtils.isEmptyAny(brandPhoto, indoorMaterialPhoto, outdoorMaterialPhoto)) {
            return false;
        }
        return true;
    }

    /**
     * 将入网事件阻塞
     * 1.记录缺少的资料信息
     * 2.将更新时间设置在无法扫描到的时间
     */
    private void pauseMultiProviderEvent(MultiProviderContractEvent event) {
        event.setResult(JSON.toJSONString(CollectionUtil.hashMap(
                "channel", ProviderUtil.SHOUQIANBA_CHANNEL,
                "message", "异地开户，待商家提交店铺照片"
        )));
        event.setUpdate_at(ScheduleUtil.PAUSE_DATE);
        multiProviderEventMapper.updateByPrimaryKeySelective(event);
    }


    /**
     * 校验下区信息是否还可用（区县重新划分会导致历史数据原区县不存在）
     * @param contextParam
     */
    private void checkMerchantDistrict(Map<String, Object> contextParam){
        try {
            Map merchant = (Map) contextParam.get("merchant");
            String provice = BeanUtil.getPropString(merchant, Merchant.PROVINCE);
            String city = BeanUtil.getPropString(merchant, Merchant.CITY);
            String district = BeanUtil.getPropString(merchant, Merchant.DISTRICT);
            District addressInfo = districtsServiceV2.getCodeByName(provice + " " + city + " " + district);
            if (addressInfo.getStatus() == 0) {
                GetPoiDetailByPoiReq req = new GetPoiDetailByPoiReq();
                req.setBusiness("支付业务");
                req.setScene("进件");
                req.setLatitude(BeanUtil.getPropString(merchant, Merchant.LATITUDE));
                req.setLongitude(BeanUtil.getPropString(merchant, Merchant.LONGITUDE));
                PoiDetail resp = gaoDeService.getPoiDetailByPoi(req);
                if(resp != null && resp.getDistrict() != null){
                    UpdateMerchantReq updateMerchantReq = new UpdateMerchantReq();
                    String extra = BeanUtil.getPropString(merchant, Merchant.EXTRA);
                    extra.replace(provice, resp.getProvince());
                    extra.replace(city, resp.getCity());
                    extra.replace(district, resp.getDistrict());
                    updateMerchantReq.setId(BeanUtil.getPropString(merchant, DaoConstants.ID));
                    updateMerchantReq.setDistrict(resp.getDistrict());
                    updateMerchantReq.setCity(resp.getCity());
                    updateMerchantReq.setProvince(resp.getProvince());
                    updateMerchantReq.setExtra(JSONObject.parseObject(extra));
                    merchantService.updateMerchant(updateMerchantReq,null);
                    MerchantInfo merchantInfo = merchantService.getMerchantBySn(BeanUtil.getPropString(merchant, Merchant.SN), devCode);
                    contextParam.put(ParamContextBiz.KEY_MERCHANT, JSON.parseObject(JSON.toJSONString(merchantInfo), Map.class));
                }
            }
        }catch (Exception e){
            log.error("更新商户失效地区信息异常:", e);
        }
    }

    private void createCrmUpdateTask(MultiProviderContractEvent event, Map<String, Object> paramContext) {
        Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
        List<Map> tables = (List) eventMsg.get("crmUpdate");
        if (CollectionUtils.isEmpty(tables)) {
            return;
        }
        // 都要更新
        paramContext.put("crmUpdate", "2");

        eventMsg.put("msg", new ArrayList<>());
        event.setEvent_msg(JSON.toJSONString(eventMsg));
        createNetInTask(event, paramContext, ContractEvent.OPT_TYPE_NET_CRM_UPDATE);
    }

    @Transactional(rollbackFor = Exception.class)
    public void createNetInTask(MultiProviderContractEvent event, Map<String, Object> paramContext, int eventType) {
        ContractEvent primaryEvent = new ContractEvent().setMerchant_sn(event.getMerchant_sn()).setEvent_type(eventType).setEvent_msg(event.getEvent_msg()).setRule_group_id(event.getPrimary_group_id());
        // 1. 创建一个主通道的contract_task
        RuleGroup primaryRuleGroup = ruleContext.getRuleGroup(event.getPrimary_group_id());
        ContractTask primaryContractTask = createMerchantContractTask(paramContext, event.getMerchant_sn(), event.getPrimary_group_id(), primaryEvent, primaryRuleGroup);
        if (primaryContractTask != null) {
            event.setPrimary_task_id(primaryContractTask.getId());
        }
        // 创建一个次通道的contract_task
        ContractTask secondaryContractTask = null;
        if (WosaiStringUtils.isNotEmpty(event.getSecondary_group_id())) {
            ContractEvent secondaryEvent = new ContractEvent().setMerchant_sn(event.getMerchant_sn()).setEvent_type(eventType).setEvent_msg(event.getEvent_msg()).setRule_group_id(event.getSecondary_group_id());
            RuleGroup secondaryRuleGroup = ruleContext.getRuleGroup(event.getSecondary_group_id());
            secondaryContractTask = createMerchantContractTask(paramContext, event.getMerchant_sn(), event.getSecondary_group_id(), secondaryEvent, secondaryRuleGroup);
            if (secondaryContractTask != null) {
                event.setSecondary_task_id(secondaryContractTask.getId());
            }
        }
        if (primaryContractTask == null && secondaryContractTask == null) {
            //无任何子任务生成
            event.setResult("no subtask create nothing need to do").setStatus(MultiProviderContractEvent.STATUS_SUCCESS);
        } else {
            event.setStatus(MultiProviderContractEvent.STATUS_PROCESS);
        }
        multiProviderEventMapper.updateByPrimaryKeySelective(event);
        dataBusBiz.insert(ContractStatus.STATUS_PROCESS, event.getMerchant_sn(), null);
    }

    private ContractTask createMerchantContractTask(Map<String, Object> paramContext, String merchantSn, String ruleGroupId, ContractEvent event, RuleGroup ruleGroup) {
        commonEventHandler.addFuYouRuleGroupRuleItem(ruleGroupId, ruleGroup, paramContext);
        paramContextBiz.fillFeeRatesByRuleGroupId(paramContext, ruleGroupId);
        List<ContractSubTask> subTaskList = new ArrayList<>();
        Map subTaskMap = new HashMap();
        Integer affectSubTaskCount = 0;
        for (RuleItem ruleItem : ruleGroup.getRules()) {
            BasicProvider provider = providerFactory.getProviderByContractRule(ruleItem.getContractRule());
            if (provider != null) {
                ContractSubTask subTask = provider.produceTaskByRule(merchantSn, event, paramContext, ruleItem.getContractRule());
                if (subTask != null) {
                    subTask.setRule_group_id(ruleGroup.getGroup_id());
                    subTaskList.add(subTask);
                    subTaskMap.put(ruleItem.getRule(), subTask);
                    if (1 == subTask.getStatus_influ_p_task()) {
                        affectSubTaskCount++;
                    }
                }
            }
        }

        //正常入网任务
        if (subTaskList.isEmpty() || affectSubTaskCount == 0) {
            //无任何子任务生成
            return null;
        }
        String merchantName = BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affectSubTaskCount)
                .setAffect_status_success_task_count(0)
                .setRule_group_id(ruleGroup.getGroup_id());
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        Map depends = new HashMap();
        for (ContractSubTask subTask : subTaskList) {
            insertSubTasks(ruleGroup, subTask, subTaskMap, depends, pTaskId);
        }
        return contractTask;
    }

    private void insertSubTasks(RuleGroup ruleGroup, ContractSubTask subTask, Map<String, ContractSubTask> subTaskMap, Map<String, Long> rules, Long taskId) {
        String rule = subTask.getContract_rule();
        if (rules.get(rule) != null) {
            return;
        }
        RuleItem ruleItem = getByRule(rule, ruleGroup);
        ContractRule depend = ruleItem.getDepend_on();
        if (depend == null) {
            subTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setP_task_id(taskId);
            contractSubTaskMapper.insert(subTask);
            rules.putIfAbsent(rule, subTask.getId());
            return;
        }
        String dependRule = depend.getRule();
        Long id = rules.get(dependRule);
        if (id == null) {
            //如果依赖的规则并没有生成 subTaskMap.get(dependRule)==null
            if (subTaskMap.get(dependRule) == null) {
                subTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setP_task_id(taskId);
                contractSubTaskMapper.insert(subTask);
                rules.putIfAbsent(rule, subTask.getId());
                return;
            }
            insertSubTasks(ruleGroup, subTaskMap.get(dependRule), subTaskMap, rules, taskId);
        }
        id = rules.get(dependRule);
        subTask.setP_task_id(taskId).setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue()).setSchedule_dep_task_id(id);
        contractSubTaskMapper.insert(subTask);
        rules.putIfAbsent(rule, subTask.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    public void createFailTask(Map<String, Object> paramContext, MultiProviderContractEvent event, String memo) {
        String merchantName = BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME);
        String result = JSON.toJSONString(CollectionUtil.hashMap(
                "channel", ProviderUtil.SHOUQIANBA_CHANNEL,
                "message", memo
        ));
        // 创建主通道的失败的任务 根据通道类型去填充费率信息
        paramContextBiz.fillFeeRatesByRuleGroupId(paramContext, event.getPrimary_group_id());
        ContractTask primaryContractTask = new ContractTask()
                .setMerchant_sn(event.getMerchant_sn())
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(0)
                .setAffect_status_success_task_count(0)
                .setStatus(TaskStatus.FAIL.getVal())
                .setRule_group_id(event.getPrimary_group_id())
                .setResult(result);
        ContractTask secondaryContractTask = null;
        if (WosaiStringUtils.isNotEmpty(event.getSecondary_group_id())) {
            paramContextBiz.fillFeeRatesByRuleGroupId(paramContext, event.getSecondary_group_id());
            secondaryContractTask = new ContractTask()
                    .setMerchant_sn(event.getMerchant_sn())
                    .setMerchant_name(merchantName)
                    .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                    .setEvent_context(JSON.toJSONString(paramContext))
                    .setAffect_sub_task_count(0)
                    .setAffect_status_success_task_count(0)
                    .setStatus(TaskStatus.FAIL.getVal())
                    .setRule_group_id(event.getSecondary_group_id())
                    .setResult(result);
        }

        contractTaskBiz.insert(primaryContractTask);
        if (secondaryContractTask != null) {
            contractTaskBiz.insert(secondaryContractTask);
            event.setSecondary_task_id(secondaryContractTask.getId());
        }
        // 更新multi_event为失败
        event.setPrimary_task_id(primaryContractTask.getId());
        event.setResult(result);
        event.setStatus(MultiProviderContractEvent.STATUS_BIZ_FAIL);
        multiProviderEventMapper.updateByPrimaryKeySelective(event);
        // 发送进件失败的消息
        dataBusBiz.insert(ContractStatus.STATUS_BIZ_FAIL, event.getMerchant_sn(), null);
        bankCardService.updateCardAfterTaskStatus(paramContext, MerchantBankAccount.VERIFY_STATUS_FAIL, memo);
    }

    private RuleItem getByRule(String rule, RuleGroup ruleGroup) {
        for (RuleItem item : ruleGroup.getRules()) {
            if (rule.equals(item.getRule())) {
                return item;
            }
        }
        throw new IllegalArgumentException("根据rule" + rule + "无法获取RuleItem");
    }

    @Override
    public boolean supports(MultiProviderContractEvent multiProviderContractEvent) {
        return true;
    }
}
