package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 个人证件类型
 *
 * <AUTHOR>
 */
public enum PersonalCertificateTypeEnum implements ITextValueEnum<Integer> {
    ID_CARD(1, "身份证"),
    HONG_KONG_AND_MACAO_RESIDENTS_TRAVEL_PERMIT(2, "港澳居民来往内地通行证"),
    TAIWAN_RESIDENTS_TRAVEL_PERMIT(3, "台湾居民来往大陆通行证"),
    NON_CHINESE_PASSPORT(4, "非中华人民共和国护照"),
    CHINESE_PASSPORT(5, "中国护照"),
    HONG_KONG_AND_MACAO_RESIDENTS_RESIDENCE_PERMIT(6, "港澳居民居住证"),
    TAIWAN_RESIDENTS_RESIDENCE_PERMIT(7, "台湾居民居住证");

    private final int value;
    private final String text;

    PersonalCertificateTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
