package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AntMerchantExpandShopCreateRequest;
import com.alipay.api.request.AntMerchantExpandShopModifyRequest;
import com.alipay.api.request.AntMerchantExpandShopQueryRequest;
import com.alipay.api.response.AntMerchantExpandShopCreateResponse;
import com.alipay.api.response.AntMerchantExpandShopModifyResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.exception.ContractException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.model.newBlueSea.request.AntMerchantExpandShopCreateReq;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.wosai.upay.job.model.CommonModel.*;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/11/16 5:07 下午
 */
@AutoJsonRpcServiceImpl
@Service
@Slf4j
public class AlipaySchoolBatchServiceImpl implements AlipaySchoolBatchService {


    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private BlueSeaService blueSeaService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private NewBlueSeaService newBlueSeaService;

    @Override
    public Map<String, String> alipaySchool(String merchantSn) {
        Map<String, String> map = new HashMap<>();

        try {
            //查询商户营业执照信息
            Map merchantBySn = merchantService.getMerchantBySn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchantBySn, "id");
            Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
            int type = BeanUtil.getPropInt(license, "type");
            if (type == 0) {
                throw new ContractBizException("小微商户无营业执照");
            }

            //商户名
            String merchantName = BeanUtil.getPropString(license, "name");
            if (StringUtils.isBlank(merchantName)) {
                throw new ContractBizException("商户营业执照名称为空");
            }

            //查询商户支付宝商户号
            Tuple2<String, Boolean> aliMchInfo = blueSeaBiz.getInUseMchId(merchantSn);
            String aliMchId = aliMchInfo.get_1();
            if (WosaiStringUtils.isEmpty(aliMchId)) {
                throw new ContractBizException("商户没有支付宝子商户号");
            }


            CustomizedInfo customizedInfo = blueSeaBiz.buildCustomizedInfo(merchantSn);
            //团餐
            customizedInfo.setMcc("5880");

            customizedInfo.setMerchant_name(merchantName);

            customizedInfo.setForceUpdate(true);
            Map<String, String> forceValue = new HashMap<>();
            forceValue.put("mcc", customizedInfo.getMcc());
            forceValue.put("name", merchantName);
            customizedInfo.setForceUpdateValue(forceValue);

            //升级M3以及更新MCC Code
            try {
                boolean result = blueSeaService.updateMerchantToM3(merchantSn, customizedInfo);
                if (!result) {
                    throw new ContractBizException("商户" + merchantSn + "升级M3异常");
                }
            } catch (Exception e) {
                log.error("商户{}升级M3异常", merchantSn, e);
                throw new ContractBizException("商户" + merchantSn + "升级M3异常");
            }

            //创建门店
            BlueSeaTask task = new BlueSeaTask();
            String storeSn = BeanUtil.getPropString(blueSeaBiz.getStoreList(merchantId).get(0), Store.SN);
            task.setStore_sn(storeSn);
            task.setAli_mch_id(aliMchId);
            task.setMerchant_id(merchantId);

            map.put("storeSn", task.getStore_sn());
            map.put("aliMchId", task.getAli_mch_id());
            map.put("merchantId", task.getMerchant_id());

            AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> response = blueSeaBiz.existAntShop(task.getMerchant_id(), task.getStore_sn(), task.getAli_mch_id());
            if (response != null && response.getResp() != null && StringUtils.isNotBlank(response.getResp().getShopCategory())) {
                //门店存在
                String shopCategory = response.getResp().getShopCategory();
                //修改门店类目
                if (!shopCategory.equals("1744")) {
                    Map<String, String> update = new HashMap<>();
                    update.put("shop_category", "B0007");
                    update.put("shop_id", response.getResp().getShopId());
                    AntMerchantExpandShopModifyRequest request = new AntMerchantExpandShopModifyRequest();
                    request.setBizContent(JSON.toJSONString(update));
                    AliCommResponse<AntMerchantExpandShopModifyRequest, AntMerchantExpandShopModifyResponse> modifyResp = newBlueSeaService.antMerchantExpandShopModify(request);
                    if (modifyResp.isSuccess()) {
                        map.put("message", "门店存在并更新类目");
                    }
                } else {
                    map.put("message", "门店存在");
                }
            } else {
                //创建门店申请
                AntMerchantExpandShopCreateReq req = AntMerchantExpandShopCreateReq.builder().build();
                //封装相同信息
                req = blueSeaBiz.buildShopCreateReq(req, merchantSn, task.getStore_sn());
                //支付宝三级类目
                req.setShopCategory("B0007");
                //支付宝子商户号
                req.setIpRoleId(task.getAli_mch_id());
                AliCommResponse<AntMerchantExpandShopCreateRequest, AntMerchantExpandShopCreateResponse> aliCommResponse = newBlueSeaService.antMerchantExpandShopCreate(req);
                if (aliCommResponse.isSuccess()) {
                    //申请门店成功
                    map.put("storeSn", task.getStore_sn());
                    map.put("aliMchId", task.getAli_mch_id());
                    map.put("merchantId", task.getMerchant_id());
                    map.put("message", "门店创建成功");
                }
            }
            map.put("code", "Y");
        } catch (Exception e) {
            log.error("支付宝高校前置处理异常{}", merchantSn, e);
            map.put("code", "N");
            map.put("message", e.getMessage());
        }
        return map;
    }


    @Override
    public Map<String, String> updateConfig(String merchantId, String storeSn, String ipRoleId) {

        Map<String, String> map = new HashMap<>();
        try {
            //查询商户门店信息
            AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> response = blueSeaBiz.existAntShop(merchantId, storeSn, ipRoleId);
            if (Objects.nonNull(response) && Objects.nonNull(response.getResp())) {
                //店铺Id
                String shopId = response.getResp().getShopId();
                //更新交易参数
                //pay_way为2
                Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.ALIPAY.getValue());
                //得到原来 的交易参数
                Map merchantParams = (Map) BeanUtil.getProperty(merchantConfig, MerchantConfig.PARAMS);
                if (!CollectionUtils.isEmpty(merchantParams)) {
                    //把shop_id 放到 XXX_trade_params里面
                    Map lkl = (Map) BeanUtil.getProperty(merchantParams, UP_DIRECT_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(lkl)) {
                        lkl.put("alipay_store_id", shopId);
                    }
                    Map lklOrg = (Map) BeanUtil.getProperty(merchantParams, LKL_ORG_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(lklOrg)) {
                        lklOrg.put("alipay_store_id", shopId);
                    }
                    Map tonglian = (Map) BeanUtil.getProperty(merchantParams, TONGLIAN_TRADE_PARAMS);
                    if (!CollectionUtils.isEmpty(tonglian)) {
                        tonglian.put("alipay_store_id", shopId);
                    }
                    //更新交易参数
                    tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(MerchantConfig.PARAMS, merchantParams,
                            DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID)));

                    map.put("message", "shopId成功写入");
                    map.put("code", "Y");
                }
            } else {
                //无门店信息
                map.put("message", "门店不存在");
                map.put("code", "N");
            }

        } catch (Exception e) {
            log.error("更新交易参数异常{},{},{}", merchantId, storeSn, ipRoleId, e);
            map.put("code", "N");
            map.put("message", e.getMessage());
        }
        return map;
    }


}
