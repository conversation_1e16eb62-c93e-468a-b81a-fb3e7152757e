package com.wosai.upay.job.biz.acquirer;


import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.util.CommonUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component("zjtlcb-AcquirerChangeBiz")
public class ChangeToZjtlcbBizBank extends AbstractBankDirectAcquirerChangeBiz{

    @Value("${zjtlcb_trade_combo_id}")
    private long zjtlcbTradeComboId;

    @Override
    protected String getDevCode(String acquirer) {
        return null;
    }


    @Override
    protected long getDefaultComboId(String acquirer) {
        return zjtlcbTradeComboId;
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_ZJTLCB_RULE_GROUP;
    }

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_ZJTLCB.getValue();
    }


    @Override
    protected void applyCombo(McAcquirerChange change) {
        Map ChangeExtra = CommonUtil.string2Map(change.getExtra());
        String feeRate = BeanUtil.getPropString(ChangeExtra, "zjtlcb_fee_rate");
        if (WosaiStringUtils.isNotEmpty(feeRate)) {
            Map<String, String> applyFeeRateMap = CollectionUtil.hashMap(
                    String.valueOf(PaywayEnum.ALIPAY.getValue()), feeRate,
                    String.valueOf(PaywayEnum.WEIXIN.getValue()), feeRate,
                    String.valueOf(PaywayEnum.UNIONPAY.getValue()), feeRate
            );
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(change.getMerchant_sn())
                    .setTradeComboId(zjtlcbTradeComboId)
                    .setAuditSn("银行业务开通成功设置费率")
                    .setApplyPartialPayway(Boolean.TRUE)
                    .setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
            return;
        }

        Map combo = new HashMap();
        // 检查历史切换成功记录
        McAcquirerChange latestSuccessApply = changeDao.getLatestSuccessApply(change.getMerchant_sn(), change.getTarget_acquirer());
        if (Objects.nonNull(latestSuccessApply)) {
            Map extra = CommonUtil.string2Map(latestSuccessApply.getExtra());
            combo = WosaiMapUtils.getMap(extra, "comboSnapshot");
        }

        long tradeComboId = BeanUtil.getPropLong(combo, "trade_combo_id");
        if (Objects.equals(tradeComboId, 0L)) {
            tradeComboId = getDefaultComboId(change.getTarget_acquirer());
        }
        List<Map<String, Object>> config = JSONObject.parseObject(BeanUtil.getPropString(combo, "merchant_config"), List.class);

        Map<String, String> applyFeeRateMap = config.stream().collect(Collectors.toMap(x -> BeanUtil.getPropString(x, "payway"), x -> BeanUtil.getPropString(x, "rate")));
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(change.getMerchant_sn())
                .setTradeComboId(tradeComboId)
                .setAuditSn("银行业务开通成功设置费率")
                .setApplyPartialPayway(Boolean.TRUE)
                .setApplyFeeRateMap(applyFeeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);

    }




}
