package com.wosai.upay.job.refactor.biz.acquirer.pab;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

/**
 * 平安银行收单处理门面
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class PabAcquirerFacade extends AbstractAcquirerHandler {

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.PAB;
    }

}
