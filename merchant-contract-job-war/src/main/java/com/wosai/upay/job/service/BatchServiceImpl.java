package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.model.request.HopeEduMerchantConfigRequest;
import com.wosai.upay.core.service.LogService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.UmsImportBiz;
import com.wosai.upay.job.biz.bankDirect.CommonImportBiz;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.enume.TaskApplyLogTypeEnum;
import com.wosai.upay.job.mapper.McBatchTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.model.dto.BatchProcessingDTO;
import com.wosai.upay.job.model.dto.ImportUmsParamsDTO;
import com.wosai.upay.job.model.dto.QueryAuthStatusDTO;
import com.wosai.upay.job.model.dto.QueryIsPostalCardDto;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.ConfigStatusEnum;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.util.BatchChangeAcquireUtil;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_FAIL;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.AUDIT_EXECUTE_SUCCESS;

/**
 * @Author: jerry
 * @date: 2019/7/22 15:03
 * @Description:
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class BatchServiceImpl implements BatchService {

    @Autowired
    McBatchTaskMapper mcBatchTaskMapper;
    @Autowired
    LogService logService;
    @Autowired
    RuleContext ruleContext;

    @Autowired
    private HxbImportBiz hxbImportBiz;

    @Autowired
    private CommonImportBiz commonImportBiz;

    @Autowired
    private UmsImportBiz umsImportBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;
    @Autowired
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private MerchantService merchantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResp batchContract(BatchResq batchResq) {
        ContractRule contractRule = ruleContext.getContractRule(batchResq.getRule());
        //待定 微信实名 流程 实名启用这里 需要限制
//        if (ProviderUtil.WEIXIN_PAY_WAY.equals(contractRule.getPayway())) {
//            throw new CommonPubBizException("微信目前暂不支持批量报备");
//        }
        Map payload = CollectionUtil.hashMap("fileUrl", batchResq.getUrl(), "rule", batchResq.getRule());
        McBatchTask mcBatchTask = new McBatchTask();
        String jsonPayLoad = JSON.toJSONString(payload);
        mcBatchTask.setPayload(jsonPayLoad);
        mcBatchTask.setStatus(0);
        mcBatchTask.setEffect_time(new Date());
        mcBatchTask.setType(1);
        mcBatchTask.setOperator_id(batchResq.getUserId());
        mcBatchTask.setOperator_name(batchResq.getUserName());

        //todo type system 在webp后台有写待定 可能需要修改web-p的代码
        Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogTypeEnum.BATCH_RE_CONTRACT.getValue(),
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                TaskApplyLog.PAYLOAD, jsonPayLoad,
                TaskApplyLog.USER_ID, batchResq.getUserName()
        ));

        mcBatchTask.setTask_apply_log_id(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        mcBatchTaskMapper.insertSelective(mcBatchTask);
        return new BatchResp(200);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResp batchChangeByFile(BatchResq batchResq) {
        Map payload = CollectionUtil.hashMap("fileUrl", batchResq.getUrl(), "rule", batchResq.getRule(), "feeRate", batchResq.getFeeRate());
        McBatchTask mcBatchTask = new McBatchTask();
        String jsonPayLoad = JSON.toJSONString(payload);
        mcBatchTask.setPayload(jsonPayLoad);
        mcBatchTask.setStatus(0);
        mcBatchTask.setEffect_time(new Date(batchResq.getEffectTime()));
        mcBatchTask.setType(2);
        mcBatchTask.setOperator_id(batchResq.getUserId());
        mcBatchTask.setOperator_name(batchResq.getUserName());

        Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogTypeEnum.BATCH_IMPORT_PARAMS.getValue(),
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                TaskApplyLog.PAYLOAD, jsonPayLoad,
                TaskApplyLog.USER_ID, batchResq.getUserName()
        ));

        mcBatchTask.setTask_apply_log_id(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        mcBatchTaskMapper.insertSelective(mcBatchTask);
        return new BatchResp(200);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchResp batchChangeByRule(BatchResq batchResq) {
        Map payload = CollectionUtil.hashMap("oldRule", batchResq.getOldRule(), "rule", batchResq.getRule(), "remark", batchResq.getRemark(), "feeRate", batchResq.getFeeRate());
        McBatchTask mcBatchTask = new McBatchTask();
        String jsonPayLoad = JSON.toJSONString(payload);
        mcBatchTask.setPayload(jsonPayLoad);
        mcBatchTask.setStatus(0);
        mcBatchTask.setEffect_time(new Date(batchResq.getEffectTime()));
        mcBatchTask.setType(3);
        mcBatchTask.setOperator_id(batchResq.getUserId());
        mcBatchTask.setOperator_name(batchResq.getUserName());

        Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogTypeEnum.BATCH_IMPORT_PARAMS_BY_RULE.getValue(),
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                TaskApplyLog.PAYLOAD, jsonPayLoad,
                TaskApplyLog.USER_ID, batchResq.getUserName()
        ));

        mcBatchTask.setTask_apply_log_id(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        mcBatchTaskMapper.insertSelective(mcBatchTask);
        return new BatchResp(200);
    }

    @Override
    public BatchResp batchImportAliOnlineMerchants(AliOnlineMerchantsBatchImportReq aliOnlineMerchantsBatchImportReq) {
        Map payload = CollectionUtil.hashMap("fileUrl", aliOnlineMerchantsBatchImportReq.getUrl());
        McBatchTask mcBatchTask = new McBatchTask();
        String jsonPayLoad = JSON.toJSONString(payload);
        mcBatchTask.setPayload(jsonPayLoad);
        mcBatchTask.setStatus(0);
        mcBatchTask.setEffect_time(new Date());
        mcBatchTask.setType(11);
        mcBatchTask.setOperator_id(aliOnlineMerchantsBatchImportReq.getUserId());
        mcBatchTask.setOperator_name(aliOnlineMerchantsBatchImportReq.getUserName());

        Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                TaskApplyLog.TYPE, TaskApplyLogTypeEnum.BATCH_IMPORT_ALI_CROSS_CITY_MERCHANTS.getValue(),
                TaskApplyLog.APPLY_SYSTEM, 2,
                TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                TaskApplyLog.PAYLOAD, jsonPayLoad,
                TaskApplyLog.USER_ID, aliOnlineMerchantsBatchImportReq.getUserName()
        ));

        mcBatchTask.setTask_apply_log_id(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
        mcBatchTaskMapper.insertSelective(mcBatchTask);
        return new BatchResp(200);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changAcquire(ChangeAcquirerApproveDTO dto) {
        //TODO 是否需要限制批量审批
        //目标收单机构
        if (Objects.equals(dto.getApplyType(), ApproveConstant.APPLY_TYPE_SINGLE)) {
            Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto);
            //向McBatchTask插入数据
            doInsertBatchTask(dto, map, 4);
        }
        if (Objects.equals(dto.getApplyType(), ApproveConstant.APPLY_TYPE_BATCH)) {
            final String attachmentUrl = dto.getAttachmentUrls().get(0);
            final List<AcquirerApproveExcel> acquirerApproveExcels = BatchChangeAcquireUtil.getExcelInfoList(attachmentUrl, new AcquirerApproveExcel());
            final int size = acquirerApproveExcels.size();
            //判断今天总的批量审批商户数量是否超过限制
            checkNum(size);
            if (size == 0) {
                throw new CommonPubBizException("Excel中没有内容");
            }
            Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto, ApproveConstant.BATCH_SIZE, size);
            //向McBatchTask插入数据
            doInsertBatchTask(dto, map, 5);
        }
    }


    @Override
    public CallBackBean cancelChangAcquire(ChangeAcquirerApproveDTO dto) {
        final CallBackBean callBackBean = dto.getCallBackBean();
        final McAcquirerChangeDO mcAcquirerChangeDO;
        String message;
        Integer resultType;
        try {
            final Optional<McAcquirerChangeDO> processChange = mcAcquirerChangeDAO.getProcessChange(dto.getMerchantSn());
            final boolean present = processChange.isPresent();
            if (!present) {
                throw new CommonPubBizException("无通道切换任务");
            }
            mcAcquirerChangeDO = processChange.get();
            final String extra = mcAcquirerChangeDO.getExtra();
            final Map map = CommonUtil.string2Map(extra);
            final Boolean cancelAble = MapUtil.getBoolean(map, "cancel_able");
            //不可取消
            if (!Objects.equals(cancelAble, Boolean.TRUE)) {
                throw new CommonPubBizException("无通道切换任务");
            }
            mcAcquirerChangeDAO.deleteByPrimaryKey(mcAcquirerChangeDO.getId());
            message = "取消成功";
            resultType = AUDIT_EXECUTE_SUCCESS;
        } catch (CommonPubBizException e) {
            message = ExceptionUtil.getThrowableMsg(e);
            resultType = AUDIT_EXECUTE_FAIL;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;
    }


    @Override
    public CallBackBean onlyContract(ChangeAcquirerApproveDTO dto) {
        final CallBackBean callBackBean = dto.getCallBackBean();
        String message = "已发起通道入网,请耐心等待";
        Integer resultType = AUDIT_EXECUTE_SUCCESS;
        try {
            //目标收单机构
            if (Objects.equals(dto.getApplyType(), ApproveConstant.APPLY_TYPE_SINGLE)) {
                Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto);
                //向McBatchTask插入数据
                doInsertBatchTask(dto, map, 12);
            }
            if (Objects.equals(dto.getApplyType(), ApproveConstant.APPLY_TYPE_BATCH)) {
                final String attachmentUrl = dto.getAttachmentUrls().get(0);
                final List<AcquirerApproveExcel> acquirerApproveExcels = BatchChangeAcquireUtil.getExcelInfoList(attachmentUrl, new AcquirerApproveExcel());
                final int size = acquirerApproveExcels.size();
                //判断今天总的批量审批商户数量是否超过限制
                checkOnlyContractNum(size);
                if (size == 0) {
                    throw new CommonPubBizException("Excel中没有内容");
                }
                Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto, ApproveConstant.BATCH_SIZE, size);
                //向McBatchTask插入数据
                doInsertBatchTask(dto, map, 13);
            }
        } catch (Exception exception) {
            log.error("批量通道入网异常:{}", exception);
            message = ExceptionUtil.getThrowableMsg(exception);
            resultType = AUDIT_EXECUTE_FAIL;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;

    }

    /**
     * @param size
     * <AUTHOR>
     * @Description: 判断今天总的批量审批商户数量是否超过限制
     * @time 13:55
     */
    public void checkNum(int size) {
        final McBatchTaskExample example = new McBatchTaskExample();
        final Date startDate = Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
        final Date endDate = Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        example.or().andCreate_atBetween(startDate, endDate).andTypeEqualTo(5);
        final List<McBatchTask> mcBatchTasks = mcBatchTaskMapper.selectByExampleWithBLOBs(example);
        //今天已经批量报的商户数量
        final int totalNum = mcBatchTasks.parallelStream().mapToInt(x -> {
            Map extra = CommonUtil.string2Map(x.getPayload());
            return BeanUtil.getPropInt(extra, ApproveConstant.BATCH_SIZE);
        }).reduce(0, (x, y) -> x + y);
        final Integer batchThreshold = applicationApolloConfig.getBatchThreshold();
        if (totalNum + size > batchThreshold) {
            throw new CommonPubBizException(String.format("每日批量审批最多%s个商户,今日已批量审批%s个商户,还能批量提交最多%s个商户", batchThreshold, totalNum, batchThreshold - totalNum));
        }
    }


    /**
     * @param size
     * <AUTHOR>
     * @Description: 判断今天总的批量仅入网审批商户数量是否超过限制
     * @time 13:55
     */
    public void checkOnlyContractNum(int size) {
        final McBatchTaskExample example = new McBatchTaskExample();
        final Date startDate = Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant());
        final Date endDate = Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant());
        example.or().andCreate_atBetween(startDate, endDate).andTypeEqualTo(13);
        final List<McBatchTask> mcBatchTasks = mcBatchTaskMapper.selectByExampleWithBLOBs(example);
        //今天已经批量报的商户数量
        final int totalNum = mcBatchTasks.parallelStream().mapToInt(x -> {
            Map extra = CommonUtil.string2Map(x.getPayload());
            return BeanUtil.getPropInt(extra, ApproveConstant.BATCH_SIZE);
        }).reduce(0, (x, y) -> x + y);
        final Integer batchThreshold = applicationApolloConfig.getBatchOnlyContract();
        if (totalNum + size > batchThreshold) {
            throw new CommonPubBizException(String.format("每日批量审批最多%s个商户,今日已批量审批%s个商户,还能批量提交最多%s个商户", batchThreshold, totalNum, batchThreshold - totalNum));
        }
    }


    public void doInsertBatchTask(ChangeAcquirerApproveDTO dto, Map map, Integer type) {
        McBatchTask mcBatchTask = new McBatchTask();
        String jsonPayLoad = JSON.toJSONString(map);
        mcBatchTask.setPayload(jsonPayLoad);
        mcBatchTask.setStatus(0);
        mcBatchTask.setEffect_time(new Date());
        mcBatchTask.setType(type);
        mcBatchTask.setOperator_id(dto.getOperator());
        mcBatchTask.setOperator_name(dto.getOperatorName());
        mcBatchTask.setTask_apply_log_id(dto.getAuditSn());
        mcBatchTaskMapper.insertSelective(mcBatchTask);
    }

    @Override
    @Deprecated
    public CallBackBean queryAuthStatus(QueryAuthStatusDTO dto) {
        String taskApplyLogId = null;
        final CallBackBean callBackBean = dto.getCallBackBean();
        String message = "正在查询微信子商户号实名状态,请耐心等待";
        Integer resultType = AUDIT_EXECUTE_SUCCESS;
        try {
            Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto);
            McBatchTask mcBatchTask = new McBatchTask();
            String jsonPayLoad = JSON.toJSONString(map);
            mcBatchTask.setPayload(jsonPayLoad);
            Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                    TaskApplyLog.TYPE, TaskApplyLogTypeEnum.HXB_SUB_MCH_STATUS_QUERY.getValue(),
                    TaskApplyLog.APPLY_SYSTEM, 2,
                    TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                    TaskApplyLog.PAYLOAD, jsonPayLoad,
                    TaskApplyLog.USER_ID, dto.getOperatorName(),
                    TaskApplyLog.APPLY_RESULT, 0
            ));
            taskApplyLogId = MapUtil.getString(taskApplyLog, DaoConstants.ID);
            mcBatchTask.setStatus(0);
            mcBatchTask.setEffect_time(new Date());
            mcBatchTask.setType(6);
            mcBatchTask.setOperator_id(dto.getOperator());
            mcBatchTask.setOperator_name(dto.getOperatorName());
            mcBatchTask.setTask_apply_log_id(taskApplyLogId);
            mcBatchTaskMapper.insertSelective(mcBatchTask);
        } catch (Exception exception) {
            log.error("queryAuthStatus taskApplyLogId:{},exception:{}", taskApplyLogId, exception);
            final String msg = ExceptionUtil.getThrowableMsg(exception);
            logService.updateTaskApplyLog(CollectionUtil.hashMap(DaoConstants.ID, taskApplyLogId, TaskApplyLog.APPLY_STATUS, 2, TaskApplyLog.APPLY_RESULT, JSON.toJSONString(CollectionUtil.hashMap("result", msg))));
            message = StringUtils.isEmpty(msg) ? "审批发起异常" : msg;
            resultType = AUDIT_EXECUTE_FAIL;
        }
        if (Objects.isNull(callBackBean)) {
            return null;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;
    }

    @Override
    @Deprecated
    public CallBackBean queryIsPostCard(QueryIsPostalCardDto dto) {
        final CallBackBean callBackBean = dto.getCallBackBean();
        String message = "正在是否是邮储卡,请耐心等待";
        Integer resultType = AUDIT_EXECUTE_SUCCESS;
        try {
            Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto);
            McBatchTask mcBatchTask = new McBatchTask();
            String jsonPayLoad = JSON.toJSONString(map);
            mcBatchTask.setPayload(jsonPayLoad);
            mcBatchTask.setStatus(0);
            mcBatchTask.setEffect_time(new Date());
            mcBatchTask.setType(7);
            mcBatchTask.setOperator_id(dto.getOperator());
            mcBatchTask.setOperator_name(dto.getOperatorName());
            mcBatchTask.setTask_apply_log_id(dto.getAuditSn());
            mcBatchTaskMapper.insertSelective(mcBatchTask);
        } catch (Exception exception) {
            log.error("queryIsPostCard auditId:{},exception:{}", callBackBean.getAuditId(), exception);
            final String msg = ExceptionUtil.getThrowableMsg(exception);
            message = StringUtils.isEmpty(msg) ? "审批发起异常" : msg;
            resultType = AUDIT_EXECUTE_FAIL;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;
    }

    @Override
    public CallBackBean doBatchProcessing(BatchProcessingDTO dto) {
        final CallBackBean callBackBean = dto.getCallBackBean();
        String message = "正在进行批处理操作,请耐心等待";
        Integer resultType = AUDIT_EXECUTE_SUCCESS;
        try {
            Map map = CollectionUtil.hashMap(ApproveConstant.APPROVE_INFO, dto);
            McBatchTask mcBatchTask = new McBatchTask();
            List<Map> batchTemlateList = batchTemplateApolloConfig.getBatchTemplates();
            String taskApplyLogType = null;
            for (Map batchTemplate : batchTemlateList) {
                String eventName = BeanUtil.getPropString(batchTemplate, "templateEvent");
                if (Objects.equals(eventName, dto.getTemplateEvent())) {
                    mcBatchTask.setType(BeanUtil.getPropInt(batchTemplate, "batchType"));
                    taskApplyLogType = BeanUtil.getPropString(batchTemplate, "taskApplyLogType");
                }
            }
            String jsonPayLoad = JSON.toJSONString(map);
            Map taskApplyLog = logService.createTaskApplyLog(CollectionUtil.hashMap(
                    TaskApplyLog.TYPE, taskApplyLogType,
                    TaskApplyLog.APPLY_SYSTEM, 2,
                    TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()),
                    TaskApplyLog.PAYLOAD, jsonPayLoad,
                    TaskApplyLog.USER_ID, dto.getOperatorName(),
                    TaskApplyLog.APPLY_RESULT, 0
            ));
            mcBatchTask.setPayload(jsonPayLoad);
            mcBatchTask.setStatus(0);
            mcBatchTask.setEffect_time(new Date());
            mcBatchTask.setOperator_id(dto.getOperator());
            mcBatchTask.setOperator_name(dto.getOperatorName());
            mcBatchTask.setTask_apply_log_id(BeanUtil.getPropString(taskApplyLog, DaoConstants.ID));
            mcBatchTaskMapper.insertSelective(mcBatchTask);
        } catch (Exception exception) {
            log.error("doBatchProcessing auditId:{},exception:{}", dto.getAuditSn(), exception);
            final String msg = ExceptionUtil.getThrowableMsg(exception);
            message = StringUtils.isEmpty(msg) ? "审批发起异常" : msg;
            resultType = AUDIT_EXECUTE_FAIL;
        }
        if (callBackBean == null) {
            return null;
        }
        callBackBean.setResultType(resultType);
        callBackBean.setMessage(message);
        return callBackBean;
    }

    @Override
    public void importHxbParams(HxbParams hxbParams) {

    }

    @Override
    public void importHxbParamsV2(HxbParamsV2 hxbParams) {
        hxbImportBiz.importHxbParamsV2(hxbParams);
    }

    @Override
    public void importIcbcParams(IcbcParams icbcParams, boolean immediately) {
        hxbImportBiz.importIcbcParams(icbcParams, immediately);
    }

    @Override
    public void fillPayAuthInfo(String merchantProviderParamsId) {
        hxbImportBiz.fillPayAuthInfo(merchantProviderParamsId);
    }

    @Override
    public void importCcb(String merchantSn, String feeRate) {
        hxbImportBiz.importCcb(merchantSn, feeRate);
    }

    @Override
    public void importHxbMultiTradeParams(@Valid HxbParamsV2 hxbParams) {
        hxbImportBiz.importHxbMultiTradeParams(hxbParams);
    }


    @Override
    public void importPab(PabParam pabParam) {
        hxbImportBiz.importPabParams(pabParam);
    }

    @Override
    public void importZjtlcb(ZjtlcbParams zjtlcbParams) {
        hxbImportBiz.importZjtlcbParams(zjtlcbParams);
    }


    @Override
    public void commonImport(List<String> row, String operator) {
        commonImportBiz.commonImport(row);
    }

    @Override
    public void importUmsParams(ImportUmsParamsDTO importUmsParamsDTO) {
        umsImportBiz.importUmsParams(importUmsParamsDTO);
    }

    @Override
    public void storeImportUmsParams(ImportUmsParamsDTO importUmsParamsDTO) {
        umsImportBiz.storeImportUmsParams(importUmsParamsDTO);
    }

    @Override
    public void configHopeEduParams(HopeEduParams hopeEduParams) {
        MerchantInfo merchant = merchantService.getMerchantById(hopeEduParams.getMerchantId(), null);
        // 先删除
        merchantProviderParamsDAO.deleteParamsByProvider(merchant.getSn(), ProviderEnum.PROVIDER_YXT.getValue());

        if (Objects.equals(ValidStatusEnum.VALID.getValue(), hopeEduParams.getB2cStatus())) {
            importHopeEduParams(merchant.getSn());
        }
        HopeEduMerchantConfigRequest request = new HopeEduMerchantConfigRequest();
        request.setMerchantId(hopeEduParams.getMerchantId());
        request.setB2cStatus(hopeEduParams.getB2cStatus());
        tradeConfigService.updateHopeEduTradeParams(request);
    }

    private void importHopeEduParams(String merchantSn) {
        ContractRule contractRule = ruleContext.getContractRule("yxt-1061-32");
        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setId(UUID.randomUUID().toString());
        merchantProviderParamsDO.setCtime(System.currentTimeMillis());
        merchantProviderParamsDO.setMtime(System.currentTimeMillis());
        merchantProviderParamsDO.setDeleted(DeleteStatusEnum.NO_DELETED.getValue());
        merchantProviderParamsDO.setMerchantSn(merchantSn);
        merchantProviderParamsDO.setOutMerchantSn(merchantSn);
        merchantProviderParamsDO.setPayway(PaywayEnum.YMT.getValue());
        merchantProviderParamsDO.setChannelNo(contractRule.getChannelNo());
        merchantProviderParamsDO.setContractRule(contractRule.getRule());
        merchantProviderParamsDO.setRuleGroupId("yxt");
        merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_YXT.getValue());
        merchantProviderParamsDO.setParentMerchantId(merchantSn);
        merchantProviderParamsDO.setProviderMerchantId(merchantSn);
        merchantProviderParamsDO.setPayMerchantId(merchantSn);
        merchantProviderParamsDO.setParamsConfigStatus(ConfigStatusEnum.NOT_REQUIRE_CONFIG.getValue());
        merchantProviderParamsDO.setExtra("{}");
        merchantProviderParamsDO.setStatus(UseStatusEnum.IN_USE.getValue());
        merchantProviderParamsDAO.saveMerchantParameters(merchantProviderParamsDO);
    }


    @Override
    public void indirectCommonImport(List<String> row, String operator) {
        commonImportBiz.indirectCommonImport(row);
    }
}
