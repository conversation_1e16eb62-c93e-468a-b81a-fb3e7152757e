package com.wosai.upay.job.refactor.task.license.crm;

import com.google.common.collect.Maps;
import com.wosai.sales.merchant.business.bean.app.request.NoticeFieldAppInfoReq;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.sales.merchant.business.service.common.TimeTaskService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.refactor.service.rpc.crm.open.CrmCommonFieldService;
import com.wosai.upay.job.refactor.service.rpc.crm.open.req.CrmUpdateFieldAppInfoReq;
import com.wosai.upay.job.refactor.task.license.account.BankAccountVerifyTaskHandler;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * crm营业执照审批单更新
 *
 * <AUTHOR>
 * @date 2025/2/19 20:10
 */
@Service
@Slf4j
public class CrmLicenseApplyUpdate {

    @Autowired
    private CommonFieldService commonFieldService;

    @Resource
    private CrmCommonFieldService crmCommonFieldService;

    public void syncCrmLicenseApplyAccountVerifyStatus(Integer fieldAppInfoId, BankAccountVerifyTaskHandler.AccountVerifyRecord accountVerifyRecord) {
        Map<String, Object> fieldAppInfo = getFieldAppInfoById(fieldAppInfoId);
        Map businessAppInfo = MapUtils.getMap(fieldAppInfo, "business_app_info", Maps.newHashMap());
        CrmUpdateFieldAppInfoReq updateFieldAppInfoReq = new CrmUpdateFieldAppInfoReq();
        updateFieldAppInfoReq.setFieldAppInfoId(fieldAppInfoId);
        businessAppInfo.put(BankAccountVerifyTaskHandler.ACCOUNT_VERIFY_STATUS_FIELD_NAME, accountVerifyRecord);
        updateFieldAppInfoReq.setBusinessAppInfo(businessAppInfo);
        // 已确认，只更新传入的字段
        crmCommonFieldService.updateFieldAppInfo(updateFieldAppInfoReq);
    }

    public Map<String, Object> getFieldAppInfoById(Integer fieldAppInfoId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(1);
        pageInfo.setPage(1);
        Map<String, Object> queryFilterMap = Maps.newHashMap();
        queryFilterMap.put("id", fieldAppInfoId);
        ListResult fieldAppInfos = commonFieldService.findFieldAppInfos(pageInfo, queryFilterMap);
        if (Objects.isNull(fieldAppInfos)
                || org.apache.commons.collections4.CollectionUtils.isEmpty(fieldAppInfos.getRecords())
                || MapUtils.isEmpty(fieldAppInfos.getRecords().get(0))) {
            throw new ContractBizException("营业执照变更申请不存在");
        }
        Map<String, Object> fieldAppInfo = fieldAppInfos.getRecords().get(0);
        return fieldAppInfo;
    }

    @Autowired
    private TimeTaskService timeTaskService;

    public void updateCrmLicenseApplyStatus(Integer fieldAppInfoId, Integer status, String failMsg) {
        NoticeFieldAppInfoReq noticeFieldAppInfoReq = new NoticeFieldAppInfoReq();
        noticeFieldAppInfoReq.setFieldAppInfoId(fieldAppInfoId);
        noticeFieldAppInfoReq.setStatus(status);
        noticeFieldAppInfoReq.setAuditCode("pay");
        if (StringUtils.isNotBlank(failMsg)) {
            noticeFieldAppInfoReq.setFailMsg(failMsg);
        }
        timeTaskService.noticeFieldAppInfoStatus(noticeFieldAppInfoReq);
    }
}
