package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.ContractEvent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContractEventMapper {
    ContractEvent selectByPrimaryKey(Long id);


    int insertSelective(ContractEvent record);


    int updateByPrimaryKeySelective(ContractEvent record);

    List<ContractEvent> selectForCreateTasks(String start, Integer limit);

    ContractEvent selectSelfHelpNetInEventBymerchantSnAndtaskId(String merchant_sn, int task_id);

    ContractEvent selectByTaskId(long taskId);

    List<ContractEvent> selectByMerchantSnRejectContract(String merchant_sn);

    List<ContractEvent> selectEventTodoByMerchantSn(String merchant_sn);

    List<ContractEvent> selectByMerchantSnPendingEventRejectContract(String merchant_sn);

    int selectEventCount(String start, String end);

    List<ContractEvent> selectForBlocking(String start,String end, Integer limit);

    int updateCreateAt(Long id, String result);

    List<ContractEvent> selectNetInEvent(@Param("start") String start, @Param("queryLimit") Integer queryLimit);

    List<ContractEvent> selectEventTodoByMerchantSnAndType(String merchant_sn, Integer type);

}