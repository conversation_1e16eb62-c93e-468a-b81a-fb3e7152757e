package com.wosai.upay.job.xxljob.template;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import static com.wosai.upay.job.xxljob.model.DirectExecTypeEnum.ASYNC;
import static com.wosai.upay.job.xxljob.model.DirectExecTypeEnum.SYNC;

@Slf4j
@Service
public abstract class AbstractDirectJobHandler implements DirectJobHandler{
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    @Qualifier("directJobHandlerThreadPool")
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Override
    public void handle(DirectJobParam param) {
        if (SYNC.equals(param.getExecType())) {
            doHandle(param);
        } else if (ASYNC.equals(param.getExecType())) {
            getExecutor().submit(RunnableWrapper.of(() -> doHandle(param)));
        } else {
            throw new IllegalArgumentException("执行模式不正确");
        }
    }

    private void doHandle(DirectJobParam param) {
        if (ShutdownSignal.isShuttingDown()) {
            return;
        }
        RLock lock = redissonClient.getLock(getLockKey());
        try {
            if (lock.tryLock()) {
                execute(param);
            }
        } catch (Exception e) {
            log.error("{} direct task execute error", this.getClass().getSimpleName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    protected JobThreadPoolExecutor getExecutor() {
        return directJobHandlerThreadPool;
    }

    public abstract String getLockKey();

    public abstract void execute(DirectJobParam param);
}
