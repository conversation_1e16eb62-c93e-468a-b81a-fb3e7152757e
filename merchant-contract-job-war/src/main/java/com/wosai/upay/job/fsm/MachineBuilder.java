package com.wosai.upay.job.fsm;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/30
 */
public class MachineBuilder<T> {
    private Map<Integer, State> states = new HashMap<>();
    private State current;

    public MachineBuilder on(Integer status) {
        current = states.get(status);
        if (current == null) {
            current = new State(null);
            states.put(status, current);
        }
        return this;
    }

    public MachineBuilder invoke(Action<T> action) {
        current.setAction(action);
        return this;
    }

    public MachineBuilder end() {
        current.setEnd(true);
        return this;
    }

    public Machine build() {
        return new Machine(states);
    }
}
