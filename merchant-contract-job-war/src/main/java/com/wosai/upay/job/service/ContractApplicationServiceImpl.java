package com.wosai.upay.job.service;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.biz.merchantlevel.service.MerchantActiveLevelService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.StoreService;
import com.wosai.mpay.util.StringUtils;
import com.wosai.task.bean.dto.req.TaskRpcStartReqDto;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ProviderTerminalConstants;
import com.wosai.upay.job.consumer.StoreCreateConsumer;
import com.wosai.upay.job.consumer.TerminalConsumer;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.exception.ApplicationException;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.*;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.model.application.ZhimaAppCreateReq;
import com.wosai.upay.job.model.dto.ProviderTerminalContext;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.model.subBizParams.SubBizParamsExample;
import com.wosai.upay.job.providers.AbstractProvider;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.util.ApplicationUtil;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.terminal.AddTermInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 应用管理 其它各个业务方接入开通应用使用
 * Created by hzq on 19/11/27.
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class ContractApplicationServiceImpl implements ContractApplicationService {


    @Autowired
    @Lazy
    MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    SupportService supportService;
    @Autowired
    RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private RuleBiz ruleBiz;
    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private BusinessLogBiz businessLogBiz;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    WeixinCustomGoldBiz weixinCustomGoldBiz;
    @Autowired
    MerchantProviderParamsExtMapper merchantProviderParamsExtMapper;
    @Autowired
    RedisLock redisLock;

    @Autowired
    private OpenJjzBiz openJjzBiz;

    @Autowired
    private BlueSeaBiz blueSeaBiz;

    @Autowired
    private AntShopBiz antShopBiz;

    @Autowired
    private TmpBiz tmpBiz;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    AuthSpTaskMapper authSpTaskMapper;

    @Autowired
    Environment environment;

    @Autowired
    TaskInstanceService taskInstanceService;

    @Autowired
    MerchantActiveLevelService merchantActiveLevelService;

    @Autowired(required = false)
    StoreCreateConsumer storeCreateConsumer;

    @Autowired
    private AgreementBiz agreementBiz;


    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;
    @Autowired
    TerminalService terminalService;
    @Autowired
    StoreService storeService;
    @Autowired
    SubBizParamsBiz subBizParamsBiz;

    @Autowired
    SubBizParamsMapper subBizParamsMapper;
    @Autowired
    HxbImportBiz hxbImportBiz;

    @Autowired
    private Fill259InfoBiz fill259InfoBiz;

    @Resource
    private ProviderTerminalBiz providerTerminalBiz;

    @Resource
    private ProviderTerminalMapper providerTerminalMapper;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;
    @Resource
    private ProviderTerminalTaskRepository providerTerminalTaskRepository;

    @Resource
    private ProviderTerminalTaskMapper providerTerminalTaskMapper;
    @Resource
    private SupplyParamsBiz supplyParamsBiz;

    @Autowired
    private DefaultChangeTradeParamsBiz defaultChangeTradeParamsBiz;

    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Resource
    private LKlV3PosService lKlV3PosService;
    @Autowired
    private T9Service t9Service;


    public static final String TRADE = "trade";

    //版本
    public static final String VERSION = "version";
    //版本号
    public static final String VERSION_NO = "2.0";

    //服务商商户编号
    public static final String HX_PROVIDER_SERVICE_ID="provider_service_id";
    //商户APPID
    public static final String HX_DEVELOP_APP_ID="develop_app_id";

    //一级服务商编号
    public String hxParentServerOrgNo;

    //一级服务商appId
    public String hxParentDevelopAppId;


    //api入网华夏银行参数
    public static final String API_HX_TRADE_PARAMS_KEY="hxbank_trade_params";
    //线下入网华夏银行参数
    public static final String  OFFLINE_TRADEPARAMS= "tradeParams";

    @PostConstruct
    public void init() {
        hxParentDevelopAppId = "596A344C454F4436416662353866464D6D7954464767306E4A546B3D";
        hxParentServerOrgNo = "**********";
    }

    public static final List<String> suZhouChannelNOS = Lists.newArrayList("***********", "***********", "36T01224211");
    public static final String ALY_ONLINE__CHANNEL = "36TB4213597";
    public static final String DBB_PLAT = "dbb";

    private static final String SH_NORMAL_WEIXIN_WM_RULE = "lkl-1016-3-********";
    private static final String SH_NORMAL_WEIXIN_LKL_RULE = "lkl-1033-3-********";
    private static final String SH_NORMAL_WEIXIN_TL_RULE = "tonglian-1020-3";
    private static final List<String> NORMAL_RULES = Arrays.asList(SH_NORMAL_WEIXIN_TL_RULE, SH_NORMAL_WEIXIN_WM_RULE, SH_NORMAL_WEIXIN_LKL_RULE);

    @Override
    public CommonResult openZhimaCreditDbb(ZhimaAppCreateReq req) {
        throw new UnsupportedOperationException("此接口已废弃");
    }

    private void validFeeRate(String feeRate) {
        if (StringUtil.empty(feeRate)) {
            return;
        }
        try {
            BigDecimal decimal = new BigDecimal(feeRate);
            if (decimal.compareTo(BigDecimal.ZERO) < 0 || decimal.compareTo(BigDecimal.ONE) > 0) {
                throw new ApplicationException("费率必须在0~1之间", CommonResult.BIZ_FAIL);
            }
        } catch (Throwable T) {
            throw new ApplicationException("费率必须在0~1之间", CommonResult.BIZ_FAIL);
        }
    }


    @Override
    public CommonResult queryZhimaCreditInfo(String serviceId, String version) {
//        ContractResponse response = zhimaCreditService.queryServiceByServiceIdAndVersion(serviceId, version);
//        return ApplicationUtil.toCommonRs(response).setBiz_response(response.getResponseParam());
        throw new UnsupportedOperationException("此接口已废弃");
    }

    /**
     * @param ruleContractRequest
     * @return
     * <AUTHOR>
     * @Description:外部应用 将商户报备于某个规则之下
     * @time 10:44 上午
     */
    @Override
    public CommonResult contractByRule(RuleContractRequest ruleContractRequest) {
        Map paramContext;
        String merchantSn = ruleContractRequest.getMerchantSn();
        String rule = ruleContractRequest.getRule();
        if (StringUtils.isEmpty(merchantSn)) {
            Map merchant = merchantService.getMerchantByMerchantId(ruleContractRequest.getMerchantId());
            merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            if (StringUtils.isEmpty(merchantSn)) {
                throw new ApplicationException("商户不存在", CommonResult.BIZ_FAIL);
            }
        }
        try {
            paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, new ContractEvent().setEvent_type(0));
            if (ruleContractRequest.isReContract()) {
                paramContext.put("type", "1");
            }
            paramContext.put(ParamContextBiz.KEY_WEIXIN_SUBDEV_CONFIG_STATUS, ruleContractRequest.getWeixinSubdevConfigStatus());
            paramContext.put("plat", ruleContractRequest.getPlat());
            paramContext.put("forceMicro", ruleContractRequest.isForceMicro());
            Map<String, String> customFields = ruleContractRequest.getCustomFields();
            if (WosaiMapUtils.isNotEmpty(customFields)) {
                setCustomField(paramContext, customFields);
            }
            //微信高校食堂和线下教培原来使用特殊渠道现在使用普通渠道这样做兼容
            if (!NORMAL_RULES.contains(rule) || Lists.newArrayList(WxUseType.SCHOOL_CANTEEN.getCode(), WxUseType.OFFLINE_EDU_TRAIN.getCode()).contains(BeanUtil.getPropInt(paramContext, WX_USE_TYPE))) {
                paramContext.put(ReContractService.RECONTRACT, Boolean.TRUE);
            }
        } catch (ContextParamException e) {
            log.error("contractByRule ContextParamException", e);
            throw new ApplicationException(e.getMessage(), CommonResult.BIZ_FAIL);
        } catch (Exception e) {
            log.error("contractByRule Exception", e);
            throw new ApplicationException(e.getMessage(), CommonResult.ERROR);
        }
        ContractRule contractRule = ruleContext.getContractRule(rule);
        Map contractRes = ruleBiz.contractByRule(merchantSn,
                contractRule,
                paramContext, !ruleContractRequest.isReContract());
        String resString = (String) contractRes.get(RuleBiz.CONTRACT_RES_KEY);
        if (!StringUtils.isEmpty(resString)) {
            return ApplicationUtil.toCommonRs(resString);
        }
        String paramId = (String) contractRes.get(RuleBiz.CONTRACT_PARAM_KEY);
        if (ruleContractRequest.isConfigParam()) {
            if (CollectionUtils.isEmpty(ruleContractRequest.getTerminals())) {
                Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
                String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
                Map before = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, contractRule.getPayway());
                tradeParamsBiz.changeTradeParams(paramId, ruleContractRequest.getFee(), false, subBizParamsBiz.getPayTradeAppId());
                Map after = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, contractRule.getPayway());
                //异步写商户日志
                businessLogBiz.sendMerchantConfigBusinessLog(before, after, ruleContractRequest.getPlat(), ruleContractRequest.getPlat(), "按规则报备后切换交易参数");
            } else {
                validFeeRate(ruleContractRequest.getFee());
                if (DBB_PLAT.equals(ruleContractRequest.getPlat())) {
                    ruleBiz.changeDbbConfigParam(paramId, ruleContractRequest.getFee(), ruleContractRequest.getTerminals());
                }
                //todo wait for 其他的平台接入
            }
        }
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.selectByPrimaryKey(paramId);
        return new CommonResult(CommonResult.SUCCESS, "SUCCESS", CollectionUtil.hashMap("merchant_sn", merchantSn, "subMchId", merchantProviderParams.getPay_merchant_id()));
    }

    private void setCustomField(Map paramContext, Map customFields) {
        Map merchant = (Map) paramContext.get(ParamContextBiz.KEY_MERCHANT);
        String merchantName = BeanUtil.getPropString(customFields, MERCHANT_NAME);
        Map custom = new HashMap();
        if (!StringUtils.isEmpty(merchantName)) {
            merchant.put(Merchant.NAME, merchantName);
            custom.put(MERCHANT_NAME, merchantName);
        }
        String shortName = BeanUtil.getPropString(customFields, SHORT_NAME);
        if (!StringUtils.isEmpty(shortName)) {
            merchant.put(Merchant.BUSINESS_NAME, shortName);
            custom.put("merchant_shortname", shortName);
        }
        String industry = BeanUtil.getPropString(customFields, INDUSTRY);
        if (!StringUtils.isEmpty(industry)) {
            merchant.put(Merchant.INDUSTRY, industry);
        }

        String licenseNumber = BeanUtil.getPropString(customFields, "business_license");
        if (!StringUtils.isEmpty(licenseNumber)) {
            custom.put("business_license", licenseNumber);
        }
        String licenseType = BeanUtil.getPropString(customFields, "business_license_type");
        if (!StringUtils.isEmpty(licenseType)) {
            custom.put("business_license_type", licenseType);
        }
        String mcc = BeanUtil.getPropString(customFields, "mcc");
        if (!StringUtils.isEmpty(mcc)) {
            custom.put("mcc", mcc);
        }
        String name = BeanUtil.getPropString(customFields, "name");
        if (!StringUtils.isEmpty(name)) {
            merchant.put(Merchant.NAME, name);
            custom.put("name", name);
        }
        String aliasName = BeanUtil.getPropString(customFields, "alias_name");
        if (!StringUtils.isEmpty(name)) {
            custom.put("alias_name", aliasName);
        }

        String business = BeanUtil.getPropString(customFields, BUSINESS);
        String aliBusiness = BeanUtil.getPropString(customFields, "ali_business_custom");
        if (!StringUtils.isEmpty(aliBusiness)) {
            paramContext.put(KEY_CUSTOM_FIELDS, custom);
        }
        if (!StringUtils.isEmpty(business)) {
            custom.put(BUSINESS, business);
            paramContext.put(KEY_CUSTOM_FIELDS, custom);
        }
        //添加微信子商户号用途标识 支付方式是微信但是是普通活动的在入网的时候会有默认值

        if (Lists.newArrayList(WxUseType.SCHOOL_CANTEEN.getCode(), WxUseType.OFFLINE_EDU_TRAIN.getCode()).contains(BeanUtil.getPropInt(customFields, WX_USE_TYPE))) {
            final int wxUseType = BeanUtil.getPropInt(customFields, WX_USE_TYPE);
            paramContext.put(WX_USE_TYPE, wxUseType);
        }

    }

    @Override
    public boolean isWeiXinSuZhou(String merchantId) {
        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        if (merchantSn == null) {
            return false;
        }
        MerchantProviderParams use = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        if (use == null) {
            return false;
        }
        return suZhouChannelNOS.contains(use.getChannel_no());
    }

    @Override
    public CommonResult openGold(String merchantSn) {
        MerchantProviderParams params = merchantProviderParamsMapper.getUseWeiXinParam(merchantSn);
        return openGold(params);
    }

    @Override
    public CommonResult openGoldByMchId(String mchId) {
        MerchantProviderParams merchantProviderParams = merchantProviderParamsMapper.getByPayMerchantId(mchId);
        if (ObjectUtils.isEmpty(merchantProviderParams)) {
            throw new ApplicationException("不存在当前微信子商户号", CommonResult.ERROR);
        }
        if (2 == merchantProviderParams.getGold_status()) {
            return new CommonResult(200, "已开通", merchantProviderParams.getId());
        }
        return openGold(merchantProviderParams);
    }

    private CommonResult openGold(MerchantProviderParams params) {
        weixinCustomGoldBiz.open(params);
        String ext_field = merchantProviderParamsExtMapper.getField1ByParamId(params.getId(), MerchantProviderParamsExt.WEIXIN_GOLD_TYPE);
        if (StringUtils.isEmpty(ext_field)) {
            throw new ApplicationException("开通失败请稍后再试", CommonResult.ERROR);
        }
        if (WeixinCustomGoldBiz.EXT_FIELD_OPEN.equals(ext_field)) {
            return new CommonResult(200, "开通成功", params.getId());
        } else if (WeixinCustomGoldBiz.EXT_FIELD_GOLD_OPEN_FAIL.equals(ext_field)) {
            throw new ApplicationException(ext_field, CommonResult.BIZ_FAIL);
        } else if (WeixinCustomGoldBiz.EXT_FIELD_CUSTOM_PAGE_OPEN_FAIL.equals(ext_field)) {
            throw new ApplicationException(ext_field, CommonResult.BIZ_FAIL);
        }
        throw new ApplicationException(ext_field, CommonResult.ERROR);
    }

    @Override
    public CommonResult reContractForEp101(String merchantSn) {
        return new CommonResult(CommonResult.SUCCESS, "成功");
    }

    @Override
    public CommonResult reContractForEp101V2(String merchantSn) {
        return new CommonResult(CommonResult.SUCCESS, "成功");
    }

    @Override
    public CommonResult reContractForEp101V3(String merchantSn) {
        AuthSpTask authSpTask = authSpTaskMapper.selectBySn(merchantSn);
        if (Objects.nonNull(authSpTask)) {
            return new CommonResult(CommonResult.SUCCESS, "成功");
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        try {
            Map level = merchantActiveLevelService.getMerchantActiveLevelByMerchantId(MapUtils.getString(merchant, CommonModel.ID));
            String merchantLevel = MapUtils.getString(level, "level");
            Long template = getTaskTemplateByMerchantLevel(merchantLevel);
            if (Objects.nonNull(template)) {
                doStartTaskForRpc(merchantSn, template);
                AuthSpTask update = new AuthSpTask().setMerchant_sn(merchantSn).setLevel(merchantLevel).setTemplate_id(template);
                authSpTaskMapper.insertTask(update);
            }
        } catch (Exception e) {
            log.error("reContractForEp101V3,{}", merchantSn, e);
            //此接口容易报错 报错则不处理
        }
        return new CommonResult(CommonResult.SUCCESS, "成功");
    }

    @Override
    public CommonResult supplyParams(ParamsSupplyReq req) {
        if (!ProviderEnum.PROVIDER_LKLORG.getValue().equals(req.getProvider())) {
            return new CommonResult(CommonResult.SUCCESS, "成功");
        }
        if (!req.getPayway().equals(PaywayEnum.WEIXIN.getValue()) && !req.getPayway().equals(PaywayEnum.ALIPAY.getValue())) {
            return new CommonResult(CommonResult.SUCCESS, "成功");
        }
        try {
            boolean needDispatchWorkers = supplyParamsBiz.supplyLklParams(req.getMerchantSn());
            if (needDispatchWorkers) {
                Long templateId = Arrays.toString(environment.getActiveProfiles()).contains("prod") ? 462L : 202751L;
                doStartTaskForRpc(req.getMerchantSn(), templateId);
            }
        } catch (Exception e) {
            log.error("补充拉卡拉参数异常，商户号: {} ", req.getMerchantSn(), e);
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
        return new CommonResult(CommonResult.SUCCESS, "成功");
    }

    private void doStartTaskForRpc(String merchantSn, Long taskTemplateId) {
        TaskRpcStartReqDto dto = new TaskRpcStartReqDto();
        dto.setOperator("SYSTEM");
        dto.setOperatorName("SYSTEM");
        dto.setPlatform("SYSTEM");
        dto.setTaskTemplateId(taskTemplateId);
        dto.setTaskObjectSn(merchantSn);
        taskInstanceService.startTaskForRpc(dto);
    }

    private Long getTaskTemplateByMerchantLevel(String level) {
        if (StringUtils.isEmpty(level)) {
            return null;
        }
        if (Arrays.toString(environment.getActiveProfiles()).contains("prod")) {
            switch (level) {
                case "D":
                    return 287L;
                case "E":
                    return 287L;
                case "F":
                    return 287L;
                case "B":
                    return 291L;
                case "C":
                    return 291L;
                default:
                    return null;
            }
        }
        switch (level) {
            case "D":
                return 45836L;
            case "E":
                return 45836L;
            case "F":
                return 45836L;
            case "B":
                return 45837L;
            case "C":
                return 45837L;
            default:
                return null;
        }
    }

    @Override
    public CommonResult openJjz(String merchantSn, String subAppId, Integer rateType) {
        rateType = 0;
        log.info("openJjz==>merchantSn=={},subAppId=={},rateType=={}", merchantSn, subAppId, rateType);
        //处理业务
        return openJjzBiz.handlerJjzBiz(merchantSn, subAppId, rateType);
    }

    @Override
    public CommonResult jjzNormalRate(String merchantSn, String subAppId) {
        log.info("JjzNormalRate==>merchantSn=={},subAppId=={}", merchantSn, subAppId);
        CommonResult result = openJjzBiz.handlerJjzNormalRate(merchantSn, subAppId);
        return result;
    }

    @Override
    public CommonResult applyNewBlueSea(String merchantSn, Long auditId, Map formBody) {
        return blueSeaBiz.applyNewBlueSea(merchantSn, auditId, formBody);
    }

    @Override
    public CommonResult applyAliCarnival(String merchantSn, Long auditId, Map formBody) {
        return blueSeaBiz.applyAliCarnival(merchantSn, auditId, formBody);
    }

    @Override
    public CommonResult applyKx(String merchantSn, Long auditId, Map formBody) {
        return blueSeaBiz.applyKx(merchantSn, auditId, formBody);
    }

    @Override
    public CommonResult alipayMessage(String msgApi, String msgId, String bizContent) {
        blueSeaBiz.onMessage(msgApi, msgId, bizContent);
        return new CommonResult();
    }

    @Override
    public CommonResult refreshTerminal(String merchantSn, Long auditId, Map formBody) {
        return blueSeaBiz.refreshTerminal(merchantSn, auditId, formBody);
    }

    @Override
    public CommonResult createAntShop(String merchantSn, Integer businessType) {
        return antShopBiz.handleAntShop(merchantSn, null, businessType);
    }

    @Override
    public CommonResult createAntShopForStore(String merchantSn, String storeSn, Integer businessType) {
        return antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
    }

    @Override
    public CommonResult copyBankToTl(String tlMchSn, String lklMchSn) {
        return tmpBiz.copyBankToTl(tlMchSn, lklMchSn);
    }

    @Override
    public CommonResult syncMchInfo2PayWay(String merchantSn, int payWay) {
        try {
            composeAcquirerBiz.syncMchInfo2PayWay(merchantSn, payWay);
            return new CommonResult(CommonResult.SUCCESS, "成功");
        } catch (Exception e) {
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        }
    }


    @Override
    public void createProviderTerminalTask(String merchantSn, Boolean allLevel) {
        //1,当前商户报过的所有收单机构
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                .andProviderIn(Arrays.asList(ProviderEnum.PROVIDER_LAKALA_V3.getValue()))
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        records.stream().forEach(param -> {
            final Integer provider = param.getProvider();
            BasicProvider contextProvider = providerFactory.getProvider(String.valueOf(provider));
            //商户级别
            contextProvider.handleSqbMerchantProviderTerminal(merchantSn, provider);
            if (Objects.equals(allLevel, Boolean.TRUE)) {
                //门店和终端级别
                contextProvider.createProviderTerminal(merchantSn, provider);
            }
        });
    }

    @Override
    public void createAssignProviderTerminalTask(String merchantSn, Boolean allLevel, Integer provider) {
        if(StringUtils.isEmpty(merchantSn)) {
           throw new CommonPubBizException("商户号不为空");
        }
        //1,当前商户报过的所有收单机构
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = example.or()
                .andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(0)
                .andDeletedEqualTo(false);
        //指定收单机构
        if(Objects.nonNull(provider)) {
            criteria.andProviderEqualTo(provider);
        }
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);
        records.stream().forEach(param -> {
            BasicProvider contextProvider = providerFactory.getProvider(String.valueOf(param.getProvider()));
            //商户级别
            contextProvider.handleSqbMerchantProviderTerminal(merchantSn, param.getProvider());
            if (Objects.equals(allLevel, Boolean.TRUE)) {
                //门店和终端级别,添加延迟创建机制
                CompletableFuture.runAsync(() ->{
                    ThreadUtil.sleep(RandomUtil.randomLong(3000,5000));
                    contextProvider.createProviderTerminal(merchantSn, param.getProvider());
                });
            }
        });
    }

    @Override
    public void syncLklStore(String merchantSn) {
        storeCreateConsumer.syncLklStore(merchantSn);
    }

    @Override
    public void recordAgreementSync(String merchantSn) {
        agreementBiz.recordAgreementSync(merchantSn);
    }


    @Override
    public CommonResult queryLklTermTaskResult(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint) {
        return lklV3ShopTermBiz.queryLklTermTask(deviceFingerprint);
    }


    @Override
    public void createLKLStore(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint) {
        final Map terminal = terminalService.getTerminalByDeviceFingerprint(deviceFingerprint);
        final String merchantId = MapUtils.getString(terminal, Terminal.MERCHANT_ID);
        final Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        final String storeId = MapUtils.getString(terminal, Terminal.STORE_ID);
        final StoreInfo store = storeService.getStoreById(storeId, null);
        final String storeSn = store.getSn();
        storeCreateConsumer.syncLklStore(MapUtils.getString(merchant, Merchant.SN), storeSn);
    }

    @Override
    public void syncLklStore(String merchantSn, String storeSn) {
        storeCreateConsumer.syncLklStore(merchantSn, storeSn);
    }

    @Override
    public void bindLklTerminal(@NotBlank(message = "设备指纹不可为空") String deviceFingerprint) {
        final Map terminal = terminalService.getTerminalByDeviceFingerprint(deviceFingerprint);
        final String merchantId = MapUtils.getString(terminal, Terminal.MERCHANT_ID);
        final Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        lklV3ShopTermBiz.bindLklTerminal(terminal, merchant);
    }


    @Override
    public Boolean IsMultiBiz(String merchantSn) {
        final SubBizParamsExample example = new SubBizParamsExample();
        example.or()
                .andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(Boolean.FALSE);
        List<SubBizParams> subBizParamsList = subBizParamsMapper.selectByExample(example);
        //多业务多通道
        Map<String, List<SubBizParams>> tradeAppIdMap = subBizParamsList.stream().collect(Collectors.groupingBy(param -> param.getTrade_app_id()));
        Map<Integer, List<SubBizParams>> providerMap = subBizParamsList.stream().filter(subParam -> !Lists.newArrayList(2, 3).contains(subParam.getProvider()))
                .collect(Collectors.groupingBy(param -> param.getProvider()));
        return tradeAppIdMap.keySet().size() > 1 && providerMap.keySet().size() > 1;
    }

    @Override
    public Boolean isInSubBizForBankAcquire(String merchantSn) {
        return subBizParamsBiz.isSubBiz(merchantSn);
    }


    @Override
    public void initBanKOfflineRecord(String merchantSn, Integer appStatus, String bankRef) {
        if (bankRef.contains("hxb")) {
            hxbImportBiz.createOrUpdateHxbBizOpenInfo(merchantSn, appStatus);
        }
        if (bankRef.contains("icbc")) {
            hxbImportBiz.createOrUpdateIcbcBizOpenInfo(merchantSn, appStatus);
        }
    }

    @Override
    public void repairMerchantAppConfig(List<String> merchantSnList) {
        merchantSnList.stream().forEach(sn -> {
            Map merchant = merchantService.getMerchantByMerchantSn(sn);
            defaultChangeTradeParamsBiz.repairMerchantAppConfigProviderHandle(merchant, null);
        });

    }


    @Override
    public void repairMerchantAppConfigAssignAcquire(String merchantSn, String acquire) {
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        defaultChangeTradeParamsBiz.repairMerchantAppConfigProviderHandle(merchant, acquire);

    }

    @Autowired
    ContractStatusService contractStatusService;

    @Override
    public void cancelOtherPayComb(List<String> merchantSnList) {
        merchantSnList.stream().forEach(merchantSn -> {
            ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
            String acquirer = contractStatus.getAcquirer();
            List<String> indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
            if (!indirectAcquireList.contains(acquirer)) {
                log.info("cancelOtherPayComb merchantSn:{},当前移动支付业务收单机构在{}", merchantSn, acquirer);
                return;
            }
            Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            List<Map<String, Object>> appConfigList = tradeConfigService.getMerchantAppConfigByMerchantIdAndApp(merchantId, null);
            if (org.springframework.util.CollectionUtils.isEmpty(appConfigList)) {
                log.info("cancelOtherCombo 商户号:{},appConfigList为空", merchantSn);
                return;
            }
            //取消其他业务交易参数
            Map<String, List<Map<String, Object>>> groupByAppId = appConfigList.stream().collect(Collectors.groupingBy(appConfig -> BeanUtil.getPropString(appConfig, "app_id")));
            Set<String> appIds = groupByAppId.keySet();
            appIds.forEach(appId -> {
                subBizParamsBiz.doCancelCombo(merchantSn, appId, "移动支付业务切回间连取消其他业务套餐");
            });
        });
    }


    public static final Map<String, Integer> acquireProviderMap = CollectionUtil.hashMap(
            AcquirerTypeEnum.PSBC.getValue(), ProviderEnum.PROVIDER_PSBC.getValue(),
            AcquirerTypeEnum.CGB.getValue(), ProviderEnum.PROVIDER_CGB.getValue(),
            AcquirerTypeEnum.CCB.getValue(), ProviderEnum.PROVIDER_CCB.getValue(),
            AcquirerTypeEnum.ICBC.getValue(), ProviderEnum.PROVIDER_ICBC.getValue(),
            AcquirerTypeEnum.HXB.getValue(), ProviderEnum.PROVIDER_HXB.getValue()
    );

    @Override
    public void configSubMchNo(String merchantSn) {
        ContractStatus contractStatus = contractStatusService.selectByMerchantSn(merchantSn);
        //商户当前收单机构
        String acquirer = contractStatus.getAcquirer();
        //间连通道
        List<String> indirectAcquireList = mcAcquirerDAO.getIndirectAcquirerList();
        if (indirectAcquireList.contains(acquirer)) {
            log.info("商户号:{},当前在间连收单机构{}", merchantSn, acquirer);
            return;
        }
        //当前商户收单机构对应的provider
        Integer provider = acquireProviderMap.get(acquirer);
        //当前merchant_config表是否已经配置了子商户号
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map<String, Object> providerTradeParamsKey = tradeConfigService.getSystemConfigContentByName("provider_trade_params_key");
        String tradeParamKey = BeanUtil.getPropString(providerTradeParamsKey, String.valueOf(provider));
        List<Map> resultList = Optional.ofNullable((List<Map>) tradeConfigService.getMerchantConfigsByMerchantId(merchantId))
                .orElseGet(ArrayList::new)
                .stream().filter(merchantConfig ->
                        //交易是否正在使用银行通道
                        Objects.equals(BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER), provider)
                                //当前只更新支付宝和微信
                                && Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY))
                                //该银行是否已经含有子商户号
                                && !BeanUtil.getPropString(merchantConfig.get(MerchantConfig.PARAMS), tradeParamKey).contains("sub_mch_id")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }
        resultList.forEach(merchantConfig -> {
                    int payway = BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY);
                    //银行参数
                    MerchantProviderParamsExample example = new MerchantProviderParamsExample();
                    example.or().andPaywayEqualTo(payway).andMerchant_snEqualTo(merchantSn).andProviderEqualTo(provider);
                    List<MerchantProviderParams> providerParamsList = merchantProviderParamsMapper.selectByExample(example);
                    MerchantProviderParams param = providerParamsList.get(0);
                    //组装特定银行特定支付方式对应的交易参数
                    String payMerchantId = param.getPay_merchant_id();
                    //添加支付宝/微信子商户号的
                    Map paramsMap = (Map) merchantConfig.get(MerchantConfig.PARAMS);
                    Map bankParamMap = MapUtils.getMap(paramsMap, tradeParamKey);
                    if (payway == PaywayEnum.ALIPAY.getValue()) {
                        bankParamMap.putIfAbsent("alipay_sub_mch_id", payMerchantId);
                    }
                    if (payway == PaywayEnum.WEIXIN.getValue()) {
                        bankParamMap.putIfAbsent("weixin_sub_mch_id", payMerchantId);
                    }
                    paramsMap.put(tradeParamKey, bankParamMap);
                    //更新信息
                    tradeConfigService.updateMerchantConfigWithoutMessage(CollectionUtil.hashMap(
                            DaoConstants.ID, BeanUtil.getPropString(merchantConfig, DaoConstants.ID),
                            MerchantConfig.PARAMS, paramsMap,
                            DaoConstants.VERSION, BeanUtil.getPropLong(merchantConfig, DaoConstants.VERSION)
                    ));
                }
        );
        //更新交易缓存
        supportService.removeCachedParams(merchantSn);
    }



    /**
     *  以终端号/(终端号+子商户)为维度,在某一段时间(apollo配置)时间只允许调用一次
     * @param rebindTerminal
     */
    @Override
    public void reBindTerminal(RebindTerminal rebindTerminal) {
        //前置业务处理
        MutableTriple<Boolean, String, ProviderTerminal> mutableTriple = preBiz(rebindTerminal);
        Boolean permission = mutableTriple.getLeft();
        ProviderTerminal providerTerminal = mutableTriple.getRight();
        if(!permission) {
            return;
        }
        //终端绑定
        bindTerminal(providerTerminal,rebindTerminal);

    }

    /**
     * 终端绑定
     * @param providerTerminal
     * @param rebindTerminal
     */
    private void bindTerminal(ProviderTerminal providerTerminal,RebindTerminal rebindTerminal) {
        String subMcNo = rebindTerminal.getSubMcNo();
        Integer provider = providerTerminal.getProvider();
        String merchantSn = providerTerminal.getMerchant_sn();
        //如果本地没有那说明业务管理和交易数据不一致,需要重新生成8位终端=>银联绑定=>替换支付测参数
        final String terminalId = providerTerminal.getProvider_terminal_id();
        if(StringUtils.isEmpty(terminalId)){
            log.info("terminalId:{},在业务管理中不存在,重新生成绑定终端",terminalId);
            //业务管理和交易数据不一致,需要重新生成8位终端=>银联绑定=>替换支付测参数
            reCreateProviderTerminal(rebindTerminal.getMerchantSn(),
                    rebindTerminal.getStoreSn(),
                    rebindTerminal.getTerminalSn(),
                    rebindTerminal.getProvider());
            return;
        }
        AbstractProvider handleProvider = (AbstractProvider)providerFactory.getProvider(String.valueOf(Objects.equals(provider,ProviderEnum.PROVIDER_LAKALA_V3.getValue()) ? String.valueOf(ProviderEnum.PROVIDER_LKLORG.getValue()) : provider));
        //指定绑定某个子商号
        List<MerchantProviderParams> paramsList = Lists.newArrayList();
        if(!StringUtils.isEmpty(subMcNo)) {
            MerchantProviderParams providerParams = Optional.ofNullable(merchantProviderParamsMapper.getByPayMerchantId(subMcNo))
                    .orElseGet(MerchantProviderParams::new);
            paramsList.add(providerParams);
        }else {
            //商户当前所有子商户号
            List<MerchantProviderParams> allParams = handleProvider.getMerchantProviderParamsByProvider(provider, merchantSn);
            if(CollectionUtils.isEmpty(allParams)) {
                return;
            }
            //强制重新绑定
            Boolean force = rebindTerminal.getForce();
            if(force) {
                //重新绑定所有子商户号
                paramsList.addAll(allParams);
            }else {
                //看哪些子商户号没有绑定就去绑定这些
                paramsList = Optional.ofNullable(providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, allParams))
                        .orElseGet(ArrayList::new);
            }
        }
        //绑定
        doBind(providerTerminal, merchantSn, handleProvider, paramsList);
    }

    /**
     * 设置缓存,写入3后自动删除
     */
    Cache<String, Boolean> delayOperate = CacheBuilder.newBuilder()
            .maximumSize(4096)
            .expireAfterWrite(3, TimeUnit.SECONDS)
            .build();

    /**
     * 完成终端绑定以及后续处理
     * @param providerTerminal
     * @param merchantSn
     * @param handleProvider
     * @param params
     */
    public void doBind(ProviderTerminal providerTerminal, String merchantSn, AbstractProvider handleProvider, List<MerchantProviderParams> params) {
        //重新绑定所有子商户
        params.stream().forEach(param -> {
            String payMerchantId = param.getPay_merchant_id();
            //哪些收单机构操作同一个终端需要间隔一段时间(单位毫秒)
            Map sameTermDelayTimeMap = applicationApolloConfig.getSameTermDelayTime();
            String provider = String.valueOf(providerTerminal.getProvider());
            Long delayTime = BeanUtil.getPropLong(sameTermDelayTimeMap, provider);
            String providerTerminalId = providerTerminal.getProvider_terminal_id();
            if(Objects.nonNull(delayTime) && delayTime > 0) {
                Boolean present =
                        delayOperate.getIfPresent(providerTerminalId);
                if(Objects.isNull(present) || Objects.equals(present,Boolean.FALSE)) {
                    //设置已经存在
                    delayOperate.put(providerTerminalId, Boolean.TRUE);
                }else  {
                    ThreadUtil.sleep(delayTime);
                }
            }
            AddTermInfoDTO addTermInfoDTO = providerTerminalBiz.assembleBoundTerminal(merchantSn, providerTerminal, payMerchantId);
            Integer payWay = param.getPayway();
            ContractResponse contractResponse = Optional.ofNullable(handleProvider.boundTerminal(addTermInfoDTO, payWay, null))
                    .orElseGet(ContractResponse::new);
            if(!contractResponse.isSuccess())  {
                return;
            }
            //再次查询获取已绑定的子商户号
            Long id = providerTerminal.getId();
            ProviderTerminal terminal = providerTerminalMapper.selectByPrimaryKey(id);
            String boundSubMchIds = terminal.getBound_sub_mch_ids();
            if(!StrUtil.contains(boundSubMchIds,payMerchantId)) {
                //不存在则记录
                String newBoundSubMchIds = StringUtils.isEmpty(boundSubMchIds) ? payMerchantId : boundSubMchIds + "," + payMerchantId;
                providerTerminalMapper.updateBoundSubMchIdsById(newBoundSubMchIds,id);

            }
            //查看是否有任务,如果有任务则置为成功,避免重复创建
            ProviderTerminalTask existTask = new ProviderTerminalTask();
            existTask.setMerchant_sn(merchantSn);
            final List<ProviderTerminalTask> taskList = providerTerminalTaskMapper.selectByCondition(existTask);
            taskList.stream().filter(task -> {
                //筛选出没有成功绑定该终端的任务
                ProviderTerminalContext contextInfo = task.getContextInfo();
                String terminalId = contextInfo.getProviderTerminalId();
                String subMerchant = contextInfo.getSubMerchant();
                Integer taskPayWay = contextInfo.getPayWay();
                return Objects.equals(terminalId, providerTerminalId)
                        && Objects.equals(subMerchant,payMerchantId)
                        && Objects.equals(payWay,taskPayWay)
                        && !Objects.equals(task.getStatus(), ProviderTerminalConstants.SUCCESS);
            }).forEach(unSuccess -> {
                //将不成功的任务置为成功
                Long taskId = unSuccess.getId();
                providerTerminalTaskRepository.updateTaskStatusById(taskId, ProviderTerminalConstants.SUCCESS, "子商户号绑定终端成功(接口触发)");
            });
        });
    }

    /**
     * 前置逻辑
     */
    private MutableTriple<Boolean, String, ProviderTerminal> preBiz(RebindTerminal rebindTerminal) {
        MutableTriple<Boolean, String, ProviderTerminal> triple = new MutableTriple<>();
        String terminalId = rebindTerminal.getProviderTerminalId();
        ProviderTerminal terminal = new ProviderTerminal();
        terminal.setProvider_terminal_id(terminalId);
        terminal.setMerchant_sn(rebindTerminal.getMerchantSn());
        terminal.setStore_sn(rebindTerminal.getStoreSn());
        terminal.setTerminal_sn(rebindTerminal.getTerminalSn());
        //是否存在
        List<ProviderTerminal> terminals = providerTerminalMapper.selectByCondition(terminal);
        if(CollectionUtils.isEmpty(terminals)) {
                log.info("terminalId:{},在业务管理中不存在,重新生成绑定终端",terminalId);
                //业务管理和交易数据不一致,需要重新生成8位终端=>银联绑定=>替换支付测参数
                reCreateProviderTerminal(rebindTerminal.getMerchantSn(),
                        rebindTerminal.getStoreSn(),
                        rebindTerminal.getTerminalSn(),
                        rebindTerminal.getProvider());
            triple.setLeft(Boolean.FALSE);
            return triple;
        }
        ProviderTerminal providerTerminal = terminals.stream()
                .filter(info -> Objects.equals(info.getProvider_terminal_id(),terminalId))
                .findFirst().orElseGet(ProviderTerminal::new);
        //强制更新
        Boolean force = rebindTerminal.getForce();
        if(force) {
            triple.setLeft(Boolean.TRUE);
            triple.setRight(providerTerminal);
            return triple;
        }

        String subMcNo = rebindTerminal.getSubMcNo();
        String key;
        if(StringUtils.isEmpty(subMcNo)) {
            key = String.format("reBindTerminal:%s", terminalId);
        }else {
            key = String.format("reBindTerminal:%s_%s", terminalId,subMcNo);
        }

        //调用周期
        Map forbiddenTimeMap = applicationApolloConfig.getReBindTerminalForbiddenTime();
        String provider = String.valueOf(providerTerminal.getProvider());
        int forbiddenTime = BeanUtil.getPropInt(forbiddenTimeMap, provider, 60);
        if (!redisLock.lock(key, key, forbiddenTime)) {
            triple.setLeft(Boolean.FALSE);
            return triple;
        }
        triple.setLeft(Boolean.TRUE);
        triple.setMiddle(key);
        triple.setRight(providerTerminal);
        return triple;
    }


    @Override
    public void configHxParam(String merchantSn) {
        //交易参数在支付组(这是为了洗之前没有通过业务管理导入的)
        //当前merchant_config表是否已经配置华夏
        Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if(Objects.isNull(merchant)) {
            return;
        }
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        //修改merchant_config表params
        updateMerchantConfigParams(merchantId,merchantSn);
        //修改merchant_provider_params中extra字段
        updateMerchantProviderParamsExtra(merchantSn);

    }

    /**
     * 将交易参数中的配置交易修改一下
     * @param merchantId
     * @param merchantSn
     */
    private void updateMerchantConfigParams(String merchantId, String merchantSn) {
        String tradeParamKey = "hxbank_trade_params";
        //获取需要被更新的交易参数
        List<Map> needUpdateList = Optional.ofNullable((List<Map>) tradeConfigService.getMerchantConfigsByMerchantId(merchantId))
                .orElseGet(ArrayList::new)
                .stream()
                .filter(merchantConfig ->
                        //交易是否正在使用华夏银行通道
                        Objects.equals(BeanUtil.getPropInt(merchantConfig, MerchantConfig.PROVIDER), ProviderEnum.PROVIDER_HXB.getValue())
                                //当前只更新支付宝,微信,云闪付,数字人民币
                                && Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue(), PaywayEnum.DCEP.getValue()).contains(BeanUtil.getPropInt(merchantConfig, MerchantConfig.PAYWAY)))
                .filter(merchantConfig ->
                {
                    Map paramsMap = (Map) merchantConfig.get(MerchantConfig.PARAMS);
                    Map bankParamMap = MapUtils.getMap(paramsMap, tradeParamKey);
                            //一级服务商编号是否匹配
                    return needUpdate(bankParamMap);
                }).collect(Collectors.toList());
        //更新
        if(CollectionUtils.isNotEmpty(needUpdateList)) {
            needUpdateList.stream()
                    .forEach(need -> {
                        String id = MapUtils.getString(need, DaoConstants.ID);
                        Map paramsMap = (Map) need.get(MerchantConfig.PARAMS);
                        Map bankParamMap = MapUtils.getMap(paramsMap, tradeParamKey);
                        bankParamMap.put(VERSION,"1.0");
                        StringBuilder builder = new StringBuilder();
                        builder.append("update merchant_config set params=json_set(CAST(`params` AS CHAR) ");
                        bankParamMap.forEach((k,v) ->{
                            builder.append(",'$.hxbank_trade_params.").append(k).append("',").append("'").append(v).append("'");
                        });
                        String prefix = builder.toString();
                        log.info(String.format(prefix +" ) where  provider = 1028 and id ='%s';",id));
                    });
        }
        needUpdateList.forEach(config -> {
            Map paramsMap = (Map) config.get(MerchantConfig.PARAMS);
            //华夏交易参数
            Map bankParamMap = MapUtils.getMap(paramsMap, tradeParamKey);
            //更新设置一级服务商模式交易参数
            bankParamMap.putAll(CollectionUtil.hashMap(
                    HX_PROVIDER_SERVICE_ID,hxParentServerOrgNo,
                    HX_DEVELOP_APP_ID,hxParentDevelopAppId,
                    VERSION,VERSION_NO
            ));
            //重新设置
            paramsMap.put(tradeParamKey, bankParamMap);
            //更新信息
            tradeConfigService.updateMerchantConfigWithoutMessage(CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(config, DaoConstants.ID),
                    MerchantConfig.PARAMS, paramsMap,
                    DaoConstants.VERSION, BeanUtil.getPropLong(config, DaoConstants.VERSION)));
        });
        //更新交易缓存
        supportService.removeCachedParams(merchantSn);
    }




    /**
     * 更新merchant_provider_params中extra字段
     * @param merchantSn
     */
    private void updateMerchantProviderParamsExtra(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andProviderEqualTo(ProviderEnum.PROVIDER_HXB.getValue()).andPaywayIn(Lists.newArrayList(PaywayEnum.ACQUIRER.getValue() , PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue(), PaywayEnum.UNIONPAY.getValue(), PaywayEnum.DCEP.getValue()));
        List<MerchantProviderParams> providerParamsList = merchantProviderParamsMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(providerParamsList)) {
            return;
        }
        providerParamsList.stream().forEach(param -> {
            MerchantProviderParams newMerchantProviderParams = new MerchantProviderParams();
            //收单机构存储的数据
            Map paywayParam = CommonUtil.bytes2Map(param.getExtra());
            //线下导入
            if(JSONObject.toJSONString(paywayParam).contains(OFFLINE_TRADEPARAMS)) {
                Map tradeParams = MapUtils.getMap(paywayParam, OFFLINE_TRADEPARAMS);
                if(!needUpdate(tradeParams)) {
                    return;
                }
                //原始记录日志方便回滚
                String id = param.getId();
                log.info("updateMerchantProviderParamsExtra," +String.format("update merchant_provider_params set extra='%s' where id ='%s';",paywayParam,id));
                tradeParams.putAll(CollectionUtil.hashMap(
                        HX_PROVIDER_SERVICE_ID,hxParentServerOrgNo,
                        HX_DEVELOP_APP_ID,hxParentDevelopAppId,
                        VERSION,VERSION_NO
                ));
                paywayParam.put(OFFLINE_TRADEPARAMS,tradeParams);
                //更新
                newMerchantProviderParams.setId(param.getId());
                newMerchantProviderParams.setExtra(CommonUtil.map2Bytes(paywayParam));
                merchantProviderParamsMapper.updateByPrimaryKeySelective(newMerchantProviderParams);
            }else {
                //api入网
                if(param.getPayway().equals(PaywayEnum.ACQUIRER.getValue())) {
                    paywayParam.forEach((k,v)->{
                        Map<String,Object> map;
                        if(!Lists.newArrayList(String.valueOf(PaywayEnum.ALIPAY.getValue()), String.valueOf(PaywayEnum.WEIXIN.getValue()),String.valueOf(PaywayEnum.UNIONPAY.getValue()),String.valueOf(PaywayEnum.DCEP.getValue())).contains(k)) {
                           return;
                        }
                        map = JSONObject.parseObject(JSONObject.toJSONString(v), Map.class);
                        Map trade = MapUtils.getMap(map, TRADE);
                        Map hxbankTradeParams = MapUtils.getMap(trade, API_HX_TRADE_PARAMS_KEY);
                        if(!needUpdate(hxbankTradeParams)) {
                            return;
                        }
                        hxbankTradeParams.putAll(CollectionUtil.hashMap(
                                HX_PROVIDER_SERVICE_ID,hxParentServerOrgNo,
                                HX_DEVELOP_APP_ID,hxParentDevelopAppId,
                                VERSION,VERSION_NO
                        ));
                        trade.put(API_HX_TRADE_PARAMS_KEY,hxbankTradeParams);
                        map.put(TRADE,trade);
                        paywayParam.put(k,map);
                    });
                    //更新
                    //原始记录日志方便回滚
                    String id = param.getId();
                    log.info("updateMerchantProviderParamsExtra," +String.format("update merchant_provider_params set extra='%s' where id ='%s';",paywayParam,id));
                    newMerchantProviderParams.setId(param.getId());
                    newMerchantProviderParams.setExtra(CommonUtil.map2Bytes(paywayParam));
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(newMerchantProviderParams);
                }else {
                    Map hxbankTradeParams = MapUtils.getMap(paywayParam, API_HX_TRADE_PARAMS_KEY);
                    if(!needUpdate(hxbankTradeParams)) {
                        return;
                    }
                    //原始记录日志方便回滚
                    String id = param.getId();
                    log.info("updateMerchantProviderParamsExtra," +String.format("update merchant_provider_params set extra='%s' where id ='%s';",paywayParam,id));
                    hxbankTradeParams.putAll(CollectionUtil.hashMap(
                            HX_PROVIDER_SERVICE_ID,hxParentServerOrgNo,
                            HX_DEVELOP_APP_ID,hxParentDevelopAppId,
                            VERSION,VERSION_NO
                    ));
                    paywayParam.put(API_HX_TRADE_PARAMS_KEY,hxbankTradeParams);
                    //更新
                    newMerchantProviderParams.setId(param.getId());
                    newMerchantProviderParams.setExtra(CommonUtil.map2Bytes(paywayParam));
                    merchantProviderParamsMapper.updateByPrimaryKeySelective(newMerchantProviderParams);
                }
            }
        });
    }

    /**
     * 是否缺少必要的交易参数,这三个缺一不可
     * @param bankParamMap
     * @return
     */
    private boolean needUpdate(Map bankParamMap) {
            //一级服务商编号是否匹配
        return !bankParamMap.containsValue(hxParentServerOrgNo)
                //一级服务商appId是否匹配
                || !bankParamMap.containsValue(hxParentDevelopAppId)
                //一级服务商版本号是否存在
                || !bankParamMap.containsValue(VERSION_NO);
    }

    @Override
    public void fill259Info(String subMchId) {
        fill259InfoBiz.fill259Info(subMchId);
    }

    /**
     * 删除原有重新生成终端表记录
     * @param merchantSn
     * @param storeSn
     * @param terminalSn
     * @param provider
     */
    @Override
    public void reCreateProviderTerminal(String merchantSn, String storeSn, String terminalSn, Integer provider) {
        providerTerminalBiz.deleteProviderTerminal(merchantSn, storeSn,terminalSn,provider);
        //生成新的记录
        createAssignProviderTerminalTask(merchantSn,Boolean.TRUE,provider);
    }


    @Override
    public Boolean getForeignCardStatus(Map params) {
        final String merchantId = MapUtils.getString(params, MERCHANT_ID);
        if(StrUtil.isBlank(merchantId)) {
            throw new ApplicationException("商户Id不为空",CommonResult.BIZ_FAIL);
        }
        final Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        final String merchantSn = MapUtils.getString(merchant, Merchant.SN);
        final Boolean result = t9Service.getForeignCardStatus(merchantSn);
        return result;
    }

    @Override
    public boolean checkBankCardConsistence(String merchantSn, String acquirer) {
        return subBizParamsBiz.checkBankCardConsistence(merchantSn, acquirer);
    }
}
