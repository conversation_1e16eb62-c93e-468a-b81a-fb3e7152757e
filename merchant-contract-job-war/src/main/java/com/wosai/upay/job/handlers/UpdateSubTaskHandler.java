package com.wosai.upay.job.handlers;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * @Description: 商户信息变更的subTask
 * <AUTHOR>
 * @Date 2021/5/28 11:28 上午
 **/
@Component
@Order(98)
public class UpdateSubTaskHandler extends AbstractSubTaskHandler {

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByName(subTask.getChannel());
        ContractResponse response = provider.processTaskByRule(task,
                ruleContext.getContractRule(subTask.getContract_rule()).getContractChannel(),
                subTask);
        handleResult(response, subTask);
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        return lklV3situation(task, subTask) || tlV2situation(task, subTask);
    }

    private Boolean lklV3situation(ContractTask task, ContractSubTask subTask) {
        if (ChannelEnum.LKLV3.getValue().equalsIgnoreCase(subTask.getChannel()) && !ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type()) &&
                (ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT.equalsIgnoreCase(task.getType())
                        || ProviderUtil.CONTRACT_TYPE_UPDATE_FEERATE.equalsIgnoreCase(task.getType())
                        || ProviderUtil.CONTRACT_TYPE_UPDATE_BASIC.equalsIgnoreCase(task.getType())
                        || ProviderUtil.CONTRACT_TYPE_MERCHANT_STATUS.equalsIgnoreCase(task.getType())
                        || ProviderUtil.CONTRACT_TYPE_UPDATE_BUSINESS_LICENSE.equalsIgnoreCase(task.getType())
                        || (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equalsIgnoreCase(task.getType()) && Objects.equals(subTask.getTask_type(), ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION))
                        || (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equalsIgnoreCase(task.getType()) && Objects.equals(subTask.getTask_type(), ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE))
                        || (ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE.equalsIgnoreCase(task.getType()) && (Objects.equals(subTask.getTask_type(), ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE) || Objects.equals(subTask.getTask_type(), ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS)))

                )
        ) {
            return true;
        }
        return false;
    }

    private Boolean tlV2situation(ContractTask task, ContractSubTask subTask){
        if (AcquirerTypeEnum.TONG_LIAN_V2.getValue().equalsIgnoreCase(subTask.getChannel()) && PaywayEnum.ACQUIRER.getValue().equals(subTask.getPayway())){
            return true;
        }
        return false;
    }
}






