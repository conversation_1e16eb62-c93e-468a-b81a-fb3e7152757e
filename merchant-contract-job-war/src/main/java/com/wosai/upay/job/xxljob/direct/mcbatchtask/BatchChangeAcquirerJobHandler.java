package com.wosai.upay.job.xxljob.direct.mcbatchtask;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.ApproveConstant;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.McBatchTask;
import com.wosai.upay.job.model.DO.McBatchTaskExample;
import com.wosai.upay.job.model.acquirer.ChangeAcquirerRequest;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApprove;
import com.wosai.upay.job.model.changeAcquirerApprove.AcquirerApproveExcel;
import com.wosai.upay.job.model.changeAcquirerApprove.ChangeAcquirerApproveDTO;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ExceptionUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * 处理批量收单机构切换任务
 *
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component("BatchChangeAcquirerJobHandler")
public class BatchChangeAcquirerJobHandler extends AbstractMcBatchTaskJobHandler {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private AcquirerService acquirerService;
    @Autowired
    private AcquirerChangeDao changeDao;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private BusinessLogBiz businessLogBiz;

    @Override
    public String getLockKey() {
        return "BatchChangeAcquirerJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        McBatchTaskExample mcBatchTaskExample = new McBatchTaskExample();
        mcBatchTaskExample.or()
                .andStatusEqualTo(0)
                .andEffect_timeLessThanOrEqualTo(new Date())
                .andTypeEqualTo(5);
        List<McBatchTask> list = mcBatchTaskMapper.selectByExampleWithBLOBs(mcBatchTaskExample);
        list.forEach(mcBatchTask -> {
            Map extra = Maps.newHashMap();
            try {
                final String payload = mcBatchTask.getPayload();
                extra = CommonUtil.string2Map(payload);
                final ChangeAcquirerApproveDTO approveDTO = getChangeAcquirerApproveDTO(extra);
                final String attachmentUrl = approveDTO.getAttachmentUrls().get(0);
                final List<AcquirerApproveExcel> acquirerApproveExcels = excelUtil.getExcelInfoList(attachmentUrl, new AcquirerApproveExcel());
                final List<AcquirerApprove> approveList = acquirerApproveExcels.stream()
                        .map(acquirerApproveExcel -> getAcquirerApprove(approveDTO, approveDTO.getTarget(), acquirerApproveExcel))
                        .collect(Collectors.toList());
                //将approveList集合变成文件上传到oss中
                final String url = excelUtil.uploadToOss(approveList, BASE_DIR);
                //初步处理的文件
                extra.put(ApproveConstant.LAST_ATTACHMENT_URL, url);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(1).setId(mcBatchTask.getId()).setPayload(JSONObject.toJSONString(extra)).setResult("处理中"));
            } catch (Exception e) {
                log.error("handleBatchAcquirerApproveChange error mc_batch_task {}  exception", mcBatchTask.getId(), e);
                mcBatchTaskMapper.updateByPrimaryKeySelective(new McBatchTask().setStatus(3).setId(mcBatchTask.getId()).setResult(ExceptionUtil.getThrowableMsg(e)));
                callBack(extra, ExceptionUtil.getThrowableMsg(e), AUDIT_EXECUTE_FAIL);
            }
        });
    }

    private AcquirerApprove getAcquirerApprove(ChangeAcquirerApproveDTO dto, String target, AcquirerApproveExcel acquirerApproveExcel) {
        //将acquirerApproveExcel实例转为acquirerApprove实例
        final AcquirerApprove acquirerApprove = new AcquirerApprove();
        final String merchantSn = acquirerApproveExcel.getMerchantSn();
        acquirerApprove.setMerchantSn(merchantSn);
        acquirerApprove.setRemark(acquirerApproveExcel.getRemark());
        //插入切换收单机构任务并返回对应的任务Id
        Integer applyId = null;
        try {
            //插入切换收单机构任务并返回对应的任务Id
            ChangeAcquirerRequest request = new ChangeAcquirerRequest();
            request.setMerchantSn(merchantSn);
            request.setAcquirer(target);
            request.setImmediately(dto.getImmediate());
            request.setTradeAppId(dto.getTradeAppId());
            request.setCancellable(Boolean.TRUE);
            McAcquirerChange change = doApplyChangeAcquirer(request);
            if (Objects.nonNull(change)) {
                applyId = change.getId();
                AbstractAcquirerChangeBiz changeBiz = applicationContext.getBean(change.getTarget_acquirer() + "-AcquirerChangeBiz", AbstractAcquirerChangeBiz.class);
                // 批量提交时，如果没进件，则间隔 5 秒
                if (!changeBiz.hasContract(change)) {
                    SECONDS.sleep(5);
                }
            }
        } catch (Exception exception) {
            //部分商户或出现校验失败需要先记录原因
            log.error("批量申请切换收单异常商户:{}", merchantSn, exception);
            String result = exception.getLocalizedMessage();
            acquirerApprove.setResult(StringUtils.isEmpty(result) ? "申请失败" : result);
        }
        if (Objects.nonNull(applyId)) {
            acquirerApprove.setApplyId(String.valueOf(applyId));
            //记录商户日志
            recordMerchantLog(dto, merchantSn, acquirerApprove.getRemark());
        }
        return acquirerApprove;
    }

    private void recordMerchantLog(ChangeAcquirerApproveDTO dto, String merchantSn, String remark) {
        final String target = dto.getTarget();
        final String operator = dto.getOperator();
        final String operatorName = dto.getOperatorName();
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        final ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        final String sourceAcquirer = contractStatus.getAcquirer();
        final String auditSn = dto.getAuditSn();
        businessLogBiz.sendChangeAcquirerNewLog(merchantId, sourceAcquirer, target, operator, operatorName, "审批编号:" + auditSn + " 申请原因:" + remark);
    }

    private McAcquirerChange doApplyChangeAcquirer(ChangeAcquirerRequest request) {
        acquirerService.applyChangeAcquirer(request);
        return changeDao.getLatestUnFinishedApply(request.getMerchantSn());
    }

    public ChangeAcquirerApproveDTO getChangeAcquirerApproveDTO(Map extra) {
        return new ObjectMapper().convertValue(MapUtils.getMap(extra, ApproveConstant.APPROVE_INFO), ChangeAcquirerApproveDTO.class);
    }
}
