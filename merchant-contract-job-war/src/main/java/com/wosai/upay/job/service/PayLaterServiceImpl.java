package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.wosai.bsm.creditpaybackend.service.FitnessNotifyService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeComboDetailService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.TradeComboDetailResult;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AgreementBiz;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.biz.ZftBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.mapper.ZftMerchantApplyMapper;
import com.wosai.upay.job.model.payLater.*;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.payLater.UpdateDTO;
import com.wosai.upay.merchant.contract.service.FitnessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 先享后付实现
 * <AUTHOR>
 * @Date 2023/7/31 下午5:37
 */
@Slf4j
@Service
@AutoJsonRpcServiceImpl
public class PayLaterServiceImpl implements PayLaterService {
    @Autowired
    PayLaterApplyMapper payLaterApplyMapper;

    @Autowired
    ZftMerchantApplyMapper zftMerchantApplyMapper;

    @Autowired
    PayLaterBiz payLaterBiz;

    @Autowired
    FitnessService fitnessService;

    @Autowired
    ZftBiz zftBiz;

    @Autowired
    private MerchantService merchantService;

    @Value("${indirect-pay.dev_code}")
    private String devCode;
    @Autowired
    private MerchantBusinessLicenseService mcMerchantBusinessLicenseService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private FitnessNotifyService fitnessNotifyService;
    @Autowired
    private AopBiz aopBiz;

    @Autowired
    private AgreementBiz agreementBiz;

    @Value("${pay_later.devCode}")
    public String payLaterDevcode;

    @Value("${pay_later.zhiMaTemplateCode}")
    public String payLaterZhimatemplatecode;
    @Value("${pay_later.zftTemplateCode}")
    public String payLaterZftTemplateCode;
    @Autowired
    private FeeRateService feeRateService;
    @Value("${pay_later.combId}")
    public Long payLaterCombId;
    @Autowired
    private TradeComboDetailService tradeComboDetailService;

    @Override
    public Boolean displayIntroduce(Map params) {
        String merchantSn = getMerchantByParams(params).getSn();
        PayLaterApply payLaterApply = payLaterApplyMapper.selectByMerchantSn(merchantSn);
        return Objects.isNull(payLaterApply);
    }

    @Override
    public String computeFeeRateByIndustry(Map params) {
        MerchantInfo merchant = getMerchantByParams(params);
        String industry = merchant.getIndustry();
        Map<String,String> industryFeeMap  = applicationApolloConfig.getPayLaterIndustryFee();
        boolean containsKey = industryFeeMap.containsKey(industry);
        if(!containsKey) {
            throw new CommonPubBizException("当前行业不支持");
        }
        return BeanUtil.getPropString(industryFeeMap,industry);
    }



    @Override
    public String getMerchantBusinessLicenseName(Map params) {
        MerchantInfo merchant = getMerchantByParams(params);
        MerchantBusinessLicenseInfo license = mcMerchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), devCode);
        MerchantBusinessLicenseInfo licenseInfo = Optional.ofNullable(license).filter(info -> !StringUtils.isEmpty(info.getLegal_person_name()))
                .orElseThrow(() -> new CommonPubBizException("法人不存在"));
        return licenseInfo.getLegal_person_name();
    }

    @Override
    public Boolean haveToLookSuccess(Map params) {
        //判断当前商户是否开通成功
        String merchantSn = getMerchantByParams(params).getSn();
        PayLaterApply laterApply = payLaterApplyMapper.selectByMerchantSn(merchantSn);
        if(Objects.isNull(laterApply) || !Objects.equals(laterApply.getStatus(),PayLaterConstant.Status.SUCCESS)) {
            return Boolean.FALSE;
        }
        final String redisKey = merchantSn + ":" +laterApply.getId();
        //审核成功并且看过审核通过页面
        if(redisTemplate.hasKey(redisKey)) {
            return Boolean.FALSE;
        }
        //添加key
        redisTemplate.opsForValue().set(redisKey, merchantSn);
        return Boolean.TRUE;
    }

    @Override
    public AppViewStatus getAppStatusView(Map params) {
        String merchantSn = getMerchantByParams(params).getSn();
        PayLaterApply payLaterApply = Optional.ofNullable(payLaterApplyMapper.selectByMerchantSn(merchantSn)).orElseGet(PayLaterApply::new);
        AppViewStatus appViewStatus = new AppViewStatus();
        appViewStatus.setStatus(payLaterApply.getStatus());
        appViewStatus.setSubStatus(payLaterApply.getSub_status());
        appViewStatus.setResult(payLaterApply.getResult());
        return appViewStatus;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePayLater(@Valid PayLaterInfoDTO payLaterInfoDTO) {
         String merchantSn = payLaterInfoDTO.getMerchantSn();
        //校验
        check(merchantSn);
        PayLaterApply payLaterApply = payLaterApplyMapper.selectByCondition(merchantSn, payLaterInfoDTO.getAccount());
        Integer status = Optional.ofNullable(payLaterApply).orElseGet(PayLaterApply::new).getStatus();
        //已开通但还未结束
        if(!PayLaterApply.isFinish(status) && Objects.nonNull(status)) {
            log.info("savePayLater payLaterApply: {}", JSONObject.toJSONString(payLaterApply));
            return;
        }
        if(PayLaterConstant.Status.SUCCESS.equals(status)) {
            log.info("savePayLater 已经开通成功 merchantSn:{}",merchantSn);
            return;
        }
        //先保存直付通商户
        ZftMerchantApply zftMerchantApply = zftBiz.saveZftMerchantApply(payLaterInfoDTO);
        MerchantInfo merchant = Optional.ofNullable(merchantService.getMerchantBySn(merchantSn,devCode)).orElseGet(MerchantInfo::new);
        String industry = merchant.getIndustry();
        Map<String,String> industryFeeMap  = applicationApolloConfig.getPayLaterIndustryFee();
        double feeRate = MapUtils.getDoubleValue(industryFeeMap, industry);
        //保存先享后付记录
        payLaterBiz.savePayLaterApply(payLaterInfoDTO,zftMerchantApply.getId(),feeRate);
        //TODO 保存协议
        agreementBiz.recordAgreementForCredit(merchantSn);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fitnessMerchantSyncStatus(Map<String, String> contentMap) {
        FitnessMerchantSyncStatus syncStatus = JSONObject.parseObject(JSONObject.toJSONString(contentMap), FitnessMerchantSyncStatus.class);
        log.info("fitnessMerchantSyncStatus param:{}",JSONObject.toJSONString(contentMap));
        String notifyId = syncStatus.getNotifyId();
        String bizTime = syncStatus.getBizTime();
        String merchantPid = syncStatus.getMerchantPid();
        ZftMerchantApply zftMerchantApply = zftMerchantApplyMapper.selectBySmid(merchantPid);
        if(Objects.isNull(zftMerchantApply)) {
            log.info("fitnessMerchantSyncStatus zftMerchantApply 记录为空");
            return;
        }
        Long zftMerchantApplyId = zftMerchantApply.getId();
        PayLaterApply payLaterApply = payLaterApplyMapper.selectByZftMerchantApplyId(zftMerchantApplyId);
        if(Objects.isNull(payLaterApply)) {
            log.info("fitnessMerchantSyncStatus payLaterApply 记录为空");
            return;
        }
        if(payLaterApply.getProcess_status().equals(PayLaterConstant.ProcessStatus.ZHIMA_SUCCESS)) {
            log.info("fitnessMerchantSyncStatus payLaterApply 已经成功");
            return;
        }
        if (!payLaterApply.needProcessNotify(notifyId, bizTime)) {
            log.info("fitnessMerchantSyncStatus payLaterApply 不需要处理该回调通知");
            return;
        }
        // 先记录一下此次回调的信息
        payLaterApply.recordNotifyInfo(notifyId, bizTime);
        payLaterApplyMapper.updateByPrimaryKeySelective(payLaterApply);

        if(syncStatus.isReviewFail()) {
            //支付宝回调通知可能是 审核中->审核失败->审核中->审核成功
            String failReason = syncStatus.getReviewFailReason();
            String failMessage = StringUtils.isEmpty(failReason) ? "芝麻商户审核失败" : failReason;
            payLaterBiz.modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ZHIMA_FAIL,
                    PayLaterConstant.SubStatus.FAIL,
                    PayLaterConstant.ProcessStatus.FAIL,
                    failMessage,0);
            return;
        }
        // 如果支付宝回调审核中，并且该申请单还是失败，需要进行状态的回退
        if (syncStatus.isUnderReview()) {
            if (payLaterApply.getStatus().equals(PayLaterConstant.Status.ZHIMA_FAIL)) {
                payLaterBiz.modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ZHIMA_APPLYING,
                        PayLaterConstant.SubStatus.ZHIMA_APPLYING,
                        PayLaterConstant.ProcessStatus.ZHIMA_APPLYING,
                        "芝麻商户申请中", 0);
                return;
            }
        }
        // 如果审核通过，则进去代授权阶段
        if(syncStatus.isReviewSuccess()) {
            payLaterBiz.modifyPayLaterApply(payLaterApply,PayLaterConstant.Status.ANT_SHOP_APPLYING,
                    PayLaterConstant.SubStatus.ANT_SHOP_APPLYING,
                    PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING, PayLaterConstant.Result.ANT_SHOP_APPLYING
                    ,0);
            MerchantInfo merchantInfo = merchantService.getMerchantBySn(payLaterApply.getMerchant_sn(), null);
            aopBiz.sendNoticeToAdmin(merchantInfo.getId(), payLaterDevcode, payLaterZftTemplateCode, new HashMap<>());
        }

    }

    /**
     * 设置先享后付套餐
     * @param merchantSn
     * @return
     */
    public ApplyFeeRateRequest applyFeeRateRequest(String merchantSn) {
        List<TradeComboDetailResult> comboDetails = tradeComboDetailService.listByComboId(payLaterCombId);
        Map feeRateMap = new HashMap();
        for (TradeComboDetailResult comboDetail : comboDetails) {
            feeRateMap.put(String.valueOf(comboDetail.getPayway()), comboDetail.getFeeRateMax());
        }
        ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                .setMerchantSn(merchantSn)
                .setAuditSn("签单易")
                .setTradeComboId(payLaterCombId)
                .setApplyFeeRateMap(feeRateMap);
        feeRateService.applyFeeRateOne(applyFeeRateRequest);
        log.info("设置先享后付套餐 doCombo merchantSn:{},applyFeeRateRequest:{}",merchantSn,JSONObject.toJSONString(applyFeeRateRequest));
        return applyFeeRateRequest;
    }

    @Override
    public void modifyFitnessFeeRate(ModifyFeeRate modifyFeeRate) {
        String merchantSn = modifyFeeRate.getMerchantSn();
        PayLaterApply laterApply = payLaterApplyMapper.selectByMerchantSn(merchantSn);
        if(Objects.isNull(laterApply) || !Objects.equals(laterApply.getSub_status(),PayLaterConstant.SubStatus.SUCCESS)) {
            throw new CommonPubBizException("当前商户业务未开通成功,暂不支持修改费率");
        }
        //费率相同不用更新
        Map<String, Object> extraMap = laterApply.getExtraMap();
        Double originFeeRate = MapUtils.getDouble(extraMap, PayLaterConstant.Extra.FEERATE);
        if(Objects.equals(originFeeRate,Double.valueOf(modifyFeeRate.getTargetFeeRate()))) {
            return;
        }
        try {
            //设置费率
            MerchantInfo merchant = merchantService.getMerchantBySn(merchantSn,null);
            String merchantId = merchant.getId();
            String antShopId = BeanUtil.getPropString(extraMap, PayLaterConstant.Extra.ANT_SHOP_ID);
            BigDecimal fee = BigDecimal.valueOf(Double.valueOf(modifyFeeRate.getTargetFeeRate()));
            Long merchantApplyId = laterApply.getZft_merchant_apply_id();
            ZftMerchantApply merchantApply = zftMerchantApplyMapper.selectByPrimaryKey(merchantApplyId);
            String smid = merchantApply.getSmid();
            tradeConfigService.updateFitnessMerchantAppConfig( CollectionUtil.hashMap("merchant_id", merchantId,
                    "merchant_pid", smid,
                    "shop_id", antShopId,
                    "fee_rate", String.valueOf(fee)));
            Map updateMap = CollectionUtil.hashMap("merchant_id", merchantId,
                    "status",1);
            fitnessNotifyService.update(updateMap);
        } catch (Exception exception) {
            log.error("modifyFitnessFeeRate merchant_sn :{} exception :{}",merchantSn,exception);
            throw new CommonPubBizException("费率修改失败");
        }

        Map map = MapUtils.getMap(extraMap, PayLaterConstant.Extra.BIZ_CONTENT);
        UpdateDTO updateDTO = new UpdateDTO();
        updateDTO.setBizContent(map);
        updateDTO.setFeeRate(Double.valueOf(modifyFeeRate.getTargetFeeRate()));
        ContractResponse response = fitnessService.update(updateDTO);
        if(!response.isSuccess()) {
            String message = StringUtils.isEmpty(response.getMessage()) ? "芝麻商户修改费率失败" : response.getMessage();
            throw new CommonPubBizException(message);
        }
        //更新记录为最新的费率
        extraMap.put(PayLaterConstant.Extra.FEERATE,Double.valueOf(modifyFeeRate.getTargetFeeRate()));
        laterApply.setExtra(JSONObject.toJSONString(extraMap));
        payLaterBiz.updatePayLater(laterApply);

    }

    @Override
    public List<SpaStage> getSpaProcess(String merchantSn) {
        PayLaterApply payLaterApply = payLaterApplyMapper.selectByMerchantSn(merchantSn);
        Map<String, Object> extraMap = payLaterApply.getExtraMap();
        final String spaStatusStr = BeanUtil.getPropString(extraMap, PayLaterConstant.Extra.SPA_STAGE);
        List<SpaStage> spaStages = JSONObject.parseArray(spaStatusStr, SpaStage.class);
        return spaStages;
    }

    @Override
    public String getMerchantAppId(String merchantId) {
        MerchantInfo merchantInfo = merchantService.getMerchantById(merchantId, null);
        String industryId = merchantInfo.getIndustry();
        Map zhimaMerchantAppIdConfig = applicationApolloConfig.getZhimaMerchantAppId();
        Map industryConfig = WosaiMapUtils.getMap(zhimaMerchantAppIdConfig, "industry_config");
        return WosaiMapUtils.getString(industryConfig, industryId, WosaiMapUtils.getString(industryConfig, "default"));
    }


    /**
     * 行业和营业执照校验
     * @param merchantSn
     */
    public void check(String merchantSn) {
        MerchantInfo merchant = Optional.ofNullable(merchantService.getMerchantBySn(merchantSn,devCode)).orElseGet(MerchantInfo::new);
        String industry = merchant.getIndustry();
        Map<String,String> industryFeeMap  = applicationApolloConfig.getPayLaterIndustryFee();
        boolean containsKey = industryFeeMap.containsKey(industry);
        if(!containsKey) {
            throw new CommonPubBizException("当前行业不支持");
        }
        MerchantBusinessLicenseInfo license = mcMerchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchant.getId(), devCode);
        Optional.ofNullable(license).filter(info -> Objects.nonNull(info.getType()) && !Objects.equals(info.getType(),BusinessLicenseTypeEnum.MICRO.getValue()))
                .orElseThrow(() -> new CommonPubBizException("当前商户营业执照类型不支持"));
        String businessName = merchant.getBusiness_name();
        boolean valid = checkBusinessName(businessName);
        if (!valid) {
            throw new CommonPubBizException("商户经营名称不符合申请标准，请联系您的业务员修改后重新提交");
        }
    }

    private boolean checkBusinessName(String businessName) {
        if (WosaiStringUtils.isEmpty(businessName)) {
            return false;
        }
        // 1.长度不超过8个中文字（不可以标点符号，1个英文字母算半个中文字）
        if (!checkBusinessNameLength(businessName)) {
            return false;
        }
        // 2.不出现禁用词
        List<String> payLaterForbidWords = applicationApolloConfig.getPayLaterForbidWords();
        return payLaterForbidWords.stream().noneMatch(businessName::contains);
    }

    private boolean checkBusinessNameLength(String businessName) {
        int chineseCount = 0;
        int englishCount = 0;
        for (int i = 0; i < businessName.length(); i++) {
            char c = businessName.charAt(i);
            if (isChineseCharacter(c)) {
                chineseCount++;
            } else if (isEnglishCharacter(c)) {
                englishCount++;
            } else if (isNumberCharacter(c)) {
                englishCount++;
            } else {
                return false;
            }
        }
        // 英文字母算半个中文字
        double totalCount = chineseCount + (englishCount * 0.5);
        if (totalCount > 8) {
            return false;
        }
        return true;
    }

    private boolean isNumberCharacter(char c) {
        return c >= '0' && c <= '9';
    }

    private boolean isChineseCharacter(char c) {
        // 判断字符是否为中文字符
        // 根据Unicode编码范围判断，中文字符的Unicode编码范围为4E00-9FA5
        return c >= '\u4E00' && c <= '\u9FA5';
    }

    private boolean isEnglishCharacter(char c) {
        // 判断字符是否为英文字符
        // 根据ASCII编码范围判断，英文字符的ASCII编码范围为65-90（大写字母）和97-122（小写字母）
        return (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z');
    }

    /**
     * 根据网关传过来的参数获取商户信息
     */

    public MerchantInfo getMerchantByParams(Map params) {
        String merchantId = BeanUtil.getPropString(params, "merchant_id");
        MerchantInfo merchant = Optional.ofNullable(merchantService.getMerchantById(merchantId, null))
                .orElseThrow(() -> new CommonPubBizException("商户不存在"));
        return merchant;
    }
}
