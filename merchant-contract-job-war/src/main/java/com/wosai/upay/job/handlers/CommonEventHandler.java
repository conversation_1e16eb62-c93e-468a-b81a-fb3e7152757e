package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.service.PaywayActivityService;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeCheckBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.util.CrmUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.FuYouConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Event 通用处理
 *
 * <AUTHOR>
 * @date 2019-07-04
 */
@Component
@Slf4j
@Order(99)
public class CommonEventHandler extends AbstractEventHandler<Void> {

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ProviderFactory providerFactory;

    @Autowired
    private ComposeAcquirerBiz acquirerBiz;

    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private DataBusBiz dataBusBiz;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    private BankCardServiceImpl bankCardService;

    @Autowired
    private ContractTaskBiz contractTaskBiz;

    @Autowired
    private AcquirerChangeCheckBiz acquirerChangeCheckBiz;
    @Autowired
    private PaywayActivityService paywayActivityService;

    @Autowired
    private EventTaskConflictBiz conflictBiz;

    @Override
    public boolean supports(ContractEvent event) {
        return event.getEvent_type() != ContractEvent.OPT_TYPE_UPDATE_MERCHANT_DATA;
    }

    @Override
    protected void handleError(ContractEvent event, Exception e) {
        if (e instanceof ContextParamException) {
            log.error("contractEvent {} merchantSn {} get contextParamException", event.getId(), event.getMerchant_sn(), e);
            event.setStatus(ContractEvent.STATUS_BIZ_FAIL);
            event.setResult(JSON.toJSONString(MapUtil.hashMap("className:", e.getClass().getName(), "cause", e.getMessage())));
            contractEventMapper.updateByPrimaryKeySelective(event);
        } else {
            log.error("contractEvent {} merchantSn {} get unkownException", event.getId(), event.getMerchant_sn(), e);
            if (event.getVersion() > 4) {
                event.setStatus(ContractEvent.STATUS_BIZ_FAIL).setResult(e.getMessage());
            }
            event.setVersion(event.getVersion() + 1);
            contractEventMapper.updateByPrimaryKeySelective(event);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Void doHandle(ContractEvent event) {
        // 前置检查
        if (!preCheck(event)) {
            return null;
        }

        Map<String, Object> paramContext = paramContextBiz.getParamContextBySnAndEvent(event.getMerchant_sn(), event);
        if (ContractEvent.OPT_TYPE_FAIL == event.getEvent_type()) {
            createFailTask(paramContext, event);
            return null;
        }

        if (ContractEvent.OPT_TYPE_NET_CRM_UPDATE == event.getEvent_type() || ContractEvent.OPT_TYPE_RE_AUTH == event.getEvent_type()) {
            crmReContract(paramContext, event);
            return null;
        }
        if (isInsertContract(event)) {
            handleInsert(event, paramContext);
        } else {
            handleUpdate(event, paramContext);
        }
        return null;
    }

    /**
     * 判断事件是否需要报备
     *
     * @param event
     * @return true：需要报备  false：不需要报备
     */
    private boolean preCheck(ContractEvent event) {
        return !shouldSkip(event) && !conflictBiz.conflictWithPendingTasks(event);
    }

    /**
     * 判断是否需要跳过报备
     *
     * @param event
     * @return true：跳过  false：不跳过
     */
    private boolean shouldSkip(ContractEvent event) {
        // 入网事件、crm更新事件、实名认证重新提交不跳过
        if (event.getEvent_type() == ContractEvent.OPT_TYPE_NET_IN ||
                event.getEvent_type() == ContractEvent.OPT_TYPE_NET_CRM_UPDATE ||
                event.getEvent_type() == ContractEvent.OPT_TYPE_RE_AUTH ||
                event.getEvent_type() == ContractEvent.OPT_TYPE_FAIL) {
            return false;
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
        int status;
        if (contractStatus == null) {
            status = ContractStatus.STATUS_PENDING;
        } else {
            status = contractStatus.getStatus();
        }
        MerchantProviderParamsExample dto = new MerchantProviderParamsExample();
        dto.createCriteria().andMerchant_snEqualTo(event.getMerchant_sn()).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue());
        List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(dto);
        if (!CollectionUtils.isEmpty(merchantProviderParams)) {
            // 富友23点50到0点20分不允许提交
            LocalTime currentTime = LocalTime.now();

            // 设置检查的开始时间和结束时间
            LocalTime startTime = LocalTime.of(23, 50);
            LocalTime endTime = LocalTime.of(0, 20);

            // 比较当前时间是否在指定范围内
            if (currentTime.isAfter(startTime) || currentTime.isBefore(endTime)) {
                return true;
            }
        }
        // 进件失败时，只有 merchant_bank_account_pre 表变更不跳过
        if (status == ContractStatus.STATUS_BIZ_FAIL) {
            Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
            String tableName = BeanUtil.getPropString(eventMsg, ConstantsEvent.EVENT_TYPE_TABLE_NAME, null);
            if (!"merchant_bank_account_pre".equals(tableName)) {
                log.info("crm进件失败更新事件忽略 merchantSn {}", event.getMerchant_sn());
                event.setStatus(ContractEvent.STATUS_BIZ_FAIL).setResult("crm进件失败更新事件忽略");
                contractEventMapper.updateByPrimaryKeySelective(event);
                return true;
            } else {
                // 为了查看进件失败时，有多少换卡
                log.info(" {} 换卡操作", event.getMerchant_sn());
            }
        }
        return false;
    }

    /**
     * 判断是新增报备还是更新
     *
     * @param event
     * @return
     */
    private boolean isInsertContract(ContractEvent event) {
        if (acquirerChangeCheckBiz.acquirerChange(event.getRule_group_id())) {
            return true;
        }
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(event.getMerchant_sn());
        if (contractStatus == null) {
            return true;
        }
        if (contractStatus.getStatus() == ContractStatus.STATUS_SUCCESS) {
            return false;
        }
        event.setRule_group_id(acquirerBiz.getMerchantDefaultRuleGroup(event.getMerchant_sn(), ""));
        return true;
    }

    /**
     * 入网要根据规则的依赖关系 插入任务
     *
     * @param:
     * @return:
     * @date: 9:33
     */
    public void handleInsert(ContractEvent event, Map<String, Object> paramContext) {
        String ruleGroupId = getRuleGroupId(event);
        RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
        addFuYouRuleGroupRuleItem(ruleGroupId, ruleGroup, paramContext);
        List<ContractSubTask> subTaskList = new ArrayList<>();
        Map<String, ContractSubTask> subTaskMap = new HashMap<>();
        Integer affect_sub_task_count = 0;
        for (RuleItem ruleItem : ruleGroup.getRules()) {
            BasicProvider provider = providerFactory.getProviderByContractRule(ruleItem.getContractRule());
            if (provider != null) {
                ContractSubTask subTask = provider.produceTaskByRule(event.getMerchant_sn(), event, paramContext, ruleItem.getContractRule());
                if (subTask != null) {
                    subTask.setRule_group_id(ruleGroup.getGroup_id());
                    subTaskList.add(subTask);
                    subTaskMap.put(ruleItem.getRule(), subTask);
                    if (1 == subTask.getStatus_influ_p_task()) {
                        affect_sub_task_count++;
                    }
                }
            }
        }
        //正常入网任务
        if (subTaskList.isEmpty() || affect_sub_task_count == 0) {
            //无任何子任务生成
            event.setResult("no subtask create nothing need to do").setStatus(5);
            contractEventMapper.updateByPrimaryKeySelective(event);
            return;
        }
        createMerchantContractTask(paramContext, event, affect_sub_task_count, ruleGroup, subTaskList, subTaskMap);

    }



    /**
     * 小微升级,重新在收单机构进件
     *
     * @param:
     * @return:
     * @date: 9:33
     */
    public Long handleMicroUpgradeContractInsertTask(String merchantSn,Map<String, Object> paramContext,String ruleGroupId) {
        RuleGroup ruleGroup = ruleContext.getRuleGroup(ruleGroupId);
        addFuYouRuleGroupRuleItem(ruleGroupId, ruleGroup, paramContext);
        List<ContractSubTask> subTaskList = new ArrayList<>();
        Map<String, ContractSubTask> subTaskMap = new HashMap<>();
        Integer affect_sub_task_count = 0;
        for (RuleItem ruleItem : ruleGroup.getRules()) {
            BasicProvider provider = providerFactory.getProviderByContractRule(ruleItem.getContractRule());
            if (provider != null) {
                ContractSubTask subTask = provider.produceMicroUpgradeTaskByRule(merchantSn, paramContext, ruleItem.getContractRule(),ruleGroupId);
                if (subTask != null) {
                    subTask.setRule_group_id(ruleGroup.getGroup_id());
                    subTaskList.add(subTask);
                    subTaskMap.put(ruleItem.getRule(), subTask);
                    if (1 == subTask.getStatus_influ_p_task()) {
                        affect_sub_task_count++;
                    }
                }
            }
        }
        //正常入网任务
        if (subTaskList.isEmpty() || affect_sub_task_count == 0) {
           log.warn("商户:{}小微升级重新入网没有生成入网任务",merchantSn);
           throw new ContractBizException("小微升级重新入网没有生成入网任务");
        }
       return createMicroUpgradeMerchantContractTask(merchantSn,paramContext, affect_sub_task_count, ruleGroup, subTaskList, subTaskMap);

    }


    public void addFuYouRuleGroupRuleItem(String ruleGroupId, RuleGroup ruleGroup, Map<String, Object> paramContext) {
        if (ruleGroupId.contains("fuyou") && com.wosai.upay.job.config.ApolloConfig.getFuYouSpecialIndustryApplySwitch()) {
            Map merchantMap = WosaiMapUtils.getMap(paramContext, "merchant");
            Map<String, String> mapping = ApolloConfig.getNetFuYouSpecChNlTypeAndBusinessCodeAndChannelNoMapping(WosaiMapUtils.getString(merchantMap, "industry"));
            if (MapUtil.isNotEmpty(mapping)) {
                ContractRule contractRule = ruleContext.getContractRule(FuYouConstant.WECHAT_CHANNEL_PREFIX
                        + WosaiMapUtils.getString(mapping, FuYouConstant.CHANNEL_NO)
                        + (ruleGroupId.contains("change") ? "-ncp" : ""));
                RuleItem ruleItem = new RuleItem();
                ruleItem.setContractRule(contractRule);
                ruleItem.setDepend_on(ruleContext.getContractRule(FuYouConstant.WECHAT_CHANNEL_NORMAL
                        + (ruleGroupId.contains("change") ? "-ncp" : "")));
                ruleGroup.getRules().add(ruleItem);
            }
        }
    }

    private void createMerchantContractTask(Map paramContext, ContractEvent event, Integer affect_sub_task_count, RuleGroup ruleGroup, List<ContractSubTask> subTaskList, Map<String, ContractSubTask> subTaskMap) {
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        //已经成功的子任务
        final int successCount = (int) subTaskList.stream()
                .filter(sub -> Objects.equals(sub.getStatus_influ_p_task(), 1)
                        && Objects.equals(sub.getStatus(), SubTaskStatus.SUCCESS.getVal())).count();

        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(event.getMerchant_sn())
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affect_sub_task_count)
                .setAffect_status_success_task_count(successCount)
                .setRule_group_id(ruleGroup.getGroup_id());
        //建行线下导入随机推迟几秒
        String fromFlag = BeanUtil.getPropString(paramContext, "from");
        if (Objects.equals(fromFlag, "batch_import")) {
            Random random = new Random();
            int delay = random.nextInt(10) + 5;
            contractTask.setPriority(DateUtils.addMinutes(new Date(), delay));
        }
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        Map depends = new HashMap();
        for (ContractSubTask subTask : subTaskList) {
            insertSubTasks(ruleGroup, subTask, subTaskMap, depends, pTaskId);
        }
        //银行直连不会在数据库创建一个event
        if (Objects.isNull(event.getId())) {
            return;
        }
        event.setTask_id(contractTask.getId());
        event.setStatus(ContractEvent.STATUS_SUCCESS);
        contractEventMapper.updateByPrimaryKeySelective(event);
        dataBusBiz.insert(1, event.getMerchant_sn(), null);
    }


    private Long createMicroUpgradeMerchantContractTask(String merchantSn,
                                                        Map paramContext,
                                                        Integer affect_sub_task_count,
                                                        RuleGroup ruleGroup,
                                                        List<ContractSubTask> subTaskList,
                                                        Map<String, ContractSubTask> subTaskMap) {
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        //已经成功的子任务
        final int successCount = (int) subTaskList.stream()
                .filter(sub -> Objects.equals(sub.getStatus_influ_p_task(), 1)
                        && Objects.equals(sub.getStatus(), SubTaskStatus.SUCCESS.getVal())).count();

        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affect_sub_task_count)
                .setAffect_status_success_task_count(successCount)
                .setRule_group_id(ruleGroup.getGroup_id());

        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        Map depends = new HashMap();
        for (ContractSubTask subTask : subTaskList) {
            insertSubTasks(ruleGroup, subTask, subTaskMap, depends, pTaskId);
        }
        return pTaskId;
    }



    /**
     * 根据规则组 规则的依赖关系 生成子任务 这里通过依赖规则和递归完成任务可调度的状态判断
     *
     * @param:
     * @return:
     * @date: 9:12
     */
    private void insertSubTasks(RuleGroup ruleGroup, ContractSubTask subTask, Map<String, ContractSubTask> subTaskMap, Map<String, Long> rules, Long taskId) {
        String rule = subTask.getContract_rule();
        if (rules.get(rule) != null) {
            return;
        }
        RuleItem ruleItem = getByRule(rule, ruleGroup);
        ContractRule depend = ruleItem.getDepend_on();
        if (depend == null) {
            subTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setP_task_id(taskId);
            contractSubTaskMapper.insert(subTask);
            rules.putIfAbsent(rule, subTask.getId());
            return;
        }
        String dependRule = depend.getRule();
        Long id = rules.get(dependRule);
        final ContractSubTask dependSubTask = subTaskMap.get(dependRule);
        if (id == null) {
            //如果依赖的规则并没有生成 subTaskMap.get(dependRule)==null
            if (dependSubTask == null) {
                subTask.setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()).setP_task_id(taskId);
                contractSubTaskMapper.insert(subTask);
                rules.putIfAbsent(rule, subTask.getId());
                return;
            }
            insertSubTasks(ruleGroup, dependSubTask, subTaskMap, rules, taskId);
        }
        id = rules.get(dependRule);
        subTask.setP_task_id(taskId)
                .setSchedule_status(Objects.equals(dependSubTask.getStatus(), SubTaskStatus.SUCCESS.getVal()) ? ScheduleEnum.SCHEDULE_ENABLE.getValue(): ScheduleEnum.SCHEDULE_DISABLE.getValue())
                .setSchedule_dep_task_id(id);
        contractSubTaskMapper.insert(subTask);
        rules.putIfAbsent(rule, subTask.getId());
    }


    private RuleItem getByRule(String rule, RuleGroup ruleGroup) {
        for (RuleItem item : ruleGroup.getRules()) {
            if (rule.equals(item.getRule())) {
                return item;
            }
        }
        throw new IllegalArgumentException("根据rule" + rule + "无法获取RuleItem");
    }

    /**
     * 更新 按规则生成每条任务，没有依赖关系
     *
     * @param event
     * @param paramContext
     */
    private void handleUpdate(ContractEvent event, Map<String, Object> paramContext) {
        String merchantSn = event.getMerchant_sn();

        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andDeletedEqualTo(false);
        List<MerchantProviderParams> records = merchantProviderParamsMapper.selectByExample(example);

        records = setRule(records);


        if (CollectionUtils.isEmpty(records)) {
            event.setStatus(6).setResult("该商户无报备记录，不生成任何更新任务");
            contractEventMapper.updateByPrimaryKeySelective(event);
            log.info("该商户 {} 无报备记录，不生成任何更新任务", event.getMerchant_sn());
            return;
        }
        records = distinctByRule(records);

        if (ContractEvent.OPT_TYPE_MERCHANT_FEERATE == event.getEvent_type() && event.getEvent_msg().contains("app_id")) {
            Map msg = (Map) JSONObject.parseArray(BeanUtil.getPropString(JSONObject.parse(event.getEvent_msg()), "msg")).get(0);
            int provider = BeanUtil.getPropInt(msg, "provider");
            if (provider == ProviderEnum.PROVIDER_LKLORG.getValue()) {
                records = records.stream().filter(params -> params.getProvider() == provider || params.getProvider() == ProviderEnum.PROVIDER_LAKALA_V3.getValue().intValue()).collect(Collectors.toList());
            } else {
                records = records.stream().filter(params -> params.getProvider() == provider).collect(Collectors.toList());
            }
        }

        List<ContractSubTask> contractSubTaskList = new ArrayList<>(records.size());
        Integer affectCount = 0;
        for (MerchantProviderParams record : records) {
            String rule = record.getContract_rule();
            String groupId = record.getRule_group_id();
            ContractRule contractRule;
            //获取默认规则
            if (WosaiStringUtils.isEmpty(rule)) {
                contractRule = ruleContext.getDefaultRule(String.valueOf(record.getProvider()), record.getPayway(), record.getChannel_no());
                if (contractRule == null) {
                    log.error("存量商户无法加载默认规则 {}", record);
                    continue;
                }
            } else {
                try {
                    //获取具体规则信息
                    contractRule = ruleContext.getContractRule(rule);
                } catch (CommonPubBizException e) {
                    log.info("更新处理事件规则被禁用或规则不存在无需更新", record);
                    continue;
                }
            }
            //生成任务
            BasicProvider provider = providerFactory.getProviderByContractRule(contractRule);
            if (Objects.isNull(provider)) {
                log.error("无相关provider, 商户号 : {} ,  ContractRule : {}", event.getMerchant_sn(), JSON.toJSONString(contractRule));
                continue;
            }

            ContractSubTask contractSubTask = provider.produceTaskByRule(merchantSn, event, paramContext, contractRule);
            if (contractSubTask != null) {
                if (contractSubTask.getStatus_influ_p_task() == 1) {
                    affectCount++;
                }
                contractSubTask.setRule_group_id(groupId);
                contractSubTaskList.add(contractSubTask);
            }
        }
        if (contractSubTaskList.isEmpty()) {
            //无任何子任务生成
            event.setResult("no subtask create nothing need to do").setStatus(ContractEvent.STATUS_SUCCESS);
            log.info(" 商户 {} 更新事件 无任何子任务生成", event.getMerchant_sn());
            contractEventMapper.updateByPrimaryKeySelective(event);
            return;
        }
        String merchantName = BeanUtil.getPropString(paramContext.get("merchant"), Merchant.NAME);
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(event.getMerchant_sn())
                .setMerchant_name(merchantName)
                .setType(getContractTaskType(event))
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(affectCount)
                .setAffect_status_success_task_count(0)
                .setRule_group_id(event.getRule_group_id())
                //如果影响任务状态的子任务数量为0,则直接设为成功
                .setStatus(Objects.equals(affectCount, 0) ? TaskStatus.SUCCESS.getVal() : 0);
        contractTaskBiz.insert(contractTask);
        Long pTaskId = contractTask.getId();
        for (ContractSubTask sub : contractSubTaskList) {
            sub.setP_task_id(pTaskId);
            contractSubTaskMapper.insert(sub);
        }
        event.setTask_id(contractTask.getId());
        event.setStatus(ContractEvent.STATUS_SUCCESS);
        contractEventMapper.updateByPrimaryKeySelective(event);
    }


    public List<MerchantProviderParams> distinctByRule(List<MerchantProviderParams> records) {
        Set<String> set = new HashSet<>();
        List<MerchantProviderParams> distinct = new ArrayList<>();
        records.forEach(r -> {
            if (set.add(r.getContract_rule())) {
                distinct.add(r);
            }
        });
        return distinct;
    }


    public List<MerchantProviderParams> setRule(List<MerchantProviderParams> list) {
        List<MerchantProviderParams> result = new ArrayList<>(list.size());
        for (MerchantProviderParams param : list) {
            if ((Objects.equals(PaywayEnum.WEIXIN.getValue(), param.getPayway()) || Objects.equals(PaywayEnum.ALIPAY.getValue(), param.getPayway())) && param.getStatus() != UseStatusEnum.IN_USE.getValue()) {
                continue;
            }
            if (checkHasPayWayActivityBySubMchId(param)) {
                continue;
            }
            if (WosaiStringUtils.isNotEmpty(param.getContract_rule())) {
                result.add(param);
                continue;
            }
            String rule = ruleContext.getDefaultContractRule(String.valueOf(param.getProvider()), param.getPayway(), param.getChannel_no());
            if (WosaiStringUtils.isNotEmpty(rule)) {
                param.setContract_rule(rule);
                param.setRule_group_id("default");
                result.add(param);
            }
        }
        return result;
    }


    private boolean checkHasPayWayActivityBySubMchId(MerchantProviderParams param) {
        if (Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue())) {//微信子商号
            //去除微信教培活动+停车场+医美类型，教培等活动并没有用活动审批模版里面的名称，所以可以进行系统数据更新
            return paywayActivityService.activityCheckWithNoSpecifiedType(param.getPay_merchant_id(), Lists.newArrayList(1, 3, 4));
        } else if (Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue())) {//支付宝子商号
            return paywayActivityService.activityCheckWithNoSpecifiedType(param.getPay_merchant_id(), null);
        }
        return false;
    }

    private void crmReContract(Map paramContext, ContractEvent event) {
        Map eventMsg = JSON.parseObject(event.getEvent_msg(), Map.class);
        List<Map> tables = (List) eventMsg.get("crmUpdate");
        if (CollectionUtils.isEmpty(tables)) {
            return;
        }
        boolean bothChange = false;
        boolean merchantChange = false;
        boolean accountChange = false;
        for (Map t : tables) {
            String table = BeanUtil.getPropString(t, "table_name");
            if (WosaiStringUtils.isEmpty(table)) {
                continue;
            }
            List changes = (List) t.get("fields");
            if (CollectionUtils.isEmpty(changes)) {
                continue;
            }

            if ("merchant".equals(table) && ProviderUtil.existsOne(CrmUtil.MERCHANTS, changes)) {
                merchantChange = true;
            }
            if ("merchant_bank_account".equals(table) && ProviderUtil.existsOne(CrmUtil.MERCHANT_BANK_ACCOUNT, changes)) {
                accountChange = true;
            }
        }

        //如果当前卡是验证失败的 且调用了重新提交接口更新（第一次验证成功 换卡失败 重新提交）
        if (!accountChange) {
            Map bankAccountPre = (Map) paramContext.get(ParamContextBiz.KEY_BANK_ACCOUNT);
            if (MerchantBankAccountPre.VERIFY_STATUS_FAIL == BeanUtil.getPropInt(bankAccountPre, MerchantBankAccountPre.VERIFY_STATUS)) {
                accountChange = true;
            }
        }
        if (merchantChange && accountChange) {
            bothChange = true;
        }

        if (merchantChange) {
            paramContext.put("crmUpdate", "0");
        }
        if (accountChange) {
            paramContext.put("crmUpdate", "1");
        }
        if (bothChange) {
            paramContext.put("crmUpdate", "2");
        }

        eventMsg.put("msg", new ArrayList<>());
        event.setEvent_msg(JSON.toJSONString(eventMsg));
        handleInsert(event, paramContext);
    }

    private void createFailTask(Map paramContext, ContractEvent event) {
        String merchantName = BeanUtil.getPropString(paramContext.get(ParamContextBiz.KEY_MERCHANT), Merchant.NAME);
        Map eventMsg = JSON.parseObject(event.getEvent_msg());
        String memo = BeanUtil.getPropString(eventMsg, "source.fail_memo");
        Map result = CollectionUtil.hashMap(
                "channel", ProviderUtil.SHOUQIANBA_CHANNEL,
                "message", memo
        );
        ContractTask contractTask = new ContractTask()
                .setMerchant_sn(event.getMerchant_sn())
                .setMerchant_name(merchantName)
                .setType(ProviderUtil.CONTRACT_TYPE_INSERT)
                .setEvent_context(JSON.toJSONString(paramContext))
                .setAffect_sub_task_count(0)
                .setAffect_status_success_task_count(0)
                .setStatus(6)
                .setRule_group_id(event.getRule_group_id())
                .setResult(JSON.toJSONString(result));
//        contractTaskMapper.insert(contractTask);
        // TODO 插入contract_task表并根据type和status判断是否发送消息到神策
        contractTaskBiz.insert(contractTask);
        event.setTask_id(contractTask.getId());
        event.setStatus(ContractEvent.STATUS_SUCCESS);
        contractEventMapper.updateByPrimaryKeySelective(event);
        dataBusBiz.insert(ContractStatus.STATUS_BIZ_FAIL, event.getMerchant_sn(), null);
        bankCardService.updateCardAfterTaskStatus(paramContext, MerchantBankAccount.VERIFY_STATUS_FAIL, memo);
    }


    /**
     * 获取报备总任务的类型
     *
     * @param event
     * @return
     */
    public String getContractTaskType(ContractEvent event) {
        switch (event.getEvent_type()) {
            case 0:
                return ProviderUtil.CONTRACT_TYPE_UPDATE_BASIC;
            case 1:
                return ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT;
            case 2:
                return ProviderUtil.CONTRACT_TYPE_UPDATE_FEERATE;
            case 11:
                return ProviderUtil.CONTRACT_TYPE_UPDATE_BUSINESS_LICENSE;
            case 12:
                return ProviderUtil.MERCHANT_BUSINESS_DATA_CHANGE;
            default:
                return "未知任务类型";
        }
    }


    /**
     * 获取报备规则组id
     *
     * @param event
     * @return
     */
    private String getRuleGroupId(ContractEvent event) {
        if (WosaiStringUtils.isNotEmpty(event.getRule_group_id())) {
            return event.getRule_group_id();
        }
        return acquirerBiz.getMerchantDefaultRuleGroup(event.getMerchant_sn(), "");
    }

}
