package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static javax.management.timer.Timer.ONE_DAY;
import static javax.management.timer.Timer.ONE_MINUTE;

/**
 * <AUTHOR>
 * @date 2022/11/14
 */
@Component
public class MultiEventBiz {

    @Autowired
    private IMerchantService iMerchantService;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;


    /**
     * 获取主通道入网超时时间
     *
     * @param multiProviderContractEvent
     * @return
     */
    public long getPrimaryContractTimeOutTime(MultiProviderContractEvent multiProviderContractEvent) {
        // 对公商户入网主通道超时时间调整为3d
        if (multiProviderContractEvent.getPrimary_task_id() != null) {
            ContractTask task = contractTaskMapper.selectByPrimaryKey(multiProviderContractEvent.getPrimary_task_id());
            // 主通道失败，次通道成功，立刻切换次通道
            if (Objects.equals(TaskStatus.FAIL.getVal(), task.getStatus())) {
                return 0L;
            }
            JSONObject eventContext = JSON.parseObject(task.getEvent_context());
            int bankAccountType = BeanUtil.getPropInt(eventContext, "bankAccount.type", BankAccountTypeEnum.PERSONAL.getValue());
            if (bankAccountType == BankAccountTypeEnum.PUBLIC.getValue()) {
                return 3 * ONE_DAY;
            }
        }
        return getPrimaryContractTimeOutByOrg(multiProviderContractEvent.getMerchant_sn());
    }

    /**
     * ka商户进件超时设置3d（支持按渠道设置）
     *
     * @param merchantSn
     * @return
     */
    public long getPrimaryContractTimeOutByOrg(String merchantSn) {
        Map<String, Object> merchant = iMerchantService.getMerchantBySn(merchantSn);
        String orgPath = WosaiMapUtils.getString(merchant, "organization_path", "");
        if (WosaiStringUtils.isEmpty(orgPath)) {
            return 5 * ONE_MINUTE;
        }
        Map primaryContractTimeOut = applicationApolloConfig.getPrimaryContractTimeOut();
        if (WosaiMapUtils.isEmpty(primaryContractTimeOut)) {
            return 5 * ONE_MINUTE;
        }
        Optional first = primaryContractTimeOut.keySet().stream().filter(r -> orgPath.startsWith((String) r)).findFirst();
        if (first.isPresent()) {
            return BeanUtil.getPropLong(primaryContractTimeOut, (String) first.get());
        } else {
            return BeanUtil.getPropLong(primaryContractTimeOut, "other");
        }
    }
}
