package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSONObject;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.crmdatabus.active.pay.events.ActiveStartEvent;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.SupplyParamsBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections4.MapUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @Description: 监听消息设置智慧经营信息
 * <AUTHOR>
 * @Date: 2023/1/3 2:33 下午
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class MerchantActiveConsumer extends AbstractDataBusConsumer {
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private SupplyParamsBiz supplyParamsBiz;


    @KafkaListener(topics = {ActiveStartEvent.TOPIC_NAME}, containerFactory = "merchantActiveKafkaListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        if (!(event instanceof ActiveStartEvent)) {
            return;
        }
        log.info("start handling activeStartEvent : {}", JSONObject.toJSONString(event));
        // 补充参数
        try {
            Map merchant = merchantService.getMerchantByMerchantId(((ActiveStartEvent) event).getMerchantId());
            String merchantSn = MapUtils.getString(merchant, Merchant.SN);
            supplyParamsBiz.supplyLklParams(merchantSn);
            supplyParamsBiz.syncAT2Lkl(merchantSn);
        } catch (Exception e) {
            log.error("activeStartEvent 商户id:{}", ((ActiveStartEvent) event).getMerchantId(), e);
        }
    }

}