package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25
 */
@Component
public class MultiProviderEventHandlerContext {

    @Autowired
    private List<AbstractMultiProviderEventHandler> handlers;

    public void handle(MultiProviderContractEvent event) throws Exception {
        AbstractMultiProviderEventHandler handler = getHandler(event);
        if (handler != null) {
            handler.handle(event);
        }
    }

    private AbstractMultiProviderEventHandler getHandler(MultiProviderContractEvent event) {
        for (AbstractMultiProviderEventHandler handler : handlers) {
            if (handler.supports(event)) {
                return handler;
            }
        }
        return null;
    }
}
