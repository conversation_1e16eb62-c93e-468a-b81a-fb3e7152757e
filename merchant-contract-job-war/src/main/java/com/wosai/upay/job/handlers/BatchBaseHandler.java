package com.wosai.upay.job.handlers;


/**
 * <AUTHOR>
 * @date 2022-05-19
 */
public interface BatchBaseHandler<T, R> {


    /**
     * 预处理
     *
     * @param batchContext
     * @return
     * @throws Exception
     */
    void doPreProcess(BatchContext batchContext);

    /**
     * 处理任务
     *
     * @param t
     * @return
     * @throws Exception
     */
    R handle(T t, BatchContext batchContext);


    /**
     * 后处理
     *
     * @param batchContext
     * @return
     * @throws Exception
     */
    void doAfterProcess(BatchContext batchContext);


    /**
     * 异常处理
     *
     * @param batchContext
     * @return
     * @throws Exception
     */
    R handleError(T t, BatchContext batchContext, Exception e);


}
