package com.wosai.upay.job.util;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
public class ThreadLocalUtil {

    private static final ThreadLocal<Boolean> SETTLEMENT_CHECK = ThreadLocal.withInitial(() -> true);

    private static final ThreadLocal<Boolean> CHECK_BRAND_PAYMENT_MODE = ThreadLocal.withInitial(() -> true);

    private static final ThreadLocal<Boolean> MICRO_IS_DONG_UPGRADE_CHECK = ThreadLocal.withInitial(() -> false);

    /**
     * 切收单机构跳过同步银行卡信息
     */
    private static final ThreadLocal<Boolean> ACQUIRER_CHANGE_SKIP_SYNC_BANK_ACCOUNT = ThreadLocal.withInitial(() -> false);

    /**
     * 是否刷新交易参数到支付测
     * true-是 说明并不是切收单机构
     * 默认为false
     */
    private static final ThreadLocal<Boolean> REFRESH_TRADE_PARAMS_TO_PAY = ThreadLocal.withInitial(() -> false);

    /**
     * 切换参数时的备注信息
     */
    private static final ThreadLocal<String> CHANGE_PARAMS_REMARK = ThreadLocal.withInitial(() -> "");

    public static void setCheckSettlement(Boolean flag) {
        SETTLEMENT_CHECK.set(flag);
    }

    public static Boolean getCheckSettlement() {
        return SETTLEMENT_CHECK.get();
    }

    public static void clearCheckSettlement() {
        SETTLEMENT_CHECK.set(true);
    }

    public static void setChangeParamsRemark(String remark) {
        CHANGE_PARAMS_REMARK.set(remark);
    }

    public static String getChangeParamsRemark() {
        return CHANGE_PARAMS_REMARK.get();
    }

    public static void clearChangeParamsRemark() {
        CHANGE_PARAMS_REMARK.set("");
    }

    public static void setCheckMicroUpgrade(Boolean flag) {
        MICRO_IS_DONG_UPGRADE_CHECK.set(flag);
    }

    public static Boolean isDoingMicroUpgrade() {
        return MICRO_IS_DONG_UPGRADE_CHECK.get();
    }

    public static void removeCheckMicroUpgrade() {
        MICRO_IS_DONG_UPGRADE_CHECK.remove();
    }

    public static void setRefreshTradeParamsToPay(Boolean flag) {
        REFRESH_TRADE_PARAMS_TO_PAY.set(flag);
    }

    public static Boolean isRefreshTradeParamsToPay() {
        return REFRESH_TRADE_PARAMS_TO_PAY.get();
    }

    public static void removeRefreshTradeParamsToPay() {
        REFRESH_TRADE_PARAMS_TO_PAY.remove();
    }
    public static void setCheckBrandPaymentMode(Boolean flag) {
        CHECK_BRAND_PAYMENT_MODE.set(flag);
    }

    public static Boolean getCheckBrandPaymentMode() {
        return CHECK_BRAND_PAYMENT_MODE.get();
    }

    public static void clearCheckBrandPaymentMode() {
        CHECK_BRAND_PAYMENT_MODE.set(true);
    }

    public static void setAcquirerChangeSkipSyncBankAccount(Boolean flag) {
        ACQUIRER_CHANGE_SKIP_SYNC_BANK_ACCOUNT.set(flag);
    }

    public static void removeAcquirerChangeSkipSyncBankAccount() {
        ACQUIRER_CHANGE_SKIP_SYNC_BANK_ACCOUNT.remove();
    }

    public static Boolean isAcquirerChangeSkipSyncBankAccount() {
        return ACQUIRER_CHANGE_SKIP_SYNC_BANK_ACCOUNT.get();
    }
}
