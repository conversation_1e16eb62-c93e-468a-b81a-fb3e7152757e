package com.wosai.upay.job.adapter.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by haochen on 2022/05/19.
 * namespace: core.batch-template
 */
@Component
public class BatchTemplateApolloConfig {
    private final static Logger log = LoggerFactory.getLogger(BatchTemplateApolloConfig.class);

    private final Config config = ConfigService.getConfig("core.batch-template");
    private final ObjectMapper objectMapper = new ObjectMapper();


    public List<Map> getBatchTemplates() {
        return getList("batch-template", "[]");
    }

    List getList(String key, String defaultParams) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultParams), List.class);
        } catch (IOException e) {
            log.error("ApolloConfig getList:{},{},{}", key, defaultParams, e);
            return new ArrayList();
        }
    }

    public Map getMap(String key, String defaultParams) {
        try {
            return objectMapper.readValue(config.getProperty(key, defaultParams), Map.class);
        } catch (IOException e) {
            log.error("ApolloConfig getMap:{},{},{}", key, defaultParams, e);
            return new HashMap();
        }
    }

    public Map getSupportBankConfig() {
        return getMap("support-bank-params", "{}");
    }

    public Map getSupportIndirectConfig() {
        return getMap("support-indirect-params", "{}");
    }
}
