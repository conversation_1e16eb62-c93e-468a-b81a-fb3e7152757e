package com.wosai.upay.job.controller;

import com.shouqianba.cua.utils.object.EnumUtils;
import com.wosai.upay.job.xxljob.JobHandlerRegistry;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.refactor.model.dto.InternalScheduleTaskResultRspDTO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.InternalScheduleTaskFactory;
import com.wosai.upay.job.refactor.task.BankAutoChangeThirdNotifyTask;
import com.wosai.upay.job.refactor.task.BnsInActiveMerchantCleanTask;
import com.wosai.upay.job.refactor.task.WeChatComplaintResolutionTask;
import com.wosai.upay.job.scheduler.OnlinePaymentApplySchedule;
import com.wosai.upay.job.xxljob.model.JobHandlerTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/13
 */
@RestController
@RequestMapping("/job/task")
public class TaskTriggerController {

    @Autowired
    private OnlinePaymentApplySchedule onlinePaymentApplySchedule;

    private static final ResponseEntity<String> SUCCESS_RESULT = ResponseEntity.ok("success");

    @Resource
    private InternalScheduleTaskFactory internalScheduleTaskFactory;

    @Resource
    private BankAutoChangeThirdNotifyTask bankAutoChangeThirdNotifyTask;

    @Resource
    private WeChatComplaintResolutionTask weChatComplaintResolutionTask;

    @Resource
    private BnsInActiveMerchantCleanTask bnsInActiveMerchantCleanTask;

    /**
     * 批量任务处理
     * @param param 任务参数
     * @param beanName 处理任务类名
     * @return 处理结果
     */
    @PostMapping("/batchJob/{bean}")
    public ResponseEntity<String> batchJob(@RequestBody BatchJobParam param, @PathVariable("bean") String beanName) {
        JobHandlerRegistry.getJobHandler(beanName, JobHandlerTypeEnum.BATCH).handle(param);
        return SUCCESS_RESULT;
    }

    /**
     * 单个简单任务处理
     * @param param 任务参数
     * @param beanName 处理任务类名
     * @return 处理结果
     */
    @PostMapping("/directJob/{bean}")
    public ResponseEntity<String> directJob(@RequestBody DirectJobParam param, @PathVariable("bean") String beanName) {
        JobHandlerRegistry.getJobHandler(beanName, JobHandlerTypeEnum.DIRECT).handle(param);
        return SUCCESS_RESULT;
    }


    @GetMapping("/onlinePaymentApplySchedule/reContractAndSubmitAuth")
    public ResponseEntity<String> onlinePaymentApplyScheduleReContractAndSubmitAuth() {
        onlinePaymentApplySchedule.reContractAndSubmitAuth();
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/queryAuth")
    public ResponseEntity<String> onlinePaymentApplyScheduleQueryAuth() {
        onlinePaymentApplySchedule.queryAuth();
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/setFailIfAuthTimeout")
    public ResponseEntity<String> setFailIfAuthTimeout() {
        onlinePaymentApplySchedule.setFailIfAuthTimeout();
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/sendEmail")
    public ResponseEntity<String> onlinePaymentApplyScheduleSendEmail() {
        onlinePaymentApplySchedule.sendEmail();
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/sendHaikeEmail")
    public ResponseEntity<String> onlinePaymentApplyScheduleSendHaikeEmail() {
        onlinePaymentApplySchedule.sendHaikeEmail(LocalDateTime.now());
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/sendLklEmail")
    public ResponseEntity<String> onlinePaymentApplyScheduleSendLklEmail() {
        onlinePaymentApplySchedule.sendLklEmail(LocalDateTime.now());
        return SUCCESS_RESULT;
    }

    @GetMapping("/onlinePaymentApplySchedule/sendEmailForTargetDataAndAcquirer")
    public ResponseEntity<String> sendEmailForTargetDataAndAcquirer(@RequestParam(name = "targetDateTime") String targetDateTime, @RequestParam(name = "acquirer") String acquirer) {
        onlinePaymentApplySchedule.sendEmailForTargetDataAndAcquirer(targetDateTime, acquirer);
        return SUCCESS_RESULT;
    }

    /**
     * xxl-job调度,根据上次执行点位，批量处理
     *
     * @param type    任务类型
     * @param taskNum 任务数量
     * @return 处理结果
     */
    @PostMapping("/internal/schedule/batch")
    public InternalScheduleTaskResultRspDTO triggerBatchInternalScheduleTasks(@RequestParam(name = "type") Integer type,
                                                                             @RequestParam(name = "taskNum") Integer taskNum) {
        Optional<InternalScheduleTaskTypeEnum> taskEnum = getTaskEnum(type);
        if (!taskEnum.isPresent()) {
            return new InternalScheduleTaskResultRspDTO(0, 0, "任务类型不存在");
        }
        return internalScheduleTaskFactory.getHandler(taskEnum.get()).batchHandleTasksInSequence(taskNum);
    }

    /**
     * 触发内部调度任务,根据主任务id，批量处理
     *
     * @param type 任务类型
     * @param ids  主任务id列表
     * @return 处理结果
     */
    @PostMapping("/internal/schedule/ids")
    public InternalScheduleTaskResultRspDTO triggerBatchInternalScheduleTasks(@RequestParam(name = "type") Integer type, @RequestBody Long[] ids) {
        Optional<InternalScheduleTaskTypeEnum> taskEnum = getTaskEnum(type);
        if (!taskEnum.isPresent()) {
            return new InternalScheduleTaskResultRspDTO(0, 0, "任务类型不存在");
        }
        return internalScheduleTaskFactory.getHandler(taskEnum.get()).batchHandleTasksByMainTaskIds(Arrays.stream(ids).collect(Collectors.toList()));
    }

    @PostMapping("/ding/bank-auto-third")
    public ResponseEntity<String> triggerBankAutoChangeToThirdSendMailTask() {
        bankAutoChangeThirdNotifyTask.sendBankAutoChangeThirdTaskEmail();
        return SUCCESS_RESULT;
    }

    @PostMapping("/wx/complaint-resolution-task")
    public ResponseEntity<String> triggerWxComplaintResolutionTask() {
        weChatComplaintResolutionTask.initTasks();
        return SUCCESS_RESULT;
    }

    @PostMapping("/bns/inactive-clean-task")
    public ResponseEntity<String> triggerBnsInActiveCleanTask() {
        bnsInActiveMerchantCleanTask.initCleanTask();
        return SUCCESS_RESULT;
    }

    private Optional<InternalScheduleTaskTypeEnum> getTaskEnum(Integer type) {
        return EnumUtils.ofNullable(InternalScheduleTaskTypeEnum.class, type);
    }
}
