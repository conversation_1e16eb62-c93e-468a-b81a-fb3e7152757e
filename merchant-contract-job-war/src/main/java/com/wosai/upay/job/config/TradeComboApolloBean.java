package com.wosai.upay.job.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.wosai.common.exception.CommonPubBizException;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * @Description:namespace:memo 阿波罗业务套餐相关配置
 * <AUTHOR>
 * Date 2020/6/5 9:57 上午
 **/
@Component
@Data
@Accessors(chain = true)
public class TradeComboApolloBean {

    @Value("${lklComboId}")
    private Long lklComboId;
    @Value("${tongLianComboId}")
    private Long tongLianId;

    @ApolloJsonValue("${channelComboMap}")
    private Map<String, Long> channelComboMap;
    @ApolloJsonValue("${channelFees}")
    private Map<String, String> channelFee;


    public Long getAcComboDetail(String ac) {
        return "lkl".equals(ac) ? lklComboId : tongLianId;
    }


    public Long getComboByChannelNo(String channelNo) {
        Long comboId = channelComboMap.get(channelNo);
        if (Objects.isNull(comboId)) {
            throw new CommonPubBizException("找不到对应的套餐id");
        }
        return comboId;
    }

    public String getFeeByChannelNo(String channelNo) {
        return channelFee.get(channelNo);
    }
}
