package com.wosai.upay.job.refactor.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.model.acquirer.CheckChangeAcquirerResp;
import com.wosai.upay.job.refactor.dao.McAcquirerChangeDAO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.bo.ScheduleTaskExecutePropertyBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerChangeDO;
import com.wosai.upay.job.refactor.model.enums.BatchGetScheduleTaskPatternTypeEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * 银行恢复后，三方自动切回银行通道(包含之前的多业务也需要回切）
 *
 * 需要获取上次回切的任务，从任务中获取到回切的银行和多业务
 * 任务：子任务 + 几个多业务就有几个子任务，子任务标明多业务，子任务有依赖可调度状态的关系
 *
 *
 * <AUTHOR>
 * @date 2024/6/20 11:21
 */
@Service
@Slf4j
public class ThirdAutoChangeToBankTask extends AbstractInternalScheduleTaskHandleTemplate {

    @Resource
    private AcquirerService acquirerService;

    @Resource
    private McAcquirerChangeDAO mcAcquirerChangeDAO;

    @Resource
    private AopBiz aopBiz;

    @Value("${third.auto.change.bank.notify.devCode}")
    private String noticeTemplateDevCode;

    @Value("${third.auto.change.bank.notify.template_id}")
    private String noticeTemplateId;

    @Autowired
    private MerchantService merchantService;

    @Resource
    private BusinessLogBiz businessLogBiz;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    /**
     * 获取任务类型
     *
     * @return 任务类型
     */
    @Override
    public InternalScheduleTaskTypeEnum getTaskType() {
        return InternalScheduleTaskTypeEnum.BANK_RECOVER_CHANGE_TO_ORIGINAL;
    }

    /**
     * 获取任务执行属性taskExecuteProperty
     */
    @Override
    protected ScheduleTaskExecutePropertyBO getTaskExecuteProperty() {
        ScheduleTaskExecutePropertyBO executeProperty = new ScheduleTaskExecutePropertyBO();
        executeProperty.setBatchGetPatternType(BatchGetScheduleTaskPatternTypeEnum.PENDING_AND_WAITING_EXT_TASKS);
        return executeProperty;
    }


    @Override
    protected InternalScheduleSubTaskProcessResultBO handleSingleSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (Objects.equals(subTaskDO.getStatus(), InternalScheduleSubTaskStatusEnum.WAIT_PROCESS.getValue())) {
            return handleApplyChangeTask(mainTaskDO, subTaskDO);
        } else if (Objects.equals(subTaskDO.getStatus(), InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT.getValue())) {
            return handleWaitChangeTaskResult(mainTaskDO, subTaskDO);
        }
        throw new ContractBizException("不支持的任务状态");
    }

    private InternalScheduleSubTaskProcessResultBO handleWaitChangeTaskResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        JSONObject contextMap = JSON.parseObject(subTaskDO.getContext());
        String targetBank = MapUtils.getString(contextMap, "targetBank");
        String changeId = MapUtils.getString(contextMap, "changeId");
        Optional<McAcquirerChangeDO> changeOpt = mcAcquirerChangeDAO.getByPrimaryKey(Long.valueOf(changeId));
        if (!changeOpt.isPresent()) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "切换任务不存在, id = " + changeId);
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeOpt.get();
        if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.SUCCESS)) {
            notifyMerchantChangeToBank(mainTaskDO);
            recordMerchantLog(mainTaskDO, targetBank, mcAcquirerChangeDO.getSourceAcquirer());
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS, "切换成功,目标收单机构: " + targetBank);
        } else if (Objects.equals(mcAcquirerChangeDO.getStatus(), AcquirerChangeStatus.FAIL)) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "切换失败");
        }
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, "切换任务已经提交,等待切换结果");
    }

    private void recordMerchantLog(InternalScheduleMainTaskDO mainTaskDO, String bankAcquirer, String thirdAcquirer) {
        businessLogBiz.recordBankProtectionAutoChangeLog(merchantService.getMerchantBySn(mainTaskDO.getMerchantSn(), devCode).getId(),
                bankAcquirer, thirdAcquirer, false);
    }

    private void notifyMerchantChangeToBank(InternalScheduleMainTaskDO mainTaskDO) {
        aopBiz.sendNoticeToAdmin(merchantService.getMerchantBySn(mainTaskDO.getMerchantSn(), devCode).getId(),
                noticeTemplateDevCode, noticeTemplateId, Collections.EMPTY_MAP);
    }

    private InternalScheduleSubTaskProcessResultBO handleApplyChangeTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
        JSONObject contextMap = JSON.parseObject(subTaskDO.getContext());
        String traAppId = MapUtils.getString(contextMap, "traAppId");
        String targetBank = MapUtils.getString(contextMap, "targetBank");
        String merchantSn = mainTaskDO.getMerchantSn();
        CheckChangeAcquirerResp checkChangeAcquirerResp = acquirerService.checkChangeAcquirer(merchantSn, targetBank, traAppId);
        if (!checkChangeAcquirerResp.isCan_change()) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, checkChangeAcquirerResp.getMsg());
        }
        boolean applySuccess = acquirerService.applyChangeAcquirer(mainTaskDO.getMerchantSn(), targetBank, true, traAppId);
        if (!applySuccess) {
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "无法回切至原银行通道");
        }
        Optional<McAcquirerChangeDO> changeTaskOpt = mcAcquirerChangeDAO.getLastedByMerchantSnAndTargetAcquirer(merchantSn, targetBank, currentTimestamp);
        if (!changeTaskOpt.isPresent()) {
            log.warn("没有生成对应切换收单机构的任务, merchantSn = {}, targetAcquirer = {}", merchantSn, targetBank);
            return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL, "生成切换收单机构任务失败");
        }
        McAcquirerChangeDO mcAcquirerChangeDO = changeTaskOpt.get();
        contextMap.put("changeId", String.valueOf(mcAcquirerChangeDO.getId()));
        subTaskDO.setContext(JSON.toJSONString(contextMap));
        return new InternalScheduleSubTaskProcessResultBO(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT, "切换任务已经提交,等待切换结果");
    }

}
