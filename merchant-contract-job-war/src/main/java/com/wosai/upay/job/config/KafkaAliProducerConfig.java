package com.wosai.upay.job.config;

import io.confluent.kafka.serializers.KafkaAvroSerializer;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaAliProducerConfig {


    @Value("${spring.kafka.ali.registry-servers}")
    private String registryAliServers;

    @Value("${spring.kafka.ali.bootstrap-servers}")
    private String bootstrapAliServers;

    @Value("${spring.kafka.producer.group-id}")
    private String groupId;


    @Bean
    public Map<String, Object> producerAliConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAliServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
                KafkaAvroSerializer.class.getName());
        // Configure the KafkaAvroSerializer.
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
                KafkaAvroSerializer.class.getName());
        // Schema Registry location.
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG,
                registryAliServers);
        return props;
    }


    @Bean
    public KafkaTemplate<String, Object> kafkaAliTemplate() {
        return new KafkaTemplate(producerAliFactory());
    }

    @Bean
    public KafkaProducer kafkaAliProducer(){
        return new KafkaProducer(producerAliConfigs());
    }

    @Bean
    public ProducerFactory<String, String> producerAliFactory() {
        return new DefaultKafkaProducerFactory<>(producerAliConfigs());
    }


}
