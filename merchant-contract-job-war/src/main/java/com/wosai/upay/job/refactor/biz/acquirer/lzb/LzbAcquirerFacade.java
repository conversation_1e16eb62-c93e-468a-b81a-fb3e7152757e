package com.wosai.upay.job.refactor.biz.acquirer.lzb;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;


@Service
public class LzbAcquirerFacade extends AbstractAcquirerHandler {

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.LZB;
    }

}
