package com.wosai.upay.job.handlers;

import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-04-08
 */
public interface SubTaskHandler {

    /**
     * 是否支持
     *
     * @param task
     * @param subTask
     * @return
     */
    boolean supports(ContractTask task, ContractSubTask subTask);


    /**
     * 处理任务
     *
     * @param task
     * @param subTask
     * @throws Exception
     */
    void handle(ContractTask task, ContractSubTask subTask) throws Exception;


}
