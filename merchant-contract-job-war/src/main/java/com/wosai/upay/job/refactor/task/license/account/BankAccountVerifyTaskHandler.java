package com.wosai.upay.job.refactor.task.license.account;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserPushSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.aop.gateway.service.ClientSidePushService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.bank.model.verify.AccountApply;
import com.wosai.upay.bank.model.verify.AccountApplyResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.service.rpc.bank.BankAccountVerifyService;
import com.wosai.upay.job.refactor.service.rpc.bank.rsp.AccountVerifyApplyRspDTO;
import com.wosai.upay.job.refactor.task.license.crm.CrmLicenseApplyUpdate;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.job.refactor.utils.TimeExpirationUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.SnowFlakeIdGenerator;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 账户校验任务处理
 *
 * <AUTHOR>
 * @date 2025/2/17 08:57
 */
@Slf4j
@Component
public class BankAccountVerifyTaskHandler {

    @Autowired
    private AccountVerifyService accountVerifyService;

    @Resource
    private BankAccountVerifyService bankAccountVerifyService;

    @Autowired
    private CommonFieldService commonFieldService;

    @Resource
    private CrmLicenseApplyUpdate crmLicenseApplyUpdate;

    public static final Integer STATUS_VERIFYING = 1;

    public static final Integer STATUS_NEED_VERIFY_AMOUNT = 2;

    public static final Integer STATUS_VERIFY_SUCCESS = 3;

    public static final Integer STATUS_VERIFY_FAIL = 4;

    public static final String ACCOUNT_VERIFY_STATUS_FIELD_NAME = "account_verify_status";

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ClientSidePushService clientSidePushService;

    @Resource
    private ApplicationApolloConfig applicationApolloConfig;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;

    public static final String NOTICE_MERCHANT_VERIFY_AMOUNT_TEMPLATE_CODE = "GH0FY1RKGQGA";

    public static final String NOTICE_MERCHANT_VERIFY_AMOUNT_MSP_TEMPLATE_CODE = "YZI9SOB3XFJM";

    public static final String PUSH_MERCHANT_VERIFY_AMOUNT_TEMPLATE_CODE = "CCGEJVCH2H69";

    public static final String MERCHANT_VERIFY_AMOUNT_DEV_CODE = "D3ZWFFMUFAEM";

    public static final String AOP_TERMINAL_MSP_CODE = "TERMINALSSQB";


    /**
     * 处理账户校验任务
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 处理结果
     */
    public InternalScheduleSubTaskProcessResultBO handleAccountVerify(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (!needVerifyAccount(mainTaskDO)) {
            return InternalScheduleSubTaskProcessResultBO.success("非三要素变更");
        }
        if (subTaskDO.isWaitProcess()) {
            return applyAccountVerify(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            return waitAccountVerifyResult(mainTaskDO, subTaskDO);
        }
        throw new ContractBizException("系统异常");
    }

    private boolean needVerifyAccount(InternalScheduleMainTaskDO mainTaskDO) {
        try {
            return changeAccountWithLicenseUpdate.isThreeElementChange(JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class));
        } catch (Exception e) {
            log.error("needVerifyAccount error, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            return true;
        }
    }

    private InternalScheduleSubTaskProcessResultBO waitAccountVerifyResult(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        try {
            boolean timeExpired = TimeExpirationUtil.isTimeExpired(subTaskDO.getCtime(), applicationApolloConfig.getLicenseUpdateVerifyAccountExpiredDays());
            if (timeExpired) {
                return InternalScheduleSubTaskProcessResultBO.fail("账户验证已超过" + applicationApolloConfig.getLicenseUpdateVerifyAccountExpiredDays() + "天等待期，验证不通过");
            }
            BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
            String accountVerifyBusinessId = subTaskContextBOInner.getAccountVerifyBusinessId();
            BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
            AccountVerifyApplyRspDTO verifyResult = bankAccountVerifyService.getVerifyTaskInfoByBusinessId(accountVerifyBusinessId);
            if (Objects.isNull(verifyResult)) {
                log.error("waitAccountVerifyResult 账户校验结果返回null, subTaskId:{}", subTaskDO.getId());
                return InternalScheduleSubTaskProcessResultBO.fail("账户校验失败");
            }
            subTaskDO.setResponseMessage(JSON.toJSONString(verifyResult));
            if (verifyResult.waitForVerifyOrProgressing()) {
                return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待账户校验结果");
            }
            if (verifyResult.waitForBackFillAmountStatus()) {
                if (Objects.equals(mainTaskContextBOInner.getAlreadySyncWaitMerchantVerifyAmount(), true)) {
                    return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待用户回填金额");
                }
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                        new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountVerifyBusinessId, STATUS_NEED_VERIFY_AMOUNT));
                sendNoticeVerifyAmount(mainTaskDO.getMerchantSn());
                mainTaskContextBOInner.setAlreadySyncWaitMerchantVerifyAmount(true);
                mainTaskDO.setContext(JSON.toJSONString(mainTaskContextBOInner));
                return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待用户回填金额");
            }
            if (verifyResult.taskSuccess()) {
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                        new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountVerifyBusinessId, STATUS_VERIFY_SUCCESS));
                return InternalScheduleSubTaskProcessResultBO.success("账户校验成功");
            }
            if (verifyResult.taskFail()) {
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                        new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountVerifyBusinessId, STATUS_VERIFY_FAIL));
                String failMsg = StringUtils.isBlank(verifyResult.getFailMsg()) ? "账户校验失败" : "账户校验失败: " + verifyResult.getFailMsg();
                mainTaskDO.setResult(failMsg);
                return InternalScheduleSubTaskProcessResultBO.fail(failMsg);
            }
            return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待账户校验结果");  // 走不到这里
        } catch (Exception e) {
            log.error("waitAccountVerifyResult 账户校验失败, subTaskId:{}", subTaskDO.getId(), e);
            return InternalScheduleSubTaskProcessResultBO.fail("账户校验失败:" + e.getMessage());
        }

    }


    public void sendNoticeVerifyAmount(String merchantSn) {
        try {
            Optional<String> merchantIdOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(merchantSn);
            if (!merchantIdOpt.isPresent()) {
                return;
            }
            String merchantId = merchantIdOpt.get();
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                final MerchantUserNoticeSendModel noticeSendModel = new MerchantUserNoticeSendModel();
                noticeSendModel.setDevCode(MERCHANT_VERIFY_AMOUNT_DEV_CODE);
                noticeSendModel.setTemplateCode(NOTICE_MERCHANT_VERIFY_AMOUNT_TEMPLATE_CODE);
                noticeSendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                noticeSendModel.setTimestamp(System.currentTimeMillis());
                clientSideNoticeService.sendToMerchantUser(noticeSendModel);
                noticeSendModel.setTemplateCode(NOTICE_MERCHANT_VERIFY_AMOUNT_MSP_TEMPLATE_CODE);
                noticeSendModel.setTimestamp(System.currentTimeMillis());
                noticeSendModel.setClientSides(Lists.newArrayList(AOP_TERMINAL_MSP_CODE));
                clientSideNoticeService.sendToMerchantUser(noticeSendModel);
                log.info("营业执照变更回填金额,发送通知:{}", JSONObject.toJSONString(noticeSendModel));
                MerchantUserPushSendModel pushSendModel = new MerchantUserPushSendModel();
                pushSendModel.setDevCode(MERCHANT_VERIFY_AMOUNT_DEV_CODE);
                pushSendModel.setTemplateCode(PUSH_MERCHANT_VERIFY_AMOUNT_TEMPLATE_CODE);
                pushSendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                pushSendModel.setTimestamp(System.currentTimeMillis());
                clientSidePushService.sendToMerchantUser(pushSendModel);
                log.info("营业执照变更回填金额,发送推送:{}", JSONObject.toJSONString(pushSendModel));
            }
        } catch (Exception e) {
            log.error("营业执照变更回填金额,发送通知异常, 商户sn:{}", merchantSn, e);
        }
    }

    private InternalScheduleSubTaskProcessResultBO applyAccountVerify(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner =
                JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
        Map<String, Object> merchantBankAccount = mainTaskContextBOInner.getBankAccount();
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        AccountApplyResp accountApplyResp = doApplyAccountVerify(merchantBankAccount, resultBO, subTaskDO);
        resultBO.setResponseMsg(JSON.toJSONString(accountApplyResp));
        if (Objects.isNull(accountApplyResp)) {
            log.error("applyAccountVerify 账户校验结果返回null, subTaskId:{}", subTaskDO.getId());
            resultBO.setResult("账户校验失败,未获取到返回结果");
        }
        if (accountApplyResp.getSyns()) {
            if (accountApplyResp.getLegal()) {
                resultBO.setResult("账户校验成功，" + accountApplyResp.getMsg());
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS);
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                        new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountApplyResp.getBusiness_id(), STATUS_VERIFY_SUCCESS));
            } else {
                resultBO.setResult("账户校验失败，" + accountApplyResp.getMsg());
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
                String failMsg = StringUtils.isBlank(accountApplyResp.getMsg()) ? "账户校验失败" : "账户校验失败: " + accountApplyResp.getMsg();
                mainTaskDO.setResult(failMsg);
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                        new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountApplyResp.getBusiness_id(), STATUS_VERIFY_FAIL));
            }
            return resultBO;
        }
        resultBO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT);
        // 填充businessId到审批单
        populateAccountVerifyBusinessIdToTask(subTaskDO, accountApplyResp.getBusiness_id());
        crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(mainTaskContextBOInner.getAuditApplyDTO().getFieldAppInfoId(),
                new BankAccountVerifyTaskHandler.AccountVerifyRecord(accountApplyResp.getBusiness_id(), STATUS_VERIFYING));
        resultBO.setResult("等待账户校验结果");
        return resultBO;
    }

    private void populateAccountVerifyBusinessIdToTask(InternalScheduleSubTaskDO subTaskDO, String businessId) {
        BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
        if (Objects.nonNull(subTaskContextBOInner)) {
            subTaskContextBOInner.setAccountVerifyBusinessId(businessId);
        } else {
            subTaskContextBOInner = new BusinessLicenceCertificationV2Task.SubTaskContextBOInner();
            subTaskContextBOInner.setAccountVerifyBusinessId(businessId);
        }
        subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
    }

    private AccountApplyResp doApplyAccountVerify(Map<String, Object> merchantBankAccount,
                                                InternalScheduleSubTaskProcessResultBO resultBO,
                                                InternalScheduleSubTaskDO subTaskDO) {
        try {
            Integer idType = MapUtils.getInteger(merchantBankAccount, MerchantBankAccount.ID_TYPE);
            String requestFlowNo = String.valueOf(SnowFlakeIdGenerator.getInstance().nextId());
            AccountApply apply = new AccountApply();
            apply.setAccount_type(MapUtils.getInteger(merchantBankAccount, MerchantBankAccount.TYPE))
                    .setMerchant_id(MapUtils.getString(merchantBankAccount, MerchantBankAccount.MERCHANT_ID))
                    .setVerify_retry(3)
                    .setBusiness_id(requestFlowNo)
                    .setBank_holder(MapUtils.getString(merchantBankAccount, MerchantBankAccount.HOLDER))
                    .setHolder_identity(MapUtils.getString(merchantBankAccount, MerchantBankAccount.IDENTITY))
                    .setPlat_form(ProviderUtil.PLAT_FORM)
                    .setPrivate_direct_pay_for(false)
                    .setHolder_type(Objects.isNull(idType) ? 1 : idType)
                    .setBank_number(MapUtils.getString(merchantBankAccount, MerchantBankAccount.NUMBER))
                    .setBank_name(MapUtils.getString(merchantBankAccount, MerchantBankAccount.BANK_NAME))
                    .setBranch_name(MapUtils.getString(merchantBankAccount, MerchantBankAccount.BRANCH_NAME))
                    .setBank_address(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.CITY));
            resultBO.setRequestMsg(JSON.toJSONString(apply));
            return accountVerifyService.apply(apply);
        } catch (Exception e) {
            log.error("账户校验失败, subTaskId:{}", subTaskDO.getId(), e);
            AccountApplyResp accountApplyResp = new AccountApplyResp();
            accountApplyResp.setLegal(false);
            accountApplyResp.setSyns(true);
            accountApplyResp.setMsg(e.getMessage());
            return accountApplyResp;
        }

    }

    @Data
    @AllArgsConstructor
    public static class AccountVerifyRecord {

        private String businessId;

        private Integer status;
    }


}
