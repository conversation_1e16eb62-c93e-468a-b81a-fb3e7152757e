package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;
import java.util.Objects;

/**
 * @Description: 目前只接入了lklv3的入网环节
 * <AUTHOR>
 * @Date 2021/4/26 2:24 下午
 **/
@Component
@Order(90)
@Slf4j
public class ContractSubTaskHandlerV3 extends AbstractSubTaskHandler {

    @Autowired
    LklV3Service lklV3Service;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;


    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByName(subTask.getChannel());
        ContractResponse response = provider.processTaskByRule(task,
                ruleContext.getContractRule(subTask.getContract_rule()).getContractChannel(),
                subTask);
        if (Objects.isNull(response)) {
            log.info("merchantSn {} subTask {} channelName {} processTask return null", task.getMerchant_sn(), subTask.getId(), subTask.getChannel());
            return;
        }
        handleResult(response, subTask);
        if (WosaiStringUtils.isNotEmpty(task.getRule_group_id())
                && (task.getRule_group_id().contains(McConstant.RULE_GROUP_LKLV3) || task.getRule_group_id().contains(McConstant.RULE_GROUP_LKLORG))
                && response.isSuccess()
                && ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type())
        ) {
            Map context = JSON.parseObject(task.getEvent_context(), Map.class);
            Map store = lklV3ShopTermBiz.findFirstStore((String) BeanUtil.getNestedProperty(context, "merchant.id"));
            String storeSn = MapUtils.getString(store, "sn");
            lklV3ShopTermBiz.addMer(task.getMerchant_sn(), storeSn, subTask.getId());
        }
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        if (ChannelEnum.LKLV3.getValue().equalsIgnoreCase(subTask.getChannel())
                && (Objects.isNull(subTask.getPayway()) || Objects.equals(subTask.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                && (ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH.equals(subTask.getTask_type())
                || ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(subTask.getTask_type()))
        ) {
            return true;
        }
        return false;
    }

}