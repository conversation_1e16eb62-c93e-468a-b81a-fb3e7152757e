package com.wosai.upay.job;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import sun.misc.Signal;
import sun.misc.SignalHandler;
import java.util.concurrent.*;


@Slf4j
public class MySignalHandler implements SignalHandler {

    private SignalHandler mainHandler;

    private ApplicationContext applicationContext;

    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    public static SignalHandler install(String signalName, ApplicationContext context) {
        MySignalHandler instance = new MySignalHandler();
        instance.applicationContext = context;
        instance.scheduledThreadPoolExecutor = context.getBean("scheduleThreadPool", ScheduledThreadPoolExecutor.class);
        Signal diagSignal = new Signal(signalName);
        instance.mainHandler = Signal.handle(diagSignal, instance);
        return instance;
    }

    @Override
    public void handle(Signal signal) {
        signalAction(signal);
    }

    public void signalAction(Signal signal) {
        try {
            log.info("关闭定时任务");
            ShutdownSignal.shutdown();
            scheduledThreadPoolExecutor.shutdown();
            String sleep = applicationContext.getEnvironment().getProperty("shutdown.sleep", "1000");
            // 将已经开始的任务执行完成
            if (!scheduledThreadPoolExecutor.awaitTermination(Long.parseLong(sleep), TimeUnit.MILLISECONDS)) {
                log.warn("ScheduledThreadPoolExecutor 任务未在规定时间完成");
            }
            mainHandler.handle(signal);
        } catch (Exception e) {
            log.error("关闭定时任务 异常 {}", e.getMessage(), e);
        }
    }
}
