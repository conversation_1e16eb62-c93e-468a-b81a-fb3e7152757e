package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.sql.Timestamp;

/**
 * 收单组路由规则详情表表实体对象
 *
 * <AUTHOR>
 */
@TableName("group_route_rule_detail")
@Data
public class GroupRouteRuleDetailDO {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 对应进件规则决策表id
     */
    @TableField(value = "rule_decision_id")
    private Long ruleDecisionId;
    /**
     * 对象属性 姓名 类型 省 市 行业 结算账户类型 所属推广组织 营业执照类型等
     */
    @TableField(value = "object_property_type")
    private String objectPropertyType;
    /**
     * 逻辑操作类型:
     * EQUAL-等于,NOT_EQUAL-不等于,
     * CONTAIN-包含(给定对象属性值包含用户特征属性值),NOT_CONTAIN-不包含(给定对象属性值不包含用户特征属性值),
     * BE_CONTAINED-被包含(用户特征属性值包含给定对象属性值),NOT_BE_CONTAINED-被不包含(用户特征属性值不包含给定对象属性值),
     * IN, NOT_IN
     * START_WITH NOT_START_WITH(主要用于所属推广组织path匹配)
     */
    @TableField(value = "logical_operation_type")
    private String logicalOperationType;
    /**
     * 对象属性值，对于等于/不等于是单个值，包含、不包含是英文逗号隔开的数组列表
     */
    @TableField(value = "object_property_value")
    private String objectPropertyValue;
    /**
     * 有效状态 0-失效 1-生效
     */
    @TableField(value = "valid_status")
    private Integer validStatus;
    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;

}

