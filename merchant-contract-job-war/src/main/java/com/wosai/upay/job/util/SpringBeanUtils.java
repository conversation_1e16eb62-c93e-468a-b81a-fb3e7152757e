package com.wosai.upay.job.util;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class SpringBeanUtils implements ApplicationContextAware {

    private static ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        Assert.notNull(context);
        return context.getBean(clazz);
    }

    public static Object getBean(String clazz) {
        Assert.notNull(context);
        return context.getBean(clazz);
    }

    public static boolean isProd() {
        Assert.notNull(context);
        if (Objects.isNull(context.getEnvironment())
                || Objects.isNull(context.getEnvironment().getActiveProfiles())) {
            return false;
        }
        return StringUtils.equalsAnyIgnoreCase("prod", context.getEnvironment().getActiveProfiles()[0]);
    }
}
