package com.wosai.upay.job.refactor.biz.acquirer;


import com.shouqianba.cua.enums.contract.AcquirerMerchantStatusEnum;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.model.dto.response.CuaCommonResultDTO;
import com.wosai.upay.job.refactor.model.bo.AcquirerInfoBO;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.service.strategy.Strategy;
import com.wosai.upay.merchant.contract.exception.ContractBizException;

import java.util.Map;
import java.util.Optional;

/**
 * 收单机构共有能力
 *
 * <AUTHOR>
 */
public interface AcquirerSharedAbility extends Strategy<AcquirerTypeEnum, String> {

    /**
     * 获取收单机构信息
     * 唯一标识，名字，provider 直连间连 直清间清 三方or银行or支付源直连
     */
    AcquirerInfoBO getAcquirerInfo();

    /**
     * 获取收单机构默认的报备规则组
     *
     * @return 默认的报备规则组
     */
    Optional<String> getDefaultContractRuleGroupId();


    /**
     * 获取小微升级收默认的报备规则组
     *
     * @return 默认的报备规则组
     */
   default String getMicroUpdageDefaultContractRuleGroupId() {
       return null;
   }


    /**
     * 根据商户特征,判断是否符合收单机构进件规则
     *
     * @param merchantFeatureBO   商户特征
     * @return 校验结果
     */
    ContractGroupRuleVerifyResultBO checkSatisfactionToAcquirerTemplate(MerchantFeatureBO merchantFeatureBO);


    /**
     * 判断商户所在收单机构的银行卡是否和收钱吧银行卡一致
     *
     * @param merchantSn 商户号
     * @return true-一致 false-不一致
     */
    default boolean isBankCardConsistentWithSqb(String merchantSn) {
        return true;
    }

    /**
     * 判断商户所在收单机构的费率是否和收钱吧一致
     *
     * @param merchantSn 商户号
     * @return true-一致 false-不一致
     */
    default boolean isFeeRateConsistentWithSqb(String merchantSn) {
        return true;
    }

    /**
     * 判断商户是否符合收单机构进件规则
     * 主要是判断商户是否命中收单机构对应的禁止类规则
     *
     * @param merchantSn 商户号
     * @return 校验结果
     */
    ContractGroupRuleVerifyResultBO checkMerchantEligibilityToAcquirer(String merchantSn);

    /**
     * 获取商户所在收单机构的商户状态
     *
     * @param merchantSn 商户号
     * @return 商户状态
     */
    default AcquirerMerchantStatusEnum getAcquirerMerchantStatus(String merchantSn) {
        return AcquirerMerchantStatusEnum.NORMAL;
    }

    /**
     * 商户向收单机构报备
     * 仅仅向收单机构做报备，不涉及其他业务逻辑校验。返回报文也不会落库。调用方需要自行处理
     * ps:目前仅仅完成拉卡拉V3, 和海科的报备逻辑
     *
     * @param merchantSn 商户号
     * @return 报备结果 todo 待重构 本次来不及
     */
    default NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn) {
        throw new ContractBizException("当前收单机构尚未实现商户报备功能");
    }

    /**
     * 商户向收单机构报备
     *
     * @param merchantSn   商户号
     * @param contextParam 上下文参数
     * @return 报备结果
     */
    default NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn, Map<String, Object> contextParam) {
        throw new ContractBizException("当前收单机构尚未实现商户报备功能");
    }

    /**
     * 商户向收单机构进件并且报备ATU
     *
     * @param merchantSn   商户号
     * @param contextParam 上下文参数
     * @return 报备结果
     */
    NewMerchantContractResultRspDTO contractToAcquirerWithAtu(String merchantSn, Map<String, Object> contextParam);


    /**
     * 通过进件任务Id获取商户在收单机构信息
     *
     * @param pTaskId  contract_task 表主键Id
     * @return 报备结果
     */
    default MerchantAcquireInfoBO getAcquireInfoFromContractSubTask(Long pTaskId) {
        return null;
    }

    /**
     * 更新支付源(微信支付宝)在银联侧的银联商户号
     *
     * @param paramsId              交易参数主键id
     * @param newUnionPayMerchantId 新的银联商户号
     * @return 更新结果
     */
     boolean updatePayUnionMerchantIdToUnion(String paramsId, String newUnionPayMerchantId);

    /**
     * 获取银联商户号
     *
     * @param unionPayParamsDO 云闪付交易参数 payWay=17
     * @param acquirerParamsDO 收单机构交易参数 payWay=0
     * @return 银联商户号
     */
     String getUnionMerchantId(MerchantProviderParamsDO unionPayParamsDO, MerchantProviderParamsDO acquirerParamsDO);

    /**
     * 校验能否从本收单机构切走
     *
     * @param merchantSn 商户号
     * @return 校验结果
     */
     CuaCommonResultDTO canChangeToOtherAcquirer(String merchantSn);
}
