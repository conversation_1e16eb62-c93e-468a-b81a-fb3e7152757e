package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 抽象的通用DAO
 *
 * <AUTHOR>
 * @date 2024/3/13 11:19
 */
public abstract class AbstractBaseDAO<M, T extends BaseMapper<M>> implements BaseDAO<M, T> {

    private static final int DEFAULT_BATCH_SIZE = 200;

    private final SqlSessionFactory sqlSessionFactory;

    protected T entityMapper;


    protected AbstractBaseDAO(SqlSessionFactory sqlSessionFactory, T entityMapper) {
        this.sqlSessionFactory = sqlSessionFactory;
        this.entityMapper = entityMapper;
    }

    /**
     * 构建查询条件
     *
     * @return LambdaQueryWrapper
     */
    protected LambdaQueryWrapper<M> buildLambdaQueryWrapper() {
        return new LambdaQueryWrapper<>();
    }


    /**
     * 获取所有的实体对象
     * 注: 该方法不适用于大数据量的查询,且已经加了1000条的限制
     *
     * @return 实体对象列表
     */
    @Override
    public List<M> listAll() {
        return entityMapper.selectList(new LambdaQueryWrapper<M>().last("limit 1000"));
    }

    /**
     * 根据主键查询
     *
     * @param id 主键
     * @return 实体对象
     */
    @Override
    public Optional<M> getByPrimaryKey(Serializable id) {
        if (Objects.isNull(id)) {
            return Optional.empty();
        }
        return Optional.ofNullable(entityMapper.selectById(id));
    }

    /**
     * 根据条件查询,返回一个
     *
     * @param queryWrapper 查询条件
     * @return 实体对象
     */
    @Override
    public Optional<M> selectOne(LambdaQueryWrapper<M> queryWrapper) {
        if (Objects.isNull(queryWrapper)) {
            return Optional.empty();
        }
        queryWrapper.last("limit 1");
        return Optional.ofNullable(entityMapper.selectOne(queryWrapper));
    }

    /**
     * 根据主键更新,null字段不参与更新
     *
     * @param entity 待更新对象
     */
    @Override
    public Integer updateByPrimaryKeySelective(M entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return entityMapper.updateById(entity);
    }


    /**
     * 根据主键id删除
     *
     * @param id 主键id
     */
    @Override
    public Integer deleteByPrimaryKey(Serializable id) {
        if (Objects.isNull(id)) {
            return 0;
        }
        return entityMapper.deleteById(id);
    }


    /**
     * 新增一条记录
     *
     * @param entity 实体对象
     */
    @Override
    public Integer insertOne(M entity) {
        if (Objects.isNull(entity)) {
            return 0;
        }
        return entityMapper.insert(entity);
    }


    /**
     * 根据id批量删除
     *
     * @param ids id列表
     * @return 删除条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDeleteByPrimaryKeys(Collection<? extends Serializable> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return entityMapper.deleteBatchIds(ids);
    }


    /**
     * 批量根据主键更新,null字段不参与更新
     *
     * @param dataList 数据列表
     * @return 更新条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateByIdSelective(List<M> dataList) {
        return batchOperation(dataList, T::updateById);
    }


    /**
     * 批量更新
     *
     * @param dataList  数据列表
     * @param operation 更新方式
     * @return 更新条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdate(List<M> dataList, BiConsumer<T, M> operation) {
        return batchOperation(dataList, operation);
    }


    /**
     * 批量新增
     *
     * @param dataList 数据列表
     * @return 新增条数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchInsert(List<M> dataList) {
        return batchOperation(dataList, T::insert);
    }


    /**
     * 批量操作
     *
     * @param dataList  数据列表
     * @param operation consumer
     * @return effect rows
     */
    protected Integer batchOperation(List<M> dataList, BiConsumer<T, M> operation) {
        if (Objects.isNull(dataList)) {
            return 0;
        }
        dataList = dataList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return 0;
        }
        int affectedRows = 0;
        int flushSize = 0;
        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false)) {
            Class<T> mapperClass = getEntityMapperClass();
            T sqlSessionMapper = sqlSession.getMapper(mapperClass);
            for (M data : dataList) {
                operation.accept(sqlSessionMapper, data);
                flushSize++;
                if (flushSize >= DEFAULT_BATCH_SIZE) {
                    affectedRows += getUpdateCounts(sqlSession);
                    flushSize = 0;
                }
            }
            affectedRows += getUpdateCounts(sqlSession);
        }
        return affectedRows;
    }

    @SuppressWarnings("unchecked")
    private Class<T> getEntityMapperClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
    }

    private int getUpdateCounts(SqlSession sqlSession) {
        int insertRows = 0;
        List<BatchResult> results = sqlSession.flushStatements();
        sqlSession.clearCache();
        for (BatchResult result : results) {
            for (int count : result.getUpdateCounts()) {
                insertRows += count;
            }
        }
        return insertRows;
    }

}
