package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 获取内部调度任务方式类型枚举
 *
 * <AUTHOR>
 */
public enum BatchGetScheduleTaskPatternTypeEnum implements ITextValueEnum<Integer> {

    PENDING_TASKS(1, "调度待处理任务"),

    PENDING_AND_WAITING_EXT_TASKS(2, "调度待处理任务和等待外部结果任务");


    private final Integer value;
    private final String text;

    BatchGetScheduleTaskPatternTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
