package com.wosai.upay.job.monitor;

import com.wosai.upay.job.model.ContractSubTask;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created by hzq on 19/6/5.
 */
@Data
@Accessors(chain = true)
public class MonitorObject {
    private String sn; //商户号
    private String event; //事件
    private Long cost; //耗时
    private Integer status; //结果 成功或失败
    private String message; //消息

    //进件
    public static final String EVENT_LKL_CONTRACT = "lkl_contract"; //拉卡拉进件
    //更新

    public static final String EVENT_ALL = "all";//进件总耗时
    public static final String EVENT_LKL_STATUS_UPDATE = "lkl_status_update"; //状态更新
    public static final String EVENT_LKL_BASIC_UPDATE = "lkl_basic_update"; //基本信息更新
    public static final String EVENT_LKL_ACCOUNT_UPDATE = "lkl_account_update"; //银行卡更新
    public static final String EVENT_LKL_FEE_RATE_UPDATE = "lkl_fee_rate_update"; //费率更新
    public static final String EVENT_LKL_UPDATE_BOTH = "lkl_update_both"; //crm变更


    public static String getLogEventName(ContractSubTask subTask) {
        if (subTask == null) {
            return null;
        }
        String channel = subTask.getChannel();
        Integer payway = subTask.getPayway();
        Integer subTaskType = subTask.getTask_type();
        return channel + "_" + subTaskType + "_" + payway;
    }


    public static final String WEIXIN_AUTH_SUBMIT = "weixin_auth_submit";
    public static final String WEIXIN_AUTH_SYNC = "weixin_auth_sync";
    public static final String AUTH_STATUS_SYNC="auth_status_sync";
    public static final String WEIXIN_DIRECT_APPLY = "weixin_direct_apply";
    public static final String ALI_DIRECT_APPLY = "ali_direct_apply";
}
