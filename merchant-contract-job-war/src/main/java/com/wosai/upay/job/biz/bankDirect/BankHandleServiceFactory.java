package com.wosai.upay.job.biz.bankDirect;

import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description: 处理银行直连业务
 * <AUTHOR>
 * @Date 2021/4/26 11:03
 */
@Component
@Slf4j
public class BankHandleServiceFactory {

    private Map<String, BankHandleService> map = Maps.newConcurrentMap();

    public BankHandleServiceFactory(List<BankHandleService> list) {
        list.stream().forEach(x -> map.put(x.getDevCode(),x));
    }


    public BankHandleService getBankHandleService(String devCode) {
        return map.get(devCode);
    }

}
