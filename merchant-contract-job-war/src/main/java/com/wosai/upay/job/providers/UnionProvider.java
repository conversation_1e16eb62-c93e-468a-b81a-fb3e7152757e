package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.provider.UnionWeixinParam;
import com.wosai.upay.merchant.contract.service.NewUnionService;
import com.wosai.upay.merchant.contract.service.ProviderTradeParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 银联
 * Created by hzq on 19/4/2.
 */
@Component(ProviderUtil.UNION_PROVIDER_CHANNEL)
public class UnionProvider extends AbstractProvider {

    @Autowired
    ProviderTradeParamsService providerTradeParamsService;

    @Autowired
    NewUnionService newUnionService;

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask subTask) {
        Integer payWay = subTask.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        if (PaywayEnum.WEIXIN.getValue().equals(payWay)) {
            UnionWeixinParam unionWeixinParam = buildParam(contractChannel, subTask, UnionWeixinParam.class);
            return newUnionService.contractWeixinWithParams(contextParam, unionWeixinParam);
        }
        return null;
    }

    /**
     * not support
     *
     * @param:
     * @return:
     * @date: 16:31
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        return null;
    }

    @Override
    protected Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        throw new ContractBizException(getProviderBeanName() + "暂不支持配置微信appid");
    }
}
