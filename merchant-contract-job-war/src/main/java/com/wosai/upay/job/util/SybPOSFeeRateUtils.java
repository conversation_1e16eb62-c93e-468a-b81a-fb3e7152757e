package com.wosai.upay.job.util;

import com.wosai.upay.merchant.contract.model.tlV2.ProductInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 收银宝刷卡相关费率比较工具类
 * <AUTHOR>
 */
@Data
@Slf4j
public class SybPOSFeeRateUtils {

    /**
     * 比较两个费率信息是否一致
     * @param source 源费率信息
     * @param target 目标费率信息
     * @return 是否一致
     */
    public static boolean isFeeRateEqual(ProductInfo source, ProductInfo target) {
        if (source == null || target == null) {
            return false;
        }
        
        return isBasicRateEqual(source.getFeerate(), target.getFeerate()) &&
               isBasicRateEqual(source.getCreditrate(), target.getCreditrate()) &&
               isTopLimitEqual(source.getToplimit(), target.getToplimit());
    }
    
    /**
     * 比较基础费率是否一致
     */
    private static boolean isBasicRateEqual(String source, String target) {
        return Objects.equals(StringUtils.trimToNull(source),
                            StringUtils.trimToNull(target));
    }
    
    /**
     * 比较封顶值是否一致
     */
    private static boolean isTopLimitEqual(String source, String target) {
        String sourceValue = StringUtils.trimToNull(source);
        String targetValue = StringUtils.trimToNull(target);
        
        if (sourceValue == null && targetValue == null) {
            return true;
        }
        if (sourceValue == null || targetValue == null) {
            return false;
        }
        
        try {
            BigDecimal sourceDecimal = new BigDecimal(sourceValue);
            BigDecimal targetDecimal = new BigDecimal(targetValue);
            return sourceDecimal.compareTo(targetDecimal) == 0;
        } catch (NumberFormatException e) {
            log.warn("封顶值比较失败: source={}, target={}", sourceValue, targetValue);
            return false;
        }
    }
}