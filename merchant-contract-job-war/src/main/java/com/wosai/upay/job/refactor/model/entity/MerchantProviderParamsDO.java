package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import lombok.Data;

import java.util.Objects;


/**
 * 商户报备获取的银行交易参数存储表(new)表实体对象
 *
 * <AUTHOR>
 */
@TableName("merchant_provider_params")
@Data
public class MerchantProviderParamsDO {

    public static final String DISABLE_REASONS_KEY = "disable_reasons";

    public static final String DISABLE_REASON_KEY = "disableReason";


    /**
     * 主键
     */
    @TableId
    private String id;
    /**
     * 收钱吧商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 报备通道的时候实际传的外部商户号
     */
    @TableField(value = "out_merchant_sn")
    private String outMerchantSn;
    /**
     * 商户名
     */
    @TableField(value = "merchant_name")
    private String merchantName;
    /**
     * 银行通道渠道号
     */
    @TableField(value = "channel_no")
    private String channelNo;
    /**
     * 银行通道渠道大商户号
     */
    @TableField(value = "parent_merchant_id")
    private String parentMerchantId;
    /**
     * 交易通道服务提供方
     */
    @TableField(value = "provider")
    private Integer provider;
    /**
     * 渠道商户号
     */
    @TableField(value = "provider_merchant_id")
    private String providerMerchantId;
    /**
     * 支付方式
     */
    @TableField(value = "payway")
    private Integer payway;
    /**
     * 微信/参数配置/通联银联开放平台注册 两者标示 0:未配置, 1:配置成功 2:不需要配置
     */
    @TableField(value = "params_config_status")
    private Integer paramsConfigStatus;
    /**
     * 支付源商户号
     */
    @TableField(value = "pay_merchant_id")
    private String payMerchantId;
    /**
     * 银行通道商户号/微信商户号对应的微信公众号APP ID
     */
    @TableField(value = "weixin_sub_appid")
    private String weixinSubAppid;
    /**
     * 微信推荐关注公众号
     */
    @TableField(value = "weixin_subscribe_appid")
    private String weixinSubscribeAppid;
    /**
     * 微信小程序appid
     */
    @TableField(value = "weixin_sub_mini_appid")
    private String weixinSubMiniAppid;
    /**
     * 微信推荐关注小程序appid
     */
    @TableField(value = "weixin_receipt_appid")
    private String weixinReceiptAppid;
    /**
     * 是否被使用 0:未被使用 1:已使用
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 是否禁用 0:禁用 1:启用
     */
    @TableField(value = "disable_status")
    private Integer disableStatus;

    /**
     * 额外记录一些返回值，以备不时之需
     */
    @TableField(value = "extra")
    private String extra;
    /**
     * 生成时间
     */
    @TableField(value = "ctime")
    private Long ctime;
    /**
     * 修改时间
     */
    @TableField(value = "mtime")
    private Long mtime;
    /**
     * 是否删除
     */
    @TableField(value = "deleted")
    private Integer deleted;
    /**
     * 版本号
     */
    @TableField(value = "version")
    private Long version;
    /**
     * 报备规则
     */
    @TableField(value = "contract_rule")
    private String contractRule;
    /**
     * 报备规则组
     */
    @TableField(value = "rule_group_id")
    private String ruleGroupId;
    /**
     * -1未知 0失败  1成功
     */
    @TableField(value = "update_status")
    private Integer updateStatus;
    /**
     * 授权状态 0未授权 1已授权
     */
    @TableField(value = "auth_status")
    private Integer authStatus;
    /**
     * 点金计划状态 0未开通1已开通3开通失败
     */
    @TableField(value = "gold_status")
    private Integer goldStatus;
    /**
     * 微信结算Id
     */
    @TableField(value = "wx_settlement_id")
    private String wxSettlementId;
    /**
     * 微信子商户号用途 null-当payway != 3的默认值, 1:一般用途,2:微信校园食堂活动,3:微信线下教培活动
     */
    @TableField(value = "wx_use_type")
    private Integer wxUseType;
    /**
     * 阿里商户类型mcc
     */
    @TableField(value = "ali_mcc")
    private String aliMcc;
    /**
     * 子商户号状态 1正常 0禁用
     */
    @TableField(value = "merchant_state")
    private Integer merchantState;

    /**
     * 是否禁用
     *
     * @return true:禁用 false:未禁用
     */
    public boolean paramsIsDisabled() {
        return Objects.equals(disableStatus, DisableStatusEnum.DISABLE.getValue());
    }

}

