package com.wosai.upay.job.config;

import com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense;
import com.wosai.upay.job.avro.MerchantMessage;
import com.wosai.upay.job.consumer.MyKafkaAvroDeserializer;
import io.confluent.kafka.serializers.KafkaAvroDeserializerConfig;
import io.confluent.kafka.serializers.KafkaAvroSerializerConfig;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.lang.ArrayUtils;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import java.util.stream.Collectors;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.*;
import org.apache.kafka.common.TopicPartition;
import java.util.Collection;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Created by lihebin on 2018/9/2.
 */
@Configuration
@EnableKafka
public class KafkaReceiverConfig {

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.consumer.group-id}")
    private String bootstrapGroupId;

    @Value("${spring.kafka.registry-servers}")
    private String registryServers;

    @Value("${spring.kafka.ali.registry-servers}")
    private String aliRegistryServers;

    @Value("${spring.kafka.ali.bootstrap-servers}")
    private String aliBootstrapServers;

    @Value("${spring.kafka.ali.consumer.group-id}")
    private String aliBootstrapGroupId;

    @Autowired
    Environment environment;

    @Bean
    public Map<String, Object> dataBusConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, bootstrapGroupId);
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        return props;
    }

    @Bean
    public Map<String, Object> consumerArvoConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, bootstrapGroupId);
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        return props;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, MerchantUpdateBusinessLicense> avroListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, MerchantUpdateBusinessLicense> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(consumerArvoConfigs()));
        return factory;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, MerchantMessage> dataBusKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, MerchantMessage> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(dataBusConsumerConfigs()));
        return factory;
    }

    /**
     * 因为现在测试环境在往阿里kafka测试集群迁移，但是大家迁移进度不一样，所以新增一个
     *
     * @return
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, MerchantMessage> dataBusKafkaListenerContainerFactoryV2() {
        ConcurrentKafkaListenerContainerFactory<String, MerchantMessage> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(aliKafkaConsumerConfigsV2()));
        return factory;
    }

    /**
     * 生产在阿里，测试在自建
     *
     * @return
     */
    @Bean
    public Map<String, Object> aliKafkaConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, aliBootstrapGroupId);
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        return props;
    }

    @Bean
    public Map<String, Object> aliKafkaBatchConsumerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
        props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, aliBootstrapGroupId);
        props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        // 增加批处理参数配置
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100); // 每批次最大消息数
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 600000);  // 增加轮询间隔，默认600s
        return props;
    }


    /**
     * 生产在自建，测试在阿里
     *
     * @return
     */
    @Bean
    public Map<String, Object> aliKafkaConsumerConfigsV2() {
        Map<String, Object> props = new HashMap<>();
        if (!ArrayUtils.isEmpty(environment.getActiveProfiles()) && Objects.equals(environment.getActiveProfiles()[0], "prod")) {//添加测试环境支付目录
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
            props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, registryServers);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, bootstrapGroupId);
            props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        } else {
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, aliBootstrapServers);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, MyKafkaAvroDeserializer.class);
            props.put(KafkaAvroSerializerConfig.SCHEMA_REGISTRY_URL_CONFIG, aliRegistryServers);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, aliBootstrapGroupId);
            props.put(KafkaAvroDeserializerConfig.SPECIFIC_AVRO_READER_CONFIG, true);
        }
        return props;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, GenericRecord> smartKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, GenericRecord> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(aliKafkaBatchConsumerConfigs()));
        return factory;
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, GenericRecord> merchantActiveKafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, GenericRecord> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(aliKafkaConsumerConfigs()));
        return factory;
    }
}
