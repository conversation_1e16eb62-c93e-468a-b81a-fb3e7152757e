package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ContractTaskProcessStatusEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
@Component("BankDirectChangeAcquireResultJobHandler")
public class BankDirectChangeAcquireResultJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private McAcquirerChangeMapper acquirerChangeMapper;

    @Override
    public String getLockKey() {
        return "BankDirectChangeAcquireResultJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(
                    Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.APPLYING_CHANGE_ACQUIRE),
                    StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()),
                    param.getBatchSize()
            );
            log.info("changeAcquireResult count: {}", applyList.size());
            applyList.forEach(apply -> {
                try {
                    if (ShutdownSignal.isShuttingDown()) {
                        return;
                    }
                    McAcquirerChange acquireChange = getAcquireChange(apply);
                    Integer status = acquireChange.getStatus();
                    if (Objects.equals(status, AcquirerChangeStatus.SUCCESS)) {
                        handleSuccess(apply, acquireChange);
                    } else if (Objects.equals(status, AcquirerChangeStatus.FAIL)) {
                        handleFailure(apply, acquireChange);
                        final String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
                        deletedMerchantBankAccountPre(bankPreId, "收单机构切换失败");
                    }
                } catch (Exception e) {
                    log.error("商户:{},设置银行直连参数异常", apply.getMerchant_sn(), e);
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("商户:%s,设置银行直连参数异常:%s", apply.getMerchant_sn(), e.getMessage()));
                    handleFailure(apply, null);
                }
            });
        } catch (Exception e) {
            log.error("changeAcquireResult exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户切换收单机构状态异常:%s", e.getMessage()));
        }
    }

    /**
     * 获取商户切换收单机构的所有任务
     *
     * @param apply
     * @return
     */
    private McAcquirerChange getAcquireChange(BankDirectApply apply) {
        //获取申请的银行
        final Map<String, Object> extraMap = apply.getExtraMap();
        final String acquire = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.ACQUIRE);

        McAcquirerChange lasted = acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(apply.getMerchant_sn(), acquire);
        if (Objects.isNull(lasted)) {
            throw new ContractBizException("不存在切换收单机构任务");
        }
        return lasted;
    }

    /**
     * 处理切换收单机构成功的情况
     *
     * @param apply
     * @param acquireChange
     */
    private void handleSuccess(BankDirectApply apply, McAcquirerChange acquireChange) {
        String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
        //无卡进件银行
        if (StringUtils.isEmpty(bankPreId)) {
            finalizeSuccess(apply, acquireChange);
            return;
        }
        // 如果卡已经设为默认了，直接成功
        Map accountPre = merchantBankService.getMerchantBankAccountPre(bankPreId);
        if (Objects.equals(BeanUtil.getPropInt(accountPre, MerchantBankAccountPre.DEFAULT_STATUS), DefaultStatusEnum.DEFAULT.getValue())) {
            finalizeSuccess(apply, acquireChange);
            return;
        }

        if (BankAccountTypeEnum.isPublic(BeanUtil.getPropInt(accountPre, MerchantBankAccountPre.TYPE))) {
            // 对公商户账户不设为默认,且需要删除预存表这样可以让商户后续绑定
            deletedMerchantBankAccountPre(bankPreId, "对公商户切换机构成功删除");
            finalizeSuccess(apply, acquireChange);
        } else {
            handleIndividualMerchant(apply, acquireChange, bankPreId, accountPre);
        }
    }

    // 处理切换失败的情况
    private void handleFailure(BankDirectApply apply, McAcquirerChange acquireChange) {
        String memo = acquireChange != null ? acquireChange.getMemo() : "异常";
        modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.FAIL,
                BankDirectApplyConstant.Status.FAIL, memo, apply.getDev_code(),
                BankDirectApplyConstant.ProcessStatus.FAIL);
    }

    /**
     * 小微和个体商户,保证银行业务的银行卡和间连一致才会将开通银行业务的银行卡设为默认
     *
     * @param apply
     * @param acquireChange
     * @param bankPreId
     * @param accountPre
     */
    private void handleIndividualMerchant(BankDirectApply apply, McAcquirerChange acquireChange, String bankPreId, Map accountPre) {
        Map<String, Object> extraMap = apply.getExtraMap();
        String taskId = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.SYNC_BANK_ACCOUNT_TASK_ID);
        //将银行卡同步到上一个间连收单机构
        if (StringUtils.isEmpty(taskId)) {
            createSyncBankTaskAndDelayApply(apply, acquireChange, bankPreId, extraMap);
            return;
        }
        //换卡任务状态
        Optional<Integer> taskStatus = Optional.ofNullable(contractTaskMapper.selectByPrimaryKey(Long.valueOf(taskId)))
                .map(task -> task.getStatus());

        if (taskStatus.isPresent()) {
            if (ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue().equals(taskStatus.get())) {
                //银行卡同步成功
                bankService.replaceBankAccountForBankDirect(bankPreId);
                finalizeSuccess(apply, acquireChange);
            } else if (ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue().equals(taskStatus.get())) {
                //银行卡同步失败
                removeMismatchedBankCard(apply, accountPre, bankPreId);
                finalizeSuccess(apply, acquireChange);
            } else {
                delayApply(apply, 5);
            }
        } else {
            createSyncBankTaskAndDelayApply(apply, acquireChange, bankPreId, extraMap);
        }
    }

    /**
     * 银行业务开通成功
     *
     * @param apply
     * @param acquireChange
     */
    private void finalizeSuccess(BankDirectApply apply, McAcquirerChange acquireChange) {
        modifyStatus(apply.getMerchant_sn(), apply, BankDirectApplyConstant.DirectStatus.SUCCESS,
                BankDirectApplyConstant.Status.SUCCESS, acquireChange.getMemo(), apply.getDev_code(),
                BankDirectApplyConstant.ProcessStatus.SUCCESS);
        recordViewProcess(apply, 60, new Date());
    }

    /**
     * 将银行卡同步到上一个间连收单机构并且推移银行任务
     *
     * @param apply
     * @param acquireChange
     * @param bankPreId
     * @param extraMap
     */
    private void createSyncBankTaskAndDelayApply(BankDirectApply apply, McAcquirerChange acquireChange, String bankPreId, Map<String, Object> extraMap) {
        ContractTask syncBankTask = contractTaskService.syncBankAccount2Acquire(acquireChange.getMerchant_sn(), acquireChange.getSource_acquirer(), bankPreId);
        Long id = syncBankTask.getId();
        extraMap.put(BankDirectApplyConstant.Extra.SYNC_BANK_ACCOUNT_TASK_ID, id);
        apply.setExtra(JSONObject.toJSONString(extraMap));
        apply.setPriority(DateUtils.addMinutes(new Date(), 5));
        bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
    }

    /**
     * 移除不匹配的银行卡
     *
     * @param apply
     * @param accountPre
     * @param bankPreId
     */
    private void removeMismatchedBankCard(BankDirectApply apply, Map accountPre, String bankPreId) {
        Map defaultBankAccount = getBankAccount(apply.getMerchant_sn());
        String defaultIdentity = BeanUtil.getPropString(defaultBankAccount, MerchantBankAccountPre.IDENTITY);
        String preIdentity = BeanUtil.getPropString(accountPre, MerchantBankAccountPre.IDENTITY);
        //异名卡
        if (!Objects.equals(defaultIdentity, preIdentity)) {
            deletedMerchantBankAccountPre(bankPreId, "同步银行卡失败删除异名卡");
        }
    }

    /**
     * 当前商户正在用的银行卡
     *
     * @param merchantSn 商户号
     * @return
     */
    private Map getBankAccount(String merchantSn) {
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        final String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", MerchantBankAccountPre.DEFAULT_STATUS_TRUE)
        );
        if (listResult == null || WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            throw new CommonPubBizException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        return listResult.getRecords().get(0);
    }
}