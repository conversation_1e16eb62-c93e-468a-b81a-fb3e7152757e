package com.wosai.upay.job.biz.store;

import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.job.model.CheckStoreDiffProvinceReq;
import com.wosai.upay.job.model.HxStoreCheckResp;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @Description: 门店
 * <AUTHOR>
 * @Date: 2022/3/4 4:57 下午
 */
@Component
@Slf4j
public class StoreBiz {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;


    public String getFirstStoreSnByMerchantSn(String merchantSn) {
        //查询商户ID
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在");
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        PageInfo pageInfo = new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));

        ListResult result = storeService.getSimpleStoreListByMerchantId(merchantId, pageInfo);
        List<Map> records = result.getRecords();
        if (CollectionUtils.isEmpty(records) || records.isEmpty()) {
            throw new ContractBizException("门店不存在");
        }
        return WosaiMapUtils.getString(records.get(0), Store.SN);
    }

    public String getFirstStoreIdByMerchantSn(String merchantSn) {
        //查询商户ID
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new ContractBizException("商户不存在");
        }
        String merchantId = WosaiMapUtils.getString(merchant, DaoConstants.ID);

        PageInfo pageInfo = new PageInfo(1, 1, null, null, Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));

        ListResult result = storeService.getSimpleStoreListByMerchantId(merchantId, pageInfo);
        List<Map> records = result.getRecords();
        if (CollectionUtils.isEmpty(records) || records.isEmpty()) {
            throw new ContractBizException("门店不存在");
        }
        return WosaiMapUtils.getString(records.get(0),"id");
    }


    /**
     * 获取商户下的所有门店
     * @param merchantId
     * @return
     */
    public List<Map> getStoreListByMerchantId(String merchantId) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setOrderBy(Arrays.asList(new OrderBy(DaoConstants.CTIME, OrderBy.OrderType.ASC)));
        ListResult result = storeService.getSimpleStoreListByMerchantId(merchantId, pageInfo);
        List<Map> records = result.getRecords();
        return records;
    }


    public HxStoreCheckResp haveDiffProvinceStore(String  merchantId) {
        HxStoreCheckResp resp = new HxStoreCheckResp();
        resp.setCheckResult(false);
        PageInfo pageInfo = new PageInfo(1,1000);
        Map merchant = merchantService.getMerchant(merchantId);
        if (Objects.isNull(merchant)) {
            throw new CommonPubBizException("商户不存在,merchantId: " + merchantId);
        }
        String merchantDistrictCode = MapUtil.getString(merchant, "district_code");
        ListResult listResult = storeService.getStoreListByMerchantId(merchantId, pageInfo,new HashMap());
        boolean haveDiffProvinceStore = listResult.getRecords().stream().anyMatch(store -> (MapUtil.getInteger(store, "status") == 1 && !Utils.compareProvince(merchantDistrictCode,MapUtil.getString(store, "district_code"))));

        if (haveDiffProvinceStore) {
            resp.setCheckResult(true)
                    .setCheckMsg("华夏银行不支持跨省异地门店收款，请访问s.shouqianba.com，登录商户后台关闭跨省异地门店后再提交。");
        }

        return resp;
    }

}
