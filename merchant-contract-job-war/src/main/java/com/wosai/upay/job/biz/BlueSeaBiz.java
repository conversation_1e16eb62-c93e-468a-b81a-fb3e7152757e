package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.msg.AlipayMsgClient;
import com.alipay.api.msg.MsgHandler;
import com.alipay.api.request.AlipayMerchantIotDeviceBindRequest;
import com.alipay.api.request.AlipayMerchantIotDeviceQueryRequest;
import com.alipay.api.request.AlipayOpenSpBlueseaactivityQueryRequest;
import com.alipay.api.request.AntMerchantExpandShopQueryRequest;
import com.alipay.api.response.AlipayMerchantIotDeviceBindResponse;
import com.alipay.api.response.AlipayMerchantIotDeviceQueryResponse;
import com.alipay.api.response.AlipayOpenSpBlueseaactivityQueryResponse;
import com.alipay.api.response.AntMerchantExpandShopQueryResponse;
import com.github.rholder.retry.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.ApplyActivityService;
import com.wosai.trade.service.activity.request.PaySourceHandelRequest;
import com.wosai.upay.bank.info.api.model.District;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.directparams.AlipayV2DirectParamsBiz;
import com.wosai.upay.job.biz.messageStrategy.AliMessageHandleFactory;
import com.wosai.upay.job.biz.messageStrategy.AliMessageHandleService;
import com.wosai.upay.job.biz.messageStrategy.BlueSeaActivityChangedBiz;
import com.wosai.upay.job.enume.PaywayAcitivityOperateEnum;
import com.wosai.upay.job.mapper.AlyDistrictCodeMapper;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.AlyDistrictCode;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.MchSnapshot;
import com.wosai.upay.job.model.Process;
import com.wosai.upay.job.model.TerminalInfo;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.directparams.AlipayV2DirectParams;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.refactor.model.enums.UseStatusEnum;
import com.wosai.upay.job.repository.BlueSeaTaskRepository;
import com.wosai.upay.job.util.CombineBeans;
import com.wosai.upay.job.util.StringFilter;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.Utils;
import com.wosai.upay.merchant.audit.api.model.MerchantAudit;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import com.wosai.upay.merchant.contract.constant.LakalaConstant;
import com.wosai.upay.merchant.contract.constant.UnionConstant;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.bluesea.CustomizedInfo;
import com.wosai.upay.merchant.contract.model.newBlueSea.request.*;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static javax.management.timer.Timer.ONE_MINUTE;


/**
 * <AUTHOR>
 * @date 2020-11-24
 */
@Component
@Slf4j
public class BlueSeaBiz {

    @Autowired
    private BlueSeaTaskMapper blueSeaMapper;
    @Autowired
    private BlueSeaTaskRepository blueSeaTaskRepository;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private BlueSeaService blueSeaService;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private NewBlueSeaService newBlueSeaService;

    @Autowired
    private TerminalService terminalService;

    @Autowired
    private MerchantAuditService merchantAuditService;

    @Autowired
    private TagIngestService tagIngestService;

    @Autowired
    private MonitorLog monitorLog;

    @Autowired
    private AlyDistrictCodeMapper alyDistrictCodeMapper;

    @Autowired
    private BlueSeaActivityChangedBiz blueSeaActivityChangedBiz;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private AlipayV2DirectParamsBiz alipayV2DirectParamsBiz;

    @Autowired
    private RedisTemplate redisTemplate;

    @Resource(name = "sqbStoreService")
    private StoreService storeService;

    private static Set<String> SUPPORT_INDUSTRY = Sets.newHashSet("5812", "5814", "5815", "5462", "5811", "5499", "5451", "5423", "5441");

    private static Set<String> KX_SUPPORT_INDUSTRY = Sets.newHashSet("5331", "5411", "5422", "5641", "5977", "5993");

    private static String TOBACCO_INDUSTRY = "5993";

    private static String SHOP_INDUSTRY = "5499";

    @Value("${ant.target.serve}")
    private String antTargetServe;

    @Value("${KX_TAG}")
    private String kxTarget;

    @Value("${BLUESEA_TAG}")
    private String blueSeaTarget;

    @Autowired
    private AliMessageHandleFactory aliMessageHandleFactory;

    @Autowired
    private DistrictsServiceV2 districtsServiceV2;

    @Autowired
    private ApplyActivityService applyActivityService;

    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    private static Retryer<AliCommResponse> retryer;

    /**
     * 实例化alipayMsgClient
     */
    @PostConstruct
    public void initAlipayMsgClient() {
        String appId = applicationApolloConfig.getNewBlueSeaAppId();
        // 目标蚂蚁金服服务端地址，线上环境为 openchannel.alipay.com
        String serverHost = antTargetServe;
        if (Objects.equals(serverHost, "")) {
            return;
        }
        // 数据签名方式，请与应用设置的默认签名方式保持一致
        String signType = applicationApolloConfig.getNewBlueSignType();
        // 应用私钥
        String appPrivateKey = applicationApolloConfig.getNewBlueSeaPrivateKey();
        // 支付宝公钥
        String alipayPublicKey = applicationApolloConfig.getNewBlueSeaPublicKey();
        // 获取client对象，一个appId对应一个实例
        final AlipayMsgClient alipayMsgClient = AlipayMsgClient.getInstance(appId);
        try {
            alipayMsgClient.setConnector(serverHost);
            alipayMsgClient.setSecurityConfig(signType, appPrivateKey, alipayPublicKey);
            //设置消息处理逻辑
            messageHandle(alipayMsgClient);
            //设置线程池
            alipayMsgClient.setBizThreadPoolCoreSize(16);
            alipayMsgClient.setBizThreadPoolMaxSize(32);
            alipayMsgClient.connect();
        } catch (InterruptedException e) {
            log.error("initAlipayMsgClient  InterruptedException : {}", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("initAlipayMsgClient  Exception : {}", e);
            throw new RuntimeException(e);
        }
        retryer = RetryerBuilder.<AliCommResponse>newBuilder()
                .retryIfException() // 抛出异常会进行重试
//                .retryIfResult(Predicates.equalTo("RECORD_NOT_EXIST"))
                // 重试策略, 此处设置的是重试间隔时间
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 10, TimeUnit.SECONDS))
                // 重试次数
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();

    }

    /**
     * 支付宝消息处理
     *
     * @param
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 15:13 2020/11/24
     */
    public void messageHandle(AlipayMsgClient alipayMsgClient) {
        alipayMsgClient.setMessageHandler(new MsgHandler() {
            /**
             * 客户端接收到消息后回调此方法
             *
             * @param msgApi     接收到的消息的消息api名
             * @param msgId      接收到的消息的消息id
             * @param bizContent 接收到的消息的内容，json格式
             */
            @Override
            public void onMessage(String msgApi, String msgId, String bizContent) {
                log.info("receive message msgApi:{}, msgId:{}, bizContent:{}", msgApi, msgId, bizContent);
                //TODO由于是集群注意幂等,在onMessage方法实现中，不抛出任何异常，则认为您消费消息成功，否则服务端认为此消息客户端没有消费成功，会触发重试。
                //根据msgApi找到消息处理工厂中对应的消息处理类
                final AliMessageHandleService messageHandleService = aliMessageHandleFactory.getAliMessageHandleService(msgApi);
                //没有匹配到处理类
                if (Objects.isNull(messageHandleService)) {
                    return;
                }
                try {
                    messageHandleService.handleMessageBiz(bizContent);
                } catch (Exception e) {
                    log.error("阿里推送消息处理异常 : {}", e);
                    monitorLog.recordMonitor("阿里推送消息处理异常", msgApi + " " + e.getMessage());
                    throw new RuntimeException("消息处理异常");
                }
            }
        });
    }

    public CommonResult applyNewBlueSea(String merchantSn, Long auditId, Map formBody) {
        if (!checkRepetiveness(merchantSn, BlueSeaConstant.blueSeaType)) {
            return new CommonResult(CommonResult.BIZ_FAIL, BlueSeaConstant.REASON_REPETIVENESS);
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = MapUtils.getString(merchant, CommonModel.ID);
        if (WosaiMapUtils.isEmpty(merchant)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户不存在");
        }
        Tuple2<String, Boolean> aliMchInfo = getInUseMchId(merchantSn);
        String aliMchId = aliMchInfo.get_1();
        if (WosaiStringUtils.isEmpty(aliMchId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户没有支付宝子商户号");
        }
        String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
        if (WosaiStringUtils.isEmpty(industryId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "行业不满足新蓝海要求");
        }
        String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        if (!SUPPORT_INDUSTRY.contains(aliMcc)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "行业不满足新蓝海要求");
        }
        //保存快照
        MchSnapshot snapshot = MchSnapshot.builder().aliMcc(aliMcc).direct(aliMchInfo.get_2()).build();
        Tuple2<Boolean, String> tuple2 = buildSnapshotPic(snapshot, merchantSn, merchantId, aliMcc, formBody);
        if (tuple2.get_1()) {
            BlueSeaTask task = new BlueSeaTask();
            task.setAudit_id(auditId);
            task.setAli_mch_id(aliMchId);
            task.setMerchant_sn(merchantSn);
            task.setMerchant_id(merchantId);
            task.setStatus(BlueSeaConstant.PENDING);
            task.setForm_body(JSON.toJSONString(formBody));
            task.setMch_snapshot(JSON.toJSONString(snapshot));
            task.setType(BlueSeaConstant.blueSeaType);
            blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
            return new CommonResult(CommonResult.SUCCESS, "成功");
        }
        return new CommonResult(CommonResult.BIZ_FAIL, tuple2.get_2());
    }

    /**
     * @param merchantSn 申请快消商户号
     * @param auditId    审批好
     * @param formBody   app传过来的信息
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 16:45 2020/12/23
     */
    public CommonResult applyKx(String merchantSn, Long auditId, Map formBody) {
        if (!checkRepetiveness(merchantSn, BlueSeaConstant.kx)) {
            return new CommonResult(CommonResult.BIZ_FAIL, BlueSeaConstant.REASON_REPETIVENESS);
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = MapUtils.getString(merchant, CommonModel.ID);
        if (WosaiMapUtils.isEmpty(merchant)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户不存在");
        }
        Tuple2<String, Boolean> aliMchInfo = getInUseMchId(merchantSn);
        String aliMchId = aliMchInfo.get_1();
        if (WosaiStringUtils.isEmpty(aliMchId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户没有支付宝子商户号");
        }
        String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
        if (WosaiStringUtils.isEmpty(industryId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "行业不满足快消要求");
        }
        String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        if (!KX_SUPPORT_INDUSTRY.contains(aliMcc)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "行业不满足快消要求");
        }
        //保存快照
        MchSnapshot snapshot = MchSnapshot.builder().aliMcc(aliMcc).direct(aliMchInfo.get_2()).build();
        Tuple2<Boolean, String> tuple2 = buildSnapshotPic(snapshot, merchantSn, merchantId, aliMcc, formBody);
        if (tuple2.get_1()) {
            BlueSeaTask task = new BlueSeaTask();
            task.setAudit_id(auditId);
            task.setAli_mch_id(aliMchId);
            task.setMerchant_sn(merchantSn);
            task.setMerchant_id(merchantId);
            task.setStatus(BlueSeaConstant.PENDING);
            task.setForm_body(JSON.toJSONString(formBody));
            task.setMch_snapshot(JSON.toJSONString(snapshot));
            task.setType(BlueSeaConstant.kx);
            blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
            return new CommonResult(CommonResult.SUCCESS, "成功");
        }
        return new CommonResult(CommonResult.BIZ_FAIL, tuple2.get_2());
    }

    private Tuple2<Boolean, String> buildSnapshotPic(MchSnapshot mchSnapshot, String merchantSn, String merchantId, String aliMcc, Map formBody) {
        Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        String business_lic = MapUtils.getString(license, MerchantBusinessLicence.PHOTO);
        if (StringUtils.isEmpty(business_lic)) {
            return new Tuple2<>(false, BlueSeaConstant.REASON_NOLICENSE);
        }
        Map audit = merchantAuditService.getAuditByMerchantSn(merchantSn);
        mchSnapshot.setBusiness_lic(business_lic).setIndoor_pic(MapUtils.getString(formBody, "indoor")).setShop_entrance_pic(MapUtils.getString(audit, MerchantAudit.BRAND_PHOTO));
        if (TOBACCO_INDUSTRY.equalsIgnoreCase(aliMcc)) {
            String tobacoo_pic = MapUtils.getString(formBody, "tobacco_url");
            if (StringUtils.isEmpty(tobacoo_pic)) {
                return new Tuple2<>(false, BlueSeaConstant.REASON_NOTOBACOO);
            }
            mchSnapshot.setTobacco_pic(tobacoo_pic);
        }
        if (SHOP_INDUSTRY.equalsIgnoreCase(aliMcc)) {
            String shop_url = MapUtils.getString(formBody, "shop_url");
            if (StringUtils.isEmpty(shop_url)) {
                return new Tuple2<>(false, BlueSeaConstant.REASON_NOSHOP);
            }
            mchSnapshot.setShop_url(shop_url);
        }
        return new Tuple2<>(true, null);
    }

    public CommonResult applyAliCarnival(String merchantSn, Long auditId, Map formBody) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (WosaiMapUtils.isEmpty(merchant)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户不存在");
        }
        Tuple2<String, Boolean> aliMchInfo = getInUseMchId(merchantSn);
        String aliMchId = aliMchInfo.get_1();
        if (WosaiStringUtils.isEmpty(aliMchId)) {
            return new CommonResult(CommonResult.BIZ_FAIL, "商户没有支付宝子商户号");
        }

        //todo 重复性校验 sn or sn+aliaccount

        //保存快照
        final MchSnapshot snapshot = MchSnapshot.builder().direct(aliMchInfo.get_2()).build();
        BlueSeaTask task = new BlueSeaTask();
        task.setAudit_id(auditId);
        task.setAli_mch_id(aliMchId);
        task.setMerchant_sn(merchantSn);
        task.setMerchant_id(BeanUtil.getPropString(merchant, DaoConstants.ID));
        task.setStatus(BlueSeaConstant.PENDING);
        task.setForm_body(JSON.toJSONString(formBody));
        task.setMch_snapshot(JSON.toJSONString(snapshot));
        task.setType(2);
        blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
        return new CommonResult(CommonResult.SUCCESS, "成功");
    }

    /**
     * @param merchantSn
     * @return 支付宝子商户号，是否直连
     */
    public Tuple2<String, Boolean> getInUseMchId(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            return new Tuple2<>("", false);
        }
        List<MerchantProviderParams> direct = params.stream().filter(param -> ProviderEnum.ALI_PAY.getValue().equals(param.getProvider())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(direct)) {
            String payMchId = direct.get(0).getPay_merchant_id();
            if (WosaiStringUtils.isEmpty(payMchId)) {
                AlipayV2DirectParams directParams = alipayV2DirectParamsBiz.getDirectParams(merchantSn);
                if (Objects.nonNull(directParams) && Objects.nonNull(directParams.getAlipay_v2_trade_params())) {
                    payMchId = directParams.getAlipay_v2_trade_params().getMch_id();
                }
            }
            return new Tuple2<>(payMchId, true);
        }
        List<MerchantProviderParams> unDirect = params.stream().filter(param -> !ProviderEnum.ALI_PAY.getValue().equals(param.getProvider())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(unDirect)) {
            return new Tuple2<>(unDirect.get(0).getPay_merchant_id(), false);
        }
        return new Tuple2<>("", false);
    }

    /**
     * @param merchantSn
     * @return 支付宝子商户号，provider
     */
    public Tuple2<String, Integer> getInUseMchIdV2(String merchantSn) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andStatusEqualTo(UseStatusEnum.IN_USE.getValue())
                .andDeletedEqualTo(false);
        List<MerchantProviderParams> params = merchantProviderParamsMapper.selectByExample(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            return new Tuple2<>("", -1);
        }
        List<MerchantProviderParams> direct = params.stream().filter(param -> PaywayEnum.ALIPAY.getValue().equals(param.getProvider())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(direct)) {
            String payMchId = direct.get(0).getPay_merchant_id();
            if (WosaiStringUtils.isEmpty(payMchId)) {
                AlipayV2DirectParams directParams = alipayV2DirectParamsBiz.getDirectParams(merchantSn);
                if (Objects.nonNull(directParams) && Objects.nonNull(directParams.getAlipay_v2_trade_params())) {
                    payMchId = directParams.getAlipay_v2_trade_params().getMch_id();
                }
            }
            return new Tuple2<>(payMchId, 2);
        }
        List<MerchantProviderParams> unDirect = params.stream().filter(param -> !PaywayEnum.ALIPAY.getValue().equals(param.getProvider())).collect(Collectors.toList());
        if (WosaiCollectionUtils.isNotEmpty(unDirect)) {
            return new Tuple2<>(unDirect.get(0).getPay_merchant_id(), unDirect.get(0).getProvider());
        }
        return new Tuple2<>("", -1);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long taskId, int status, String description, MchSnapshot mchSnapshot, String aliShopOrderId, List<TerminalInfo> terminalInfos, String activityOrderId, Integer operateId) {//, Integer operateId
        BlueSeaTask task = blueSeaMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new ContractBizException("任务不存在");
        }
        if (task.getStatus() == BlueSeaConstant.SUCCESS || task.getStatus() == BlueSeaConstant.FAIL) {
            throw new ContractBizException("任务已结束");
        }
        // todo 记录详情

        BlueSeaTask updateValue = new BlueSeaTask();
        updateValue.setId(taskId);
        updateValue.setStatus(status);
        updateValue.setDescription(description);
        updateValue.setActivity_order_id(activityOrderId);
        //记录变更过程
        //原有记录
        final List<Process> processes = task.getProcessList();
        final Process process = Process.builder().status(status).updateAt(System.currentTimeMillis()).build();
        if (!CollectionUtils.isEmpty(processes)) {
            //添加新纪录
            processes.add(process);
            updateValue.setProcess(JSON.toJSONString(processes));
        } else {
            final ArrayList<Process> list = Lists.newArrayList(process);
            updateValue.setProcess(JSON.toJSONString(list));
        }
        if (Objects.nonNull(mchSnapshot)) {
            //原有商户快照
            final MchSnapshot targetSnapshot = task.getMchSnapshot();
            //原有属性与添加新属性组合
            CombineBeans.combineAttributes(mchSnapshot, targetSnapshot);
            updateValue.setMch_snapshot(JSON.toJSONString(targetSnapshot));
        }
        //记录aliShopOrderId
        if (StringUtils.isNotEmpty(aliShopOrderId)) {
            updateValue.setAli_shop_order_id(aliShopOrderId);
        }
        if (!StringUtil.listEmpty(terminalInfos)) {
            updateValue.setTerminal_info(JSON.toJSONString(terminalInfos));
        }
        blueSeaMapper.updateByPrimaryKeySelective(updateValue);

        //回调给审批中心
        callBack(status, description, task.getAudit_id(), task.getApply_id(), MapUtils.getLong(JSONObject.parseObject(task.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID), task.getMerchant_id(), task.getType(), operateId);
    }

    /**
     * @param status      成功or失败
     * @param description 失败描述
     * @param auditId     审批Id
     * @param type        type=3 标识快消,其他值代表蓝海
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 16:58 2020/12/4
     */
    public void callBack(int status, String description, Long auditId, Long applyId, Long templateId, String merchantId, Integer type, Integer operateId) {
        if (Utils.isNullOrZero(applyId)) {
            if (!Lists.newArrayList(BlueSeaConstant.SUCCESS, BlueSeaConstant.FAIL).contains(status)) {
                return;
            }
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(auditId).templateId(templateId)
                    .build();
            if (status == BlueSeaConstant.SUCCESS) {
                callBackBean.setResultType(1);
                String msg = StringUtils.isNotBlank(description) ? "报名成功" + description : "报名成功";
                callBackBean.setMessage(msg);
                if (Objects.equals(type, BlueSeaConstant.kx)) {
                    tagIngestService.ingest(Arrays.asList(merchantId), kxTarget, BlueSeaConstant.KX_TAG_OPEN);
                } else if (Objects.equals(type, BlueSeaConstant.blueSeaType)) {
                    tagIngestService.ingest(Arrays.asList(merchantId), blueSeaTarget, BlueSeaConstant.BLUESEA_TAG_OPEN);
                }
            } else {
                //文案转义
                final Map<String, String> map = applicationApolloConfig.getNewBlueSeaErrorMessage().get(description);
                //apollo找到不到
                if (MapUtils.isEmpty(map)) {
                    callBackBean.setMessage(StringUtils.isEmpty(description) ? "处理异常" : description);
                } else {
                    final String retMessage = map.get("retMessage");
                    callBackBean.setMessage(StringUtils.isEmpty(retMessage) ? map.get("subMessage") : retMessage);
                }
                callBackBean.setResultType(2);
            }
            log.info("callBackBean:{}", JSONObject.toJSONString(callBackBean));
            if (!redisTemplate.hasKey(String.valueOf(auditId))) {
                callBackService.addComment(callBackBean);
                redisTemplate.opsForValue().set(String.valueOf(auditId), String.valueOf(auditId), 10L, TimeUnit.SECONDS);
            }
        } else {
            // 失败重试的不需要处理
            if (StringUtils.isNotEmpty(description) && description.contains("收单机构更新MCC码失败定时任务继续处理")) {
                return;
            }
            //调活动管理平台接口
            //通过状态进行判断  支付宝高校-9，教培-2 是前置状态的最后一个阶段    19 报名成功   20 处理失败
            if (judgeSendManagementPlatform(status, type)) {
                if (!(type == 6 && status == 2 && Objects.equals(operateId, PaywayAcitivityOperateEnum.APPLY.getVal()))) {//审批通过了，支付宝教培，还是会有升级m3的情况，不需要发送给管理平台
                    PaySourceHandelRequest paySourceHandelRequest = new PaySourceHandelRequest();
                    paySourceHandelRequest.setType(operateId);
                    paySourceHandelRequest.setStatus(status != 20 ? 1 : 2);
                    paySourceHandelRequest.setMsg(StringUtils.isEmpty(description) ? "处理异常" : description);
                    applyActivityService.paySourceResultHandle(applyId, paySourceHandelRequest);
                }
            }
        }
    }

    private boolean judgeSendManagementPlatform(Integer status, Integer type) {
        return Objects.equals(status, activityTypeToStatusMap.get(type)) || Objects.equals(status, 19) || Objects.equals(status, 20);
    }

    private final static Map<Integer, Integer> activityTypeToStatusMap = new HashMap<Integer, Integer>() {
        {
            put(5, 9);
            put(6, 2);
        }
    };

    /**
     * 更新重试次数
     *
     * @param task
     */
    public void updateReTry(BlueSeaTask task, String description, MchSnapshot mchSnapshot) {
        int retry = task.getRetry() == null ? 0 : task.getRetry();
        BlueSeaTask updateValue = new BlueSeaTask();
        updateValue.setId(task.getId());
        updateValue.setRetry(retry + 1);
        updateValue.setPriority(new Date(System.currentTimeMillis() + retry * ONE_MINUTE));
        if (!StringUtil.empty(description)) {
            updateValue.setDescription(description);
        }
        if (!Objects.isNull(mchSnapshot)) {
            updateValue.setMch_snapshot(JSON.toJSONString(mchSnapshot));
        }
        blueSeaMapper.updateByPrimaryKeySelective(updateValue);

    }

    public void updateTerminalRetry(BlueSeaTask task, String description, List<TerminalInfo> terminalInfoList) {
        int retry = task.getRetry() == null ? 0 : task.getRetry();
        BlueSeaTask blueSeaTask = new BlueSeaTask().setId(task.getId()).setRetry(retry + 1);
        if (!StringUtil.empty(description)) {
            blueSeaTask.setDescription(description);
        }
        if (!StringUtil.listEmpty(terminalInfoList)) {
            blueSeaTask.setTerminal_info(JSON.toJSONString(terminalInfoList));
        }
        blueSeaMapper.updateByPrimaryKeySelective(blueSeaTask);
    }

    public List<BlueSeaTask> getBlueSeaTasks(List<Integer> status, List<Integer> type, int limit, long queryTime) {
        long current = System.currentTimeMillis();
        String startTime = StringUtil.formatDate(current - queryTime);
        String endTime = StringUtil.formatDate(current);
        return blueSeaMapper.selectByStatus(status, type, limit, startTime, endTime);
    }

    /**
     * 升级M3
     *
     * @param pendingTask 活动任务
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 16:50 2020/11/26
     */
    public void updateMerchantToM3(BlueSeaTask pendingTask) {
        //商户号
        final String merchantSn = pendingTask.getMerchant_sn();
        final CustomizedInfo customizedInfo = buildCustomizedInfo(merchantSn);
        //主键Id
        final Long id = pendingTask.getId();
        try {
            log.info("updateMerchantToM3 merchantSn:{},customizedInfo:{}", merchantSn, JSONObject.toJSONString(customizedInfo));
            final boolean result = blueSeaService.updateMerchantToM3(merchantSn, customizedInfo);
            if (!result) {
                updateStatus(id, BlueSeaConstant.FAIL, "商户升级M3失败", null, null, null, null, null);
                return;
            }
            //记录商户是从M2升级到M3的
            updateStatus(pendingTask.getId(), BlueSeaConstant.M3, null, null, null, null, null, null);
        } catch (Exception e) {
            log.error("updateMerchantToM3 error:{}", e);

            updateStatus(id, BlueSeaConstant.FAIL, "升级M3" + e.getMessage(), null, null, null, null, null);
        }
    }

    /**
     * 根据商户号获取升级M3需要信息
     *
     * @param merchantSn 商户号
     * @return CustomizedInfo 上传信息
     * @Author: zhmh
     * @Description:
     * @time: 17:03 2020/11/26
     */
    public CustomizedInfo buildCustomizedInfo(String merchantSn) {
        final Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        final CustomizedInfo customizedInfo = new CustomizedInfo();
        String servicePhone = BeanUtil.getPropString(merchant, Merchant.CUSTOMER_PHONE);
        if (StringUtils.isEmpty(servicePhone)) {
            servicePhone = BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE);
            if (StringUtils.isEmpty(servicePhone)) {
                servicePhone = "***********";
            }
        }
        customizedInfo.setService_phone(servicePhone);
        String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
        String industryAlyCode = industryMappingCommonBiz.getAliIndirectWmAly(industryId);
        if (com.wosai.data.util.StringUtil.empty(industryAlyCode)) {
            throw new CommonPubBizException("商户行业信息为空");
        }
        customizedInfo.setBusiness(industryAlyCode);

        final String merchantId = BeanUtil.getPropString(merchant, "id");
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", 1));
        Map bankAccount = null;
        if (listResult != null && listResult.getTotal() > 0) {
            bankAccount = listResult.getRecords().get(0);
        }
        if (MapUtils.isEmpty(bankAccount)) {
            throw new CommonPubBizException("银行账户信息不存在");
        }
        Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId(merchantId);
        String merchantName = MapUtils.getString(license, Merchant.NAME);
        if (StringUtil.empty(merchantName)) {
            merchantName = MapUtils.getString(merchant, Merchant.NAME);
        }
        customizedInfo.setMerchant_name(merchantName);

        String alias = BeanUtil.getPropString(merchant, Merchant.BUSINESS_NAME);
        if (StringUtils.isEmpty(alias)) {
            alias = Utils.substring(merchantName, 20);
        } else {
            alias = Utils.substring(StringFilter.filter(alias), 20);
        }
        customizedInfo.setMerchant_shortname(alias);

        String business = BeanUtil.getPropString(license, MerchantBusinessLicence.NUMBER);
        if (StringUtils.isEmpty(business)) {
            business = BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.IDENTITY);
        } else {
            //BUSINESS_LICENSE_TYPE 有营业执照 才传
            int type = BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE);
            String business_license_type = "NATIONAL_LEGAL_MERGE";
            if (type == BusinessLicenseTypeEnum.INSTITUTIONAL.getValue() || type == BusinessLicenseTypeEnum.UNIFORM.getValue()) {
                business_license_type = "INST_RGST_CTF";
            }
            customizedInfo.setBusiness_license_type(business_license_type);
        }
        customizedInfo.setBusiness_license(business);

        String address = StringFilter.replaceNumber(BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS));
        address = Utils.substring(com.wosai.data.util.StringUtil.empty(address) ? LakalaConstant.SQB_MERCHANT_ADDRESS : StringFilter.filter(address), 256);
        customizedInfo.setAddress(address);

        customizedInfo.setAdd_type("BUSINESS_ADDRESS");
        customizedInfo.setCard_no(BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.NUMBER));
        customizedInfo.setCard_name(BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.HOLDER));

        customizedInfo.setContact_name(BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.HOLDER));
        customizedInfo.setContact_id_no(BeanUtil.getPropString(bankAccount, MerchantBankAccountPre.IDENTITY));
        customizedInfo.setContact_phone(servicePhone);

        int type = BeanUtil.getPropInt(bankAccount, MerchantBankAccountPre.TYPE);
        //对公 如果存在法人信息  上送作为联系人
        if (type == BusinessLicenseTypeEnum.ENTERPRISE.getValue()) {
            String legalPersonName = BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_NAME);
            String legalPersonIdNo = BeanUtil.getPropString(license, MerchantBusinessLicence.LEGAL_PERSON_ID_NUMBER);
            if (!StringUtils.isEmpty(legalPersonName)) {
                customizedInfo.setContact_name(legalPersonName);
            }
            if (!StringUtils.isEmpty(legalPersonIdNo)) {
                customizedInfo.setContact_id_no(legalPersonIdNo);
            }
        }

        String unionMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
        if (com.wosai.data.util.StringUtil.empty(unionMcc)) {
            throw new CommonPubBizException("mcc行业为空");
        }
        customizedInfo.setMcc(unionMcc);
        return customizedInfo;
    }

    /**
     * @param
     * @param merchantId
     * @param ipRoleId
     * @return
     * @Author: zhmh
     * @Description: 判断是否存在蚂蚁店铺
     * @time: 10:18 2021/2/24
     */
    public AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> existAntShop(String merchantId, String storeSn, String ipRoleId) {
        if (WosaiStringUtils.isEmpty(storeSn)) {
            List<Map> records = getStoreList(merchantId);
            storeSn = BeanUtil.getPropString(records.get(0), Store.SN);
        }
        final AntMerchantExpandShopQueryReq build = AntMerchantExpandShopQueryReq.builder().storeId(storeSn).ipRoleId(ipRoleId).build();
        final AliCommResponse<AntMerchantExpandShopQueryRequest, AntMerchantExpandShopQueryResponse> shopQuery = newBlueSeaService.antMerchantExpandShopQuery(build);
        if (shopQuery.isSuccess()) {
            return shopQuery;
        }
        return null;
    }

    public AntMerchantExpandShopCreateReq buildShopCreateReq(AntMerchantExpandShopCreateReq req, String merchantSn, String storeSn) {
        //经营地址。地址对象中省、市、区、地址必填，其余选填
        final AntMerchantExpandShopCreateReq.AddressInfo addressInfo = new AntMerchantExpandShopCreateReq.AddressInfo();
        final Map merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            throw new CommonPubBizException("商户不存在");
        }
        if (WosaiStringUtils.isEmpty(storeSn)) {
            List<Map> records = getStoreList(BeanUtil.getPropString(merchant, DaoConstants.ID));
            storeSn = BeanUtil.getPropString(records.get(0), Store.SN);
        }

        Map store = storeService.getStoreByStoreSn(storeSn);
        if (MapUtils.isEmpty(store)) {
            throw new CommonPubBizException("门店不存在");
        }

        //收钱吧省,市,区,详细
        final String province = BeanUtil.getPropString(store, Store.PROVINCE, BeanUtil.getPropString(merchant, Merchant.PROVINCE));
        final String city = BeanUtil.getPropString(store, Store.CITY, BeanUtil.getPropString(merchant, Merchant.CITY));
        final String district = BeanUtil.getPropString(store, Store.DISTRICT, BeanUtil.getPropString(merchant, Merchant.DISTRICT));
        final String streetAddress = BeanUtil.getPropString(store, Store.STREET_ADDRESS, BeanUtil.getPropString(merchant, Merchant.STREET_ADDRESS));
        final String storeName = BeanUtil.getPropString(store, Store.NAME, BeanUtil.getPropString(merchant, Merchant.NAME));
        addressInfo.setAddress(streetAddress);

        final District districtObject = districtsServiceV2.getCodeByName(province + " " + city + " " + district);
        if (Objects.isNull(districtObject)) {
            throw new CommonPubBizException("省市区信息不存在");
        }

        String provinceCode = districtObject.getProvince_code();
        String cityCode = districtObject.getCity_code();
        String districtCode = districtObject.getCode();

        // 为什么写以下这么复杂的逻辑，是因为收钱吧和支付宝使用的省市区数据并不完全统一，有些省市区支付宝不支持，
        // 所以需要到支付宝支持的省市区重新匹配一遍
        List<AlyDistrictCode> alyCity = alyDistrictCodeMapper.selectByCondition(
                AlyDistrictCode.builder().code(cityCode).build()
        );
        List<AlyDistrictCode> alyDistrict = alyDistrictCodeMapper.selectByCondition(
                AlyDistrictCode.builder().code(districtCode).build()
        );
        if (CollectionUtils.isEmpty(alyCity)) {
            cityCode = alyDistrictCodeMapper.selectByCondition(
                    AlyDistrictCode.builder().parent_code(provinceCode).build()
            ).get(0).getCode();
            districtCode = alyDistrictCodeMapper.selectByCondition(
                    AlyDistrictCode.builder().parent_code(cityCode).build()
            ).get(0).getCode();
        } else if (CollectionUtils.isEmpty(alyDistrict)) {
            districtCode = alyDistrictCodeMapper.selectByCondition(
                    AlyDistrictCode.builder().parent_code(cityCode).build()
            ).get(0).getCode();
        }

        addressInfo.setProvinceCode(provinceCode);
        addressInfo.setCityCode(cityCode);
        addressInfo.setDistrictCode(districtCode);
        req.setBusinessAddress(addressInfo);
        //构建店铺类目
        //支付宝三级类目
//        final Map<String, String> categoryMap = paramsConfig.getMap("aly_third_category", "aly_third_category");
//        final String aliMcc = blueSeaTask.getMchSnapshot().getAliMcc();
//        req.setShopCategory(BeanUtil.getPropString(categoryMap, aliMcc));
        req.setStoreId(storeSn);
        //店铺经营类型,产品定义默认01
        req.setShopType("01");
        //TODO 产品定义间连的支付宝PID就是M4查回来的binduserid,直连使用阿里子商户号
//        final MchSnapshot snapshot = blueSeaTask.getMchSnapshot();
//        req.setIpRoleId(snapshot.getDirect() ? blueSeaTask.getAli_mch_id() : snapshot.getBindUserId());
        //店铺名称

        req.setShopName(storeName);
        req.setContactPhone(BeanUtil.getPropString(merchant, Merchant.CONTACT_PHONE));
        req.setContactMobile(BeanUtil.getPropString(merchant, Merchant.CONTACT_CELLPHONE));
        return req;
    }

    public List<Map> getStoreList(String merchantId) {
        final ListResult listResult = storeService.getStoreListByMerchantId(merchantId, null, null);
        final List<Map> records = listResult.getRecords();
        log.info("商户 Id:{},records : {} ", records, JSONObject.toJSONString(records));
        if (CollectionUtils.isEmpty(records)) {
            throw new CommonPubBizException("商户 Id:" + merchantId + ", 没有门店");
        }
        return records;
    }

    public Map getStoreBySn(String storeSn) {
        return storeService.getStoreByStoreSn(storeSn);
    }


    /**
     * 获取该商户id当下绑定的所有蜻蜓终端
     *
     * @param merchantId
     * @return
     */
    public List<Map> findAllDragonFlyOrPc(String merchantId, String key) {
        List<String> vendorAppids = Arrays.asList();
        if (BlueSeaConstant.BLUESEA_TERMINAL_VENDER_APP_APPID.equalsIgnoreCase(key)) {
            vendorAppids = applicationApolloConfig.getBlueseaTerminalVenderAppAPPid();
        } else if (BlueSeaConstant.BLUESEA_PCTERMINAL_VENDER_APP_APPID.equalsIgnoreCase(key)) {
            vendorAppids = applicationApolloConfig.getBlueseaPcTerminalVenderAppAPPid();
        }
        if (StringUtil.listEmpty(vendorAppids)) {
            return null;
        }
        //获取所有终端
        List<Map> terminalList = new ArrayList<>();
        vendorAppids.stream().forEach(vendor -> {
            Map filter = CollectionUtil.hashMap("vendor_app_appid", vendor, "status", 1);
            ListResult terminals = terminalService.getTerminals(merchantId, null, null, filter);
            if (terminals.getTotal() < 1) {
                return;
            }
            List<Map> devices = terminals.getRecords().stream().map(terminal -> {
                String fingerPrint = MapUtils.getString(terminal, BlueSeaConstant.FINGER_PRINT);
                return CollectionUtil.hashMap(BlueSeaConstant.FINGER_PRINT, fingerPrint, BlueSeaConstant.SUPPLIER_ID, getSupplierId(fingerPrint), CommonModel.ID, MapUtils.getString(terminal, CommonModel.ID));
            }).collect(Collectors.toList());
            terminalList.addAll(devices);
        });
        return terminalList;
    }

    /**
     * 更新终端信息
     *
     * @param merchantSn
     * @param auditId
     * @param formBody
     * @return
     */
    public CommonResult refreshTerminal(String merchantSn, Long auditId, Map formBody) {
        //查看成功记录
        BlueSeaTask blueSeaTask = blueSeaMapper.selectSuccessTaskBySnAndType(merchantSn, Arrays.asList(BlueSeaConstant.blueSeaType, BlueSeaConstant.kx));
        if (Objects.isNull(blueSeaTask)) {
            return new CommonResult(CommonResult.BIZ_FAIL, BlueSeaConstant.REASON_NOSUCCESSTASK);
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            return new CommonResult(CommonResult.BIZ_FAIL, BlueSeaConstant.REASON_NOMERCHANT);
        }
        //查询当前绑定的终端
        List<Map> terminals = findAllDragonFlyOrPc(blueSeaTask.getMerchant_id(), BlueSeaConstant.BLUESEA_TERMINAL_VENDER_APP_APPID);
        if (StringUtil.listEmpty(terminals)) {
            return new CommonResult(CommonResult.BIZ_FAIL, BlueSeaConstant.REASON_NODEVICE);
        }
        Tuple2<String, Boolean> aliMchInfo = getInUseMchId(merchantSn);

        MchSnapshot snapshot = MchSnapshot.builder().direct(aliMchInfo.get_2()).build();
        BlueSeaTask task = new BlueSeaTask();
        task.setAudit_id(auditId);
        task.setAli_mch_id(blueSeaTask.getAli_mch_id());
        task.setMerchant_sn(merchantSn);
        task.setMerchant_id(blueSeaTask.getMerchant_id());
        task.setStatus(BlueSeaConstant.PENDING);
        task.setForm_body(JSON.toJSONString(formBody));
        task.setType(BlueSeaConstant.terminalFresh);
        task.setMch_snapshot(JSON.toJSONString(snapshot));
        blueSeaTaskRepository.insertSelectiveAndStoreSn(task);
        CompletableFuture.runAsync(() -> {
            refreshTerminal(blueSeaTask, terminals, blueSeaTask.getType());
        });
        return new CommonResult(CommonResult.SUCCESS, BlueSeaConstant.REASON_SUCCESS);
    }

    private Boolean checkTerminalStill(BlueSeaTask blueSeaTask, AlipayMerchantIotDeviceQueryResponse response) {
        MchSnapshot mchSnapshot = blueSeaTask.getMchSnapshot();
        if (!response.isSuccess()) {
            return false;
        }
        Boolean pid;
        if (mchSnapshot.getDirect()) {
            pid = blueSeaTask.getAli_mch_id().equals(response.getPid());
        } else {
            pid = mchSnapshot.getBindUserId().equals(response.getPid()) && blueSeaTask.getAli_mch_id().equalsIgnoreCase(response.getSmid());
        }
        Boolean shopId = mchSnapshot.getShopId().equals(response.getShopId());
        return pid && shopId;
    }

    @Transactional(rollbackFor = Exception.class)
    public void refreshTerminal(BlueSeaTask blueSeaTask, List<Map> terminals, Integer type) {
        MchSnapshot mchSnapshot = blueSeaTask.getMchSnapshot();
        String alipayId = blueSeaTask.getAli_mch_id();
        String shopId = mchSnapshot.getShopId();
        String bindUserId = mchSnapshot.getBindUserId();
        String merchantSn = blueSeaTask.getMerchant_sn();
        String activityOrderId = blueSeaTask.getActivity_order_id();
        String merchantId = blueSeaTask.getMerchant_id();
        //终端内容
        List<TerminalInfo> terminalInfos = new ArrayList<>();
        BlueSeaTask terminalTask = blueSeaMapper.selectTerminalTask(merchantSn);
        //前端文案返回内容
        List<Map> result = new ArrayList<>();
        try {
            terminals.stream().forEach(terminal -> {
                String deviceSn = MapUtils.getString(terminal, BlueSeaConstant.FINGER_PRINT);
                String supplierId = MapUtils.getString(terminal, BlueSeaConstant.SUPPLIER_ID);
                String terminalId = MapUtils.getString(terminal, CommonModel.ID);
                AlipayMerchantIotDeviceQueryReq queryReq1 = AlipayMerchantIotDeviceQueryReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).build();
                AliCommResponse<AlipayMerchantIotDeviceQueryRequest, AlipayMerchantIotDeviceQueryResponse> queryRes1 = newBlueSeaService.alipayMerchantIotDeviceQuery(queryReq1);
                if (checkTerminalStill(blueSeaTask, queryRes1.getResp())) {
                    terminalInfos.add(TerminalInfo.builder().deviceFingerprint(deviceSn).bindStatus(200).bindMsg("SUCCESS").queryStatus(200).queryMsg("SUCCESS").build());
                    if (type == BlueSeaConstant.kx) {
                        result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_SUCCESS_NONEEDUPDATE));
                        return;
                    }
                    if (blueSeaActivityChangedBiz.updateTerminalConfig(merchantSn, activityOrderId, terminalId)) {
                        result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_SUCCESS));
                    } else {
                        result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_FAIL_UPDATE));
                    }
                } else {
                    //绑定后切参数
                    AlipayMerchantIotDeviceBindReq bindReq;
                    if (mchSnapshot.getDirect()) {
                        bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("direct").pid(alipayId).shopId(shopId).build();
                    } else {
                        bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("indirect").pid(bindUserId).smid(alipayId).shopId(shopId).build();
                    }
                    AliCommResponse<AlipayMerchantIotDeviceBindRequest, AlipayMerchantIotDeviceBindResponse> bindResp = newBlueSeaService.alipayMerchantIotDeviceBind(bindReq);
                    TerminalInfo terminalInfo = TerminalInfo.builder().deviceFingerprint(deviceSn).bindStatus(bindResp.getCode()).bindMsg(bindResp.getMessage()).build();
                    if (bindResp.isSuccess()) {
                        AliCommResponse aliCommResponse;
                        try {
                            aliCommResponse = retryIotQuery(deviceSn, supplierId);
                            if (aliCommResponse.isSuccess()) {
                                //切参数
                                if (BlueSeaConstant.kx == type) {
                                    result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_SUCCESS_NONEEDUPDATE));
                                } else if (blueSeaActivityChangedBiz.updateTerminalConfig(merchantSn, activityOrderId, terminalId)) {
                                    //非快消，需要切换交易参数
                                    result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_SUCCESS));
                                } else {
                                    result.add(CollectionUtil.hashMap(deviceSn, BlueSeaConstant.TERMINAL_FAIL_UPDATE));
                                }
                            }
                            terminalInfo.setQueryStatus(aliCommResponse.getCode()).setQueryMsg(aliCommResponse.getMessage());
                        } catch (Exception e) {
                            //retry 的异常
                            terminalInfo.setQueryStatus(400).setQueryMsg(e.getMessage());
                            result.add(CollectionUtil.hashMap(deviceSn, e.getMessage()));
                        }
                    } else {
                        //失败，记录失败结果
                        result.add(CollectionUtil.hashMap(deviceSn, String.format("%s:%s", BlueSeaConstant.TERMINAL_FAIL, bindResp.getMessage())));
                    }
                    terminalInfos.add(terminalInfo);
                }
            });
            // 新蓝海活动需要 pc终端数据修改
            if (BlueSeaConstant.blueSeaType == type) {
                List<Map> pcTerminals = findAllDragonFlyOrPc(merchantId, BlueSeaConstant.BLUESEA_PCTERMINAL_VENDER_APP_APPID);
                if (!StringUtil.listEmpty(pcTerminals)) {
                    pcTerminals.stream().forEach(pcTerminal -> {
                        blueSeaActivityChangedBiz.updatePcTerminalConfig(merchantSn, MapUtils.getString(pcTerminal, CommonModel.ID));
                    });
                    List<String> collect = pcTerminals.stream().map(pcTerminal -> MapUtils.getString(pcTerminal, BlueSeaConstant.FINGER_PRINT)).collect(Collectors.toList());
                    mchSnapshot.setPcTerminals(collect);
                    result.add(CollectionUtil.hashMap(BlueSeaConstant.TERMINAL_PC, collect));
                }
            }
            //数据库记录
            updateStatus(terminalTask.getId(), BlueSeaConstant.SUCCESS, null, mchSnapshot, null, terminalInfos, null, null);
            //回调
            if (Utils.isNullOrZero(terminalTask.getApply_id())) {
                CallBackBean callBackBean = CallBackBean.builder()
                        .auditId(terminalTask.getAudit_id()).templateId(MapUtils.getLong(JSONObject.parseObject(terminalTask.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID))
                        .resultType(1).message(JSON.toJSONString(result))
                        .build();
                callBackService.addComment(callBackBean);
            }
        } catch (Exception e) {
            updateStatus(terminalTask.getId(), BlueSeaConstant.FAIL, BlueSeaConstant.REASON_REFRESHTERMINAL, null, null, null, null, null);
            log.error("pc插件终端绑定失败,sn:{},{}", merchantSn, e);
        }
    }

    private AliCommResponse retryIotQuery(String deviceSn, String supplierId) throws ExecutionException, RetryException {
        return retryer.call(new Callable<AliCommResponse>() {
            @Override
            public AliCommResponse call() {
                AlipayMerchantIotDeviceQueryReq req = AlipayMerchantIotDeviceQueryReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).build();
                AliCommResponse<AlipayMerchantIotDeviceQueryRequest, AlipayMerchantIotDeviceQueryResponse> response = newBlueSeaService.alipayMerchantIotDeviceQuery(req);
                if ("RECORD_NOT_EXIST".equalsIgnoreCase(response.getMessage())) {
                    throw new RuntimeException("RECORD_NOT_EXIST");
                }
                return response;
            }
        });
    }

    public void handleMerchantIotDeviceBind(BlueSeaTask blueSeaTask) {
        if (preCheckIot(blueSeaTask)) {
            return;
        }
        String merchantId = blueSeaTask.getMerchant_id();
        String alipayId = blueSeaTask.getAli_mch_id();
        MchSnapshot mchSnapshot = blueSeaTask.getMchSnapshot();
        String shopId = mchSnapshot.getShopId();
        String bindUserId = mchSnapshot.getBindUserId();
        Boolean direct = mchSnapshot.getDirect();
        List<TerminalInfo> oriTerminals = blueSeaTask.getTerminalInfo();
        List<TerminalInfo> newTerminals = new ArrayList<>();
        if (StringUtil.listEmpty(oriTerminals)) {
            List<Map> terminalList = findAllDragonFlyOrPc(merchantId, BlueSeaConstant.BLUESEA_TERMINAL_VENDER_APP_APPID);
            if (StringUtil.listEmpty(terminalList)) {
                updateStatus(blueSeaTask.getId(), BlueSeaConstant.FAIL, BlueSeaConstant.REASON_NODEVICE, null, null, null, null, null);
                return;
            }
            terminalList.stream().forEach(terminal -> {
                String deviceSn = MapUtils.getString(terminal, BlueSeaConstant.FINGER_PRINT);
                String supplierId = MapUtils.getString(terminal, "supplier_id");
                AlipayMerchantIotDeviceBindReq bindReq;
                if (direct) {
                    bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("direct").pid(alipayId).shopId(shopId).build();
                } else {
                    bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("indirect").pid(bindUserId).smid(alipayId).shopId(shopId).build();
                }
                AliCommResponse<AlipayMerchantIotDeviceBindRequest, AlipayMerchantIotDeviceBindResponse> bindResponse = newBlueSeaService.alipayMerchantIotDeviceBind(bindReq);
                AlipayMerchantIotDeviceQueryReq queryReq = AlipayMerchantIotDeviceQueryReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).build();
                AliCommResponse<AlipayMerchantIotDeviceQueryRequest, AlipayMerchantIotDeviceQueryResponse> queryResponse = newBlueSeaService.alipayMerchantIotDeviceQuery(queryReq);
                newTerminals.add(
                        TerminalInfo.builder().deviceFingerprint(deviceSn).bindStatus(bindResponse.getCode())
                                .bindMsg(bindResponse.getMessage()).queryStatus(queryResponse.getCode()).queryMsg(queryResponse.getMessage()).build()
                );
            });
        }
        oriTerminals.stream().forEach(oriTerminal -> {
            TerminalInfo freshTerminal = TerminalInfo.builder().build();
            BeanUtils.copyProperties(oriTerminal, freshTerminal);
            String deviceSn = oriTerminal.getDeviceFingerprint();
            String supplierId = getSupplierId(deviceSn);
            if (200 != oriTerminal.getBindStatus()) {
                AlipayMerchantIotDeviceBindReq bindReq;
                if (direct) {
                    bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("direct").pid(alipayId).shopId(shopId).build();
                } else {
                    bindReq = AlipayMerchantIotDeviceBindReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).merchantType("indirect").pid(bindUserId).smid(alipayId).shopId(shopId).build();
                }
                AliCommResponse<AlipayMerchantIotDeviceBindRequest, AlipayMerchantIotDeviceBindResponse> bindResponse = newBlueSeaService.alipayMerchantIotDeviceBind(bindReq);
                freshTerminal.setBindStatus(bindResponse.getCode()).setBindMsg(bindResponse.getMessage());
            }
            if (200 != oriTerminal.getQueryStatus()) {
                AlipayMerchantIotDeviceQueryReq queryReq = AlipayMerchantIotDeviceQueryReq.builder().deviceIdType("SN").deviceSn(deviceSn).supplierId(supplierId).build();
                AliCommResponse<AlipayMerchantIotDeviceQueryRequest, AlipayMerchantIotDeviceQueryResponse> queryResponse = newBlueSeaService.alipayMerchantIotDeviceQuery(queryReq);
                freshTerminal.setQueryStatus(queryResponse.getCode()).setQueryMsg(queryResponse.getMessage());
            }
            newTerminals.add(freshTerminal);
        });
        checkAndSaveTerminalV2(blueSeaTask, newTerminals);
    }

    /**
     * 检查该商户是否已经进行过iot绑定:
     * 若进行过且成功，将terminalinfo 传入 return true;
     * 若没进行过或进行过但失败， return false;
     *
     * @param blueSeaTask
     * @return
     */
    private Boolean preCheckIot(BlueSeaTask blueSeaTask) {
        BlueSeaTask recent = blueSeaMapper.selectRecentTaskBySnAndIdAndType(blueSeaTask.getMerchant_sn(), blueSeaTask.getType(), blueSeaTask.getId());
        if (Objects.isNull(recent)) {
            return false;
        }
        if (recent.getProcessList().stream().filter(process -> process.getStatus() == BlueSeaConstant.DEVICE_BIND).findAny().isPresent()) {
            updateStatus(blueSeaTask.getId(), BlueSeaConstant.DEVICE_BIND, null, null, null, recent.getTerminalInfo(), null, null);
            return true;
        }
        return false;
    }

    /**
     * 获取蜻蜓的供应商id
     *
     * @param supply 设备sn，数据库中存储字段fingerprint
     * @return
     */
    private String getSupplierId(String supply) {
        Map map = applicationApolloConfig.getBlueSeaSupply();
        return MapUtils.getString(map, supply.substring(0, 2));
    }

    /**
     * 判断每个终端的最终结果
     * 0->绑定成功
     * 1->system fail 可重试
     * 2->business fail  不可重试
     *
     * @param blueSeaTask
     * @param terminalInfos
     */
    private void checkAndSaveTerminalV2(BlueSeaTask blueSeaTask, List<TerminalInfo> terminalInfos) {
        String result = "";
        String message = "";
        for (TerminalInfo terminalInfo : terminalInfos) {
            if (terminalInfo.isBindSuccess() && terminalInfo.isQuerySuccess()) {
                result = result.concat("0");
            } else if (terminalInfo.isBindSuccess() && terminalInfo.isQueryBusinessFail()) {
                result = result.concat("2");
                message = terminalInfo.getQueryMsg();
            } else if (terminalInfo.isBindSuccess() && terminalInfo.isQuerySystemFail()) {
                result = result.concat("1");
                message = terminalInfo.getQueryMsg();
            } else if (terminalInfo.isBindSystemFail()) {
                result = result.concat("1");
                message = terminalInfo.getBindMsg();
            } else if (terminalInfo.isBindBusinessFail()) {
                result = result.concat("2");
                message = terminalInfo.getBindMsg();
            }
            terminalInfo.setRetry(terminalInfo.getRetry() + 1);
        }
        int retry = terminalInfos.get(0).getRetry();
        if (result.contains("1")) {
            if (retry > 5) {
                if (result.contains("0")) {
                    updateStatus(blueSeaTask.getId(), BlueSeaConstant.DEVICE_BIND, null, null, null, terminalInfos, null, null);
                } else {
                    updateStatus(blueSeaTask.getId(), BlueSeaConstant.FAIL, message, null, null, terminalInfos, null, null);
                }
            } else {
                updateTerminalRetry(blueSeaTask, null, terminalInfos);
            }
        } else {
            if (result.contains("0")) {
                updateStatus(blueSeaTask.getId(), BlueSeaConstant.DEVICE_BIND, null, null, null, terminalInfos, null, null);
            } else {
                updateStatus(blueSeaTask.getId(), BlueSeaConstant.FAIL, message, null, null, terminalInfos, null, null);
            }
        }
    }

    /**
     * 构建任务的过程记录和快照
     *
     * @param taskId      任务Id
     * @param status      状态
     * @param mchSnapshot 快照
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 10:31 2020/12/3
     */
    public BlueSeaTask buildProcessAndMchSnapshot(Long taskId, Integer status, MchSnapshot mchSnapshot) {
        final BlueSeaTask blueSeaTask = blueSeaMapper.selectByPrimaryKey(taskId);
        //新的蓝海任务值
        BlueSeaTask updateValue = new BlueSeaTask();
        //记录变更过程
        //原有记录
        final List<Process> processes = blueSeaTask.getProcessList();
        final Process process = Process.builder().status(status).updateAt(System.currentTimeMillis()).build();
        if (!CollectionUtils.isEmpty(processes) && Objects.nonNull(status)) {
            //添加新纪录
            processes.add(process);
            updateValue.setProcess(JSON.toJSONString(processes));
        }
        if (CollectionUtils.isEmpty(processes) && Objects.nonNull(status)) {
            final ArrayList<Process> list = Lists.newArrayList(process);
            updateValue.setProcess(JSON.toJSONString(list));
        }
        if (Objects.nonNull(mchSnapshot)) {
            //原有商户快照
            final MchSnapshot targetSnapshot = blueSeaTask.getMchSnapshot();
            //原有属性与添加新属性组合
            CombineBeans.combineAttributes(mchSnapshot, targetSnapshot);
            updateValue.setMch_snapshot(JSON.toJSONString(targetSnapshot));
        }
        return updateValue;
    }


    /**
     * 重复性校验
     *
     * @param merchantSn 商户号
     * @param type       活动类型
     * @return true-> 无记录||无成功的记录||所有记录均失败，可申请   false-> 有成功的记录||存在进行中的记录，不可申请
     * 暂时先这样，实际门店创建一类多个判断
     */
    public Boolean checkRepetiveness(String merchantSn, int type) {
        final Tuple2<String, Boolean> aliMchInfo = getInUseMchId(merchantSn);
        String aliMchId = aliMchInfo.get_1();
        if (WosaiStringUtils.isEmpty(aliMchId)) {
            return true;
        }
        List<BlueSeaTask> blueSeaTasks = blueSeaMapper.selectRepetitiveness(aliMchId, type);
        //不存在记录 可申请
        if (StringUtil.listEmpty(blueSeaTasks)) {
            return true;
        }
        //存在 status == 19 即成功记录 不可申请
        boolean success = blueSeaTasks.stream().anyMatch(blueSeaTask -> BlueSeaConstant.SUCCESS == blueSeaTask.getStatus());
        if (success) {
            return false;
        }
        //存在 status != 20 即过程中记录    不可申请
        boolean process = blueSeaTasks.stream().anyMatch(blueSeaTask -> BlueSeaConstant.FAIL != blueSeaTask.getStatus());
        if (process) {
            return false;
        }
        return true;
    }


    /**
     * 主动查询蓝海报名活动结果
     *
     * @param blueSeaTask
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 15:06 2020/12/14
     */
    public void handleActivityStatusQuery(BlueSeaTask blueSeaTask) {
        if (Objects.isNull(blueSeaTask) || Objects.isNull(blueSeaTask.getActivity_order_id())) {
            return;
        }
        //活动申请单Id
        final String activityOrderId = blueSeaTask.getActivity_order_id();
        final AlipayOpenSpBlueSeaActivityQueryReq build = AlipayOpenSpBlueSeaActivityQueryReq.builder().orderId(activityOrderId).build();
        final AliCommResponse<AlipayOpenSpBlueseaactivityQueryRequest, AlipayOpenSpBlueseaactivityQueryResponse> activityResult = newBlueSeaService.alipayOpenSpBlueSeaActivityQuery(build);
        log.info("order_id : {} 查询结果 :{} ,", activityOrderId, JSONObject.toJSONString(activityResult));
        if (activityResult.isSuccess()) {//业务成功
            //申请单状态，状态机参考（AUDITING:审核中，FAIL:报名失败，PASS:报名成功）
            final AlipayOpenSpBlueseaactivityQueryResponse resp = activityResult.getResp();
            final String status = resp.getStatus();
            if (BlueSeaConstant.AlipayActivityStatus.STATUS_AUDITING.equalsIgnoreCase(status)) {//活动审核中就结束查询等待下一个周期查询
                return;
            }
            final boolean pass = BlueSeaConstant.AlipayActivityStatus.STATUS_PASS.equalsIgnoreCase(status);
            final String memo = pass ? null : MapUtils.getString(JSON.parseObject(MapUtils.getString(JSON.parseObject(activityResult.getResp().getBody(), Map.class), "alipay_open_sp_blueseaactivity_query_response"), Map.class), "memo");
            blueSeaActivityChangedBiz.handleActivityStatusAndTerminalConfig(memo, activityOrderId, blueSeaTask, status);
        } else if (activityResult.isSystemFail()) {//系统异常无需处理
            return;
        } else {//业务异常
            updateStatus(blueSeaTask.getId(), BlueSeaConstant.FAIL, activityResult.getMessage(), null, null, null, null, null);
        }
    }

    /**
     * 仅作为模拟阿里消息推送手动触发,测试通过会被删除
     *
     * @param
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 09:50 2020/12/4
     */
    public void onMessage(String msgApi, String msgId, String bizContent) {
        log.info("receive message msgApi:{}, msgId:{}, bizContent:{}", msgApi, msgId, bizContent);
        //TODO由于是集群注意幂等,在onMessage方法实现中，不抛出任何异常，则认为您消费消息成功，否则服务端认为此消息客户端没有消费成功，会触发重试。
        //根据msgApi找到消息处理工厂中对应的消息处理类
        final AliMessageHandleService messageHandleService = aliMessageHandleFactory.getAliMessageHandleService(msgApi);
        //没有匹配到处理类
        if (Objects.isNull(messageHandleService)) {
            return;
        }
        try {
            messageHandleService.handleMessageBiz(bizContent);
        } catch (Exception e) {
            log.error("阿里推送消息处理异常 : {}", e);
            monitorLog.recordMonitor("阿里推送消息处理异常", msgApi + " " + e.getMessage());
            throw new RuntimeException("消息处理异常");
        }
    }

    public CustomizedInfo getCustomizedInfo(BlueSeaTask task) {
        CustomizedInfo customizedInfo = buildCustomizedInfo(task.getMerchant_sn());
        Map map = JSONObject.parseObject(task.getForm_body(), Map.class);
        //商户名
        String merchantName = BeanUtil.getPropString(map, "merchant_name");
        //商户Mcc码
        final String mcc = BeanUtil.getPropString(map, "mcc");
        customizedInfo.setMerchant_name(merchantName);
        customizedInfo.setMcc(mcc);
        customizedInfo.setForceUpdate(true);
        Map<String, String> forceValue = new HashMap<>();
        forceValue.put("mcc", mcc);
        forceValue.put("name", merchantName);
        customizedInfo.setForceUpdateValue(forceValue);
        return customizedInfo;
    }
}
