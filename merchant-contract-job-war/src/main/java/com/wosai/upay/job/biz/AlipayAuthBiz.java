package com.wosai.upay.job.biz;

import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.service.AlipayAuthService;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.alipay.AlipayAuthStatusQueryRequest;
import com.wosai.upay.merchant.contract.model.alipay.AlipayAuthStatusQueryResponse;
import com.wosai.upay.merchant.contract.service.AliPayAuthApplyFlowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AlipayAuthBiz {

    @Autowired
    private AliPayAuthApplyFlowService aliPayAuthApplyFlowService;

    @Autowired
    protected MerchantProviderParamsMapper paramsMapper;

    //检查开关
    public static final String ALI_SWITCH = "direct_apply_ali_check_switch";


    /**
     * 查询子商户号认证状态
     *
     * @param merchantProviderParams
     */
    public boolean queryAuthByMchId(MerchantProviderParams merchantProviderParams) {
        if (WosaiStringUtils.isEmpty(merchantProviderParams.getPay_merchant_id())) {
            return false;
        }

        AlipayAuthStatusQueryRequest request = new AlipayAuthStatusQueryRequest();
        request.setSub_merchant_id(merchantProviderParams.getPay_merchant_id());
        AliCommResponse<AlipayAuthStatusQueryRequest, AlipayAuthStatusQueryResponse> response = aliPayAuthApplyFlowService.queryAuthStatus(request);
        if (response.getCode() == 200) {
            boolean authed = "AUTHORIZED".equals(response.getResp().getCheck_result());

            if (authed) {
                paramsMapper.updateByPrimaryKeySelective(
                        new MerchantProviderParams()
                                .setId(merchantProviderParams.getId())
                                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                                .setMtime(System.currentTimeMillis())
                );
            }
            return authed;
        } else {
            throw new ContractSysException("查询支付宝子商户号授权状态异常：" + response.getMessage());
        }
    }

    public boolean queryAuthByMchId(MerchantProviderParamsDO merchantProviderParams) {
        if (WosaiStringUtils.isEmpty(merchantProviderParams.getPayMerchantId())) {
            return false;
        }

        AlipayAuthStatusQueryRequest request = new AlipayAuthStatusQueryRequest();
        request.setSub_merchant_id(merchantProviderParams.getPayMerchantId());
        AliCommResponse<AlipayAuthStatusQueryRequest, AlipayAuthStatusQueryResponse> response = aliPayAuthApplyFlowService.queryAuthStatus(request);
        if (response.getCode() == 200) {
            boolean authed = "AUTHORIZED".equals(response.getResp().getCheck_result());

            if (authed) {
                paramsMapper.updateByPrimaryKeySelective(
                        new MerchantProviderParams()
                                .setId(merchantProviderParams.getId())
                                .setAuth_status(PayMchAuthStatusEnum.YES.getValue())
                                .setMtime(System.currentTimeMillis())
                );
            }
            return authed;
        } else {
            throw new ContractSysException("查询支付宝子商户号授权状态异常：" + response.getMessage());
        }
    }

    /**
     * 查询当前支付宝子商户的实名状态
     * @param aliPayMchId 支付宝子商户号
     * @return
     */
    public boolean queryAuthByMchId(String aliPayMchId) {
        if (WosaiStringUtils.isEmpty(aliPayMchId)){
            return false;
        }
        AlipayAuthStatusQueryRequest request = new AlipayAuthStatusQueryRequest();
        request.setSub_merchant_id(aliPayMchId);
        AliCommResponse<AlipayAuthStatusQueryRequest, AlipayAuthStatusQueryResponse> response = aliPayAuthApplyFlowService.queryAuthStatus(request);
        if (response.getCode() == 200) {
            boolean authed = "AUTHORIZED".equals(response.getResp().getCheck_result());
            return authed;
        } else {
            throw new ContractSysException("查询支付宝子商户号授权状态异常：" + response.getMessage());
        }
    }
}
