package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 选择类型枚举
 *
 * <AUTHOR>
 */
public enum ChooseTypeEnum implements ITextValueEnum<String> {

    ENABLE("ENABLE", "可选择"),

    UNABLE("UNABLE", "不可选择"),

    ONLY_CAN("ONLY_CAN", "只可选择");

    private final String value;
    private final String text;

    ChooseTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
