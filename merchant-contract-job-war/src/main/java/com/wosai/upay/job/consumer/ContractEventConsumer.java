package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSONObject;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicEvent;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.MerchantAppConfigChangeEvent;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.PendingTasksBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.PendingTasks;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.model.bo.AcquirerInfoBO;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.job.util.JacksonHelperUtils;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.RecordEventUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: jerry
 * @date: 2019/7/29 09:54
 * @Description:报备event事件数据总线消费
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class ContractEventConsumer extends AbstractDataBusConsumer {

    @Autowired
    ContractStatusMapper contractStatusMapper;
    @Autowired
    DataSyncHandler dataSyncHandler;
    @Autowired
    RecordEventUtil recordEventUtil;
    @Autowired
    SelfHelpNetInEventService selfHelpNetInEventService;

    @Autowired
    ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private PendingTasksBiz pendingTasksBiz;

    @Autowired
    private McProviderDAO mcProviderDAO;

    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private AcquirerFacade acquirerFacade;

    private static final String KEY_PREFIX = "dts_update_";

    @KafkaListener(topics = "#{'${databus.consumer.topic}'.split(',')}", containerFactory = "dataBusKafkaListenerContainerFactoryV2")
    @Transactional(rollbackFor = Exception.class)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }


    @Override
    protected void doHandleEvent(AbstractEvent event) {
        long timestamp = event.getTimestamp();
        String key = String.format("%s:%s:%s", event.getObjectType(), event.getEventType(), event.getSeq());
        log.info("start handling event : {} {} {}", key, timestamp, JacksonHelperUtils.toJsonString(event));
        if (event instanceof MerchantBasicEvent) {
            handleMerchantBasicEvent((MerchantBasicEvent) event);
        } else if (event instanceof FeeRateEvent) {
            handleFeeRateEvent((FeeRateEvent) event);
        } else if (event instanceof MerchantAppConfigChangeEvent) {
            handleMerchantAppConfigChangeEvent((MerchantAppConfigChangeEvent) event);
        }
    }

    private void handleMerchantBasicEvent(MerchantBasicEvent event) {
        // event.getSeq() 现在是时间戳，可能重复
        if (!redisLock.lock(KEY_PREFIX + "MerchantBasicEvent" + event.getSeq() + event.getMerchantSn(), "", 60)) {
            log.info("ignore duplicate event {} {}", event.getSeq(), JacksonHelperUtils.toJsonString(event));
            return;
        }
        Map merchantDataChanges = JSONObject.parseObject(new String(persistenceHelper.toJsonBytes(event)), Map.class);
        String module = (String) merchantDataChanges.get(ConstantsEvent.MODULE);
        String event_type = (String) merchantDataChanges.get(ConstantsEvent.EVENT_TYPE);
        String merchantSn = (String) merchantDataChanges.get(ConstantsEvent.MERCHANT_SN);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            log.info("商户{}未入网成功,不生成此次变更事件,数据总线的seq为:{}", merchantSn, event.getSeq());
            return;
        }

        Map msg = CollectionUtil.hashMap(ConstantsEvent.EVENT_TYPE_TABLE_NAME, "merchant");
        if (ConstantsEvent.MERCHANT.equals(module) && ConstantsEvent.UPDATE.equals(event_type)) {
            Map beforeData = (Map) merchantDataChanges.get(ConstantsEvent.BEFORE);
            Map afterData = (Map) merchantDataChanges.get(ConstantsEvent.AFTER);

            //查询最新的一条成功的 task , 商户部分和本次新提交部分是否一致,一致不用同步
            ContractTask createDesc = contractTaskMapper.getBySnAndTypeAndStatusByCreateDesc(merchantSn, ProviderUtil.CONTRACT_TYPE_UPDATE_BASIC, 5);
            if (createDesc != null) {
                Map eventContext = createDesc.getEventContext();
                if (!recordEventUtil.checkIsUpdate((Map) eventContext.get("merchant"), afterData, DataSyncHandler.merchantChangeToLklFieldsArray)) {
                    return;
                }
            }
            recordEventUtil.createEventAfterChangeMerchant(beforeData, afterData, merchantSn, msg, DataSyncHandler.merchantChangeToLklFieldsArray);
            //wxSettlementIdChangeBiz.handleIfIndustryChange(beforeData, afterData, merchantSn, merchantId);
        }
    }

    private void handleFeeRateEvent(FeeRateEvent event) {
        String merchantId = event.getMerchantId();
        Map merchant = merchantService.getMerchant(merchantId);
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            log.info("商户{}未入网成功,不生成此次变更事件,数据总线的seq为:{}", merchantSn, event.getSeq());
            return;
        }

        // 实时处理
        if (needRealTimeSyncFee(contractStatus.getAcquirer())) {
            log.info("收到商户 {} 费率变更变更消息,数据总线的seq为 {}", merchantSn, event.getSeq());
            List<PendingTasks> pendingTasksList = pendingTasksBiz.selectByMerchantSnAndEventType(merchantSn, PendingTasks.TYPE_NOW_FEERATE);
            if (CollectionUtils.isEmpty(pendingTasksList)) {
                pendingTasksBiz.insertImmediatelyFeeRateTask(event, merchantSn);
            }
            return;
        }
        // 费率变更太频繁，且有冗余变更，造成了系统压力，因费率变更对时效性要求不高，所以添加到临时任务表，每天晚上生成任务批量处理
        // 入口： ContractEventSchedule -> createTasksByPendingTask
        if (applicationApolloConfig.getSyncFeeRate()) {
            if (applicationApolloConfig.getIsDelayCreateFeeRateTask()) {
                log.info("收到商户  :{} 费率变更变更消息,数据总线的seq为 :{}", merchantSn, event.getSeq());
                List<PendingTasks> pendingTasksList = pendingTasksBiz.selectByMerchantSnAndEventType(merchantSn, PendingTasks.TYPE_FEERATE);
                if (CollectionUtils.isEmpty(pendingTasksList)) {
                    pendingTasksBiz.insertFeeRateTask(event, merchantSn);
                }
            } else {
                List<PendingTasks> pendingTasksList = pendingTasksBiz.selectByMerchantSnAndEventType(merchantSn, PendingTasks.TYPE_NOW_FEERATE);
                if (CollectionUtils.isEmpty(pendingTasksList)) {
                    pendingTasksBiz.insertImmediatelyFeeRateTask(event, merchantSn);
                }
            }
        }
    }

    private void handleMerchantAppConfigChangeEvent(MerchantAppConfigChangeEvent event) {
        Map merchant = merchantService.getMerchantByMerchantId(event.getBefore().get("merchant_id").toString());
        String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
        log.info("收到商户  :{} 业务费率变更变更消息,数据总线的seq为 :{}", merchantSn, event.getSeq());
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        if (contractStatus == null || contractStatus.getStatus() != ContractStatus.STATUS_SUCCESS) {
            log.info("商户{}未入网成功,不生成此次变更事件,数据总线的seq为:{}", merchantSn, event.getSeq());
            return;
        }
        Map before = event.getBefore();
        Map after = event.getAfter();
        if (Objects.equals(BeanUtil.getPropString(after, MerchantConfig.B2C_FEE_RATE), BeanUtil.getPropString(before, MerchantConfig.B2C_FEE_RATE))) {
            log.info("商户 {} 费率未发生变更 不生成此次变更事件 数据总线的seq为 {}", merchantSn, event.getSeq());
            return;
        }

        Optional<McProviderDO> mcProviderDOOptional = mcProviderDAO.getByProvider(BeanUtil.getPropString(after, MerchantConfig.PROVIDER));
        if (mcProviderDOOptional.isPresent() && needRealTimeSyncFee(mcProviderDOOptional.get().getAcquirer())) {
            List<PendingTasks> pendingTasksList = pendingTasksBiz.selectByMerchantSnAndEventType(merchantSn, PendingTasks.TYPE_APP_FEERATE);
            if (CollectionUtils.isEmpty(pendingTasksList)) {
                pendingTasksBiz.insertAppFeeRateTask(event, merchantSn);
            }
        }
    }

    private boolean needRealTimeSyncFee(String acquirer) {
        Optional<AcquirerSharedAbility> sharedAbility = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        if (!sharedAbility.isPresent()) {
            return false;
        }
        AcquirerInfoBO acquirerInfo = sharedAbility.get().getAcquirerInfo();
        return !acquirerInfo.isFeeBasedOnSQB();
    }


}
