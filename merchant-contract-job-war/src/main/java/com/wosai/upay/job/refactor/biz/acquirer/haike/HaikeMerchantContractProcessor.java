package com.wosai.upay.job.refactor.biz.acquirer.haike;

import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.refactor.model.dto.NewMerchantContractResultRspDTO;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 海科商户进件处理
 *
 * <AUTHOR>
 * @date 2024/9/10 10:08
 */
@Component
public class HaikeMerchantContractProcessor {

    @Resource
    private HaikeService haikeService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Resource
    private ParamContextBiz paramContextBiz;


    /**
     * 商户向收单机构报备
     *
     * @param merchantSn 商户号
     * @return 报备结果
     */
    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn) {
        Map<String, Object> contextMap = paramContextBiz.getNetInParamContextByMerchantSn(merchantSn);
        return contractToAcquirer(merchantSn, contextMap);
    }

    public NewMerchantContractResultRspDTO contractToAcquirer(String merchantSn, Map<String, Object> contextMap) {
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        paramContextBiz.fillFeeRatesByRuleGroupId(contextMap, McConstant.RULE_GROUP_HAIKE);
        ContractResponse contractResponse = haikeService.contractToAcquirer(contextMap, haikeParam);
        NewMerchantContractResultRspDTO rsp = new NewMerchantContractResultRspDTO();
        rsp.setRequest(contractResponse.getRequestParam());
        rsp.setResponse(contractResponse.getResponseParam());
        if (contractResponse.isSuccess() && MapUtils.isNotEmpty(contractResponse.getResponseParam())) {
            Map<String, Object> responseParam = contractResponse.getResponseParam();
            rsp.setStatus(NewMerchantContractResultRspDTO.STATUS_SUCCESS);
            rsp.setAcquirerMerchantId(MapUtils.getString(responseParam, "merch_no"));
            rsp.setUnionMerchantId(MapUtils.getString(responseParam, "bank_merch_no"));
            rsp.setTermId(MapUtils.getString(responseParam, "terminal_id"));
        } else {
            rsp.setStatus(NewMerchantContractResultRspDTO.STATUS_FAIL);
        }
        return rsp;
    }
}
