package com.wosai.upay.job.biz.comboparams;

import avro.shaded.com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.github.rholder.retry.*;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预授权补偿
 * @date 2022/8/19 10:39
 */
@Slf4j
@Component
public class AliPayServiceCodeBiz {

    static Retryer retryer;

    static {
        retryer = RetryerBuilder.newBuilder()
                .retryIfException() // 抛出异常会进行重试
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 10, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(10))
                .build();
    }

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    BlueSeaService blueSeaService;

    @Autowired
    @Qualifier("syncPreAuthThreadPoolTaskScheduler")
    private ThreadPoolTaskScheduler syncPreAuthThreadPoolTaskScheduler;

    public void appendServiceCode(String merchantId, String aliPayMerchantId) {
        syncPreAuthThreadPoolTaskScheduler.getScheduledExecutor().schedule(() -> {
            try {
                retryer.call(new AppendAliPayServiceCodeCall(merchantId, aliPayMerchantId));
            } catch (ExecutionException e) {
                log.error("retryer call ExecutionException :{}", e);
            } catch (RetryException e) {
                log.error("retryer call RetryException :{}", e);
            } catch (Exception e) {
                log.error("{} : {} 切换交易参数补偿预授权， 重试后失败", merchantId, aliPayMerchantId, e);
            }
        }, 30, TimeUnit.SECONDS);
    }


    public class AppendAliPayServiceCodeCall implements Callable<Object> {

        private String merchantId;
        private String payMerchantId;

        public AppendAliPayServiceCodeCall(String merchantId, String payMerchantId) {
            this.merchantId = merchantId;
            this.payMerchantId = payMerchantId;
        }

        @Override
        public Object call() throws Exception {
            Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null);
            int alipay = BeanUtil.getPropInt(merchantConfig, "params.deposit.alipay", TransactionParam.DEPOSIT_CLOSE);
            if(alipay== TransactionParam.DEPOSIT_OPEN){
                log.info("开始开启预授权，商户id:{},子商户号:{}",merchantId,payMerchantId);
                blueSeaService.appendServiceCode(payMerchantId);
                log.info("开启预授权成功，商户id:{},子商户号:{}",merchantId,payMerchantId);
            }
            return null;
        }
    }


}