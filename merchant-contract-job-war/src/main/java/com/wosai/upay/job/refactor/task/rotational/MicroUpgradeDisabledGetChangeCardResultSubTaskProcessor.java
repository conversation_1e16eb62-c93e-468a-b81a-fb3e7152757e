package com.wosai.upay.job.refactor.task.rotational;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.FuYouBusinessAuditModifyStatusEnum;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.model.LklV3ContractResultDTO;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleSubTaskStatusEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 小微升级禁用的商户获取改卡结果子任务处理器
 * 需要轮询获取结果
 *
 * <AUTHOR>
 * @date 2025/5/14 14:29
 */
@Component
@Slf4j
public class MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor implements RotationalSubTaskProcessor{

    public static final String ACQUIRER_KEY = "acquirer";

    public static final String AUDIT_ID_KEY = "auditId";

    public static final String ACQUIRER_MERCHANT_ID = "acquirerMerchantId";

    public static final int AUDIT_SUCCESS_TYPE = 1;

    public static final int AUDIT_FAIL_TYPE = 2;

    @Resource
    private AcquirerFacade acquirerFacade;

    @Autowired
    private CallBackService callBackService;

    @Autowired
    private FuyouService fuyouService;

    /**
     * 获取子任务类型
     *
     * @return 子任务类型
     */
    @Override
    public RotationalSubTaskTypeEnum getSubTaskType() {
        return RotationalSubTaskTypeEnum.DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT;
    }

    /**
     * 处理具体轮询子任务
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return 处理结果
     */
    @Override
    public InternalScheduleSubTaskProcessResultBO handleRotationalSubTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        RotationalTaskContext rotationalTaskContext = JSON.parseObject(mainTaskDO.getContext(), RotationalTaskContext.class);
        String acquirer = MapUtils.getString(rotationalTaskContext.getParamContext(), ACQUIRER_KEY);
        String rotationId = rotationalTaskContext.getRotationId();
        String auditId = MapUtils.getString(rotationalTaskContext.getParamContext(), AUDIT_ID_KEY);
        try {
            if (StringUtils.equals(AcquirerTypeEnum.LKL_V3.getValue(), acquirer)) {
                return queryLklChangeCardResult(rotationId, auditId);
            }
            if (StringUtils.equals(AcquirerTypeEnum.FU_YOU.getValue(), acquirer)) {
                String fuYouAcquirerId = MapUtils.getString(rotationalTaskContext.getParamContext(), ACQUIRER_MERCHANT_ID);
                return queryFuYouChangeCardResult(mainTaskDO.getMerchantSn(), fuYouAcquirerId, rotationId, auditId);
            }
            syncAuditMsg(auditId, "收单机构不支持", AUDIT_FAIL_TYPE);
            return InternalScheduleSubTaskProcessResultBO.fail("收单机构不支持");
        } catch (Exception e) {
            log.error("获取改卡结果失败, mainTaskId:{}, subTaskId:{}", mainTaskDO.getId(), subTaskDO.getId(), e);
            syncAuditMsg(auditId, "获取改卡结果失败:" + e.getMessage(), AUDIT_FAIL_TYPE);
            return InternalScheduleSubTaskProcessResultBO.fail("获取改卡结果失败: " + e.getMessage());
        }

    }


    private InternalScheduleSubTaskProcessResultBO queryFuYouChangeCardResult(String merchantSn, String fuYouAcquirerId, String rotationId, String auditId) {
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        ContractResponse contractResponse = fuyouService.queryBusinessAuditStatus(merchantSn, fuYouAcquirerId, rotationId, "RZ");
        resultBO.setRequestMsg(JSON.toJSONString(contractResponse.getRequestParam()));
        resultBO.setResponseMsg(JSON.toJSONString(contractResponse.getResponseParam()));
        Map<String, Object> responseParam = contractResponse.getResponseParam();
        String modifyStatus = MapUtils.getString(responseParam, "modify_st");
        String modifyDealMsg = MapUtils.getString(responseParam, "modify_deal_msg");
        if (contractResponse.isSuccess()) {
            if (FuYouBusinessAuditModifyStatusEnum.PROCESSED.getValue().equals(modifyStatus)) {
                resultBO.setResult("审核通过");
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS);
                syncAuditMsg(auditId,  "富友变更卡成功", AUDIT_SUCCESS_TYPE);
            } else if (FuYouBusinessAuditModifyStatusEnum.REFUSED.getValue().equals(modifyStatus)) {
                resultBO.setResult(modifyDealMsg);
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
                syncAuditMsg(auditId, "富友变更卡失败：" + modifyDealMsg, AUDIT_FAIL_TYPE);
            } else {
                resultBO.setResult("等待富友人工审核完成");
                resultBO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT);
            }
        } else {
            resultBO.setResult(contractResponse.getMessage());
            resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
            syncAuditMsg(auditId, "富友变更卡失败：" + contractResponse.getMessage(), AUDIT_FAIL_TYPE);
        }
        return resultBO;
    }

    private InternalScheduleSubTaskProcessResultBO queryLklChangeCardResult(String rotationId, String auditId) {
        InternalScheduleSubTaskProcessResultBO resultBO = new InternalScheduleSubTaskProcessResultBO();
        LklV3ContractResultDTO lklV3ContractResultDTO = acquirerFacade
                .getUniqueAbilityAcquirer(AcquirerTypeEnum.LKL_V3, LklV3AcquirerFacade.class)
                .queryTaskResultByContractId(rotationId);
        resultBO.setRequestMsg(JSON.toJSONString(lklV3ContractResultDTO.getRequestMap()));
        resultBO.setResponseMsg(JSON.toJSONString(lklV3ContractResultDTO.getResponseMap()));
        if (lklV3ContractResultDTO.isNotFinished()) {
            resultBO.setResult("等待拉卡拉结果");
            resultBO.setStatus(InternalScheduleSubTaskStatusEnum.WAIT_EXTERNAL_RESULT);
            return resultBO;
        } else if (lklV3ContractResultDTO.isContractSuccess()) {
            resultBO.setResult("拉卡拉变更卡成功");
            resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_SUCCESS);
            syncAuditMsg(auditId, "拉卡拉变更卡成功", AUDIT_SUCCESS_TYPE);
            return resultBO;
        } else {
            resultBO.setResult("拉卡拉变更卡失败");
            resultBO.setStatus(InternalScheduleSubTaskStatusEnum.PROCESS_FAIL);
            String failMsg = getErrorMsg(lklV3ContractResultDTO.getResponseMap());
            syncAuditMsg(auditId, failMsg, AUDIT_FAIL_TYPE);
            return resultBO;
        }
    }


    private String getErrorMsg(Map<String, Object> responseMap) {
        Map<String, Object> respData = MapUtils.isEmpty(MapUtils.getMap(responseMap, "respData")) ?
                (Map) MapUtils.getMap(responseMap, "data") : (Map) MapUtils.getMap(responseMap, "respData");
        if (MapUtils.isNotEmpty(respData) && respData.containsKey("contractMemo")) {
            String contractMemo = Objects.toString(respData.get("contractMemo"), "");
            if (StringUtils.isNotBlank(contractMemo)) {
                return contractMemo;
            }
        }
        return "拉卡拉变更卡失败";
    }

    private void syncAuditMsg(String auditId, String msg, Integer resultType) {
        try {
            CallBackBean callBackBean = CallBackBean.builder()
                    .auditId(Long.valueOf(auditId)).message(msg).resultType(resultType)
                    .build();
            callBackService.addComment(callBackBean);
        } catch (Exception e) {
            log.error("同步审核消息失败, auditId:{}, resultType:{}, msg:{}", auditId, resultType, msg, e);
        }
    }
}
