package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.ConvertUtil;
import com.shouqianba.cua.utils.object.ObjectExtensionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.terminal.DataBusTerminal;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.LklV3Provider;
import com.wosai.upay.job.refactor.biz.acquirer.lkl.LklAcquirerFacade;
import com.wosai.upay.job.refactor.dao.LklV3ShopTermDAO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 拉卡拉v3营业执照认证重新入网成功后参数处理
 *
 * <AUTHOR>
 * @date 2024/9/13 16:49
 */
@Slf4j
@Component
public class LklV3UpdateTradeParams extends AbstractUpdateTradeParamsTemplate {

    @Resource
    private LklV3ShopTermDAO lklV3ShopTermDAO;

    @Resource
    private LklPayMerchantBiz lklPayMerchantBiz;

    @Resource
    private ProviderTerminalBiz providerTerminalBiz;

    @Resource
    private LklV3ShopTermBiz lklV3ShopTermBiz;

    @Resource
    private LklV3Provider lklV3Provider;

    @Resource
    private UpdateTradeParamsBiz updateTradeParamsBiz;

    @Override
    public String getAcquirer() {
        return AcquirerTypeEnum.LKL_V3.getValue();
    }

    @Override
    Integer getProvider() {
        return ProviderEnum.PROVIDER_LAKALA_V3.getValue();
    }


    @Override
    public String getContractTermNo(String merchantSn) {
        try {
            ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(getProvider(), merchantSn, null);
            return Objects.isNull(providerTerminal) ? "" : providerTerminal.getProvider_terminal_id();
        } catch (Exception e) {
            log.error("获取商户{}老终端号失败", merchantSn, e);
        }
        return "";
    }

    @Override
    public String getMerchantConfigProviderMerchantIdKey() {
        return "provider_mch_id";
    }

    @Override
    void rollbackTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(),
                BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        if (Objects.isNull(subTaskContextBOInner)) {
            return;
        }
        String oldUnionMerchantId = subTaskContextBOInner.getOldUnionMerchantId();
        String oldTermNo = subTaskContextBOInner.getOldMerchantTermNo();
        if (StringUtils.isBlank(oldUnionMerchantId) || StringUtils.isBlank(oldTermNo)) {
            log.warn("商户{}回滚lkl_v3交易参数失败，老银联商户号{}或老终端号{}为空", merchantInfo.getSn(), oldUnionMerchantId, oldTermNo);
            return;
        }
        updateLklTradeParams(merchantInfo, oldUnionMerchantId, oldTermNo);
    }

    private void updateLklTradeParams(MerchantInfo merchantInfo, String oldUnionMerchantId, String oldTermNo) {
        updateTradeParamsBiz.updateLklTradeParams(merchantInfo.getId(), CollectionUtil.hashMap("lakala_merc_id", oldUnionMerchantId,
                "lakala_term_id", oldTermNo));
    }

    @Override
    public void updateClearanceProviderWhenSameAcquirer(String merchantId) {
        coreBTradeConfigService.updateMerchantSwitchMchTime(merchantId, TransactionParam.CLEARANCE_PROVIDER_LKL, System.currentTimeMillis());
        tradeConfigService.updateClearanceProvider(merchantId, TransactionParam.CLEARANCE_PROVIDER_LKL);
    }

    @Override
    protected void deleteTerminalRelatedParams(InternalScheduleSubTaskDO subTaskDO) {
        super.deleteTerminalRelatedParams(subTaskDO);
        deleteShopTerm(subTaskDO);
    }

    private void deleteShopTerm(InternalScheduleSubTaskDO subTaskDO) {
        List<LklV3ShopTermDO> shopTermDOS = lklV3ShopTermDAO.listByMerchantSn(subTaskDO.getMerchantSn());
        if (CollectionUtils.isEmpty(shopTermDOS)) {
            return;
        }
        lklV3ShopTermDAO.batchDeleteByPrimaryKeys(shopTermDOS.stream().map(LklV3ShopTermDO::getId).collect(Collectors.toList()));
    }



    @Override
    void acquirerSpecialProcess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        lklPayMerchantBiz.createMerRelation(subTaskDO.getMerchantSn(), subTaskResponseDTOInner.getNewUnionMerchantId());
    }




    @Override
    void acquirerSpecialProcessV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        lklPayMerchantBiz.createMerRelation(subTaskDO.getMerchantSn(), subTaskResponseDTOInner.getNewUnionMerchantId());
    }



    @Override
    void asyncExistedStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        final Long mainContractTaskId = mainTaskContextBOInner.getMainContractTaskId();
        final MerchantAcquireInfoBO merchantAcquireInfoBO = lklAcquirerFacade.getAcquireInfoFromContractSubTask(mainContractTaskId);
        lklPayMerchantBiz.createMerRelation(subTaskDO.getMerchantSn(), merchantAcquireInfoBO.getUnionNo());
    }




    @Override
    void addMerchantLevelStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {
        addLklV3ShopAndMerchantLevelTerminal(subTaskDO);
        addPayWayParamsTerminalBindTask(subTaskDO, newParams);
    }

    @Override
    void addMerchantLevelStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {
        addLklV3ShopAndMerchantLevelTerminalV3(mainTaskDO,subTaskDO);
        addPayWayParamsTerminalBindTask(subTaskDO, newParams);
    }

    private void addLklV3ShopAndMerchantLevelTerminal(InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        // 直接写一个商户级别终端到provider_terminal和交易侧
        providerTerminalBiz.merchantConnectionProviderTerminal(subTaskDO.getMerchantSn(),  subTaskResponseDTOInner.getNewTermId(), subTaskResponseDTOInner.getNewAcquirerMerchantId(),subTaskDO.getProvider());
        AtomicReference<String> storeSn = new AtomicReference<>();
        ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(subTaskResponseDTOInner.getQueryResultRsqMap(), "data.termDatas"),
                termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                        .stream().findFirst().ifPresent(map -> {
                            createDefaultStore(subTaskDO, map, storeSn, subTaskResponseDTOInner);
                        }));
        if (StringUtils.isBlank(storeSn.get())) {
            ObjectExtensionUtils.ifNonNull(BeanUtil.getNestedProperty(subTaskResponseDTOInner.getQueryResultRsqMap(), "respData.termDatas"),
                    termData -> ConvertUtil.castToExpectedList(termData, Map.class)
                            .stream().findFirst().ifPresent(map -> {
                                createDefaultStore(subTaskDO, map, storeSn, subTaskResponseDTOInner);
                            }));
        }
        lklV3Provider.handleSqbStoreTerminal(storeSn.get(), subTaskDO.getMerchantSn(), subTaskDO.getProvider());
        lklV3Provider.handleSqbMerchantProviderTerminal(subTaskDO.getMerchantSn(), subTaskResponseDTOInner.getNewAcquirerMerchantId(), subTaskDO.getProvider(),  subTaskResponseDTOInner.getNewTermId());
        // 这里防止后续过滤掉进件的门店进件导致无法触发终端进件
        insertTerminalContractTaskByStore(storeSn.get(), subTaskDO.getMerchantSn());
    }

    private void addLklV3ShopAndMerchantLevelTerminalV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationV3Task.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationV3Task.SubTaskResponseDTOInner.class);
        BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV3MainTaskContext.class);
        final Long mainContractTaskId = mainTaskContextBOInner.getMainContractTaskId();
        final MerchantAcquireInfoBO merchantAcquireInfoBO = lklAcquirerFacade.getAcquireInfoFromContractSubTask(mainContractTaskId);
        // 直接写一个商户级别终端到provider_terminal和交易侧
        providerTerminalBiz.merchantConnectionProviderTerminal(subTaskDO.getMerchantSn(),  merchantAcquireInfoBO.getLklTermNo(), merchantAcquireInfoBO.getAcquireMerchantId(),subTaskDO.getProvider());
        //将第一个门店信息写入lkl_term_shop表
        lklV3ShopTermBiz.createDefaultStore(merchantAcquireInfoBO.getLklShopId(),
                merchantAcquireInfoBO.getAcquireMerchantId(),
                merchantAcquireInfoBO.getLklV3Term(), null,
                subTaskDO.getMerchantSn());
        //触发门店级别绑定
        lklV3Provider.handleSqbStoreTerminal(merchantAcquireInfoBO.getLklV3Term().getDevSerialNo(), subTaskDO.getMerchantSn(), subTaskDO.getProvider());
        //收钱吧商户级别绑定
        lklV3Provider.handleSqbMerchantProviderTerminal(subTaskDO.getMerchantSn(), subTaskResponseDTOInner.getNewAcquirerMerchantId(), subTaskDO.getProvider(),  subTaskResponseDTOInner.getNewTermId());
        // 这里防止后续过滤掉进件的门店进件导致无法触发终端进件
//        insertTerminalContractTaskByStore(merchantAcquireInfoBO.getLklV3Term().getDevSerialNo(), subTaskDO.getMerchantSn());
    }

    private void createDefaultStore(InternalScheduleSubTaskDO subTaskDO, Map map, AtomicReference<String> storeSn, BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner) {
        try {
            LklV3Term lklV3Term = JSON.parseObject(JSON.toJSONString(map), LklV3Term.class);
            storeSn.set(lklV3Term.getDevSerialNo());
            lklV3ShopTermBiz.createDefaultStore(subTaskResponseDTOInner.getNewShopId(),
                    subTaskResponseDTOInner.getNewAcquirerMerchantId(),
                    lklV3Term, null, subTaskDO.getMerchantSn());
        } catch (Exception e) {
            log.error("商户{}入网后新增lkl_v3_shop_term失败", subTaskDO.getMerchantSn(), e);
        }
    }

    public void insertTerminalContractTaskByStore(String storeSn, String merchantSn) {
        // todo 一体化刷卡的不需要绑定和报备了
        List<Map> terminals = merchantBasicInfoBiz.listAllSqbTerminalsByStoreSn(storeSn);
        if (org.springframework.util.CollectionUtils.isEmpty(terminals)) {
            log.warn("商户{}, 门店sn{}, 门店下没有终端", merchantSn, storeSn);
            return;
        }
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(merchantSn);
        for (Map terminal : terminals) {
            try {
                log.info("商户{},门店{},终端{}, 新增拉卡拉终端进件任务", merchantSn, storeSn, MapUtils.getString(terminal, "sn"));
                insertContractTerminalTask(terminal, merchantInfo);
            } catch (Exception e) {
                log.error("商户{},终端{},新增拉卡拉终端任务失败", merchantSn, MapUtils.getString(terminal, "sn"), e);
            }
        }
    }

    private void insertContractTerminalTask(Map terminal, MerchantInfo merchantInfo) {
        TerminalBasicInsertEvent event = new TerminalBasicInsertEvent();
        DataBusTerminal dataBusTerminal = new DataBusTerminal();
        dataBusTerminal.setType(org.apache.commons.collections4.MapUtils.getInteger(terminal, "type"));
        dataBusTerminal.setStoreId(org.apache.commons.collections4.MapUtils.getString(terminal, "store_id"));
        dataBusTerminal.setMerchantId(merchantInfo.getId());
        event.setTerminalId(org.apache.commons.collections4.MapUtils.getString(terminal, "id"));
        event.setData(dataBusTerminal);
        lklV3Provider.produceInsertTerminalTaskByRule(event, merchantInfo);
    }

    @Override
    void updateTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        updateLklTradeParams(merchantInfo, subTaskResponseDTOInner.getNewUnionMerchantId(), subTaskResponseDTOInner.getNewTermId());
    }

    // 这里后续有其他的支付源参数，还要维护数据结构一致性
    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMap(InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        BusinessLicenceCertificationTask.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationTask.SubTaskContextBOInner.class);
        Map<String, MerchantProviderParamsDO> oldNewParamsMap = Maps.newHashMap();
        for (MerchantProviderParamsDO params : subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList())) {
            MerchantProviderParamsDO newAcquirerParams = BeanCopyUtils.copyProperties(params, MerchantProviderParamsDO.class);
            newAcquirerParams.setId(UUID.randomUUID().toString());
            newAcquirerParams.setCtime(System.currentTimeMillis());
            newAcquirerParams.setMtime(System.currentTimeMillis());
            newAcquirerParams.setProviderMerchantId(subTaskResponseDTOInner.getNewUnionMerchantId());
            if (Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newAcquirerParams.setPayMerchantId(subTaskResponseDTOInner.getNewAcquirerMerchantId());
            } else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())
                    || Objects.equals(params.getPayway(), PaywayEnum.JD_WALLET.getValue())) {
                if (StringUtils.isNotBlank(params.getPayMerchantId())) {
                    newAcquirerParams.setPayMerchantId(subTaskResponseDTOInner.getNewUnionMerchantId());
                }
                LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
                lklOpenUnionPayTradeParamBO.setProviderMerchantId(subTaskResponseDTOInner.getNewUnionMerchantId());
                lklOpenUnionPayTradeParamBO.setTermId(subTaskResponseDTOInner.getNewTermId());
                newAcquirerParams.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
            }
            oldNewParamsMap.put(params.getId(), newAcquirerParams);
        }
        return oldNewParamsMap;
    }

    @Resource
    private LklAcquirerFacade lklAcquirerFacade;

    @Resource
    private IndustryMappingCommonBiz industryMappingCommonBiz;
    @Resource
    private WechatAuthBiz wechatAuthBiz;
    @Resource
    @Lazy
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;

    public static final String AUTH_TIME = "auth_time";

    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenseCertificationV3MainTaskContext mainCtx = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        final Map<String, Object> merchant = mainCtx.getMerchant();

        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContextBOInner = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        final String wxAuthTime = mainCtx.getWxAuthTime();
        final Long aliAuthTime = mainCtx.getAliAuthTime();

        Map<String, MerchantProviderParamsDO> oldNewParamsMap = Maps.newHashMap();

        final MerchantAcquireInfoBO merchantAcquireInfoBO = lklAcquirerFacade.getAcquireInfoFromContractSubTask(subTaskContextBOInner.getContractTaskId());

        for (MerchantProviderParamsDO params : subTaskContextBOInner.getOldPayWayParamsMap().values().stream().flatMap(Collection::stream).collect(Collectors.toList())) {
            MerchantProviderParamsDO newParam = BeanCopyUtils.copyProperties(params, MerchantProviderParamsDO.class);
            newParam.setId(UUID.randomUUID().toString());
            newParam.setCtime(System.currentTimeMillis());
            newParam.setMtime(System.currentTimeMillis());
            //payway=0的参数
            if(Objects.equals(params.getPayway(), PaywayEnum.ACQUIRER.getValue())) {
                newParam.setProviderMerchantId(merchantAcquireInfoBO.getUnionNo());
                newParam.setPayMerchantId(merchantAcquireInfoBO.getAcquireMerchantId());
            }else if (Objects.equals(params.getPayway(), PaywayEnum.ALIPAY.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个支付宝参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.ALIPAY.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getAliNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                newParam.setProviderMerchantId(merchantAcquireInfoBO.getUnionNo());
                newParam.setPayMerchantId(merchantAcquireInfoBO.getAliNo());
                String industryId = BeanUtil.getPropString(merchant, Merchant.INDUSTRY);
                String aliMcc = industryMappingCommonBiz.getAliIndirectMcc(industryId);
                newParam.setAliMcc(aliMcc);
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME,aliAuthTime)));
            }else if (Objects.equals(params.getPayway(), PaywayEnum.WEIXIN.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个微信参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.WEIXIN.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getWxNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                newParam.setProviderMerchantId(merchantAcquireInfoBO.getUnionNo());
                newParam.setPayMerchantId(merchantAcquireInfoBO.getWxNo());
                final Map<String, Object> contractParamContext = businessLicenceCertificationV3Task.buildContractContext(mainTaskDO);
                final WechatAuthBiz.WechatAuthNameAndSettId merchantNameAndSettlementId = wechatAuthBiz.getMerchantNameAndSettlementId(contractParamContext);
                newParam.setWeixinSubAppid(null);
                newParam.setWeixinSubMiniAppid(null);
                newParam.setWxSettlementId(merchantNameAndSettlementId.getSettlementId());
                newParam.setMerchantName(merchantNameAndSettlementId.getMerchantName());
                newParam.setParamsConfigStatus(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE);
                newParam.setExtra(JSONObject.toJSONString(CollectionUtil.hashMap(AUTH_TIME,wxAuthTime)));
            }else if (Objects.equals(params.getPayway(), PaywayEnum.UNIONPAY.getValue())) {
                //同一个子商户号只能出现一次,对于在收单机构下有多个云闪付参数的情况下,小微升级以后只允许出现一个
                final boolean present = oldNewParamsMap.values().stream().filter(param -> Objects.equals(param.getPayway(), PaywayEnum.UNIONPAY.getValue())
                                && Objects.equals(param.getPayMerchantId(), merchantAcquireInfoBO.getUnionNo()))
                        .findFirst()
                        .isPresent();
                if(present) {
                    continue;
                }
                newParam.setProviderMerchantId(merchantAcquireInfoBO.getUnionNo());
                newParam.setPayMerchantId(merchantAcquireInfoBO.getUnionNo());
                LklOpenUnionPayTradeParamBO lklOpenUnionPayTradeParamBO = new LklOpenUnionPayTradeParamBO();
                lklOpenUnionPayTradeParamBO.setProviderMerchantId(merchantAcquireInfoBO.getUnionNo());
                lklOpenUnionPayTradeParamBO.setTermId(merchantAcquireInfoBO.getLklTermNo());
                newParam.setExtra(JSON.toJSONString(lklOpenUnionPayTradeParamBO));
            }

            oldNewParamsMap.put(params.getId(), newParam);
        }
        return oldNewParamsMap;
    }




    @Override
    void asyncExistedStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
            // 门店新增成功的回调场景会区分小微升级，做终端报备
            // lklV3Provider.doCreateProviderTerminal(subTaskDO.getMerchantSn(), ProviderEnum.PROVIDER_LAKALA_V3.getValue());
            contractExistedStoresAndBindToPayWayForLklV3(subTaskDO);
        });
    }

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    private void contractExistedStoresAndBindToPayWayForLklV3(InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationTask.SubTaskResponseDTOInner subTaskResponseDTOInner = JSON.parseObject(subTaskDO.getResponseMessage(), BusinessLicenceCertificationTask.SubTaskResponseDTOInner.class);
        List<Map> stores = merchantBasicInfoBiz.listAllSqbStores(subTaskDO.getMerchantSn());
        if (CollectionUtils.isEmpty(stores)) {
            log.warn("商户{}没有门店", subTaskDO.getMerchantSn());
            return;
        }
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(subTaskDO.getMerchantSn());
        for (Map store : stores) {
            try {
                addStoreContractTask(store, merchantInfo, subTaskResponseDTOInner.getNewAcquirerMerchantId());
            } catch (Exception e) {
                log.error("商户{}, 门店sn{}, 新增门店进件任务失败", subTaskDO.getMerchantSn(), MapUtils.getString(store, "sn"), e);
            }
        }
    }

    private void addStoreContractTask(Map store, MerchantInfo merchantInfo, String acquirerMerchantId) {
        // copy from StoreCreateConsumer 待优化
        String storeSn = MapUtils.getString(store, "sn");
        Map firstStore = lklV3ShopTermBiz.findFirstStore(merchantInfo.getId());
        if (StringUtils.equalsIgnoreCase(storeSn, MapUtils.getString(firstStore, Store.SN))) {
            return ;
        }
        if (Objects.nonNull(lklV3ShopTermBiz.shopContract(storeSn))) {
            return ;
        }
        Tuple2<Integer, Long> dependId = lklV3ShopTermBiz.merchantContract(merchantInfo.getSn());
        if (dependId == null) {
            log.error("未找到商户入网子任务: {}", merchantInfo.getSn());
            return ;
        }
        Map context = CollectionUtil.hashMap(CommonModel.STORE_SN, storeSn,
                ParamContextBiz.MERCHANT_FEE_RATES, lklV3Provider.getFeeRate(merchantInfo.getId()),
                MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_KEY, MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_VALUE
        );
        ContractTask task = buildStoreContractTask(merchantInfo, context);
        contractTaskBiz.insert(task);
        ContractSubTask update = buildStroreContractSubTask(merchantInfo, dependId, task);
        contractSubTaskMapper.insert(update);
        lklV3ShopTermBiz.addShop(merchantInfo.getSn(), storeSn, acquirerMerchantId, update.getId());
    }

    public static final String MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_KEY = "source";
    public static final String MICRO_UPGRADE_CONTRACT_STORE_CONTEXT_SOURCE_VALUE = "小微升级门店重新报备";

    private ContractSubTask buildStroreContractSubTask(MerchantInfo merchantInfo, Tuple2<Integer, Long> dependId, ContractTask task) {
        return new ContractSubTask()
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setMerchant_sn(merchantInfo.getSn())
                .setSchedule_dep_task_id(dependId.get_2())
                .setSchedule_status(dependId.get_1())
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setP_task_id(task.getId())
                .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_ADD_SHOP);
    }

    private ContractTask buildStoreContractTask(MerchantInfo merchantInfo, Map context) {
        return new ContractTask()
                .setMerchant_sn(merchantInfo.getSn())
                .setMerchant_name(merchantInfo.getName())
                .setStatus(TaskStatus.PENDING.getVal())
                .setRule_group_id(AcquirerTypeEnum.LKL_V3.getValue())
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM)
                .setAffect_status_success_task_count(0)
                .setAffect_sub_task_count(1)
                .setEvent_context(JSON.toJSONString(context));
    }

}
