package com.wosai.upay.job.biz.acquirer;

import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.upay.job.adapter.apollo.BatchTemplateApolloConfig;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("commonBank-AcquirerChangeBiz")
public class ChangeToCommonBizBank extends AbstractBankDirectAcquirerChangeBiz {

    @Autowired
    private BatchTemplateApolloConfig batchTemplateApolloConfig;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;

    @Override
    public String getContractGroup(String merchantSn) {
        return null;
    }

    @Override
    public int getProviderCode(String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        return Integer.parseInt(mcAcquirerDO.getProvider());
    }

    @Override
    protected String getDevCode(String acquirer) {
        Map bankMap = getBanMap(acquirer);
        return WosaiMapUtils.getString(bankMap, "devCode");
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        Map bankMap = getBanMap(acquirer);
        return WosaiMapUtils.getLongValue(bankMap, "defaultComboId");
    }

    private Map getBanMap(String acquirer) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(acquirer);
        Map map = batchTemplateApolloConfig.getSupportBankConfig();
        return WosaiMapUtils.getMap(map, mcAcquirerDO.getProvider());
    }

}
