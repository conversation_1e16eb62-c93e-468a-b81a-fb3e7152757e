package com.wosai.upay.job.refactor.task.license.update;

import avro.shaded.com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.status.ConfigStatusEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerSharedAbility;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.bo.InternalScheduleSubTaskProcessResultBO;
import com.wosai.upay.job.refactor.task.license.account.BankAccountChangeTaskHandler;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.DefaultStatusEnum;
import com.wosai.upay.job.refactor.model.enums.PaywayEnum;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.account.ChangeAccountWithLicenseUpdate;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 营业执照更新同步收单机构任务处理
 *
 * <AUTHOR>
 * @date 2025/2/10 11:48
 */
@Slf4j
@Component
public class LicenseUpdateToAcquirerTaskHandler {

    @Resource
    private AcquirerFacade acquirerFacade;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private McProviderDAO mcProviderDAO;

    @Resource
    private McAcquirerDAO mcAcquirerDAO;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private ContractStatusDAO contractStatusDAO;

    @Resource
    private ContractTaskDAO contractTaskDAO;

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;

    @Resource
    private BankAccountChangeTaskHandler bankAccountChangeTaskHandler;

    public static final String LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY = "licenseUpdateV2NeedUpdateAccount";

    public static final String ORIGINAL_BANK_ACCOUNT_TYPE_KEY = "originalBankAccountType";

    public static final String ORIGINAL_BANK_ACCOUNT_NO_KEY = "originalBankAccountNo";

    /**
     * 根据上下文，判断更新营业执照，是否也需要更新结算信息
     *
     * @param contextMap 上下文
     * @return 结果
     */
    public boolean isUpdateLicenseNeedUpdateAccount(Map<String, Object> contextMap) {
        try {
            if (MapUtils.isEmpty(contextMap) || !contextMap.containsKey(LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY)) {
                return false;
            }
            return MapUtils.getBoolean(contextMap, LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY);
        } catch (Exception e) {
            return false;
        }
    }
    public InternalScheduleSubTaskProcessResultBO handleLicenseUpdate(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        if (subTaskDO.isWaitProcess()) {
            return updateLicenseAndAccountToAcquirer(mainTaskDO, subTaskDO);
        } else if (subTaskDO.isWaitExternalResult()) {
            InternalScheduleSubTaskProcessResultBO resultBO = waitUpdateLicenseContractTaskDone(mainTaskDO, subTaskDO);
            BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = getMainTaskContextBOInner(mainTaskDO);
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            String merchantId = MapUtils.getString(mainTaskContextBOInner.getMerchant(), DaoConstants.ID);
            if (resultBO.processStatusIsSuccess()) {
                if (mainTaskContextBOInner.getNeedUpdateBankAccount() && isUpdateBankAccountWithLicenseOneTaskAcquirer(mainTaskDO.getAcquirer(), mainTaskDO)) {
                    changeAccountWithLicenseUpdate.updateAccountWhenContractSuccess(mainTaskContextBOInner, merchantId, auditApplyDTO);
                }
            }
            if (resultBO.processStatusIsFail()) {
                if (mainTaskContextBOInner.getNeedUpdateBankAccount() && isUpdateBankAccountWithLicenseOneTaskAcquirer(mainTaskDO.getAcquirer(), mainTaskDO)) {
                    changeAccountWithLicenseUpdate.updateAccountWhenContractFail(mainTaskContextBOInner, merchantId, auditApplyDTO);
                }
            }
            return resultBO;
        }
        log.error("不支持的任务状态,subTaskId:{}", subTaskDO.getId());
        throw new ContractBizException("系统异常");
    }




    private InternalScheduleSubTaskProcessResultBO waitUpdateLicenseContractTaskDone(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = getSubTaskContextBOInner(subTaskDO);
        Long contractTaskId = subTaskContextBOInner.getUpdateLicenseContractTaskId();
        Optional<ContractTaskDO> taskOpt = contractTaskDAO.getByPrimaryKey(contractTaskId);
        if (!taskOpt.isPresent()) {
            return InternalScheduleSubTaskProcessResultBO.fail("营业执照更新任务为空");
        }
        ContractTaskDO taskDO = taskOpt.get();
        if (taskDO.processFail()) {
            return InternalScheduleSubTaskProcessResultBO.fail(getErrorMsgFromLicenseContractTask(taskDO));
        }
        if (taskDO.processSuccess()) {
            return InternalScheduleSubTaskProcessResultBO.success("更新成功");
        }
        return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待更新任务完成");
    }

    private String getErrorMsgFromLicenseContractTask(ContractTaskDO taskDO) {
        try {
            if (Objects.isNull(taskDO) || StringUtils.isBlank(taskDO.getResult())) {
                return  "营业执照更新失败";
            }
            Map<String, Object> root = JSON.parseObject(taskDO.getResult(), new TypeReference<Map<String, Object>>(){});
            Object result = root.getOrDefault("result", "");
            return result instanceof String ? (String) result : "营业执照更新失败";
        } catch (Exception e) {
            log.warn("getErrorMsgFromLicenseContractTask error, merchantSn:{}", taskDO.getMerchantSn(), e);
            return "营业执照更新失败";
        }
    }

    private BusinessLicenceCertificationV2Task.SubTaskContextBOInner getSubTaskContextBOInner(InternalScheduleSubTaskDO subTaskDO) {
        return JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV2Task.SubTaskContextBOInner.class);
    }

    private InternalScheduleSubTaskProcessResultBO updateLicenseAndAccountToAcquirer(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        try {
            Long taskId = produceUpdateLicenseTask(mainTaskDO, subTaskDO);
            MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
            BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = getMainTaskContextBOInner(mainTaskDO);
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            if (mainTaskContextBOInner.getNeedUpdateBankAccount() && isUpdateBankAccountWithLicenseOneTaskAcquirer(mainTaskDO.getAcquirer(), mainTaskDO)) {
                // 当前营业执照变更也变更了结算信息，才会修改银行卡状态
                changeAccountWithLicenseUpdate.updateDefaultAccountVerifyStatus(merchantInfo.getId(), MerchantBankAccount.VERIFY_STATUS_INPROGRESS,
                        auditApplyDTO.getSubmitUserId(), auditApplyDTO.getSubmitUserName(), auditApplyDTO.getPlatform());
            }
            BusinessLicenceCertificationV2Task.SubTaskContextBOInner subTaskContextBOInner = getSubTaskContextBOInner(subTaskDO);
            if (Objects.isNull(subTaskContextBOInner)) {
                subTaskContextBOInner = new BusinessLicenceCertificationV2Task.SubTaskContextBOInner();
            }
            subTaskContextBOInner.setUpdateLicenseContractTaskId(taskId);
            subTaskDO.setContext(JSON.toJSONString(subTaskContextBOInner));
            return InternalScheduleSubTaskProcessResultBO.waitExternalResult("等待更新任务完成");
        } catch (Exception e) {
            log.error("更新营业执照，构建任务失败, merchantSn:{}", mainTaskDO.getMerchantSn(), e);
            return InternalScheduleSubTaskProcessResultBO.fail(e.getMessage());
        }

    }


    private boolean isUpdateBankAccountWithLicenseOneTaskAcquirer(String acquirer,  InternalScheduleMainTaskDO mainTaskDO) {
        return !bankAccountChangeTaskHandler.isLicenseTaskNeedExtraChangeAccountTask(acquirer, mainTaskDO);
    }


    /**
     * 因为营业执照更新有的是异步的，涉及回调，为了沿用原有的逻辑，生成对应的contract_task
     * 构建营业执照更新的任务（contract_task  contract_sub_task）
     * 以前是营业执照先落库，再同步收单机构
     * 本次是同步收单机构，再落库，所以没法用原来的contract_event（主要是没法从现有表构建context）
     * 同时需要merchant-contract更新营业执照接口，也需要根据配置，更新结算信息
     * 富友变更卡和变更执照是两个接口，且变更卡可能次日失效
     *
     * @param mainTaskDO 主任务
     * @param subTaskDO  子任务
     * @return contract_task的主键
     */
    private Long produceUpdateLicenseTask(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        Map<String, Object> contextMap = getContextMapByMainTaskDO(mainTaskDO);
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoBySn(mainTaskDO.getMerchantSn());
        List<MerchantProviderParamsDO> params = merchantTradeParamsBiz.listParamsByMerchantSn(mainTaskDO.getMerchantSn());
        Set<String> thirdExistedAcquirers = params.stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .map(t -> mcProviderDAO.getByProvider(t.getProvider().toString()))
                .filter(t -> t.isPresent() && (mcAcquirerDAO.listThirdAcquirers().contains(t.get().getAcquirer()) || AcquirerTypeEnum.UMB.getValue().equals(t.get().getAcquirer())))
                .map(t -> t.get().getAcquirer())
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(thirdExistedAcquirers)) {
            throw new ContractBizException("没有进件成功的三方收单机构");
        }

        Optional<ContractStatusDO> contractStatusDOOpt = contractStatusDAO.getByMerchantSn(mainTaskDO.getMerchantSn());
        if (!contractStatusDOOpt.isPresent() || !Objects.equals(contractStatusDOOpt.get().getStatus(), ContractStatus.STATUS_SUCCESS)) {
            log.error("该商户尚未报备成功, merchantSn:{}, subTaskId:{}", mainTaskDO.getMerchantSn(), subTaskDO.getId());
            throw new ContractBizException("该商户尚未报备成功");
        }
        String acquirer = contractStatusDOOpt.get().getAcquirer();
        ContractTaskDO contractTaskDO = getContractTaskDO(mainTaskDO, acquirer, merchantInfo, contextMap);

        Map<String, MerchantProviderParamsDO> acquirerParamsMap = params.stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .collect(Collectors.toMap(
                        t -> mcProviderDAO.getByProvider(t.getProvider().toString()).map(McProviderDO::getAcquirer).orElse(""),
                        Function.identity(),
                        (a, b) -> a));
        List<ContractSubTaskDO> contractSubTaskDOS = new ArrayList<>();
        for (String thirdExistedAcquirer : thirdExistedAcquirers) {
            MerchantProviderParamsDO merchantProviderParamsDO = acquirerParamsMap.get(thirdExistedAcquirer);
            if (Objects.isNull(merchantProviderParamsDO)) {
                continue;
            }
            ContractSubTaskDO contractSubTaskDO = getContractSubTaskDO(mainTaskDO, thirdExistedAcquirer, acquirer, merchantProviderParamsDO);
            contractSubTaskDOS.add(contractSubTaskDO);
        }
        if (CollectionUtils.isEmpty(contractSubTaskDOS)) {
            throw new ContractBizException("子任务为空");
        }
        return contractTaskDAO.batchInsertTasks(contractTaskDO, contractSubTaskDOS);

    }

    @NotNull
    private ContractSubTaskDO getContractSubTaskDO(InternalScheduleMainTaskDO mainTaskDO, String thirdExistedAcquirer, String acquirer, MerchantProviderParamsDO merchantProviderParamsDO) {
        ContractSubTaskDO contractSubTaskDO = new ContractSubTaskDO();
        if (StringUtils.equals(thirdExistedAcquirer, acquirer)) {
            contractSubTaskDO.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.YES.getValue());
        } else {
            contractSubTaskDO.setStatusInfluPTask(AffectPrimaryTaskStatusEnum.NO.getValue());
        }
        contractSubTaskDO.setMerchantSn(mainTaskDO.getMerchantSn());
        contractSubTaskDO.setDefaultChannel(DefaultStatusEnum.NOT_DEFAULT.getValue());
        contractSubTaskDO.setChangeConfig(ConfigStatusEnum.NOT_CONFIG.getValue());
        contractSubTaskDO.setTaskType(ContractSubTaskTypeEnum.BUSINESS_LICENSE_CHANGE.getValue());
        contractSubTaskDO.setPayway(PaywayEnum.ACQUIRER.getValue());
        contractSubTaskDO.setScheduleStatus(ScheduleStatusEnum.CAN.getValue());
        contractSubTaskDO.setChannel(thirdExistedAcquirer);
        contractSubTaskDO.setContractRule(merchantProviderParamsDO.getContractRule());
        contractSubTaskDO.setRuleGroupId(merchantProviderParamsDO.getRuleGroupId());
        return contractSubTaskDO;
    }

    @NotNull
    private ContractTaskDO getContractTaskDO(InternalScheduleMainTaskDO mainTaskDO, String acquirer, MerchantInfo merchantInfo, Map<String, Object> contextMap) {
        Optional<AcquirerSharedAbility> sharedAbilityByAcquirer = acquirerFacade.getSharedAbilityByAcquirer(acquirer);
        String mainTaskGroupId = sharedAbilityByAcquirer
                .map(t -> t.getDefaultContractRuleGroupId().orElseGet(() -> acquirer)).orElseGet(() -> acquirer);
        ContractTaskDO contractTaskDO = new ContractTaskDO();
        contractTaskDO.setMerchantSn(mainTaskDO.getMerchantSn());
        contractTaskDO.setMerchantName(merchantInfo.getName());
        contractTaskDO.setType(ContractTaskTypeEnum.UPDATE_BUSINESS_LICENSE.getValue());
        contractTaskDO.setEventContext(JSON.toJSONString(contextMap));
        // 当前收单机构成功，就成功
        contractTaskDO.setAffectSubTaskCount(1);
        contractTaskDO.setAffectStatusSuccessTaskCount(0);
        contractTaskDO.setRuleGroupId(mainTaskGroupId);
        return contractTaskDO;
    }

    private Map<String, Object> getContextMapByMainTaskDO(InternalScheduleMainTaskDO mainTaskDO) {
        BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = getMainTaskContextBOInner(mainTaskDO);
        Map<String, Object> contextMap = Maps.newHashMap();
        contextMap.put(ParamContextBiz.KEY_MERCHANT, mainTaskContextBOInner.getMerchant());
        contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, mainTaskContextBOInner.getBankAccount());
        contextMap.put(ParamContextBiz.KEY_BANK_INFO, mainTaskContextBOInner.getBankInfo());
        contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, mainTaskContextBOInner.getMerchantBusinessLicense());
        contextMap.put(LICENSE_UPDATE_V2_NEED_UPDATE_ACCOUNT_KEY, mainTaskContextBOInner.getNeedUpdateBankAccount());
        // 原账户类型 账户号
        contextMap.put(ORIGINAL_BANK_ACCOUNT_TYPE_KEY, mainTaskContextBOInner.getOriginalDefaultAccountType());
        contextMap.put(ORIGINAL_BANK_ACCOUNT_NO_KEY, mainTaskContextBOInner.getOriginalDefaultAccountNumber());
        return contextMap;
    }


    private BusinessLicenseCertificationV2MainTaskContext getMainTaskContextBOInner(InternalScheduleMainTaskDO mainTaskDO) {
        return JSON.parseObject(mainTaskDO.getContext(),
                BusinessLicenseCertificationV2MainTaskContext.class);
    }
}
