package com.wosai.upay.job.handlers;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.PayWayConfigChangeMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.TongLianV2Provider;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.constant.UnionConstant;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

/**
 * @Description: 通联收银宝 支付源查询落库
 * <AUTHOR>
 * @Date 2023/6/14 18:08
 **/

@Component
@Order(90)
public class TongLianV2PaywayHandler extends AbstractSubTaskHandler {

    @Autowired
    TongLianV2Service tongLianV2Service;

    @Autowired
    RuleContext ruleContext;

    @Autowired
    ProviderTerminalTaskRepository providerTerminalTaskRepository;

    @Autowired
    PayWayConfigChangeMapper payWayConfigChangeMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    MerchantService merchantService;

    @Autowired
    TongLianV2Provider tongLianV2Provider;
    @Autowired
    IndustryMappingCommonBiz industryMappingCommonBiz;

    @Override
    protected void handleError(ContractTask task, ContractSubTask subTask, Exception e) throws Exception {
        handleResult(new ContractResponse().setCode(500).setMessage(e.toString()), subTask);
    }

    @Override
    public void doHandle(ContractTask task, ContractSubTask subTask) {
        BasicProvider provider = providerFactory.getProviderByName(subTask.getChannel());
        ContractResponse response = provider.processTaskByRule(task,
                ruleContext.getContractRule(subTask.getContract_rule()).getContractChannel(),
                subTask);
        if (response.isSuccess()) {
            handlePaywayParams(response.getResponseParam(), subTask);
        }
        handleResult(response, subTask);
    }

    @Override
    public boolean supports(ContractTask task, ContractSubTask subTask) {
        if (McConstant.RULE_GROUP_TONGLIAN_V2.equals(subTask.getRule_group_id())
                && ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT.equals(subTask.getTask_type())
                && !PaywayEnum.ACQUIRER.getValue().equals(subTask.getPayway())
        ) {
            return true;
        }
        return false;
    }

    private void handlePaywayParams(Map response, ContractSubTask subTask) {
        String merchantSn = subTask.getMerchant_sn();
        Integer payway = subTask.getPayway();
        String subMchId = MapUtils.getString(response, "cmid");
        String cusid = MapUtils.getString(response, "cusid");
        String paychnl = MapUtils.getString(response, "paychnl");
        String merchantName = tongLianV2Provider.getMerchantName(merchantSn);
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);
        String id = UUID.randomUUID().toString();
        long now = System.currentTimeMillis();
        ContractRule contractRule = ruleContext.getContractRule(subTask.getContract_rule());
        MerchantProviderParams params = new MerchantProviderParams()
                .setId(id)
                .setMerchant_sn(merchantSn)
                .setChannel_no(contractRule.getChannelNo())
                .setOut_merchant_sn(merchantSn)
                .setMerchant_name(merchantName)
                .setParent_merchant_id(subMchId)
                .setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                .setProvider_merchant_id(cusid)
                .setPayway(payway)
                .setPay_merchant_id(subMchId)
                .setStatus(UseStatusEnum.NO_USE.getValue())
                .setDeleted(false)
                .setRule_group_id(subTask.getRule_group_id())
                .setUpdate_status(1)
                .setContract_rule(subTask.getContract_rule())
                .setCtime(now)
                .setMtime(now)
                ;
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            String settlementid = MapUtils.getString(response, "settid");
            Map extra = CollectionUtil.hashMap(CommonModel.WEIXIN_SUB_MCH_ID, subMchId,
                    CommonModel.CHANNEL_ID, paychnl,
                    CommonModel.PROVIDER_MCH_ID, cusid);
            params.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE)
                    .setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap("tradeParams", extra)))
                    .setWx_settlement_id(settlementid)
                    .setWx_use_type(WxUseType.NORMAL.getCode())
            ;
        } else if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            Map extra = CollectionUtil.hashMap(CommonModel.ALIPAY_SUB_MCH_ID, subMchId,
                    CommonModel.PROVIDER_MCH_ID, cusid);
            params.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                    .setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap("tradeParams", extra)))
                    .setAli_mcc(industryMappingCommonBiz.getAliIndirectMcc(merchantInfo.getIndustry()))
            ;
        } else if (PaywayEnum.UNIONPAY.getValue().equals(payway)) {
            return;
            // 通联收银宝不返回云闪付子商户号
//            Map extra = CollectionUtil.hashMap(CommonModel.MCH_ID, cusid,
//                    CommonModel.MERNAME, merchantName,
//                    CommonModel.PROVIDER_MCH_ID, cusid,
//                    "mer_cat_code", MapUtils.getString(industry, UnionConstant.SIGN_TL)
//            );
//            params.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
//                    .setContract_rule(UNION_RULE)
//                    .setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap("tradeParams", extra)))
//            ;
        }
        merchantProviderParamsMapper.insertSelective(params);
        response.put("merchantProviderParamsId", id);
        if (subTask.getChange_config() == 1) {
            PayWayConfigChange payWayConfigChange = new PayWayConfigChange().
                    setBody(JSON.toJSONString(response))
                    .setMerchant_sn(merchantSn)
                    .setPayway(payway)
                    .setChannel(subTask.getChannel());
            payWayConfigChangeMapper.insertSelective(payWayConfigChange);
        }
        providerTerminalTaskRepository.addBoundTerminalTask(merchantSn, subMchId, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue(), payway, ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(), null, null, null);
    }

}