package com.wosai.upay.job.refactor.model.enums;

/**
 * 删除枚举
 *
 * <AUTHOR>
 */
public enum Deleted implements ITextValueEnum {
    /**
     * 未删除
     */
    NO_DELETED(0, "未删除"),
    /**
     * 已删除
     */
    DELETED(1, "已删除");

    private final int value;
    private final String text;

    Deleted(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
