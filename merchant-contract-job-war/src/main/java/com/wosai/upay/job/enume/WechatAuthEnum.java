package com.wosai.upay.job.enume;

import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.constant.WechatAuthUrlConstants;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/6/2 10:30 上午
 **/
@Getter
public enum WechatAuthEnum {

    LKL("union_wm", "36002013293", 1016, "lkl-1016-3-32631798", WechatAuthUrlConstants.LKL, McConstant.RULE_GROUP_LKL),
    LKLORG("lkl_org", "32631798", 1033, "lkl-1033-3-32631798", WechatAuthUrlConstants.LKL, McConstant.RULE_GROUP_LKLORG),
    TONGLIAN("tonglian", "313848752", 1020, "tonglian-1020-3", WechatAuthUrlConstants.TONGLIAN, McConstant.RULE_GROUP_TONGLIAN),
    UMS("ums", "207597046", 1018, "ums-1018-3", WechatAuthUrlConstants.UMS, McConstant.RULE_GROUP_UMS),
    PSBC("psbc", "441216660", 1023, "psbc-1023-3",WechatAuthUrlConstants.PSBC,McConstant.RULE_GROUP_PSBC),
    CGB("cgb", "450206481", 1024, "cgb-1024-3",WechatAuthUrlConstants.CGB,McConstant.RULE_GROUP_CGB),
    CCB("ccb", "458407926", 1026, "ccb-1026-3",WechatAuthUrlConstants.CCB,McConstant.RULE_GROUP_CCB),
    ;

    private String channel;
    private String ChannelNo;
    private Integer provider;
    private String contractChannel;
    private String channelCodeUrl;
    private String ruleGroupId;

    WechatAuthEnum(String channel, String channelNo, Integer provider, String contractChannel, String channelCodeUrl, String ruleGroupId) {
        this.channel = channel;
        ChannelNo = channelNo;
        this.provider = provider;
        this.contractChannel = contractChannel;
        this.channelCodeUrl = channelCodeUrl;
        this.ruleGroupId = ruleGroupId;
    }

    public static WechatAuthEnum valueAcquirer(String acquirer){
        switch (acquirer){
            case McConstant.ACQUIRER_LKL: return LKL;
            case McConstant.ACQUIRER_LKLV3: return LKL;
            case McConstant.ACQUIRER_PSBC: return PSBC;
            case McConstant.ACQUIRER_CCB: return CCB;
            case McConstant.ACQUIRER_UMS: return UMS;
            case McConstant.ACQUIRER_TONGLIAN: return TONGLIAN;
            default: return LKL;
        }
    }

    public static WechatAuthEnum valueChannel(String channel) {
        if (StringUtils.isEmpty(channel)) {
            return null;
        }
        for (WechatAuthEnum authEnum : WechatAuthEnum.values()) {
            if (channel.equalsIgnoreCase(authEnum.getChannel())) {
                return authEnum;
            }
        }
        return null;
    }

    public static WechatAuthEnum valueProvider(Integer provider) {
        if (Objects.isNull(provider)) {
            return null;
        }
        for (WechatAuthEnum authEnum : WechatAuthEnum.values()) {
            if (provider.equals(authEnum.getProvider())) {
                return authEnum;
            }
        }
        return null;
    }
}
