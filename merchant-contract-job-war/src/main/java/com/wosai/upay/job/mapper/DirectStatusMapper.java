package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DirectStatus;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DirectStatusMapper {
    int deleteByPrimaryKey(Long id);

    int insert(DirectStatus record);

    int insertSelective(DirectStatus record);

    DirectStatus selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DirectStatus record);

    int updateByPrimaryKey(DirectStatus record);

    DirectStatus selectDirectStatusByMerchantSn(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode);

    int updateDirectStatusBySnAndDevCode(@Param("merchantSn") String merchantSn, @Param("devCode") String devCode, @Param("status") int status);

    @Select("select * from direct_status where merchant_sn=#{merchantSn}")
    List<DirectStatus> getDirectByMerchantSn(@Param("merchantSn") String merchantSn);
}