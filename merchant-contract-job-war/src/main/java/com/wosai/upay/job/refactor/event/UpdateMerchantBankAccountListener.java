package com.wosai.upay.job.refactor.event;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.refactor.dao.ContractSubTaskExtDAO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskExtDO;
import com.wosai.upay.job.util.ProviderUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 银行卡信息同步到收单机构成功后，也要同步到AT
 *
 * <AUTHOR>
 * @date 2023/12/19
 */
@Component
public class UpdateMerchantBankAccountListener implements ApplicationListener<UpdateMerchantBankAccountEvent> {

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private RuleContext ruleContext;

    @Autowired
    private ContractSubTaskExtDAO contractSubTaskExtDAO;

    @Override
    public void onApplicationEvent(UpdateMerchantBankAccountEvent event) {
        Long subTaskId = event.getSubTaskId();
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        String merchantSn = contractSubTask.getMerchant_sn();

        // 检查是否已经存在
        // 失败后SPA点击重新执行，或者拉卡拉失败复议后自动重试
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByATDependTaskId(contractSubTask.getId());

        if (WosaiCollectionUtils.isNotEmpty(contractSubTasks)) {
            return;
        }
        // 防并发导致子任务无法启动调度,推迟两秒执行
        try {
            Thread.sleep(2000L);
        } catch (Exception e) {

        }

        // 查询当前在用的AT子商户号
        List<MerchantProviderParams> inUserParams = getInUserParams(merchantSn, event.getProvider());
        // 创建更新任务
        for (MerchantProviderParams params : inUserParams) {
            ContractSubTask subTask = new ContractSubTask()
                    .setP_task_id(contractSubTask.getP_task_id())
                    .setSchedule_dep_task_id(contractSubTask.getId())
                    .setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue())
                    .setMerchant_sn(merchantSn)
                    .setStatus_influ_p_task(0)
                    .setChannel(event.getChannel())
                    .setChange_config(0)
                    .setDefault_channel(0)
                    .setPayway(params.getPayway())
                    .setContract_rule(getContractRule(params))
                    .setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE)
                    .setRetry(0);
            contractSubTaskMapper.insert(subTask);
            ContractSubTaskExtDO contractSubTaskExtDO = new ContractSubTaskExtDO();
            contractSubTaskExtDO.setSubTaskId(subTask.getId());
            contractSubTaskExtDO.setPayMerchantId(params.getPay_merchant_id());
            contractSubTaskExtDAO.insertOne(contractSubTaskExtDO);
        }
        // 再次查询，修正状态
        contractSubTask = contractSubTaskMapper.selectByPrimaryKey(subTaskId);
        if (Objects.equals(contractSubTask.getStatus(), TaskStatus.SUCCESS.getVal())) {
            contractSubTaskMapper.setEnableScheduleByDepId(contractSubTask.getId());
        }
    }

    /**
     * 获取间连在用AT参数
     *
     * @param merchantSn
     * @return
     */
    private List<MerchantProviderParams> getInUserParams(String merchantSn, int provider) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayIn(Arrays.asList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(false);
        return merchantProviderParamsMapper.selectByExample(example);
    }

    private String getContractRule(MerchantProviderParams params) {
        if (WosaiStringUtils.isNotEmpty(params.getContract_rule())) {
            return params.getContract_rule();
        }
        return ruleContext.getDefaultContractRule(String.valueOf(params.getProvider()), params.getPayway(), params.getChannel_no());
    }
}
