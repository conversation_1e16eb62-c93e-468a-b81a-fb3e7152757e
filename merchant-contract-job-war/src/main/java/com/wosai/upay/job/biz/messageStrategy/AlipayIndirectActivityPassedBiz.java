package com.wosai.upay.job.biz.messageStrategy;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.newBlueSea.AlipayIndirectActivityStatus;
import com.wosai.upay.job.payactivity.biz.AlipayUniversityBiz;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

import static com.wosai.upay.job.Constants.BlueSeaConstant.ACTIVITY_CREATE_SUCCESS;

/**
 * @Description: 支付宝间连活动报名成功处理
 * <AUTHOR>
 * @Date: 2021/11/22 4:54 下午
 */
@Component
@Slf4j
public class AlipayIndirectActivityPassedBiz implements AliMessageHandleService {

    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private BlueSeaBiz blueSeaBiz;
    @Autowired
    private AlipayUniversityBiz alipayUniversityBiz;
    @Autowired
    private RedisLock redisLock;

    private static final String STATUS="alipay_indirect_activity_apply_status";

    @Override
    public String getMsgMethod() {
        return "ant.merchant.expand.indirect.activity.passed";
    }

    @Override
    public void handleMessageBiz(String bizContent) {
        log.info("支付宝间连活动报名成功业务处理 : {}", bizContent);
        AlipayIndirectActivityStatus success = JSONObject.parseObject(bizContent, AlipayIndirectActivityStatus.class);
        String key = STATUS + success.getOrder_id();
        try {
            if (!redisLock.lock(key, key, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
                return;
            }
            //活动申请单Id
            String activityOrderId = success.getOrder_id();
            //是否存在这个记录
            BlueSeaTask task = blueSeaTaskMapper.findTaskByActivityOrderId(activityOrderId);
            if (Objects.isNull(task)) {
                return;
            }
            //申请单存在 更改状态为报名成功
            blueSeaBiz.updateStatus(task.getId(), ACTIVITY_CREATE_SUCCESS, null, null, null, null, null, 2);
            //写评论, 新的申请就不发送消息给审批中心
            if(Utils.isNullOrZero(task.getApply_id())) {
                alipayUniversityBiz.addComment(task.getAudit_id(), MapUtils.getLong(JSONObject.parseObject(task.getForm_body(), Map.class), BlueSeaConstant.TEMPLATE_ID),
                        1, "高校活动报名支付宝审核通过，等待切换费率");
            }
        } finally {
            redisLock.unlock(key, key);
        }

    }
}
