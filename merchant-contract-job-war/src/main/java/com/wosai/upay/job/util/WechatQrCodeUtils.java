package com.wosai.upay.job.util;

import com.wosai.mpay.util.StringUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.UUID;

/**
 * @Auther: hrx
 * @Date: 2019-08-26
 * @Description: 微信小程序码的生成工具类
 * @version: 1.0
 */
@Component
public class WechatQrCodeUtils {

    private final static Logger logger = LoggerFactory.getLogger(WechatQrCodeUtils.class);

    @Value("${wechat_applet_code.build.path}")
    private String qrcodeOutputFolder;

    @Autowired
    private OSSFileUploader ossFileUploader;

    /**
     * 商户信息授权码
     *
     * @param info
     * @return
     */
    public String authorizationCodeUrl(Map info, String codeName) throws IOException {
        String filePath = qrcodeOutputFolder + System.currentTimeMillis() + ".png";
        String url = null;
        BufferedImage image = null;
        try {
            String merchantName = MapUtils.getString(info, "merchantName");
            image = loadImageLocal(this.getClass().getResourceAsStream("/image/" + codeName + ".png"));
            //添加商户名
            Color white = new Color(255, 255, 255);
            Color black = new Color(0, 0, 0);
            image = addMerchantName(image, merchantName, 919, white);
            //添加商户联系人姓名
            String concatName = MapUtils.getString(info, "concatName");
            image = addConcatName(image, concatName, 299, black);
            //添加商户信息
            image = addMerchantInfo(image, info);
            //写入到本地
            writeImageLocal(filePath, image);
            //2.上传oss
            String key = "merchant-contract-job/" + "authorizationCodeUrl" + UUID.randomUUID() + ".png";
            url = ossFileUploader.upload(key, filePath);
        } catch (Exception e) {
            logger.error("Exception : ", e);
            throw e;
        } finally {
            if (image != null) {
                image = null;
            }
            FileUtils.delete(filePath);
        }
        return url;
    }

    /**
     * 将字节输入流转成图片输入流
     *
     * @param inputStream 目标流
     * @return
     */
    private static BufferedImage loadImageLocal(InputStream inputStream) {
        try {
            return ImageIO.read(inputStream);
        } catch (IOException e) {
            logger.info("Exception : ", e);
        }
        return null;
    }
    /**
     * 生成新图片到本地
     */
    private void writeImageLocal(String newImage, BufferedImage img) {
        logger.info("writeImageLocal:{}", newImage);
        if (newImage != null && img != null) {
            try {
                File file = new File(newImage);
                ImageIO.write(img, "png", file);
            } catch (IOException e) {
                logger.info(e.getMessage());
            }
        }
    }

    private BufferedImage addConcatName(BufferedImage image, String userName, int y, Color color) {
        if(StringUtils.isEmpty(userName)) {
            return image;
        }
        String pingFangMediumFontName = "/font/PingFangMediumCoarse.ttf";
        Font fontByWhite = FontUtil.getFont(pingFangMediumFontName, Font.PLAIN, 28);
        Font fontByYellow = FontUtil.getFont(pingFangMediumFontName, Font.PLAIN, 30);
        Color yellow = new Color(255, 188, 54);
        //获取特殊处理文字x轴的数值大小
        String content = "请姓" + userName + "使用本人已绑卡微信扫描以下";//加“字”是为了添加空格的长度，直接加空格会被去空
        int xValue = getXValue(image, content, color, fontByWhite);
        String content1 = "请";
        image = addWordToQrcodeImage(image, content1, y + 42, fontByWhite, color, xValue);//所有文字居中后距离左边的距离
        image = addWordToQrcodeImage(image, userName, y + 42, fontByYellow, yellow, xValue + 28 + 14);//加 ("请 ") 的距离
        String content3 = "使用本人已绑卡微信扫描以下";
        image = addWordToQrcodeImage(image, content3, y + 42, fontByWhite, color, xValue + userName.length() * 30 + 28 * 2);//加( "请 "+userName )的距离
        String content4 = "小程序码确认微信支付商户信息";
        return addWordToQrcodeImage(image, content4, y, fontByWhite, color, 0);
    }


    private BufferedImage addMerchantName(BufferedImage image, String merchantName, int y, Color color) {
        if(StringUtils.isEmpty(merchantName)) {
            return image;
        }
        String pingFangMediumFontName = "/font/PingFangMediumCoarse.ttf";
        Font font = FontUtil.getFont(pingFangMediumFontName, Font.PLAIN, 46);
        return addWordToQrcodeImage(image, merchantName, y, font, color, 0);
    }

    private BufferedImage addMerchantInfo(BufferedImage image, Map info) {
        if(MapUtils.isEmpty(info)) {
            return image;
        }
        String pingFangMediumFontName = "/font/PingFangMediumCoarse.ttf";
        Font font = FontUtil.getFont(pingFangMediumFontName, Font.PLAIN, 30);
        Color black = new Color(0, 0, 0);
        //商户经营名称
        String businessName = "商户经营名称: " + MapUtils.getString(info, "merchantBusinessName");
        image = addWordToQrcodeImage(image, businessName, 154, font, black, 0);
        //微信商户号
        String weixinMerchantId = "微信商户号: " + MapUtils.getString(info, "weixinMerchantId");
        image = addWordToQrcodeImage(image, weixinMerchantId, 110, font, black, 0);
        //客服电话 =>商户名称
        String serverPhone = "商户名称: " + MapUtils.getString(info, "merchantName");
        image = addWordToQrcodeImage(image, serverPhone, 66, font, black, 0);
        //支付源渠道码
        String paywayChannel_no = "ID: " + MapUtils.getString(info, "payway_channel_no");
        image = addWordToQrcodeImage(image, paywayChannel_no, 1150, font, Color.white, 540);
        return image;
    }


    private int getXValue(BufferedImage img, String content, Color color, Font font) {
        Graphics2D g = img.createGraphics();
        if (font != null)
            g.setFont(font);
        int strWidth = g.getFontMetrics().stringWidth(content);
        return (img.getWidth() - strWidth) / 2;
    }


    /**
     * 往图片添加文字
     *
     * @param img      目标图片
     * @param content  文字内容
     * @param yPartion 距离底部距离
     * @param font     字体
     * @param x        字体距离左边的距离
     * @return
     */
    private BufferedImage addWordToQrcodeImage(BufferedImage img, String content, int yPartion, Font font, Color color, int x) {
        try {
            Graphics2D g = img.createGraphics();
            //消除文字上面的锯齿
            g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g.setBackground(Color.WHITE);
            g.setColor(color);//设置字体颜色
            if (font != null)
                g.setFont(font);
            if (content != null) {
                //获取字符串转换为字符串图片的宽度
                if (x == 0) {
                    int strWidth = g.getFontMetrics().stringWidth(content);
                    x = (img.getWidth() - strWidth) / 2;
                }
                g.drawString(content, x, img.getHeight() - yPartion);
            }
            g.dispose();
        } catch (Exception e) {
            logger.info(e.getMessage());
        }
        return img;
    }
}
