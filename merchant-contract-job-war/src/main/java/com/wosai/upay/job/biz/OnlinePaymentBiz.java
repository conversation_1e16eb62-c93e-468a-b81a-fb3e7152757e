package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.model.SystemResponse;
import com.wosai.mpay.util.StringUtils;
import com.wosai.service.SystemService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.avro.OpenOnlinePaymentApplyStatusChange;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.acquirer.IAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.BatchImportAliOnlineMerchantsExcel;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.onlinePayment.OnlinePaymentOpenResp;
import com.wosai.upay.job.refactor.dao.McProviderDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.bo.*;
import com.wosai.upay.job.refactor.model.entity.McProviderDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/25
 */
@Slf4j
@Component
public class OnlinePaymentBiz {

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private AlipayAuthBiz alipayAuthBiz;
    @Autowired
    @Lazy
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private RuleBiz ruleBiz;
    @Autowired
    private RuleContext ruleContext;
    @Autowired
    private ParamContextBiz paramContextBiz;
    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private SystemService systemService;
    @Autowired
    @Lazy
    private MerchantProviderParamsService merchantProviderParamsService;
    @Autowired
    private FeeRateService feeRateService;
    @Autowired
    private AcquirerChangeDao acquirerChangeDao;
    @Autowired
    private RedisLock redisLock;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    private static final String OPEN_ONLINE_PAYMENT_TOPIC = "events_CUA_job_open-online-payment";

    public void insertApplyWithKafka(String merchantSn, Integer payway, String acquirer) {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = openOnlinePaymentApplyDAO.saveApply(merchantSn, payway, acquirer);
        kafkaTemplate.send(OPEN_ONLINE_PAYMENT_TOPIC, new OpenOnlinePaymentApplyStatusChange(String.valueOf(openOnlinePaymentApplyDO.getId()), merchantSn, payway, OnlinePaymentConstant.ApplyStatus.PENDING, OnlinePaymentConstant.ApplyStatus.APPLYING, null));
    }


    public void reContractAndSubmitAuth(OpenOnlinePaymentApplyDO apply) {
        String subMchId = doReContract(apply.getMerchantSn(), apply.getPayway());
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = savePayMerchantId(apply, subMchId);
        SystemResponse systemResponse = systemService.createAuthTask(CollectionUtil.hashMap("reason", "online", "subMchId", subMchId, "isCheckSettlementId", false));
        if (systemResponse.getResult()) {
            updateApply(apply, OnlinePaymentConstant.ApplyStatus.APPLYING, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH, null, onlinePaymentApplyExtraBO);
        } else {
            updateApply(apply, OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.SUBMIT_AUTH_FAIL, "提交授权申请失败:" + systemResponse.getDesc(), onlinePaymentApplyExtraBO);
        }
    }

    public Boolean queryAuthStatusAndSetDefault(OpenOnlinePaymentApplyDO apply) {
        String id = String.valueOf(apply.getId());
        String cacheKey = "queryAuthStatusAndSetDefault:"+ id;
        // fix CUA-10673
        if (!redisLock.lock(cacheKey, cacheKey, ProviderUtil.REDIS_KEY_EXPIRE_SECONDS)) {
            return false;
        }
        try {
            String payMerchantId = apply.getPayMerchantId();
            Optional<MerchantProviderParamsDO> param = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(payMerchantId);
            if (!param.isPresent() || param.get().getDeleted() == Deleted.DELETED.getValue()) {
                throw new ContractBizException("交易参数不存在");
            }
            MerchantProviderParamsDO paramsDO = param.get();
            Boolean authStatus = queryAuthStatus(paramsDO);
            if (!authStatus) {
                if (ChronoUnit.DAYS.between(apply.getCreateAt().toInstant(), Instant.now()) >= OnlinePaymentConstant.AUTH_TIMEOUT_DAY) {
                    updateApply(apply, OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.QUERY_AUTH_FAIL, "认证失败:7天内未完成认证", null);
                    return false;
                }
                openOnlinePaymentApplyDAO.delayApply(apply.getId(), 2);
                return false;
            }
            // 支付宝这里多一个步骤，要去审核
            if (apply.isAliApply()) {
                updateApply(apply, OnlinePaymentConstant.ApplyStatus.APPLYING, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT, null, null);
                return true;
            }
            try {
                // 判断是否有正在切换收单机构的任务
                if (Objects.nonNull(acquirerChangeDao.getLatestUnFinishedApply(apply.getMerchantSn()))) {
                    throw new ContractBizException("当前商户正在切换收单机构");
                }
                // 当前收单机构和线上收款子商户号的收单机构是否一致
                String acquirer = composeAcquirerBiz.getMerchantAcquirer(apply.getMerchantSn());
                ContractRule contractRule = ruleContext.getContractRule(paramsDO.getContractRule());
                if (Objects.isNull(contractRule) || !Objects.equals(acquirer, contractRule.getAcquirer())) {
                    throw new ContractBizException("当前收单机构与线上收款子商户号收单机构不一致");
                }
                merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(paramsDO.getId(), null, "线上收款参数配置", subBizParamsBiz.getOnlinePaymentTradeAppId());
                updateApply(apply, OnlinePaymentConstant.ApplyStatus.SUCCESS, OnlinePaymentConstant.ApplyProcessStatus.SUCCESS, "开通成功", null);
                return true;
            } catch (Exception exception) {
                updateApply(apply, OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.CHANGE_PARAMS_FAIL, "切换交易参数失败:" + exception.getMessage(), null);
                log.error("商户开通线上收款异常:切换交易参数失败 {} ", apply.getMerchantSn(), exception);
                return false;
            }
        } finally {
            redisLock.unlock(cacheKey, cacheKey);
        }
    }

    public void importAliAuditResult(BatchImportAliOnlineMerchantsExcel excel) {
        if (excel.isAuditSuccess()) {
            setAliOnlinePayMerchantId(excel.getMerchantSn());
        } else {
            Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApply(excel.getMerchantSn(), PaywayEnum.ALIPAY.getValue());
            if (!openOnlinePaymentApplyDO.isPresent() || !openOnlinePaymentApplyDO.get().isWaitForAudit()) {
                throw new ContractBizException("支付宝申请单不存在");
            }
            updateApply(openOnlinePaymentApplyDO.get(), OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.AUDIT_FAIL, excel.getRejectReason(), null);
        }
    }


    public void setAliOnlinePayMerchantId(String merchantSn) {
        Optional<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDO = openOnlinePaymentApplyDAO.queryLatestOpenOnlinePaymentApply(merchantSn, PaywayEnum.ALIPAY.getValue());
        if (!openOnlinePaymentApplyDO.isPresent() || !openOnlinePaymentApplyDO.get().isWaitForAudit()) {
            throw new ContractBizException("支付宝申请单不存在");
        }
        OpenOnlinePaymentApplyDO apply = openOnlinePaymentApplyDO.get();
        // 根据审核单中的子商户号查询交易参数
        String payMerchantId = apply.getPayMerchantId();
        Optional<MerchantProviderParamsDO> param = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(payMerchantId);
        if (!param.isPresent() || param.get().getDeleted() == Deleted.DELETED.getValue()) {
            throw new ContractBizException("交易参数不存在");
        }
        try {
            if (Objects.nonNull(acquirerChangeDao.getLatestUnFinishedApply(apply.getMerchantSn()))) {
                throw new ContractBizException("当前商户正在切换收单机构");
            }
            // 当前收单机构和线上收款子商户号的收单机构是否一致
            String acquirer = composeAcquirerBiz.getMerchantAcquirer(apply.getMerchantSn());
            ContractRule contractRule = ruleContext.getContractRule(param.get().getContractRule());
            if (Objects.isNull(contractRule) || !Objects.equals(acquirer, contractRule.getAcquirer())) {
                throw new ContractBizException("当前收单机构与线上收款子商户号收单机构不一致");
            }
            merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(param.get().getId(), null, "线上收款参数配置", subBizParamsBiz.getOnlinePaymentTradeAppId());
            updateApply(apply, OnlinePaymentConstant.ApplyStatus.SUCCESS, OnlinePaymentConstant.ApplyProcessStatus.SUCCESS, "开通成功", null);
        } catch (Exception exception) {
            updateApply(apply, OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.CHANGE_PARAMS_FAIL, "切换交易参数失败:" + exception.getMessage(), null);
            log.error("商户开通线上收款异常:切换交易参数失败 {} ", apply.getMerchantSn(), exception);
            throw exception;
        }
    }

    public void setFailWhenChangeAcquirer(String merchantSn) {
        List<OpenOnlinePaymentApplyDO> openOnlinePaymentApplyDOS = openOnlinePaymentApplyDAO.queryProcessingAppliesByMerchantSn(merchantSn);
        if (WosaiCollectionUtils.isEmpty(openOnlinePaymentApplyDOS)) {
            return;
        }
        for (OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO : openOnlinePaymentApplyDOS) {
            updateApply(openOnlinePaymentApplyDO, OnlinePaymentConstant.ApplyStatus.FAIL, OnlinePaymentConstant.ApplyProcessStatus.CHANGE_PARAMS_FAIL, "开通失败:收单机构发生变更", null);
        }
    }

    public void processApplyException(OpenOnlinePaymentApplyDO apply, Integer processStatus, Exception e) {
        String result = e.getMessage();
        openOnlinePaymentApplyDAO.updateApply(apply.getId(), OnlinePaymentConstant.ApplyStatus.FAIL, processStatus, result, null, null);
        kafkaTemplate.send(OPEN_ONLINE_PAYMENT_TOPIC, new OpenOnlinePaymentApplyStatusChange(String.valueOf(apply.getId()), apply.getMerchantSn(), apply.getPayway(), apply.getStatus(), OnlinePaymentConstant.ApplyStatus.FAIL, result));
    }

    public Optional<OpenOnlinePaymentApplyDO> queryCurrentAcquirerSuccessApply(String merchantSn, Integer payway) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        return openOnlinePaymentApplyDAO.querySuccessApplyByAcquirer(merchantSn, payway, contractStatus.getAcquirer());
    }

    /**
     * 关闭之后重新开通，将参数再去设置一遍
     * @param currentAcquirerSuccessApply
     * @return
     */
    public OnlinePaymentOpenResp resetOnlinePaymentParams(OpenOnlinePaymentApplyDO currentAcquirerSuccessApply) {
        // 已经在生效中，就直接返回
        if (currentAcquirerSuccessApply.isEffect()) {
            return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.SUCCESS);
        }
        String payMerchantId = currentAcquirerSuccessApply.getPayMerchantId();
        Optional<MerchantProviderParamsDO> merchantProviderParams = merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId(payMerchantId);
        if (!merchantProviderParams.isPresent()) {
            return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.FAIL).setFailMsg("子商户号对应的交易参数不存在");
        }
        //1.切参数
        merchantProviderParamsService.setDefaultMerchantProviderParamsByTradeApp(merchantProviderParams.get().getId(), null, "线上收款参数配置", subBizParamsBiz.getOnlinePaymentTradeAppId());
        //2.如果套餐和费率和 appId_subBiz配置的不一致，则去设置一下
        OnlinePaymentApplyComboDetailBO onlineComboDetail = currentAcquirerSuccessApply.getOnlinePaymentComboDetail();
        if (Objects.nonNull(onlineComboDetail)) {
            Map appIdSubBiz = applicationApolloConfig.getAppIdSubBiz();
            String configComboId = BeanUtil.getPropString(appIdSubBiz, "onlinePayment.mappingComboId");
            String configFeeRate = BeanUtil.getPropString(appIdSubBiz, "onlinePayment.feeRate." + currentAcquirerSuccessApply.getPayway());
            if (!Objects.equals(onlineComboDetail.getTradeComboId(), configComboId) || !Objects.equals(onlineComboDetail.getFeeRate(), configFeeRate)) {
                ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                        .setMerchantSn(currentAcquirerSuccessApply.getMerchantSn())
                        .setAuditSn(String.format("业务管理发起:重新开通%s", BeanUtil.getPropString(appIdSubBiz, "onlinePayment.mappingTradeName")))
                        .setTradeComboId(Long.valueOf(onlineComboDetail.getTradeComboId()))
                        .setApplyPartialPayway(true)
                        .setApplyFeeRateMap(CollectionUtil.hashMap(
                                String.valueOf(currentAcquirerSuccessApply.getPayway()), onlineComboDetail.getFeeRate()
                        ));
                feeRateService.applyFeeRateOne(applyFeeRateRequest);
            }
        }
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(currentAcquirerSuccessApply.getExtra(), OnlinePaymentApplyExtraBO.class);
        onlinePaymentApplyExtraBO.setEffect(true);
        openOnlinePaymentApplyDAO.updateApplyExtra(currentAcquirerSuccessApply.getId(), onlinePaymentApplyExtraBO);
        return new OnlinePaymentOpenResp().setStatus(OnlinePaymentConstant.ApplyStatus.SUCCESS).setReOpen(true);
    }

    private String doReContract(String merchantSn, Integer payway) {
        // 先查询当前通道下是否已经有了线上的子商户号，如果已经存在则不重新报备
        Map<String, Object> paramContext = paramContextBiz.getParamContextByMerchantSn(merchantSn, new ContractEvent().setEvent_type(0));
        int licenseType = BeanUtil.getPropInt(paramContext, "merchantBusinessLicense.type");
        boolean forceMicro = checkForceMicro(merchantSn, licenseType, payway);

        String reContractWxRule = getReContractRule(merchantSn, paramContext, forceMicro, payway);
        ContractRule contractRule = ruleContext.getContractRule(reContractWxRule);
        Optional<MerchantProviderParamsDO> onlineParams = merchantProviderParamsDAO.getOnlineMerchantProviderParams(merchantSn, reContractWxRule, payway);
        if (onlineParams.isPresent()) {
            return onlineParams.get().getPayMerchantId();
        }

        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            String merchantName = BeanUtil.getPropString(paramContext, "merchant.name");
            paramContext.put(ParamContextBiz.KEY_WEIXIN_SUBDEV_CONFIG_STATUS, 0);
            // 指定微信子商户号用途 结算ID
            paramContext.put("wxUseType", WxUseType.ONLINE_PAYMENT.getCode());
            paramContext.put(ContractApplicationService.KEY_CUSTOM_FIELDS, CollectionUtil.hashMap(
                    "business", wechatAuthBiz.getOnlineSettlementId(licenseType, merchantName, forceMicro)
            ));
        }
        paramContext.put("forceMicro", forceMicro);
        paramContext.put("type", "1");

        Map contractRes = ruleBiz.contractByRule(merchantSn,
                contractRule,
                paramContext, false);
        String resString = (String) contractRes.get(RuleBiz.CONTRACT_RES_KEY);
        if (!StringUtils.isEmpty(resString)) {
            throw new ContractBizException(resString);
        }
        String paramId = (String) contractRes.get(RuleBiz.CONTRACT_PARAM_KEY);
        Optional<MerchantProviderParamsDO> merchantProviderParamsDO = merchantProviderParamsDAO.getMerchantProviderParamsById(paramId);
        if (!merchantProviderParamsDO.isPresent()) {
            throw new ContractBizException("未找到线上收款子商户号");
        }
        MerchantProviderParamsDO paramsDO = merchantProviderParamsDO.get();
        // 如果是支付宝，则将WxUseType修改为线上收款，微信的在merchant-contract里面就做了
        if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            paramsDO.setWxUseType(WxUseType.SCENARIO_PAYMENT.getCode());
            merchantProviderParamsDAO.updateByPrimaryKey(paramsDO);
        }
        return paramsDO.getPayMerchantId();
    }

    /**
     * 如果有营业执照的商户当前在用的微信子商户号是小微类型的，那报备线上收款的子商户号也用小微的
     * @param merchantSn 商户号
     * @param licenseType 营业执照类型
     * @return 是否强制小微报备
     */
    private boolean checkForceMicro(String merchantSn, int licenseType, int payway) {
        Optional<MerchantProviderParamsDO> params = merchantProviderParamsDAO.getInUseProviderParams(merchantSn, payway);
        if (licenseType > BusinessLicenseTypeEnum.MICRO.getValue() && params.isPresent() && WosaiStringUtils.isNotEmpty(params.get().getMerchantName()) && params.get().getMerchantName().startsWith("商户_")) {
            return true;
        }
        return false;
    }

    private String getReContractRule(String merchantSn, Map<String, Object> paramContext, boolean forceMicro, Integer payway) {
        String merchantAcquirer = composeAcquirerBiz.getMerchantAcquirer(merchantSn);
        IAcquirerBiz acquirerBiz = composeAcquirerBiz.getAcquirerBiz(merchantAcquirer);
        if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            if (merchantAcquirer.contains(McConstant.ACQUIRER_LKL)) {
                if (BeanUtil.getPropInt(paramContext, "merchantBusinessLicense.type") == BusinessLicenseTypeEnum.MICRO.getValue() || forceMicro) {
                    return "lkl-1033-3-270860769";
                }
            }
            return acquirerBiz.getNormalWxRule();
        }
        if (merchantAcquirer.contains(McConstant.ACQUIRER_LKL)) {
            return ContractRuleConstants.LKL_ORG_NORMAL_ALI_RULE;
        }
        return ContractRuleConstants.HAIKE_NORMAL_ALI_RULE;
    }

    private void updateApply(OpenOnlinePaymentApplyDO apply, Integer status, Integer processStatus, String result, OnlinePaymentApplyExtraBO extra) {
        List<OnlinePaymentApplyProcessBO> processList = apply.processList();
        if (processStatus.equals(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH)) {
            processList.forEach(onlinePaymentApplyProcessBO -> {
                if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                    onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
                }
            });
        }
        if (processStatus.equals(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT)) {
            processList.forEach(onlinePaymentApplyProcessBO -> {
                if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                    onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
                }
                if (OnlinePaymentApplyProcessBO.AUTHING.equals(onlinePaymentApplyProcessBO.getStage())) {
                    onlinePaymentApplyProcessBO.setFinish(true);
                }
            });
        }
        if (processStatus.equals(OnlinePaymentConstant.ApplyProcessStatus.SUCCESS)) {
            processList.forEach(onlinePaymentApplyProcessBO -> {
                if (OnlinePaymentApplyProcessBO.CONTRACTING.equals(onlinePaymentApplyProcessBO.getStage())) {
                    onlinePaymentApplyProcessBO.setFinish(true).setMessage("报备成功，请继续完成认证");
                } else {
                    onlinePaymentApplyProcessBO.setFinish(true);
                }
            });
        }
        openOnlinePaymentApplyDAO.updateApply(apply.getId(), status, processStatus, result, processList, extra);
        // 如果status 没有发生变更 就先不发消息出去
        if (apply.getStatus().equals(status)) {
            return;
        }
        kafkaTemplate.send(OPEN_ONLINE_PAYMENT_TOPIC, new OpenOnlinePaymentApplyStatusChange(String.valueOf(apply.getId()), apply.getMerchantSn(), apply.getPayway(), apply.getStatus(), status, result));
    }

    private Boolean queryAuthStatus(MerchantProviderParamsDO paramsDO) {
        if (PaywayEnum.WEIXIN.getValue().equals(paramsDO.getPayway())) {
            return wechatAuthBiz.getAuthStatus(paramsDO, paramsDO.getProvider());
        }
        if (PaywayEnum.ALIPAY.getValue().equals(paramsDO.getPayway())) {
            return alipayAuthBiz.queryAuthByMchId(paramsDO);
        }
        throw new ContractBizException("非支付宝微信子商户号不支持查询");
    }

    private OnlinePaymentApplyExtraBO savePayMerchantId(OpenOnlinePaymentApplyDO apply, String subMchId) {
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(apply.getExtra(), OnlinePaymentApplyExtraBO.class);
        if (apply.isWeixinApply()) {
            if (Objects.isNull(onlinePaymentApplyExtraBO)) {
                onlinePaymentApplyExtraBO = new OnlinePaymentApplyExtraBO();
            }
            OnlinePaymentApplyWeixinBO weixin = onlinePaymentApplyExtraBO.getWeixin();
            if (Objects.isNull(weixin)) {
                weixin = new OnlinePaymentApplyWeixinBO();
                onlinePaymentApplyExtraBO.setWeixin(weixin);
            }
            weixin.setPayMerchantId(subMchId);
        }
        if (apply.isAliApply()) {
            if (Objects.isNull(onlinePaymentApplyExtraBO)) {
                onlinePaymentApplyExtraBO = new OnlinePaymentApplyExtraBO();
            }
            OnlinePaymentApplyAliBO ali = onlinePaymentApplyExtraBO.getAli();
            if (Objects.isNull(ali)) {
                ali = new OnlinePaymentApplyAliBO();
                onlinePaymentApplyExtraBO.setAli(ali);
            }
            ali.setPayMerchantId(subMchId);
        }
        return onlinePaymentApplyExtraBO;
    }

    public void saveFeeRateAndInvalidApply(OpenOnlinePaymentApplyDO apply, Tuple2<ListMchFeeRateResult, ListMchFeeRateResult> tradeAppFeeRate) {
        ListMchFeeRateResult onlineFeeRate = tradeAppFeeRate.get_1();
        ListMchFeeRateResult crossCityFeeRate = tradeAppFeeRate.get_2();
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = JSON.parseObject(apply.getExtra(), OnlinePaymentApplyExtraBO.class);
        OnlinePaymentApplyComboBO onlinePaymentApplyCombo =  new OnlinePaymentApplyComboBO();
        if (Objects.nonNull(onlineFeeRate)) {
            onlinePaymentApplyCombo.setOnline(new OnlinePaymentApplyComboDetailBO()
                    .setTradeComboId(String.valueOf(onlineFeeRate.getTradeComboId()))
                    .setFeeRate(onlineFeeRate.getBscFeeRate()));
        }
        if (Objects.nonNull(crossCityFeeRate)) {
            onlinePaymentApplyCombo.setCrossCity(new OnlinePaymentApplyComboDetailBO()
                    .setTradeComboId(String.valueOf(crossCityFeeRate.getTradeComboId()))
                    .setFeeRate(crossCityFeeRate.getBscFeeRate()));
        }
        onlinePaymentApplyExtraBO.setCombo(onlinePaymentApplyCombo);
        onlinePaymentApplyExtraBO.setEffect(false);
        openOnlinePaymentApplyDAO.updateApplyExtra(apply.getId(), onlinePaymentApplyExtraBO);
    }
}
