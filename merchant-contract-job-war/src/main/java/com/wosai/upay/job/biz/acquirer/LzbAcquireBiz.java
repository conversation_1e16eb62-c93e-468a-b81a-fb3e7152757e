package com.wosai.upay.job.biz.acquirer;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ContractSubTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskReq;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component("lzb-biz")
public class LzbAcquireBiz implements IAcquirerBiz {
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;
    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private RuleContext ruleContext;

    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_LZB;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return null;
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.LZB_NORMAL_WEIXIN_RULE;
    }


    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        final String merchantSn = providerParams.getMerchant_sn();
        final List<ContractSubTask> subTasks = contractSubTaskMapper.findTasksByMerchantAndPayway(
                new ContractSubTaskReq().setPayway(PaywayEnum.ACQUIRER.getValue()).setMerchantSn(merchantSn)
                        .setTaskType(ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue())
                        .setStatus(TaskStatus.SUCCESS.getVal())
        );
        List<ContractSubTask> lzb = subTasks.stream().filter(subTask -> Objects.equals(subTask.getChannel(), ChannelEnum.LZB_BANK.getValue()))
                .limit(1)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(lzb)) {
            throw new CommonPubBizException("找不到线上记录");
        }
        Map requestBody = JSON.parseObject(lzb.get(0).getRequest_body());
        Map merchantInfo = MapUtils.getMap(requestBody, "merchantInfo");
        String name = (String) merchantInfo.get("mchtName");
        return new AlipayMchInfo().setName(name).setSub_merchant_id(providerParams.getPay_merchant_id());
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_LZB.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.LZB.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }

    @Override
    public void updateClearanceProvider(String merchantId) {

    }
}
