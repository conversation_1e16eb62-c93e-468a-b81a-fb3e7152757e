package com.wosai.upay.job.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.config.OssAK;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.List;

/**
 * @Description: excel编写util
 * <AUTHOR>
 * @Date 2021/10/27 3:37 下午
 **/
@Slf4j
@Component
public class ExcelUtil {

    public static final String ENDPOINT_URL = "http://oss-cn-hangzhou.aliyuncs.com/";
    public static String STATICS_BUCKET_NAME = "wosai-images";

    private static final OSSClient client = OssAK.buildOSSClient(ENDPOINT_URL);

    public static byte[] writeExcel(String sheetName, List<String[]> content) {
        XSSFWorkbook wb = new XSSFWorkbook();
        XSSFSheet sheet = wb.createSheet(sheetName);
        for (int i = 0; i < content.size(); i++) {
            XSSFRow row = sheet.createRow(i);
            String[] strings = content.get(i);
            for (int j = 0; j < strings.length; j++) {
                XSSFCell cell = row.createCell(j);
                cell.setCellValue(strings[j]);
            }
        }
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            wb.write(out);
            return out.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public <T> List<T> getExcelInfoList(String url, T t) {
        ImportParams params = new ImportParams();
        InputStream inputStream = null;
        ExcelImportResult<T> res;
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                inputStream = connection.getInputStream();
            }
            res = ExcelImportUtil.importExcelMore(inputStream, t.getClass(), params);
        } catch (Exception e) {
            log.error("获取网络图片出现异常{}", e);
            throw new CommonPubBizException("获取网络文件出现异常，文件路径为：" + url);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流异常", e);
            }
        }
        return res.getList();
    }


    public <T> String uploadToOss(List<T> sources, String baseDir) {
        File file = null;
        InputStream inputStream = null;
        try {
            //上传结果
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            final Class<?> aClass = Class.forName(sources.get(0).getClass().getName());
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, aClass, sources);
            file = File.createTempFile(System.currentTimeMillis() + "", ".xlsx");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(file.length());
            String key = baseDir + file.getName();
            inputStream = Files.newInputStream(file.toPath());
            client.putObject(STATICS_BUCKET_NAME, key, inputStream, objectMeta);
            return ENDPOINT_URL + STATICS_BUCKET_NAME + "/" + key;
        } catch (Exception e) {
            log.error(" uploadToOss error", e);
        } finally {
            FileUtils.deleteQuietly(file);
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.error("关闭流异常", e);
            }
        }
        return null;
    }

    public void deleteTempExcel(String lastAttachmentUrl, String baseDir) {
        final int index = lastAttachmentUrl.indexOf(baseDir);
        final String key = lastAttachmentUrl.substring(index);
        client.deleteObject(STATICS_BUCKET_NAME, key);
    }
}