package com.wosai.upay.job.refactor.model.bo;

import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 内部调度任务BO
 *
 * <AUTHOR>
 * @date 2024/6/17 15:09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InternalScheduleTaskBO {

    private InternalScheduleMainTaskDO mainTaskDO;

    private List<InternalScheduleSubTaskDO> subTaskDOList;
}
