package com.wosai.upay.job.util;

import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.model.ContractEvent;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author: jerry
 * @date: 2019/4/4 06:40
 * @Description:判断哪些字段的变更
 */
public class ProviderUtil {

    public static final String LKL_PROVIDER_CHANNEL = "lkl";
    public static final String LKL_V3_PROVIDER_CHANNEL = "lklV3";
    public static final String LKL_ORG_PROVIDER_CHANNEL = "lkl_org";
    public static final String HAIKE_CHANNEL = "haike";
    public static final String FUYOU_CHANNEL = "fuyou";
    public static final String GUOTONG_CHANNEL = "guotong";
    public static final String UNION_PROVIDER_CHANNEL = "union";
    public static final String UNION_WM_PROVIDER_CHANNEL = "union_wm";
    public static final String NUCC_PROVIDER_CHANNEL = "nucc";
    public static final String UMS_PROVIDER_CHANNEL = "ums";

    public static final String PAY_FOR_CHANNEL = "pay_for";
    public static final String LKL_CALLBACK_CHANNEL = "lkl_callback";
    public static final String FUYOU_CALLBACK_CHANNEL = "fuyou_callback";

    public static final String WECHAT_AUTH = "wechat_auth";
    public static final String WEIXIN_DIRECT = "weixin_direct";
    public static final String ALI_DIRECT = "ali_direct";
    public static final String UNION_OPEN_CHANNEL = "union_open";
    public static final String TONG_LIAN_CHANNEL = "tonglian";
    public static final String TONGLIAN_V2_CHANNEL = "tonglianV2";
    public static final String SHOUQIANBA_CHANNEL = "shouqianba";
    public static final String CGB_CHANNEL = "cgb";
    public static final String PSBC_CHANNEL = "psbc";
    public static final String PSBC_SX_CHANNEL = "psbcsx";
    public static final String CCB_CHANNEL = "ccb";
    public static final String ICBC_CHANNEL = "icbc";
    public static final String HXB_CHANNEL = "hxb";
    public static final String PAB_CHANNEL = "pab";
    public static final String LZB_CHANNEL = "lzb";
    public static final String ZJTLCB_CHANNEL = "zjtlcb";
    public static final String UMB_CHANNEL = "umb";
    public static final String COMMON_CHANNEL = "common";
    public static final String CMBC_CHANNEL = "cmbc";

    public static final String CONTRACT_TYPE_INSERT = "新增商户入网";
    public static final String CONTRACT_TYPE_UPDATE_BANK_ACCOUNT = "结算账户变更";
    public static final String CONTRACT_TYPE_UPDATE_FEERATE = "更新商户费率";
    public static final String CONTRACT_TYPE_UPDATE_BASIC = "更新商户基本信息";
    public static final String CONTRACT_TYPE_MERCHANT_STATUS = "更新商户状态";
    public static final String CONTRACT_TYPE_UPDATE_BUSINESS_LICENSE = "更新营业执照";
    public static final String MERCHANT_BUSINESS_DATA_CHANGE = "商户信息变更";
    public static final String CONTRACT_TYPE_ATTACH_UPLOAD = "附件上传";
    public static final String CONTRACT_TYPE_UNKNOWN_STATUS = "不明状态";
    public static final String CONTRACT_TYPE_WEIXIN_MCH_POOL = "微信商户池报备";
    public static final String CONTRACT_TYPE_BATCH = "批量任务生成";
    public static final String CONTRACT_RECONTRACT = "重新报备";
    public static final String CONTRACT_TYPE_AUTH = "微信商家认证";
    public static final String CONTRACT_TYPE_ALIPAY_AUTH = "支付宝商家认证";
    public static final String CONTRACT_TYPE_ADDTERM = "增网增终";
    public static final String WEIXIN_DIRECT_ONLINE = "微信直连线上";
    public static final String WEIXIN_DIRECT_OFFLINE = "微信直连线下";
    public static final String ALI_DIRECT_APPLY = "支付宝直连";
    public static final Integer ALY_PAY_WAY = 2;
    public static final Integer WEIXIN_PAY_WAY = 3;
    public static final Integer BEST_WAY = 18;
    public static final Integer UNION_OPEN_WAY = 17;
    //通联报备时候存的payway为0 + 拉卡拉商户报备结果查询的subtask
    public static final Integer PROVIDER_PAY_WAY = 0;


    /**
     * 有些业务需要业务方传服务标示
     **/
    public static final String PLAT_FORM = "merchant-contract-job";

    /**
     * 基本信息
     */
    public static final Integer SUB_TASK_TASK_TYPE_BASIC_INFORMATION = 0;

    /**
     * 商户状态
     */
    public static final Integer SUB_TASK_TASK_TYPE_STATUS_UPDATE = 1;

    /**
     * 结算账户变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS = 2;

    /**
     * 商户费率变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_MERCHANT_FEERATE = 3;

    /**
     * 卡变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_CARD_UPDATE = 4;

    /**
     * 进件
     */
    public static final Integer SUB_TASK_TASK_TYPE_CONTRACT = 5;
    /**
     * 更新
     */
    public static final Integer SUB_TASK_TASK_TYPE_UPDATE = 6;

    /**
     * 微信商户池报备
     */
    @Deprecated
    public static final Integer SUB_TASK_TASK_TYPE_WEIXIN_MCH_POOL = 7;
    /**
     * 微信实名失败重新提交
     */
    public static final Integer EVENT_TYPE_RE_AUTH = 8;
    /**
     * crm变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_CRM_BOTH = 9; //d

    /**
     * lklv3特有
     */

    /**
     * 增网
     */
    public static final Integer SUB_TASK_TASK_TYPE_ADD_SHOP = 10;

    /**
     * 增终
     */
    public static final Integer SUB_TASK_TASK_TYPE_ADD_TERM = 11;

    /**
     * 解绑终端
     */
    public static final Integer SUB_TASK_TASK_TYPE_UNBIND_TERM = 12;
    /**
     * 营业执照变更
     */
    public static final Integer SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE = 13;

    /**
     * 银联商户报备查询
     */
    public static final Integer SUB_TASK_TASK_TYPE_UNION_MER_QUERY = 14;
    /**
     * 品牌新增门店
     */
    public static final Integer SUB_TASK_TASK_TYPE_BRAND_ADD_SHOP = 17;

    /**
     * 附件上传
     */
    public static final Integer SUB_TASK_TASK_TYPE_FILE_SUPPLY = 99;

    /**
     * 附件上传（不用）
     */
    public static final Integer SUB_TASK_TASK_TYPE_ATTACHMENT_UPLOADING = 99;


    private static final List MERCHANT_STATUS = Arrays.asList("status");

    private static final List PSBC_MERCHANT_STANDARD = Arrays.asList(Merchant.BUSINESS_NAME,
            Merchant.PROVINCE,
            Merchant.CITY,
            Merchant.DISTRICT,
            Merchant.STREET_ADDRESS,
            Merchant.CONTACT_CELLPHONE);

    /**
     * 已经处理的子任务但是需要等待回调或者主动查询
     */
    public final static List<String> CHANNELS = Lists.newArrayList(ProviderUtil.LKL_PROVIDER_CHANNEL,
            ProviderUtil.PSBC_CHANNEL,
            ProviderUtil.UMS_PROVIDER_CHANNEL,
            ProviderUtil.CGB_CHANNEL,
            ProviderUtil.CCB_CHANNEL,
            ProviderUtil.LKL_V3_PROVIDER_CHANNEL,
            ProviderUtil.HXB_CHANNEL,
            ProviderUtil.FUYOU_CHANNEL,
            ProviderUtil.GUOTONG_CHANNEL,
            ProviderUtil.PAB_CHANNEL,
            ProviderUtil.LZB_CHANNEL,
            ProviderUtil.UMB_CHANNEL,
            ProviderUtil.CMBC_CHANNEL
    );


    public static final long REDIS_KEY_EXPIRE_SECONDS = 5 * 60;

    public static final long REDIS_KEY_EXPIRE_TEN_MINUTES = 10 * 60;

    public static final long REDIS_KEY_EXPIRE_THIRTY_MINUTES = 30 * 60;

    public static final long REDIS_KEY_EXPIRE_ONE_DAY = 24 * 60 * 60;


    private static final List BASIC_ALIPAY_UPDATE = Arrays.asList("name", "business_name", "industry");

    private static final List ACCOUNT_ALIPAY_UPDATE = Arrays.asList("holder", "number", "identity", "legal_person_id_number");

    private static final List BASIC_WEIXIN_UPDATE = Arrays.asList("business_name", "customer_phone");

    private static final List BASIC_WEIXIN_CONTRACT = Arrays.asList("name");

    public static boolean hasStatus(List<String> fields) {
        return existsOne(fields, MERCHANT_STATUS);
    }

    public static boolean triggerPsbcUpdate(List<String> fields) {
        return existsOne(fields, PSBC_MERCHANT_STANDARD);
    }


    public static boolean getPayWayUpdate(Integer payway, int eventType, List<String> fields) {
        if (PaywayEnum.ALIPAY.getValue().equals(payway)) {
            return getAlyPayUpdate(eventType, fields);
        } else if (PaywayEnum.WEIXIN.getValue().equals(payway)) {
            return getWeixinPayUpdate(eventType, fields);
        } else {
            return false;
        }
    }


    public static boolean getAlyPayUpdate(int eventType, List<String> fields) {
        //支付宝目前基本信息变更
        if (eventType == ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION || eventType == ContractEvent.OPT_TYPE_NET_CRM_UPDATE) {
            if (existsOne(BASIC_ALIPAY_UPDATE, fields)) {
                return true;
            }
            // 结算账户变更不需要走更新
            if (existsOne(ACCOUNT_ALIPAY_UPDATE, fields)) {
                return false;
            }
            return false;
        }
        return false;
    }

    public static boolean getWeixinPayUpdate(int eventType, List<String> fields) {
        //微信目前基本信息变更走更新
        if (eventType == ContractEvent.OPT_TYPE_MERCHANT_BASIC_INFORMATION || eventType == ContractEvent.OPT_TYPE_NET_CRM_UPDATE) {
            return existsOne(BASIC_WEIXIN_UPDATE, fields);
        }
        return false;
    }

    public static boolean existsOne(List<String> source, List<String> target) {
        for (String sr : source) {
            if (target.contains(sr)) {
                return true;
            }
        }
        return false;
    }


    public static boolean exsistWeixinContractMore(int type, List<String> fields) {
        if (type == 0) {
            for (String field : fields) {
                if (MerchantBankAccount.HOLDER.equals(field)) {
                    return true;
                }
            }
        } else {
            for (String field : fields) {
                if (MerchantBusinessLicence.NAME.equals(field)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String switchAcquirerToRuleGroupId(String acquirer) {
        return Objects.equals(acquirer, AcquirerTypeEnum.LKL.getValue()) ? McConstant.RULE_GROUP_LKL : acquirer;
    }

    public static String switchRuleGroupIdToAcquirer(String ruleGroupId) {
        if (Objects.equals(ruleGroupId, McConstant.RULE_GROUP_LKL)) {
            return AcquirerTypeEnum.LKL.getValue();
        }
        if (Objects.equals(ruleGroupId, McConstant.RULE_GROUP_LKLORG)) {
            return AcquirerTypeEnum.LKL_V3.getValue();
        }
        return ruleGroupId;
    }

    public static String externalAcquirer(String internalAcquirer) {
        return Objects.equals(AcquirerTypeEnum.LKL_V3.getValue(), internalAcquirer) ? AcquirerTypeEnum.LKL.getValue() : internalAcquirer;
    }
}
