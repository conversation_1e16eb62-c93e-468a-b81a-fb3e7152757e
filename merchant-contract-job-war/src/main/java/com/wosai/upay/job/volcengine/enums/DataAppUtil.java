package com.wosai.upay.job.volcengine.enums;

import com.wosai.upay.job.util.SpringBeanUtils;

/**
 * 数据部应用枚举类
 *
 * @link https://confluence.wosai-inc.com/pages/viewpage.action?pageId=611647934
 */
public enum DataAppUtil {

    B_MERCHANT(DataAppEnum.B_MERCHANT),
    B_USER(DataAppEnum.B_USER),
    ;

    private final DataAppEnum dataAppEnum;

    DataAppUtil(DataAppEnum dataAppEnum) {
        this.dataAppEnum = dataAppEnum;
    }

    public String getCurrentAppId() {
        return SpringBeanUtils.isProd() ? dataAppEnum.getAppId() : dataAppEnum.getAppTestId();
    }
}
