package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.model.MerchantUserNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.dto.MerchantUserSimpleInfo;
import com.wosai.app.service.MerchantUserService;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.core.model.User;
import com.wosai.sales.core.service.UserService;
import com.wosai.service.SystemService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.Constants.CcbConstant;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.bankDirect.CcbDirectBiz;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.CcbMerchantFeeRateMapper;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.dto.HandleQueryStatusResp;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbAuditResp;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbMerchantInfoResp;
import com.wosai.upay.merchant.contract.model.ccb.response.CcbRateList;
import com.wosai.upay.merchant.contract.model.provider.CcbParam;
import com.wosai.upay.merchant.contract.service.CcbService;
import entity.common.PageListResult;
import entity.request.CustomerRelationOriginReq;
import entity.response.CustomerRelationOriginResp;
import facade.ICustomerRelationFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.wosai.upay.job.providers.PsbcProvider.replaceHttp;

/**
 * <AUTHOR>
 * @date 2021/8/6
 */
@Slf4j
@Component(ProviderUtil.CCB_CHANNEL)
public class CcbProvider extends AbstractProvider {

    @Autowired
    private CcbService ccbService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantUserService merchantUserService;

    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    @Autowired
    private ICustomerRelationFacade iCustomerRelationFacade;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private RuleContext ruleContext;

    @Lazy
    @Autowired
    private CcbDirectBiz ccbDirectBiz;

    @Autowired
    private WechatQrCodeUtils wechatQrCodeUtils;

    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    private CcbMerchantFeeRateMapper feeRateMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${ccb_dev_code}")
    public String ccbDevCode;

    @Value("${ccb_sign_template_code}")
    private String signTemplateCode;

    @Value("${ccb_crm_template_code}")
    public String ccbCrmTemplateCode;

    @Value("${ccb_crm_dev_code}")
    public String ccbCrmDevCode;

    @Value("${ccb_app_template_code}")
    public String ccbAppTemplateCode;

    @Value("${ccb_app_dev_code}")
    public String ccbAppDevCode;

    @Value("${indirect_crm_customer_relation}")
    public String indirectCrmCustomerRelation;

    @Value("${kabu_organization_path}")
    public String kabuOrganizationPath;

    @Value("${ccb_tag}")
    private String ccbTag;

    @Autowired
    private ChatBotUtil chatBotUtil;

    @Autowired
    private UserService userService;
    @Autowired
    SystemService systemService;

    private static final String CCB_VIEW_PROCESS_30 = "处理时效:预计银行在 1-2 个工作日内完成审核. 建行商户编号${Mrch_ID}, 管辖机构号:${insNo}";

    @Override
    protected ContractSubTask produceUpdateTaskByRule(String merchantSn, ContractEvent event, Map<String, Object> paramContext, ContractRule contractRule) {
        return null;
    }

    /**
     * 任务新增处理
     */
    @Override
    protected ContractResponse processInsertTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        CcbParam ccbParam = buildParam(contractChannel, sub, CcbParam.class);
        //新增商户入网
        if (ProviderUtil.CONTRACT_TYPE_INSERT.equals(contractTask.getType())) {
            if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
                ContractResponse contractResponse = ccbService.contractMerchant(contextParam, ccbParam);
                if (contractResponse.getCode() == 200) {
                    Map merchant = WosaiMapUtils.getMap(contextParam, "merchant");
                    final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
                    // 进件成功要给APP发送签约链接
                    this.sendNoticeToApp(merchantId, signTemplateCode, null);
                    //钉钉提醒
                    dingTalkRemind(contractTask, CcbConstant.WAIT_FOR_SIGN);
                    BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    ccbDirectBiz.recordViewProcess(apply, 20, new Date());
                    // TODO 建行标签tag还没有配置在配置文件中
                    if (!StringUtils.isEmpty(BeanUtil.getPropString(contractTask.getEventContext(), "from"))) {
                        tagIngestService.ingest(Lists.newArrayList(merchantId), ccbTag, "未签约");
                    }
                }
                return contractResponse;
            } else {
                //其他支付类型：微信，支付宝、云闪付
                ContractResponse contractResponse = ccbService.contractOtherPayWay(sub, ccbParam);
                if (contractResponse.isSuccess() && payWay.equals(PaywayEnum.WEIXIN.getValue())) {
                    //TODO 银行审核通过后,进入商户待实名认证状态时,在crm中通知销售,告知微信实名认证方法
                    final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(contractTask.getId());
                    final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
                    //通知crm和商户老板
                    notifyCrmAndMerchant(crmUserId, contractTask);
                }
                if (Lists.newArrayList(PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()).contains(payWay) &&
                        !StringUtils.isEmpty(BeanUtil.getPropString(contractTask.getEventContext(), "from"))) {
                    Map<String, Object> tradeParam = contractResponse.getTradeParam();
                    String payMerchantId = BeanUtil.getPropString(tradeParam, "pay_merchant_id");
                    if (!StringUtils.isEmpty(payMerchantId)) {
                        try {
                            systemService.createAuthTask(CollectionUtil.hashMap("reason", "ccb", "subMchId", payMerchantId, "isSubmitApply", true));
                        } catch (Exception e) {
                            log.error("ccb createAuthTask error {} {}", contractTask.getMerchant_sn(), payMerchantId, e);
                        }
                    }
                }
                return contractResponse;
            }
        }
        return null;
    }

    public void notifyCrmAndMerchant(String userId, ContractTask contractTask) {
        try {
            String merchantChannelCode = wechatQrCodeUtils.authorizationCodeUrl(Maps.newHashMap(), "ccb-1026-3");
            final String imageUrl = replaceHttp(merchantChannelCode);

            //给商户超管角色发送通知（收钱吧APP)
            //获取商户信息
            final String merchantSn = contractTask.getMerchant_sn();
            final Map merchant = merchantService.getMerchantBySn(merchantSn);
            final String merchantId = BeanUtil.getPropString(merchant, com.wosai.data.dao.DaoConstants.ID);
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            //生成二维码和文案
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(ccbAppDevCode);
                sendModel.setTemplateCode(ccbAppTemplateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                //需要传的数据
                sendModel.setData(CollectionUtil.hashMap("text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",建行活动审核通过,请用微信扫描下方建行服务商授权码,完成微信实名认证",
                        "text2", "认证通过后,方可参加活动",
                        "qrCodeImg", imageUrl));
                log.info("建行给收钱吧app发送实名认证通知{},商户号:{}", JSONObject.toJSONString(sendModel), contractTask.getMerchant_sn());
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }

            if (StringUtils.isEmpty(userId)) {
                return;
            }
            //crm
            final ClientSideNoticeSendModel crmSendModel = new ClientSideNoticeSendModel();
            //产品开发标识
            crmSendModel.setDevCode(ccbCrmDevCode);
            //短信通知模板标识
            crmSendModel.setTemplateCode(ccbCrmTemplateCode);
            //账户Id
            crmSendModel.setAccountId(userId);
            //需要传的数据
            crmSendModel.setData(CollectionUtil.hashMap(
                    "ycbank", "建设银行",
                    "mername", WosaiMapUtils.getString(merchant, Merchant.NAME),
                    "text1", WosaiMapUtils.getString(merchant, Merchant.NAME) + ",建行入网审核已通过,请联系商户完成微信实名认证",
                    "text2", "请商户通过微信扫描建行服务商授权码,完成微信实名认证",
                    "qrCodeImg", imageUrl));
            crmSendModel.setClientSides(Lists.newArrayList("TERMINALCRM"));
            //请求时间戳
            crmSendModel.setTimestamp(System.currentTimeMillis());
            log.info("建行给crm发送实名认证通知{},商户号:{}", JSONObject.toJSONString(crmSendModel), contractTask.getMerchant_sn());
            clientSideNoticeService.send(crmSendModel);
        } catch (Exception e) {
            log.error("建行通知发送通知异常id:{},异常信息{}", userId, e);
        }
    }

    public void sendNoticeToApp(String merchantId, String templateCode, Map data) {
        // 1:给商户超管角色发送通知（收钱吧APP)
        try {
            MerchantUserSimpleInfo userInfo = merchantUserService.getSuperAdminSimpleInfoByMerchantId(merchantId);
            if (userInfo != null) {
                final MerchantUserNoticeSendModel sendModel = new MerchantUserNoticeSendModel();
                sendModel.setDevCode(ccbDevCode);
                sendModel.setTemplateCode(templateCode);
                sendModel.setMerchantUserId(userInfo.getMerchant_user_id());
                sendModel.setTimestamp(System.currentTimeMillis());
                sendModel.setData(data);
                log.info("merchantId:{},消息内容:{}", merchantId, JSONObject.toJSONString(sendModel));
                clientSideNoticeService.sendToMerchantUser(sendModel);
            }
        } catch (Exception e) {
            log.error("建行给商户超管角色发送通知异常id:{},异常信息{}", merchantId, e);
        }
    }

    /**
     * 任务更新处理
     *
     * @param contractTask
     * @param contractChannel
     * @param sub
     * @return
     */
    @Override
    protected ContractResponse processUpdateTaskByRule(ContractTask contractTask, ContractChannel contractChannel, ContractSubTask sub) {
        Integer payWay = sub.getPayway();
        Map contextParam = JSON.parseObject(contractTask.getEvent_context(), Map.class);
        CcbParam ccbParam = buildParam(contractChannel, sub, CcbParam.class);
        if (PaywayEnum.ACQUIRER.getValue().equals(payWay)) {
            return ccbService.updateMerchant(contextParam, ccbParam);
        }
        return null;
    }


    @Override
    public ContractResponse queryContractStatus(ContractSubTask contractSubTask) {
        ContractChannel contractChannel = ruleContext.getContractRule(contractSubTask.getContract_rule()).getContractChannel();
        CcbParam ccbParam = buildParam(contractChannel, contractSubTask, CcbParam.class);
        // 新建一个对象，不设置未使用的response_body，这个字段长度太大且在contract中没有使用
        ContractSubTask querySubTask = new ContractSubTask();
        querySubTask.setId(contractSubTask.getId())
                .setP_task_id(contractSubTask.getP_task_id())
                .setMerchant_sn(contractSubTask.getMerchant_sn())
                .setChannel(contractSubTask.getChannel())
                .setDefault_channel(contractSubTask.getDefault_channel())
                .setChange_config(contractSubTask.getChange_config())
                .setChange_body(contractSubTask.getChange_body())
                .setTask_type(contractSubTask.getTask_type())
                .setContract_id(contractSubTask.getContract_id())
                .setPayway(contractSubTask.getPayway())
                .setStatus(contractSubTask.getStatus())
                .setRequest_body(contractSubTask.getRequest_body())
                .setResult(contractSubTask.getResult())
                .setContract_rule(contractSubTask.getContract_rule())
                .setRule_group_id(contractSubTask.getRule_group_id());
        return ccbService.queryContractStatus(querySubTask, ccbParam);
    }


    @Override
    public Map doWeixinSubDevConfig(WeixinConfig weixinConfig, MerchantProviderParams merchantProviderParams) {
        CcbParam ccbParam = contractParamsBiz.buildContractParams(String.valueOf(merchantProviderParams.getProvider()), merchantProviderParams.getPayway(), merchantProviderParams.getChannel_no(), CcbParam.class);
        return ccbService.weChatSubDevConfig(ccbParam, weixinConfig);
    }

    @Override
    public HandleQueryStatusResp queryAndHandleContractStatus(ContractSubTask contractSubTask) {
        ContractResponse contractResponse = this.queryContractStatus(contractSubTask);
        if (contractResponse == null || contractResponse.isBusinessFail()) {
            String message = contractResponse == null ? "进件失败" : contractResponse.getMessage();
            return new HandleQueryStatusResp()
                    .setFail(true)
                    .setMessage(message);
        } else {
            return handleContractStatus(contractSubTask, contractResponse);
        }
    }

    /**
     * 处理进件状态
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    @Override
    protected HandleQueryStatusResp handleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        HandleQueryStatusResp statusResp = doHandleContractStatus(contractSubTask, response);
        boolean retry = statusResp.isRetry();
        boolean b = CcbConstant.WAIT_FOR_SIGN.equals(contractSubTask.getResult());
        if (!retry || !b) {
            Map merchant = merchantService.getMerchantByMerchantSn(contractSubTask.getMerchant_sn());
            tagIngestService.ingest(Lists.newArrayList(BeanUtil.getPropString(merchant, DaoConstants.ID)), ccbTag, "已签约");
        }
        return statusResp;
    }

    /**
     * 查询的状态做处理
     *
     * @param contractSubTask
     * @param response
     * @return
     */
    public HandleQueryStatusResp doHandleContractStatus(ContractSubTask contractSubTask, ContractResponse response) {
        if (response.isSystemFail()) {
            // 如果是超时之类等等的系统异常，这里不会有信息的
            if (WosaiMapUtils.isEmpty(response.getResponseParam()) || WosaiMapUtils.isEmpty(MapUtils.getMap(response.getResponseParam(), "dataInfo"))) {
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage(response.getMessage());
            }
            CcbAuditResp ccbAuditResp = JSON.parseObject(JSON.toJSONString(response.getResponseParam().get("dataInfo")), CcbAuditResp.class);
            // 如果是审核通过子商户不能为空的这种情况 要去配置数币 要更新文案
            if ("1".equals(ccbAuditResp.getRvwStCd()) && "1".equals(ccbAuditResp.getVrfStCd()) && WosaiStringUtils.isNotEmpty(response.getMessage()) && response.getMessage().contains("支付源商户号不能为空")) {
                //configDigitalCurrency(contractSubTask, response);
                updateSubTaskResult(contractSubTask, response.getMessage());
            }
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        } else {
            BankDirectApply apply = bankDirectApplyMapper.getApplyByTaskId(contractSubTask.getP_task_id());
            CcbAuditResp ccbAuditResp = JSON.parseObject(JSON.toJSONString(response.getResponseParam().get("dataInfo")), CcbAuditResp.class);
            // 待签约 这种情况特殊处理一下
            if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getMessage())) {
                updateSubAndParentTaskResultAndResponseBody(contractSubTask.getId(), contractSubTask.getResult(), CcbConstant.WAIT_FOR_SIGN, null);
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage(response.getMessage());
            }
            // 这个值不是空 则说明可能是签约失败了
            if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getErrCode())) {
                String failReason = WosaiStringUtils.isNotEmpty(ccbAuditResp.getErrDescription()) ? ccbAuditResp.getErrDescription() : "审核驳回";
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(failReason);

            }
            // 两个都是1就是成功了
            if ("1".equals(ccbAuditResp.getRvwStCd()) && "1".equals(ccbAuditResp.getVrfStCd())) {
                // 将商户信息等设置到返回值当中去
                contractSubTask.setResponse_body(JSON.toJSONString(response.getTradeParam()));
//                configDigitalCurrency(contractSubTask, response);
                updateSubAndParentTaskResultAndResponseBody(contractSubTask.getId(), contractSubTask.getResult(), CcbConstant.CCB_AUDIT_SUCCESS, JSON.toJSONString(response.getTradeParam()));
                recordCcbMerchantFeeRateInfo(contractSubTask, response.getTradeParam());
                String remark = generateViewProcess30(response.getTradeParam(), contractSubTask);
                ccbDirectBiz.replaceApplyViewProcessRemark(apply, 30, remark);
                ccbDirectBiz.recordViewProcess(apply, 41, new Date());
                return new HandleQueryStatusResp().setSuccess(true)
                        .setMessage(CcbConstant.CCB_AUDIT_SUCCESS);
            }
            // 复核状态是2、3或者审核状态是2 都认为是失败
            if ("2".equals(ccbAuditResp.getRvwStCd()) || "3".equals(ccbAuditResp.getRvwStCd()) || "2".equals(ccbAuditResp.getVrfStCd())) {
                StringBuilder stringBuilder = new StringBuilder();
                if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getPshFalRs1())) {
                    stringBuilder.append("推送失败原因:").append(ccbAuditResp.getPshFalRs1()).append(";");
                }
                if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getLv1RjRsDsc())) {
                    stringBuilder.append("一级拒绝原因").append(ccbAuditResp.getLv1RjRsDsc()).append(";");
                }
                if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getLv2RjRsDsc())) {
                    stringBuilder.append("二级拒绝原因").append(ccbAuditResp.getLv2RjRsDsc()).append(";");
                }
                String result = stringBuilder.toString();
                return new HandleQueryStatusResp()
                        .setFail(true)
                        .setMessage(WosaiStringUtils.isEmpty(result) ? "审核驳回" : result);
            }
            // Vrf_StCd=0-待审核 and Synz_Mod_Rcrd_ValSt=5-审核退回
            if ("0".equals(ccbAuditResp.getVrfStCd()) && "5".equals(ccbAuditResp.getSyncModRcrdValst()) && WosaiStringUtils.isNotEmpty(ccbAuditResp.getRetRdsc())) {
                recordAuditBackMsgToSubTask(contractSubTask, ccbAuditResp.getRetRdsc());
                ccbDirectBiz.replaceApplyViewProcessRemark(apply, 30, ccbAuditResp.getRetRdsc());
                return new HandleQueryStatusResp()
                        .setRetry(true)
                        .setMessage(response.getMessage());
            }
            updateSubAndParentTaskResultAndResponseBody(contractSubTask.getId(), contractSubTask.getResult(), CcbConstant.IN_CCB_AUDITING, null);
            ccbDirectBiz.recordViewProcess(apply, 30, new Date());
            return new HandleQueryStatusResp()
                    .setRetry(true)
                    .setMessage(response.getMessage());
        }
    }

    private String generateViewProcess30(Map<String, Object> tradeParam, ContractSubTask contractSubTask) {
        //从contract_task 中获取到 机构号.
        Map<String, String> valuesMap = new HashMap<>();
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(contractSubTask.getP_task_id());
        if (contractTask == null) {
            valuesMap.put("insNo", "");
        } else {
            valuesMap.put("insNo", (String) contractTask.getEventContext().getOrDefault("ins_no", ""));
        }
        valuesMap.put("Mrch_ID", (String) tradeParam.getOrDefault("Mrch_ID", ""));
        StrSubstitutor sub = new StrSubstitutor(valuesMap);
        return sub.replace(CCB_VIEW_PROCESS_30);
    }

    private String getOriginalRemark(int viewStatus) {
        List<ViewProcess> viewProcesses = preViewProcess(AcquirerTypeEnum.CCB.getValue());
        if (WosaiCollectionUtils.isEmpty(viewProcesses)) {
            return null;
        }
        for (ViewProcess viewProcess : viewProcesses) {
            if (viewProcess.getViewStatus() == viewStatus) {
                return viewProcess.getRemark();
            }
        }
        return null;
    }

    private List<ViewProcess> preViewProcess(String acquire) {
        final List<Map<String, String>> process = applicationApolloConfig.getBankViewProcess().get(acquire);
        if (org.springframework.util.CollectionUtils.isEmpty(process)) {
            return null;
        }
        return JSONObject.parseArray(JSONObject.toJSONString(process), ViewProcess.class);
    }

    /**
     * 将审核回退信息记录到sub_task和bank_direct_apply中
     */
    private void recordAuditBackMsgToSubTask(ContractSubTask contractSubTask, String message) {
        if (Objects.equals(contractSubTask.getResult(), message)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(contractSubTask.getId());
        subTask.setResult(message);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
    }

    private void updateSubTaskResult(ContractSubTask contractSubTask, String message) {
        String targetResult;
        if (message.contains("微信") && message.contains("支付宝")) {
            targetResult = "支付宝、微信未开立成功";
        } else if (message.contains("微信")) {
            targetResult = "微信未开立成功";
        } else {
            targetResult = "支付宝未开立成功";
        }
        if (Objects.equals(contractSubTask.getResult(), targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(contractSubTask.getId());
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
    }


    public void updateSubAndParentTaskResultAndResponseBody(Long id, String originResult, String targetResult, String responseBody) {
        if (Objects.equals(originResult, targetResult)) {
            return;
        }
        ContractSubTask subTask = new ContractSubTask();
        subTask.setId(id);
        if (WosaiStringUtils.isNotEmpty(responseBody)) {
            subTask.setResponse_body(responseBody);
        }
        subTask.setResult(targetResult);
        contractSubTaskMapper.updateByPrimaryKey(subTask);
        //为了保持contractTask同步;
        final Long pTaskId = contractSubTaskMapper.selectByPrimaryKey(id).getP_task_id();
        final ContractTask task = contractTaskMapper.selectByPrimaryKey(pTaskId);
        //机器人推送 包含字段有：城市，商户号，推广人|手机号，商户进件状态
        dingTalkRemind(task, targetResult);
        if (Objects.equals(task.getResult(), targetResult)) {
            return;
        }
        final ContractTask contractTask = new ContractTask();
        contractTask.setId(pTaskId);
        contractTask.setResult(targetResult);
        contractTask.setPriority(task.getPriority());
        contractTaskMapper.updateByPrimaryKey(contractTask);
    }

    public void configDigitalCurrency(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        // 如果已经配置过数币 就不再去配置了
        if (Boolean.TRUE.equals(redisTemplate.hasKey(String.format("ccb_decp_config:%s", contractSubTask.getMerchant_sn())))) {
            return;
        }
        Map merchant = merchantService.getMerchantBySn(contractSubTask.getMerchant_sn());
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        Map decpConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
        // 表明已经配置过了，不用再配置了 或者是已经有了自助开通数币的配置
        if (MapUtil.isNotEmpty(decpConfig)
                && ("1026_*_*_false_true_0001".equals(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))) || "1026_*_*_false_true_0002".equals(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))) {
            redisTemplate.opsForValue().set(String.format("ccb_decp_config:%s", contractSubTask.getMerchant_sn()), contractSubTask.getMerchant_sn(), 1L, TimeUnit.DAYS);
            return;
        }
        CustomerRelationOriginReq req = new CustomerRelationOriginReq();
        req.setCustomerIds(Collections.singletonList(merchantId));
        req.setConfigCodes(Collections.singletonList(indirectCrmCustomerRelation));
        PageListResult<CustomerRelationOriginResp> customerRelationOrigin = null;
        try {
            customerRelationOrigin = iCustomerRelationFacade.findCustomerRelationOrigin(req);
        } catch (Exception e) {
            log.error("查询客户关系异常，参数:{} 异常:", JSON.toJSONString(req), e);
        }
        if (customerRelationOrigin == null) {
            return;
        }
        // 说明这里商户是大客户,如果该商户已经配置了其他的数币，就不再去配置建行的数币了
        for (CustomerRelationOriginResp record : customerRelationOrigin.getRecords()) {
            if (record.getOrganization() != null
                    && WosaiStringUtils.isNotEmpty(record.getOrganization().getPath())
                    && record.getOrganization().getPath().startsWith(kabuOrganizationPath)) {
                if (MapUtils.isNotEmpty(decpConfig) && WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))) {
                    redisTemplate.opsForValue().set(String.format("ccb_decp_config:%s", contractSubTask.getMerchant_sn()), contractSubTask.getMerchant_sn(), 1L, TimeUnit.DAYS);
                    return;
                }
            }
        }
        CcbMerchantInfoResp ccbMerchantInfoResp = JSON.parseObject(JSON.toJSONString(contractResponse.getTradeParam()), CcbMerchantInfoResp.class);
        Map ccbTradeParams = CollectionUtil.hashMap(
                "merchant_id", ccbMerchantInfoResp.getMrchId(),
                "pos_id", ccbMerchantInfoResp.getPosGrp().get(0).getTmnlCd(),
                "terminal_id", ccbMerchantInfoResp.getPosGrp().get(0).getEdCrdMchnTmnlIdrCd(),
                "terminal_no", ccbMerchantInfoResp.getPosGrp().get(0).getPosId(),
                "branch_id", ccbMerchantInfoResp.getBranchId()
        );

        Map updateDecpConfig = CollectionUtil.hashMap(
                MerchantConfig.PAYWAY, PaywayEnum.DCEP.getValue(),
                MerchantConfig.MERCHANT_ID, merchantId,
                MerchantConfig.B2C_STATUS, MerchantConfig.STATUS_OPENED,
                // 这里的agent_name可以写死也可以调用agentBiz去获取
                MerchantConfig.B2C_AGENT_NAME, "1026_*_*_false_true_0001",
                MerchantConfig.B2C_FEE_RATE, "0.0",
                MerchantConfig.B2C_FORMAL, false,
                MerchantConfig.C2B_FORMAL, false,
                MerchantConfig.WAP_FORMAL, false,
                MerchantConfig.MINI_FORMAL, false,
                MerchantConfig.PROVIDER, ProviderEnum.PROVIDER_CCB.getValue()
        );
        if (MapUtils.isEmpty(decpConfig)) {
            updateDecpConfig.put(MerchantConfig.PARAMS, CollectionUtil.hashMap("ccb_trade_params", ccbTradeParams));
            tradeConfigService.createMerchantConfig(updateDecpConfig);
        } else {
            Map params = MapUtils.getMap(decpConfig, MerchantConfig.PARAMS);
            if (params == null) {
                params = new HashMap(1);
            }
            params.put("ccb_trade_params", ccbTradeParams);
            updateDecpConfig.put(MerchantConfig.PARAMS, params);
            updateDecpConfig.put(DaoConstants.ID, BeanUtil.getPropString(decpConfig, DaoConstants.ID));
            tradeConfigService.updateMerchantConfig(updateDecpConfig);
        }
        redisTemplate.opsForValue().set(String.format("ccb_decp_config:%s", contractSubTask.getMerchant_sn()), contractSubTask.getMerchant_sn(), 1L, TimeUnit.DAYS);
        supportService.removeCachedParams(contractSubTask.getMerchant_sn());
    }

    private void recordCcbMerchantFeeRateInfo(ContractSubTask contractSubTask, Map<String, Object> tradeParam) {
        CcbMerchantInfoResp ccbMerchantInfoResp = JSON.parseObject(JSON.toJSONString(tradeParam), CcbMerchantInfoResp.class);
        CcbMerchantFeeRate update = new CcbMerchantFeeRate();
        Map<String, String> feeRates = new HashMap<>();
        Map<String, String> ratios = new HashMap<>();
        for (CcbRateList ccbRateList : ccbMerchantInfoResp.getRateList()) {
            feeRates.put(ccbRateList.getAcqCmsnChrgRateTpCd(), ccbRateList.getFstLvlHdCgRate());
            if (WosaiCollectionUtils.isNotEmpty(ccbRateList.getFeeDisList())) {
                ratios.put(ccbRateList.getAcqCmsnChrgRateTpCd(), ccbRateList.getFeeDisList().get(0).getDcnPrDsbPyAlctPctgVal());
            }
        }
        update.setMerchant_sn(contractSubTask.getMerchant_sn());
        update.setProvider_merchant_id(ccbMerchantInfoResp.getMrchId());
        update.setWh(feeRates.get("WH"));
        update.setWd(feeRates.get("WD"));
        update.setZh(feeRates.get("ZH"));
        update.setZd(feeRates.get("ZD"));
        update.setTh(feeRates.get("TH"));
        update.setTd(feeRates.get("TD"));
        update.setDj(feeRates.get("DJ"));
        update.setFd(feeRates.get("FD"));
        update.setWh_ratio(ratios.get("WH"));
        update.setWd_ratio(ratios.get("WD"));
        update.setZh_ratio(ratios.get("ZH"));
        update.setZd_ratio(ratios.get("ZD"));
        update.setQuery_date(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        CcbMerchantFeeRate ccbMerchantFeeRate = feeRateMapper.selectByMerchantSn(contractSubTask.getMerchant_sn());
        if (ccbMerchantFeeRate == null) {
            long ctime = System.currentTimeMillis();
            update.setCtime(ctime);
            update.setMtime(ctime);
            feeRateMapper.insertSelective(update);
        } else {
            update.setMtime(System.currentTimeMillis());
            feeRateMapper.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * @param task
     * @param targetResult
     * <AUTHOR>
     * @Description: 城市，商户号，推广人|手机号，商户进件状态 异步发送钉钉提醒
     * @time 16:46
     */
    private void dingTalkRemind(ContractTask task, String targetResult) {
        try {
            final Map merchant = merchantService.getMerchantByMerchantSn(task.getMerchant_sn());
            //城市
            final String city = BeanUtil.getPropString(merchant, Merchant.CITY);
            //商户号
            final String merchantSn = task.getMerchant_sn();
            //推广人|手机号
            final BankDirectApply bankDirectApply = bankDirectApplyMapper.getApplyByTaskId(task.getId());
            final String crmUserId = BeanUtil.getPropString(JSONObject.parseObject(bankDirectApply.getForm_body(), Map.class), BankDirectApplyConstant.CRM_UESRID);
            if (StringUtils.isEmpty(crmUserId)) {
                return;
            }
            final Map user = userService.getUser(crmUserId);
            final String cellphone = BeanUtil.getPropString(user, User.CELLPHONE);
            final String linkman = BeanUtil.getPropString(user, User.LINKMAN);
            final String join = Joiner.on("|").skipNulls().join(Lists.newArrayList(linkman, cellphone));
            final String message = String.format("建行进件状态更新提醒 %s, %s, %s, %s", city, merchantSn, join, targetResult);
            log.info("建行状态更新提醒:{}", message);
            chatBotUtil.sendMessageToCcbChatBot(message);
        } catch (Exception exception) {
            log.error("建行状态更新提醒异常:{}", exception);
        }
    }

    @Override
    public Boolean doCheck(ContractSubTask contractSubTask, ContractResponse contractResponse) {
        CcbAuditResp ccbAuditResp = JSON.parseObject(JSON.toJSONString(contractResponse.getResponseParam().get("dataInfo")), CcbAuditResp.class);
        // 待签约
        if (WosaiStringUtils.isNotEmpty(ccbAuditResp.getMessage())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
