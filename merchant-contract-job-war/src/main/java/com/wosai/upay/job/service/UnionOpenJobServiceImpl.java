package com.wosai.upay.job.service;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsExtMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExt;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Map;

/**
 * @Description: 银联开放平台
 * <AUTHOR>
 * @Date 2020/8/28 6:15 PM
 **/

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class UnionOpenJobServiceImpl implements UnionOpenJobService {

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    MerchantProviderParamsExtMapper merchantProviderParamsExtMapper;

    @Autowired
    MerchantService merchantService;

    @Autowired
    ComposeAcquirerBiz acquirerBiz;

    @Autowired
    ScheduleUtil scheduleUtil;

    @Autowired
    RedisLock redisLock;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Override
    public Map<String, Object> getUnionOpenParamByMerchantSn(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            return null;
        }
        return composeAcquirerBiz.getUnionOpenParam(merchantSn);
    }

    @Override
    public ContractResponse contractUnionMerchant(String merchantSn) {
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        if (MapUtils.isEmpty(merchant)) {
            return new ContractResponse().setSuccess(false).setMsg(String.format("%s 商户号不存在", merchantSn));
        }
        if (!ProviderUtil.TONG_LIAN_CHANNEL.equalsIgnoreCase(acquirerBiz.getMerchantAcquirer(merchantSn))) {
            return new ContractResponse().setSuccess(false).setMsg(String.format("%s 此商户非通联商户", merchantSn));
        }
        MerchantProviderParams unionOpenParamUnSubmit = merchantProviderParamsMapper.getUnionOpenParamCheck(merchantSn);
        if (ObjectUtils.isEmpty(unionOpenParamUnSubmit)) {
            return new ContractResponse().setSuccess(false).setMsg(String.format("%s 此商户未报备银联云闪付平台", merchantSn));
        }
        MerchantProviderParamsExt merchantProviderParamsExt = merchantProviderParamsExtMapper.getByParamId(unionOpenParamUnSubmit.getId(), MerchantProviderParamsExt.UNION_OPEN_TYPE);
        if (MerchantProviderParams.PARAMS_CONFIG_STATUS_SUCCESS == unionOpenParamUnSubmit.getParams_config_status()) {
            return new ContractResponse().setSuccess(true).setMsg(merchantProviderParamsExt.getExt_field_1());
        }
        if (MerchantProviderParams.PARAMS_CONFIG_STATUS_FAIL == unionOpenParamUnSubmit.getParams_config_status()
                || MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE == unionOpenParamUnSubmit.getParams_config_status()) {
            return new ContractResponse().setSuccess(true).setMsg(String.format("%s 此商户正在注册开放平台商户号排队中", merchantSn));
        }
        if (MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL == unionOpenParamUnSubmit.getParams_config_status()) {
            if (ObjectUtils.isEmpty(merchantProviderParamsExt)) {
                //未注册过
                unionOpenParamUnSubmit.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_PRE).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(unionOpenParamUnSubmit);
                return new ContractResponse().setSuccess(true).setMsg(String.format("%s 已提交，请耐心等待", merchantSn));
            } else {
                //注册过，前一次注册失败
                MerchantProviderParamsExt ext = new MerchantProviderParamsExt();
                BeanUtils.copyProperties(merchantProviderParamsExt, ext);
                ext.setVersion(merchantProviderParamsExt.getVersion() + 1).setUpdate_at(null).setExt_field_1("0").setExt_field_2("0");
                merchantProviderParamsExtMapper.updateByPrimaryKeySelective(ext);
                MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
                BeanUtils.copyProperties(unionOpenParamUnSubmit, merchantProviderParams);
                merchantProviderParams.setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_FAIL).setVersion(unionOpenParamUnSubmit.getVersion() + 1).setMtime(System.currentTimeMillis());
                merchantProviderParamsMapper.updateByPrimaryKeySelective(merchantProviderParams);
                return new ContractResponse().setSuccess(true).setMsg(String.format("%s 已重新提交", merchantSn));
            }
        } else {
            return new ContractResponse().setSuccess(false).setMsg(String.format("%s 服务异常", merchantSn));
        }
    }
}