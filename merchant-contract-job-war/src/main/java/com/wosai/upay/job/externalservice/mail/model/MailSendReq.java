package com.wosai.upay.job.externalservice.mail.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/7
 */
@Data
@Accessors(chain = true)
public class MailSendReq {

    private Long id;

    private String to;

    private String content;

    private List<Map<String, Object>> attachments;


    public Map<String, Object> genMailSendRequest() {
        Map<String, Object> request = new HashMap<>(6);
        request.put("id", getId());
        request.put("to", getTo());
        request.put("content", getContent());
        request.put("attachments", getAttachments());
        return request;
    }

}
