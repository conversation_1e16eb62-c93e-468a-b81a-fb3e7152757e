package com.wosai.upay.job.refactor.dao;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.refactor.mapper.OpenOnlinePaymentApplyDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyExtraBO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyProcessBO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.DefaultValueUtil;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/12/21
 */
@Repository
public class OpenOnlinePaymentApplyDAO {

    @Resource
    private OpenOnlinePaymentApplyDynamicMapper openOnlinePaymentApplyDynamicMapper;
    @Resource
    private ScheduleUtil scheduleUtil;


    public OpenOnlinePaymentApplyDO saveApply(String merchantSn, Integer payway, String acquirer) {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setMerchantSn(merchantSn);
        openOnlinePaymentApplyDO.setPayway(payway);
        openOnlinePaymentApplyDO.setAcquirer(acquirer);
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);
        openOnlinePaymentApplyDO.setProcess(JSON.toJSONString(OpenOnlinePaymentApplyDO.initProcessList(payway)));
        openOnlinePaymentApplyDynamicMapper.insert(openOnlinePaymentApplyDO);
        return openOnlinePaymentApplyDO;
    }

    public int updateApplyExtra(Long id, OnlinePaymentApplyExtraBO extraBO) {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(id);
        openOnlinePaymentApplyDO.setExtra(JSON.toJSONString(extraBO));
        return openOnlinePaymentApplyDynamicMapper.updateById(openOnlinePaymentApplyDO);
    }

    public Optional<OpenOnlinePaymentApplyDO> queryLatestOpenOnlinePaymentApplyByAcquirer(String merchantSn, Integer payway, String acquirer) {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenOnlinePaymentApplyDO::getMerchantSn, merchantSn)
                .eq(OpenOnlinePaymentApplyDO::getPayway, payway)
                .eq(OpenOnlinePaymentApplyDO::getAcquirer, acquirer)
                .orderByDesc(OpenOnlinePaymentApplyDO::getCreateAt)
                .last("limit 1");
        return Optional.ofNullable(openOnlinePaymentApplyDynamicMapper.selectOne(wrapper));
    }

    public Optional<OpenOnlinePaymentApplyDO> queryLatestOpenOnlinePaymentApply(String merchantSn, Integer payway) {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenOnlinePaymentApplyDO::getMerchantSn, merchantSn)
                .eq(OpenOnlinePaymentApplyDO::getPayway, payway)
                .orderByDesc(OpenOnlinePaymentApplyDO::getCreateAt)
                .last("limit 1");
        return Optional.ofNullable(openOnlinePaymentApplyDynamicMapper.selectOne(wrapper));
    }

    public List<OpenOnlinePaymentApplyDO> queryProcessingAppliesByMerchantSn(String merchantSn) {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenOnlinePaymentApplyDO::getMerchantSn, merchantSn)
                .in(OpenOnlinePaymentApplyDO::getStatus, Arrays.asList(OnlinePaymentConstant.ApplyStatus.PENDING, OnlinePaymentConstant.ApplyStatus.APPLYING))
                .orderByDesc(OpenOnlinePaymentApplyDO::getCreateAt);
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public Optional<OpenOnlinePaymentApplyDO> querySuccessApplyByAcquirer(String merchantSn, Integer payway, String acquirer) {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenOnlinePaymentApplyDO::getMerchantSn, merchantSn)
                .eq(OpenOnlinePaymentApplyDO::getAcquirer, acquirer)
                .eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.SUCCESS)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.SUCCESS)
                .eq(OpenOnlinePaymentApplyDO::getPayway, payway)
                .orderByDesc(OpenOnlinePaymentApplyDO::getCreateAt)
                .last("limit 1");
        return Optional.ofNullable(openOnlinePaymentApplyDynamicMapper.selectOne(wrapper));
    }


    public List<OpenOnlinePaymentApplyDO> queryWaitForReContractApplies() {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.PENDING)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.PENDING)
                .ge(OpenOnlinePaymentApplyDO::getPriority, now.minus(Duration.ofMillis(getQueryTime())))
                .le(OpenOnlinePaymentApplyDO::getPriority, now)
                .orderByDesc(OpenOnlinePaymentApplyDO::getPriority)
                .last("limit " + getQueryLimit());
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public List<OpenOnlinePaymentApplyDO> queryWaitForAuthApplies() {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.APPLYING)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH)
                .ge(OpenOnlinePaymentApplyDO::getPriority, now.minus(Duration.ofMillis(getQueryTime())))
                .le(OpenOnlinePaymentApplyDO::getPriority, now)
                .orderByDesc(OpenOnlinePaymentApplyDO::getPriority)
                .last("limit " + getQueryLimit());
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public List<OpenOnlinePaymentApplyDO> queryAliWaitForAuditApplies() {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.eq(OpenOnlinePaymentApplyDO::getPayway, PaywayEnum.ALIPAY.getValue())
                .eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.APPLYING)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT)
                .ge(OpenOnlinePaymentApplyDO::getPriority, now.minusDays(1))
                .le(OpenOnlinePaymentApplyDO::getPriority, now)
                .orderByDesc(OpenOnlinePaymentApplyDO::getPriority);
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public List<OpenOnlinePaymentApplyDO> queryAliWaitForAuditAppliesByPriorityAndAcquirer(LocalDateTime start, LocalDateTime end, String acquirer) {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OpenOnlinePaymentApplyDO::getPayway, PaywayEnum.ALIPAY.getValue())
                .eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.APPLYING)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT)
                .eq(OpenOnlinePaymentApplyDO::getAcquirer, acquirer)
                .ge(OpenOnlinePaymentApplyDO::getPriority, start)
                .le(OpenOnlinePaymentApplyDO::getPriority, end)
                .orderByDesc(OpenOnlinePaymentApplyDO::getPriority);
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public List<OpenOnlinePaymentApplyDO> queryTimeoutWaitForAuthApplies() {
        LambdaQueryWrapper<OpenOnlinePaymentApplyDO> wrapper = new LambdaQueryWrapper<>();
        LocalDateTime now = LocalDateTime.now();
        wrapper.eq(OpenOnlinePaymentApplyDO::getStatus, OnlinePaymentConstant.ApplyStatus.APPLYING)
                .eq(OpenOnlinePaymentApplyDO::getProcessStatus, OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH)
                .le(OpenOnlinePaymentApplyDO::getCreateAt, now.minusDays(7))
                .ge(OpenOnlinePaymentApplyDO::getCreateAt, now.minusDays(8))
                .orderByAsc(OpenOnlinePaymentApplyDO::getCreateAt)
                .last("limit " + getQueryLimit());
        return openOnlinePaymentApplyDynamicMapper.selectList(wrapper);
    }

    public void updateApply(Long id, Integer status, Integer processStatus, String result, List<OnlinePaymentApplyProcessBO> processList, OnlinePaymentApplyExtraBO extraBO) {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(id);
        openOnlinePaymentApplyDO.setStatus(status);
        openOnlinePaymentApplyDO.setResult(result);
        openOnlinePaymentApplyDO.setProcessStatus(processStatus);
        if (Objects.nonNull(processList)) {
            openOnlinePaymentApplyDO.setProcess(JSON.toJSONString(processList));
        }
        if (Objects.nonNull(extraBO)) {
            openOnlinePaymentApplyDO.setExtra(JSON.toJSONString(extraBO));
        }
        openOnlinePaymentApplyDynamicMapper.updateById(openOnlinePaymentApplyDO);
    }

    public void delayApply(Long id, long minutes) {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(id);
        openOnlinePaymentApplyDO.setPriority(Timestamp.from(Instant.now().plus(Duration.ofMinutes(minutes))));
        openOnlinePaymentApplyDynamicMapper.updateById(openOnlinePaymentApplyDO);
    }

    private int getQueryLimit() {
        return DefaultValueUtil.value(scheduleUtil.getQueryLimit().getOpenOnlinePaymentApply(), ScheduleUtil.DEFAULT_QUERY_LIMIT);
    }

    private long getQueryTime() {
        return DefaultValueUtil.value(scheduleUtil.getQueryTime().getOpenOnlinePaymentApply(), ScheduleUtil.DEFAULT_TWO_DAYS_MILLIS_QUERY);
    }
}
