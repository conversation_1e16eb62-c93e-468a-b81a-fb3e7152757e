package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.merchant.contract.MerchantContractOpinionEvent;
import com.wosai.databus.event.merchant.contract.MerchantContractStatusChangeEvent;
import com.wosai.databus.event.merchant.contract.MerchantContractWeixinAuthEvent;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.acquirer.AcquirerChangeDao;
import com.wosai.upay.job.mapper.ContractOpinionEventLogMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.WeiXinAuthEventLogMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.ContractOpinionEventLog;
import com.wosai.upay.job.model.DO.WeiXinAuthEventLog;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.refactor.event.ContractStatusChangeEvent;
import com.wosai.upay.job.util.JacksonHelperUtils;
import com.wosai.upay.job.volcengine.dataCenter.DataCenterProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: jerry
 * @date: 2019/4/15 09:53
 * @Description:数据总线事件处理类
 */
@Component
@Slf4j
public class DataBusBiz {

    @Autowired
    ContractStatusMapper contractStatusMapper;

    @Autowired
    MerchantService merchantService;

    @Autowired
    SensorSendBiz sensorSendBiz;

    @Autowired
    WeiXinAuthEventLogMapper authEventLogMapper;

    @Autowired
    ContractOpinionEventLogMapper contractOpinionEventLogMapper;

    @Autowired
    private TradeManageBiz tradeManageBiz;

    @Autowired
    private AcquirerChangeDao acquirerChangeDao;

    @Autowired
    private DataCenterProducer dataCenterProducer;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private JdBiz jdBiz;
    @Autowired
    @Qualifier("contractStatusThreadPoolTaskScheduler")
    private ThreadPoolTaskScheduler contractStatusThreadPoolTaskScheduler;

    private static final String MERCHANT_CONTRACT_TOPIC = "databus.event.merchant.contract.allin";

    private static final SerializeConfig config;

    static {
        config = new SerializeConfig();
        config.propertyNamingStrategy = PropertyNamingStrategy.SnakeCase;
    }

    public int insert(int status, String merchantSn, String msg, String acquirer) {
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        int pre;
        int statusChange;
        //新增商户入网时如果不支持白条也要通知支付
        if(Objects.equals(status, ContractStatus.STATUS_SUCCESS)) {
            jdBiz.openSuccess(merchantSn,acquirer);
        }
        if(Objects.equals(status, ContractStatus.STATUS_BIZ_FAIL)) {
            jdBiz.openFail(merchantSn,msg);
        }
        if (contractStatus == null) {
            ContractStatus contractStatusInsert = new ContractStatus().setMerchant_sn(merchantSn).setStatus(status);
            pre = ContractStatus.STATUS_PENDING;
            statusChange = contractStatusMapper.insertSelective(contractStatusInsert);
            contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
            //银行直连不发消息
        } else if (Objects.equals(contractStatus.getStatus(), ContractStatus.STATUS_SUCCESS)) {
            return 0;
        } else {
            pre = contractStatus.getStatus();
            Long version = contractStatus.getVersion();
            contractStatus.setStatus(status).setVersion(version + 1);
            if (WosaiStringUtils.isNotEmpty(acquirer)) {
                contractStatus.setAcquirer(acquirer);
            }
            //如果不同时出现status和acquirer就不允许更新contract_status
            //对于间连扫码入网A失败,然后发起仅入网B成功就会导致将contract_status表中的status改成了成功但是acquire还是A
            if(Objects.equals(pre, ContractStatus.STATUS_BIZ_FAIL)
                    && Objects.equals(status, ContractStatus.STATUS_SUCCESS)
                    && WosaiStringUtils.isEmpty(acquirer)) {
                return 0;
            }

            statusChange = contractStatusMapper.updateByPrimaryKeySelective(contractStatus);
        }
        // 切换收单机构不发消息
        if (acquirerChangeDao.getLatestUnFinishedApply(merchantSn) != null) {
            return 0;
        }
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantContractStatusChangeEvent merchantContractStatusChangeEvent = new MerchantContractStatusChangeEvent();
        merchantContractStatusChangeEvent.setPreStatus(pre);
        merchantContractStatusChangeEvent.setStatus(status);
        merchantContractStatusChangeEvent.setMsg(msg);
        merchantContractStatusChangeEvent.setMerchantSn(contractStatus.getMerchant_sn());
        merchantContractStatusChangeEvent.setMerchantId(merchantId);
        merchantContractStatusChangeEvent.setOperatorId("merchant-contract-job");
        merchantContractStatusChangeEvent.setOperatorPlatform("merchant-contract-job");
        changeMerchantPay(merchantSn, merchantId, status, pre);
        int logInsert = sendContractMsg(merchantContractStatusChangeEvent);
        if (status == ContractStatus.STATUS_SUCCESS || status == ContractStatus.STATUS_BIZ_FAIL) {
            sensorSendBiz.sendMessageToSensor(merchantId, merchantSn, status, msg);
            dataCenterProducer.publishProfile(merchantId, CollectionUtil.hashMap(
                    "pay_acquire", contractStatus.getAcquirer()
            ));
        }
        if (statusChange > 0 && logInsert > 0) {
            return 1;
        }
        return 0;
    }


    public int insert(int status, String merchantSn, String msg) {
        return this.insert(status, merchantSn, msg, null);
    }

    public void changeMerchantPay(String merchantSn, String merchantId, int status, int pre) {
        try {
            if (ContractStatus.STATUS_SUCCESS == status) {
                tradeManageBiz.openMerchantPay(merchantSn, merchantId);
            } else if (ContractStatus.STATUS_PENDING == pre) {
                tradeManageBiz.closeMerchantPay(merchantSn, merchantId);
            }
        } catch (Exception e) {
            // do nothing
        }
    }

    public int insertPayForEvent(String merchantSn, String merchantId, int status) {
        MerchantContractStatusChangeEvent merchantContractStatusChangeEvent = new MerchantContractStatusChangeEvent();
        merchantContractStatusChangeEvent.setPreStatus(ContractStatus.STATUS_PROCESS);
        merchantContractStatusChangeEvent.setStatus(status);
        merchantContractStatusChangeEvent.setMerchantSn(merchantSn);
        merchantContractStatusChangeEvent.setMerchantId(merchantId);
        merchantContractStatusChangeEvent.setOperatorId("merchant-contract-job");
        merchantContractStatusChangeEvent.setOperatorPlatform("merchant-contract-job");
        return sendContractMsg(merchantContractStatusChangeEvent);
    }

    public void sendProcessEvent(int status, String merchantSn, String msg){
        Map merchant = merchantService.getMerchantBySn(merchantSn);
        String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
        MerchantContractStatusChangeEvent merchantContractStatusChangeEvent = new MerchantContractStatusChangeEvent();
        merchantContractStatusChangeEvent.setPreStatus(ContractStatus.STATUS_PENDING);
        merchantContractStatusChangeEvent.setStatus(status);
        merchantContractStatusChangeEvent.setMsg(msg);
        merchantContractStatusChangeEvent.setMerchantSn(merchantSn);
        merchantContractStatusChangeEvent.setMerchantId(merchantId);
        merchantContractStatusChangeEvent.setOperatorId("merchant-contract-job");
        merchantContractStatusChangeEvent.setOperatorPlatform("merchant-contract-job");
        sendContractMsg(merchantContractStatusChangeEvent);
    }

    public int insertWeixinAuthEvent(MerchantContractWeixinAuthEvent event) {
        WeiXinAuthEventLog eventLog = new WeiXinAuthEventLog().setTs(System.currentTimeMillis()).setEvent(JSON.toJSONString(event, config).getBytes());
        return authEventLogMapper.insertSelective(eventLog);
    }

    @Transactional(rollbackFor = Exception.class)
    public int insertOpinionEvent(MerchantContractOpinionEvent merchantContractOpinionEvent) {
        ContractOpinionEventLog contractOpinionEventLog = new ContractOpinionEventLog().setTs(System.currentTimeMillis()).setEvent(JSON.toJSONString(merchantContractOpinionEvent, config).getBytes());
        return contractOpinionEventLogMapper.insert(contractOpinionEventLog);
    }

    private int sendContractMsg(MerchantContractStatusChangeEvent contractStatusChangeEvent) {
        AvroEventEntry avroEventEntry = new AvroEventEntry();
        avroEventEntry.setTs(System.currentTimeMillis());
        avroEventEntry.setEvent(ByteBuffer.wrap(JacksonHelperUtils.toJsonBytes(contractStatusChangeEvent)));
        contractStatusThreadPoolTaskScheduler.getScheduledExecutor().schedule(new Runnable() {
            @Override
            public void run() {
                kafkaTemplate.send(MERCHANT_CONTRACT_TOPIC, avroEventEntry);
                applicationEventPublisher.publishEvent(new ContractStatusChangeEvent(contractStatusChangeEvent));
            }
        }, 2, TimeUnit.SECONDS);
        return 1;
    }

}
