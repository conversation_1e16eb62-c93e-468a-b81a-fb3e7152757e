package com.wosai.upay.job.util;

import lombok.extern.slf4j.Slf4j;

import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: hrx
 * @Date: 2019-05-14
 * @Description: 字体加载工具类  需要将自己需要的字体拷贝到resources
 * @version: 1.0
 */
@Slf4j
public class FontUtil {

    private static final Map<String, Font> fontMap = new HashMap<>();

    public static synchronized Font getFont(String fontResourceName, int style, int size) {
        String key = String.format("%s-%d-%d", fontResourceName, style, size);
        Font font = fontMap.get(key);
        if (font != null) {
            return font;
        }
        font = doCreateFont(fontResourceName, style, size);
        fontMap.put(key, font);
        return font;
    }

    private static Font doCreateFont(String fontResourceName, int style, int size) {
        InputStream inputStream = null;
        try {
            inputStream = FontUtil.class.getResourceAsStream(fontResourceName);
            return Font.createFont(Font.TRUETYPE_FONT, inputStream).deriveFont(style, size);
        } catch (Exception e) {
            log.info("加载字体文件出错", e);
            throw new RuntimeException("加载字体文件出错，字体资源路径:" + fontResourceName);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("inputStream关闭异常:", e);
                }
            }
        }
    }

}
