package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.MemoApolloConfig;
import com.wosai.upay.job.biz.sceneManage.ErrorCodeManageBiz;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.AppModuleResponse;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import com.wosai.upay.job.model.ErrorInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
@Slf4j
@RestController
@RequestMapping("/module")
public class AppModuleController {

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private MerchantBusinessLicenseService licenseService;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MemoApolloConfig memoApolloConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${ccb_decp_wait_open}")
    private String waitOpenUrl;

    @Value("${ccb_decp_process_open}")
    private String processOpenUrl;

    @Value("${ccb_decp_success_open}")
    private String successOpenUrl;

    @Value("${ccb_decp_fail_open}")
    private String failOpenUrl;

    @Autowired
    private ErrorCodeManageBiz errorCodeManageBiz;

    @RequestMapping(path = "/ccbDecp", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public Map<String, Object> ccbDecp(@RequestBody Map<String, Object> params) {
        Map<String, Object> responese = new HashMap<String, Object>(1);
        String merchantId = BeanUtil.getPropString(params, "merchantId");
        if (WosaiStringUtils.isEmpty(merchantId)) {
            log.error("商户id不存在: {}", JSON.toJSONString(params));
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }
        // 如果有这个则表示不展示
        if (Boolean.TRUE.equals(redisTemplate.hasKey(String.format("decp_module:%s", merchantId)))) {
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }
        Map ccbDecpCity = applicationApolloConfig.getCcbDecpCity();
        if (WosaiMapUtils.isEmpty(ccbDecpCity)) {
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }
        List<String> idList = (List<String>) ccbDecpCity.get("idList");
        if (WosaiCollectionUtils.isNotEmpty(idList) && !idList.contains(merchantId)) {
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }

        // 兼容逻辑，兼容老的开通成功的商户
        List<String> ccbDecpOldIds = applicationApolloConfig.getCcbDecpOldIds();
        if (WosaiCollectionUtils.isNotEmpty(ccbDecpOldIds) && ccbDecpOldIds.contains(merchantId)) {
            responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.SUCCESS_OPEN).setSubpage(successOpenUrl));
            return responese;
        }

        Map merchant = merchantService.getMerchantByMerchantId(merchantId);
        if (WosaiMapUtils.isEmpty(merchant)) {
            log.error("商户不存在: {}", merchantId);
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }

        // 1 判断是否在城市白名单当中
        if (!ccbDecpCity.containsKey(BeanUtil.getPropString(merchant, Merchant.CITY)) && !ccbDecpCity.containsKey(BeanUtil.getPropString(merchant, Merchant.PROVINCE))) {
            redisTemplate.opsForValue().set(String.format("decp_module:%s", merchantId), merchantId, 1L, TimeUnit.HOURS);
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }
        // 2 判断商户类型是否是组织类型的
        Map<String, Object> license = licenseService.getBusinessLicenseByMerchantId(merchantId);
        if (WosaiMapUtils.isNotEmpty(license) && BeanUtil.getPropInt(license, MerchantBusinessLicence.TYPE) > 1) {
            redisTemplate.opsForValue().set(String.format("decp_module:%s", merchantId), merchantId, 1L, TimeUnit.HOURS);
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }
        // 3 判断商户是否有银行卡数据
        Map merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
        if (WosaiMapUtils.isEmpty(merchantBankAccount) || WosaiStringUtils.isEmpty(BeanUtil.getPropString(merchantBankAccount, MerchantBankAccount.IDENTITY))) {
            redisTemplate.opsForValue().set(String.format("decp_module:%s", merchantId), merchantId, 1L, TimeUnit.HOURS);
            responese.put("data", new AppModuleResponse().setVisible(false));
            return responese;
        }

        // 5 是否属于批量开通的商户
        SelfOpenCcbDecp selfOpenCcbDecp = selfOpenCcbDecpMapper.selectByMerchantSn(BeanUtil.getPropString(merchant, Merchant.SN));
        // 5.1 如果不是批量开通的商户
        if (selfOpenCcbDecp == null) {
            Map decpConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, PaywayEnum.DCEP.getValue());
            // 5.1.1 如果开通过其他通道的数字货币
            if (WosaiMapUtils.isNotEmpty(decpConfig)
                    && WosaiStringUtils.isNotEmpty(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))
                    && !SelfOpenCcbDecp.DECP_AGENTS.contains(BeanUtil.getPropString(decpConfig, MerchantConfig.B2C_AGENT_NAME))) {
                responese.put("data", new AppModuleResponse().setVisible(false));
                return responese;
            } else {
                // 5.1.2 没有开通过其他通道的数字货币
                responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.WAIT_OPEN).setSubpage(waitOpenUrl));
                return responese;
            }
        } else {
            if (selfOpenCcbDecp.getOpen_status() == SelfOpenCcbDecp.WAIT_OPEN_STATUS) {
                responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.WAIT_OPEN).setSubpage(waitOpenUrl));
                return responese;
            } else if (selfOpenCcbDecp.getOpen_status() == SelfOpenCcbDecp.PROCESS_OPEN_STATUS) {
                responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.PROCESS_OPEN).setSubpage(processOpenUrl));
                return responese;
            } else if (selfOpenCcbDecp.getOpen_status() == SelfOpenCcbDecp.SUCCESS_OPEN_STATUS) {
                responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.SUCCESS_OPEN).setSubpage(successOpenUrl));
                return responese;
            } else {
                responese.put("data", new AppModuleResponse().setVisible(true).setLetters(AppModuleResponse.FAIL_OPEN).setSubpage(getFailUrl(selfOpenCcbDecp.getResult())));
                return responese;
            }
        }
    }

    private String getFailUrl(String result) {
        final ErrorInfo errorInfo = errorCodeManageBiz.getPromptMessageFromErrorCodeManager("app_msg", result, ErrorCodeManageBiz.PLATFORM_CCB_DECP);
        return failOpenUrl + "&failMsg=" + errorInfo.getMsg() + "&failCode=" + errorInfo.getCode();
    }
}
