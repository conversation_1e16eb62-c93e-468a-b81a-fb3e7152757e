package com.wosai.upay.job.refactor.model.entity;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 进件报备规则组策略组合detail表表实体对象
 *
 * <AUTHOR>
 */
@TableName("group_combined_strategy_detail")
@Data
public class GroupCombinedStrategyDetailDO implements Comparable<GroupCombinedStrategyDetailDO> {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 收单组策略id，对应group_combined_strategy.id
     */
    @TableField(value = "group_strategy_id")
    private Long groupStrategyId;
    /**
     * 规则组id，对应mc_rule_group.group_id
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;
    /**
     * 报备规则组类型 1-主规则组 2-备用规则组
     */
    @TableField(value = "group_type")
    private Integer groupType;
    /**
     * 优先级
     */
    @TableField(value = "priority")
    private Integer priority;
    /**
     * 每日限制额度
     */
    @TableField(value = "daily_limit_quota")
    private Integer dailyLimitQuota;
    /**
     * 当日已使用额度
     */
    @TableField(value = "daily_usage_quota")
    private Integer dailyUsageQuota;
    /**
     * 负载比例
     */
    @TableField(value = "load_ratio")
    private BigDecimal loadRatio;
    /**
     * 有效状态 0-失效 1-生效
     */
    @TableField(value = "valid_status")
    private Integer validStatus;
    /**
     * 更新人
     */
    @TableField(value = "update_by")
    private String updateBy;
    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;
    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Timestamp updateTime;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Timestamp createTime;

    @Override
    public int compareTo(GroupCombinedStrategyDetailDO other) {
        Integer thisPriority = this.priority != null ? this.priority : Integer.MAX_VALUE;
        Integer otherPriority = other.priority != null ? other.priority : Integer.MAX_VALUE;

        if (this.groupType.equals(other.groupType)) {
            return thisPriority.compareTo(otherPriority);
        }
        return this.groupType.compareTo(other.groupType);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GroupCombinedStrategyDetailDO that = (GroupCombinedStrategyDetailDO) o;
        return StringUtils.equals(groupId, that.groupId) &&
                Objects.equals(groupStrategyId, that.groupStrategyId) &&
                Objects.equals(groupType, that.groupType) &&
                Objects.equals(validStatus, that.validStatus);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (groupId != null ? groupId.hashCode() : 0);
        return result;
    }

}

