package com.wosai.upay.job.model.DO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.wosai.upay.job.enume.WxUseType;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.CommonUtil;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

@Data
@Accessors(chain = true)
public class MerchantProviderParams {
    private String id;

    private String merchant_sn;

    private String out_merchant_sn;

    private String merchant_name;

    private String channel_no;

    private String parent_merchant_id;

    private Integer provider;

    private String provider_merchant_id;

    private Integer payway;

    private Integer params_config_status;

    private String pay_merchant_id;

    private String weixin_sub_appid;

    private String weixin_subscribe_appid;

    private String weixin_sub_mini_appid;

    private String weixin_receipt_appid;

    private Integer status;

    private Integer disable_status;

    private Long ctime;

    private Long mtime;

    private Boolean deleted;

    private Long version;

    private String contract_rule;

    private String rule_group_id;

    private Integer update_status;

    private byte[] extra;

    private Integer auth_status;

    private String wx_settlement_id;

    private Integer wx_use_type;

    private String ali_mcc;

    private Integer merchant_state;

    /**
     * 是否禁用
     *
     * @return true-禁用
     */
    public boolean paramsIsDisabled() {
        return Objects.equals(disable_status, DisableStatusEnum.DISABLE.getValue());
    }

    /**
     * 维护禁用状态
     * 统一使用access服务端维护
     *
     * @param disable_status 禁用状态
     */
    @Deprecated
    public void setDisable_status(Integer disable_status) {
        this.disable_status = disable_status;
    }

    public MerchantProviderParamsDto toProviderParamsModule() {
        MerchantProviderParamsDto merchantProviderParamsDto = new MerchantProviderParamsDto();
        BeanUtils.copyProperties(this, merchantProviderParamsDto);
        merchantProviderParamsDto.setExtra(CommonUtil.bytes2Map(this.getExtra()));
        return merchantProviderParamsDto;
    }

    public MerchantProviderParams toMerchantProviderParams(MerchantProviderParamsDto merchantProviderParamsDto) {
        MerchantProviderParams merchantProviderParams = new MerchantProviderParams();
        BeanUtils.copyProperties( merchantProviderParamsDto,merchantProviderParams);
        merchantProviderParams.setExtra(CommonUtil.map2Bytes(merchantProviderParamsDto.getExtra()));
        return merchantProviderParams;
    }

    @JsonIgnore
    public boolean isOnlineParams() {
        return WxUseType.ONLINE_PAYMENT.getCode().equals(wx_use_type) || WxUseType.SCENARIO_PAYMENT.getCode().equals(wx_use_type);
    }

    public static final int PARAMS_CONFIG_STATUS_PRE = 0;       //未配置
    public static final int PARAMS_CONFIG_STATUS_SUCCESS = 1;   //配置成功
    public static final int PARAMS_CONFIG_STATUS_NULL = 2;      //不需要配置
    public static final int PARAMS_CONFIG_STATUS_FAIL = 3;      //最近一次配置失败

    public static final int GOLD_STATUS_PRE = 0;       //未配置
    public static final int GOLD_STATUS_SUCCESS = 2;       //配置成功
    public static final int GOLD_STATUS_FAIL = 3;       //配置失败
    public static final int GOLD_STATUS_CLOSE = 4;     //手动关闭
    private Integer gold_status;

    public static final int AUTH_STATUS_AUTHED = 1;
    public static final int AUTH_STATUS_NOAUTHED = 0;

}