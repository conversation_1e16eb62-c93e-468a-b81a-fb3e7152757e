package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.DO.TaskMch;
import com.wosai.upay.job.model.MchAuthApply;
import com.wosai.upay.job.model.MchAuthInfo;
import com.wosai.upay.job.model.WeixinParamsUpdateApply;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by hzq on 19/8/26.
 */
@Service
@Slf4j
public class WeixinAuthApplyBiz {

    @Autowired
    MchAuthApplyMapper mchAuthApplyMapper;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    TaskMchMapper taskMchMapper;

    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;

    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    public MchAuthInfo getMchAuthApplyByTaskId(Long taskId) {
        MchAuthApply apply;
        TaskMch taskMch = taskMchMapper.selectTaskMchByTaskId(taskId);
        if (Objects.nonNull(taskMch) && Objects.nonNull(taskMch.getAuth_apply_id()) && 0L != taskMch.getAuth_apply_id()) {
            apply = mchAuthApplyMapper.selectByPrimaryKey(taskMch.getAuth_apply_id());
        } else {
            apply = mchAuthApplyMapper.selectByTaskId(taskId);
        }
        if (Objects.isNull(apply)) {
            return null;
        }
        MchAuthInfo mchAuthInfo = new MchAuthInfo(apply);
        if (Objects.nonNull(taskMch)) {
            mchAuthInfo.setMerchantSn(taskMch.getMerchant_sn())
                    .setTaskPayMerchantId(taskMch.getPay_merchant_id())
                    .setTaskId(taskMch.getTask_id());
        }

        return mchAuthInfo;
    }

    /**
     * 根据微信子商户号查询曾经的关联关系
     * 备注：需要整理查询是否为三期上线前未记录数据的商户
     *
     * @param payMchId
     * @return null->通过我司申请单绑定
     */
    public List<TaskMch> getTaskMchByPayMchId(String payMchId) {
        return taskMchMapper.selectByPayMerchantId(payMchId);
    }

    public TaskMch getTaskMchByTaskId(Long taskId) {
        return taskMchMapper.selectTaskMchByTaskId(taskId);
    }

    /**
     * 实名认证成功时 写入认证成功状态: merchant_provider_params: extra->auth_time写入  auth_status = 1
     * mch_auth_apply: pay_merchant_id 写入子商户号
     * 取消时 删除认证成功状态: merchant_provider_params: extra->auth_time删除  auth_status = 0
     * mch_auth_apply: pay_merchant_id 删除子商户号
     *
     * @param payMerchantId 微信子商户号
     * @param mchId         mch_auth_apply id
     * @param type          true->认证  false->取消认证
     */
    public void updateAuthStatus(String payMerchantId, Long mchId, Boolean type) {
        MerchantProviderParamsDto merchantProviderParamsDto = merchantProviderParamsMapper.getByPayMerchantId(payMerchantId).toProviderParamsModule();
        if (type) {
            merchantProviderParamsDto.getExtra().put(WeixinParamsUpdateApply.AUTH_TIME, System.currentTimeMillis());
            merchantProviderParamsDto.setAuth_status(PayMchAuthStatusEnum.YES.getValue());
        } else {
            merchantProviderParamsDto.getExtra().remove(WeixinParamsUpdateApply.AUTH_TIME);
            merchantProviderParamsDto.setAuth_status(PayMchAuthStatusEnum.NOT.getValue());
        }
        merchantProviderParamsMapper.updateByPrimaryKeySelective(merchantProviderParamsBiz.fromDO(merchantProviderParamsDto));
        if (Objects.nonNull(mchId)) {
            MchAuthApply mchAuthApply = mchAuthApplyMapper.selectByPrimaryKey(mchId);
            Map<String, List<String>> map = mchAuthApply.getPayMerchantId();
            String rule = composeAcquirerBiz.getMerchantAcquirer(merchantProviderParamsDto.getMerchant_sn());
            //银行业务特殊处理
            if (Objects.equals(merchantProviderParamsDto.getProvider(), ProviderEnum.PROVIDER_PSBC.getValue())) {
                rule = "psbc";
            }
            Boolean contain = false;
            List<String> ids = new ArrayList<>();
            if (!MapUtils.isEmpty(map)) {
                List<String> oriIds = map.get(rule);
                if (!StringUtil.listEmpty(oriIds)) {
                    ids = oriIds;
                    contain = ids.contains(payMerchantId);
                }
            }
            Boolean change = false;
            if (type && !contain) {
                ids.add(payMerchantId);
                change = true;
            } else if (!type && contain) {
                ids.remove(payMerchantId);
                change = true;
            }
            if (change) {
                map.put(rule, ids);
                mchAuthApplyMapper.updatePayMerchantId(mchAuthApply.getId(), JSON.toJSONString(map));
            }
        }
    }
}
