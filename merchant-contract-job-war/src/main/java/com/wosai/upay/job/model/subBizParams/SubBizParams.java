package com.wosai.upay.job.model.subBizParams;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
@Accessors(chain = true)
public class SubBizParams {
    private Long id;

    private String merchant_sn;

    private String trade_app_id;

    private Integer provider;

    private Date create_at;

    private Date update_at;

    private Boolean deleted;

    private String extra;


    public Map<String, Object> getExtraMap() {
        return JSONObject.parseObject(extra,Map.class);
    }
}