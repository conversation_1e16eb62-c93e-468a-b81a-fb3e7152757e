package com.wosai.upay.job.model.DO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class McAcquirerChangeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public McAcquirerChangeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andApply_idIsNull() {
            addCriterion("apply_id is null");
            return (Criteria) this;
        }

        public Criteria andApply_idIsNotNull() {
            addCriterion("apply_id is not null");
            return (Criteria) this;
        }

        public Criteria andApply_idEqualTo(String value) {
            addCriterion("apply_id =", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idNotEqualTo(String value) {
            addCriterion("apply_id <>", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idGreaterThan(String value) {
            addCriterion("apply_id >", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idGreaterThanOrEqualTo(String value) {
            addCriterion("apply_id >=", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idLessThan(String value) {
            addCriterion("apply_id <", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idLessThanOrEqualTo(String value) {
            addCriterion("apply_id <=", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idLike(String value) {
            addCriterion("apply_id like", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idNotLike(String value) {
            addCriterion("apply_id not like", value, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idIn(List<String> values) {
            addCriterion("apply_id in", values, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idNotIn(List<String> values) {
            addCriterion("apply_id not in", values, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idBetween(String value1, String value2) {
            addCriterion("apply_id between", value1, value2, "apply_id");
            return (Criteria) this;
        }

        public Criteria andApply_idNotBetween(String value1, String value2) {
            addCriterion("apply_id not between", value1, value2, "apply_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIsNull() {
            addCriterion("merchant_sn is null");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIsNotNull() {
            addCriterion("merchant_sn is not null");
            return (Criteria) this;
        }

        public Criteria andMerchant_snEqualTo(String value) {
            addCriterion("merchant_sn =", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotEqualTo(String value) {
            addCriterion("merchant_sn <>", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snGreaterThan(String value) {
            addCriterion("merchant_sn >", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_sn >=", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLessThan(String value) {
            addCriterion("merchant_sn <", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLessThanOrEqualTo(String value) {
            addCriterion("merchant_sn <=", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snLike(String value) {
            addCriterion("merchant_sn like", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotLike(String value) {
            addCriterion("merchant_sn not like", value, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snIn(List<String> values) {
            addCriterion("merchant_sn in", values, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotIn(List<String> values) {
            addCriterion("merchant_sn not in", values, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snBetween(String value1, String value2) {
            addCriterion("merchant_sn between", value1, value2, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_snNotBetween(String value1, String value2) {
            addCriterion("merchant_sn not between", value1, value2, "merchant_sn");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIsNull() {
            addCriterion("merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIsNotNull() {
            addCriterion("merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andMerchant_idEqualTo(String value) {
            addCriterion("merchant_id =", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotEqualTo(String value) {
            addCriterion("merchant_id <>", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idGreaterThan(String value) {
            addCriterion("merchant_id >", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idGreaterThanOrEqualTo(String value) {
            addCriterion("merchant_id >=", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLessThan(String value) {
            addCriterion("merchant_id <", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLessThanOrEqualTo(String value) {
            addCriterion("merchant_id <=", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idLike(String value) {
            addCriterion("merchant_id like", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotLike(String value) {
            addCriterion("merchant_id not like", value, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idIn(List<String> values) {
            addCriterion("merchant_id in", values, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotIn(List<String> values) {
            addCriterion("merchant_id not in", values, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idBetween(String value1, String value2) {
            addCriterion("merchant_id between", value1, value2, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andMerchant_idNotBetween(String value1, String value2) {
            addCriterion("merchant_id not between", value1, value2, "merchant_id");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerIsNull() {
            addCriterion("source_acquirer is null");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerIsNotNull() {
            addCriterion("source_acquirer is not null");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerEqualTo(String value) {
            addCriterion("source_acquirer =", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerNotEqualTo(String value) {
            addCriterion("source_acquirer <>", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerGreaterThan(String value) {
            addCriterion("source_acquirer >", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerGreaterThanOrEqualTo(String value) {
            addCriterion("source_acquirer >=", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerLessThan(String value) {
            addCriterion("source_acquirer <", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerLessThanOrEqualTo(String value) {
            addCriterion("source_acquirer <=", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerLike(String value) {
            addCriterion("source_acquirer like", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerNotLike(String value) {
            addCriterion("source_acquirer not like", value, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerIn(List<String> values) {
            addCriterion("source_acquirer in", values, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerNotIn(List<String> values) {
            addCriterion("source_acquirer not in", values, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerBetween(String value1, String value2) {
            addCriterion("source_acquirer between", value1, value2, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andSource_acquirerNotBetween(String value1, String value2) {
            addCriterion("source_acquirer not between", value1, value2, "source_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerIsNull() {
            addCriterion("target_acquirer is null");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerIsNotNull() {
            addCriterion("target_acquirer is not null");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerEqualTo(String value) {
            addCriterion("target_acquirer =", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerNotEqualTo(String value) {
            addCriterion("target_acquirer <>", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerGreaterThan(String value) {
            addCriterion("target_acquirer >", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerGreaterThanOrEqualTo(String value) {
            addCriterion("target_acquirer >=", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerLessThan(String value) {
            addCriterion("target_acquirer <", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerLessThanOrEqualTo(String value) {
            addCriterion("target_acquirer <=", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerLike(String value) {
            addCriterion("target_acquirer like", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerNotLike(String value) {
            addCriterion("target_acquirer not like", value, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerIn(List<String> values) {
            addCriterion("target_acquirer in", values, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerNotIn(List<String> values) {
            addCriterion("target_acquirer not in", values, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerBetween(String value1, String value2) {
            addCriterion("target_acquirer between", value1, value2, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andTarget_acquirerNotBetween(String value1, String value2) {
            addCriterion("target_acquirer not between", value1, value2, "target_acquirer");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andMemoIsNull() {
            addCriterion("memo is null");
            return (Criteria) this;
        }

        public Criteria andMemoIsNotNull() {
            addCriterion("memo is not null");
            return (Criteria) this;
        }

        public Criteria andMemoEqualTo(String value) {
            addCriterion("memo =", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotEqualTo(String value) {
            addCriterion("memo <>", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThan(String value) {
            addCriterion("memo >", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoGreaterThanOrEqualTo(String value) {
            addCriterion("memo >=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThan(String value) {
            addCriterion("memo <", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLessThanOrEqualTo(String value) {
            addCriterion("memo <=", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoLike(String value) {
            addCriterion("memo like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotLike(String value) {
            addCriterion("memo not like", value, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoIn(List<String> values) {
            addCriterion("memo in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotIn(List<String> values) {
            addCriterion("memo not in", values, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoBetween(String value1, String value2) {
            addCriterion("memo between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andMemoNotBetween(String value1, String value2) {
            addCriterion("memo not between", value1, value2, "memo");
            return (Criteria) this;
        }

        public Criteria andCreate_atIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreate_atIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreate_atEqualTo(Date value) {
            addCriterion("create_at =", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atGreaterThan(Date value) {
            addCriterion("create_at >", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atLessThan(Date value) {
            addCriterion("create_at <", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atIn(List<Date> values) {
            addCriterion("create_at in", values, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "create_at");
            return (Criteria) this;
        }

        public Criteria andCreate_atNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "create_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdate_atEqualTo(Date value) {
            addCriterion("update_at =", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atGreaterThan(Date value) {
            addCriterion("update_at >", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atLessThan(Date value) {
            addCriterion("update_at <", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atIn(List<Date> values) {
            addCriterion("update_at in", values, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "update_at");
            return (Criteria) this;
        }

        public Criteria andUpdate_atNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "update_at");
            return (Criteria) this;
        }
        public Criteria andImmediatelyIsNull() {
            addCriterion("immediately is null");
            return (Criteria) this;
        }

        public Criteria andImmediatelyIsNotNull() {
            addCriterion("immediately is not null");
            return (Criteria) this;
        }

        public Criteria andImmediatelyEqualTo(Boolean value) {
            addCriterion("immediately =", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyNotEqualTo(Boolean value) {
            addCriterion("immediately <>", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyGreaterThan(Boolean value) {
            addCriterion("immediately >", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyGreaterThanOrEqualTo(Boolean value) {
            addCriterion("immediately >=", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyLessThan(Boolean value) {
            addCriterion("immediately <", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyLessThanOrEqualTo(Boolean value) {
            addCriterion("immediately <=", value, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyIn(List<Boolean> values) {
            addCriterion("immediately in", values, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyNotIn(List<Boolean> values) {
            addCriterion("immediately not in", values, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyBetween(Boolean value1, Boolean value2) {
            addCriterion("immediately between", value1, value2, "immediately");
            return (Criteria) this;
        }

        public Criteria andImmediatelyNotBetween(Boolean value1, Boolean value2) {
            addCriterion("immediately not between", value1, value2, "immediately");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}