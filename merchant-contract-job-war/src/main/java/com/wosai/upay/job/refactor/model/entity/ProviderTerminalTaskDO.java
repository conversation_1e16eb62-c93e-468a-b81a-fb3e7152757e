package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 收单机构终端报备任务表表实体对象
 *
 * <AUTHOR>
 */
@TableName("provider_terminal_task")
@Data
public class ProviderTerminalTaskDO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商户号
     */
    @TableField(value = "merchant_sn")
    private String merchantSn;
    /**
     * 类型 1新增子商户号绑定所有终端 2新增终端绑定所有子商户号
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 任务上下文
     */
    @TableField(value = "context")
    private String context;
    /**
     * 状态 0未处理  1处理中  2处理成功 3处理失败
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 重试次数
     */
    @TableField(value = "retry")
    private Integer retry;
    /**
     * 备注
     */
    @TableField(value = "result")
    private String result;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;

    @TableField(value = "priority")
    private Timestamp priority;


}

