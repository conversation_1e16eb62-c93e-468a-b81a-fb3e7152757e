package com.wosai.upay.job.model.guotong;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.shouqianba.cua.enums.core.PaywayEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/5
 */
@Data
public class GuotongReportCallbackModel {
    /**
     * 机构号
     */
    @JSONField(name = "AGET_ID")
    private String agetId;
    /**
     * 商户号
     */
    @JSONField(name = "CUST_ID")
    private String custId;
    /**
     * 商户名称
     */
    @JSONField(name = "BUS_NAME")
    private String busName;
    /**
     * 支付通道 01银联微信、 02银联支付宝、 12收单刷卡、 13银联扫码（云闪付）
     */
    @JSONField(name = "PAY_WAY")
    private String payWay;
    /**
     * 子商户号
     */
    @JSONField(name = "MERCHNT_NO")
    private String MERCHNT_NO;
    /**
     * 通道报备状态
     * 00-初始 01-报备成功 02-报备失败
     */
    @JSONField(name = "CHANEL_BAK_STATUS")
    private String chanelBakStatus;
    /**
     * 收单刷卡实际报备状态
     * 00-初始 01-报备成功 02-报备失败 03-报备审核中
     */
    @JSONField(name = "YL_BAK_STATUS")
    private String ylBakStatus;
    /**
     * 银联商户状态
     * 00-冻结 01-启用 02-注销。（指商户在银联的商户状态。 针对银联云闪付/银联收单刷卡 通道）
     */
    @JSONField(name = "MERC_STATUS")
    private String mercStatus;
    /**
     * 报备时间 yyyy-MM-dd HH:mm:ss
     */
    @JSONField(name = "BAK_TIME")
    private String bakTime;
    /**
     * 报备结果描述
     */
    @JSONField(name = "BAK_RESULT")
    private String bakResult;
    private String sign;

    public PaywayEnum transferPayway() {
        switch (payWay) {
            case "01":
                return PaywayEnum.WEIXIN;
            case "02":
                return PaywayEnum.ALIPAY;
            case "12":
                return PaywayEnum.BANK_CARD;
            case "13":
                return PaywayEnum.UNIONPAY;
            default:
                return null;
        }
    }
}
