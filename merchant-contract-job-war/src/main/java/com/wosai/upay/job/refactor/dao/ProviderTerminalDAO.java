package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.ProviderTerminalDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalDO;

import javax.annotation.Resource;
import java.util.List;


/**
 * 收单机构终端表表数据库访问层 {@link ProviderTerminalDO}
 * 对ProviderTerminalMapper层做出简单封装 {@link ProviderTerminalDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class ProviderTerminalDAO extends AbstractBaseDAO<ProviderTerminalDO, ProviderTerminalDynamicMapper> {

    public ProviderTerminalDAO(SqlSessionFactory sqlSessionFactory, ProviderTerminalDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和收单机构对应的provider获取终端列表
     *
     * @param merchantSn 商户号
     * @param provider   收单机构对应的provider
     * @return 终端列表
     */
    public List<ProviderTerminalDO> listByMerchantSnAndProvider(String merchantSn, String provider) {
        LambdaQueryWrapper<ProviderTerminalDO> query = new LambdaQueryWrapper<>();
        query.eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider);
        return entityMapper.selectList(query);
    }

    /**
     * 根据商户号和收单机构对应的provider删除终端参数
     *
     * @param merchantSn    商户号
     */
    public void deleteByMerchantSnAndProviders(String merchantSn, List<String> providers) {
        if (StringUtils.isBlank(merchantSn) || CollectionUtils.isEmpty(providers)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<ProviderTerminalDO>()
                .eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .in(ProviderTerminalDO::getProvider, providers));
    }

    public void deleteTerminalLevelProviderTerminal(String merchantSn, String provider) {
        entityMapper.delete(new LambdaQueryWrapper<ProviderTerminalDO>()
                .eq(ProviderTerminalDO::getMerchantSn, merchantSn)
                .eq(ProviderTerminalDO::getProvider, provider)
                .isNotNull(ProviderTerminalDO::getTerminalSn));
    }


}
