package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_MSG;
import static com.wosai.upay.job.constant.CallBackConstants.CONTRACT_CALLBACK_TIME;

/**
 * @Description: 银联商务业务处理
 * <AUTHOR>
 * @Date: 2021/3/16 4:34 下午
 */
@Component
@Slf4j
public class UmsCallBackBiz {
    @Autowired
    private CallBackBiz callBackBiz;
    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    /**
     * 处理银联商务 商户进件 结果
     *
     * @param contractResponse
     * @param contractSubTask
     * @return
     */
    public Boolean handUmsMerchantContractResult(ContractResponse contractResponse, ContractSubTask contractSubTask) {
        //结果处理
        if (200 == contractResponse.getCode()) {
            //成功 更新任务状态 成功
            updateSubTaskStatus(contractSubTask, true, contractResponse.getResponseParam(), "银联商务商户进件成功");
        } else if (400 == contractResponse.getCode()) {
            log.warn("查询ums进件状态 业务异常 contractId:{},merchantSn:{}, message:{}, request:{}, resp:{}",
                    contractSubTask.getContract_id(), contractSubTask.getMerchant_sn(), contractResponse.getMessage(), contractResponse.getRequestParam(), contractResponse.getResponseParam());
            //失败
            updateSubTaskStatus(contractSubTask, false, contractResponse.getResponseParam(), contractResponse.getMessage());
        } else {
            //系统异常
            log.error("查询ums进件状态 系统异常 contractId:{},merchantSn:{},  message {} resp {} resq {}",
                    contractSubTask.getContract_id(), contractSubTask.getMerchant_sn(), contractResponse.getMessage(), contractResponse.getResponseParam(), contractResponse.getRequestParam());
        }

        return true;
    }


    /**
     * 更新任务状态
     *
     * @param contractSubTask
     * @param isSuccess
     * @param callbackMsg
     * @param errorMessage
     */
    public void updateSubTaskStatus(ContractSubTask contractSubTask, boolean isSuccess, Map callbackMsg, String errorMessage) {
        Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        callbackMsg.put(CONTRACT_CALLBACK_TIME, System.currentTimeMillis());
        ArrayList<Map> callBack = Lists.newArrayList(callbackMsg);
        if (resp == null) {
            resp = new HashMap();
        }
        resp.put(CONTRACT_CALLBACK_MSG, callBack);

        String result = "银联商务回调失败";
        int resultStatus = TaskStatus.FAIL.getVal();
        if (isSuccess) {
            result = "银联商务回调成功";
            resultStatus = TaskStatus.SUCCESS.getVal();
        }
        int updateResult = contractSubTaskMapper.setSubTaskResult(TaskStatus.PROGRESSING.getVal(), resultStatus, JSON.toJSONString(resp), result, contractSubTask.getId());
        if (updateResult < 1) {
            return;
        }
        callBackBiz.handleAfterSubTask(contractSubTask, isSuccess, errorMessage);

    }

}
