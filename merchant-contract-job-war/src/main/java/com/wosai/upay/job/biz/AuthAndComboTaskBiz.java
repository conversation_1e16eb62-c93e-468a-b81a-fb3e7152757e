package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.request.ApplyFeeRateRequest;
import com.wosai.upay.job.Constants.AuthAndComboTaskConstant;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.avro.AuthAndComboStatusChange;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/7
 */
@Slf4j
@Component
public class AuthAndComboTaskBiz {

    @Autowired
    private ChangeTradeParamsBiz tradeParamsBiz;
    @Autowired
    private SubBizParamsBiz subBizParamsBiz;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private FeeRateService feeRateService;

    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Autowired
    private AuthAndComboTaskMapper authAndComboTaskMapper;

    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    public boolean preHandle(AuthAndComboTask task) {
        MerchantProviderParams providerParams = merchantProviderParamsMapper.selectByPayMerchantId(task.getSub_mch_id());
        if (UseStatusEnum.IN_USE.getValue().equals(providerParams.getStatus())) {
            applyNewCombo(task);
            return true;
        }
        MerchantProviderParams useWeiXinParam = merchantProviderParamsMapper.getUseWeiXinParam(task.getMerchant_sn());
        if (useWeiXinParam == null) {
            changeTradeParamsAndApplyCombo(task, providerParams);
            return true;
        } else {
            Boolean authStatus = getAuthStatus(useWeiXinParam);
            if (authStatus != null && !authStatus) {
                //富友这里不需要考虑切子商号+费率修改
                if (!Objects.equals(useWeiXinParam.getProvider(), ProviderEnum.PROVIDER_FUYOU.getValue())) {
                    changeTradeParamsAndApplyCombo(task, providerParams);
                }
                return true;
            }
        }
        return false;
    }

    private Boolean getAuthStatus(MerchantProviderParams useWeiXinParam) {
        try {
            return wechatAuthBiz.getAuthStatus(useWeiXinParam, useWeiXinParam.getProvider());
        } catch (com.wosai.upay.common.exception.CommonPubBizException e) {
            // 有些channel里的auth_v3_param不存在了，查询子商户授权状态会报错，这种情况任务未授权
            if (WosaiStringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("不能为空")) {
                return false;
            }
            throw e;
        }
    }

    public void changeTradeParamsAndApplyCombo(AuthAndComboTask task, MerchantProviderParams params) {
        try {
            // 独立行业变更这个时候还没有写回原表,所以切参数时候不校验结算ID是否匹配
            ThreadLocalUtil.setCheckSettlement(false);
            changeTradeParams(params);
            applyNewCombo(task);
        } finally {
            ThreadLocalUtil.clearCheckSettlement();
        }
    }

    private void changeTradeParams(MerchantProviderParams params) {
        boolean isChangedTradeParams = tradeParamsBiz.changeTradeParams(params, null, Boolean.FALSE, subBizParamsBiz.getPayTradeAppId());
        if (!isChangedTradeParams) {
            throw new CommonPubBizException("切换交易参数失败");
        }
    }

    public void applyNewCombo(AuthAndComboTask task) {
        applyNewCombo(task, "授权完成切换套餐");
    }


    public void applyFuYouNewCombo(AuthAndComboTask task) {
        if (StringUtils.isEmpty(task.getForm_body())) {
            throw new CommonPubBizException("商户Sn" + task.getMerchant_sn() +"费率信息不能为空");
        }
        applyNewCombo(task, "富友行业变更套餐修改");
    }

    private void applyNewCombo(AuthAndComboTask task, String message) {
        Map formBody = JSON.parseObject(task.getForm_body(), Map.class);
        if (needApplyNewCombo(formBody)) {
            long tradeComboId = BeanUtil.getPropLong(formBody, AuthAndComboTaskConstant.NEW_COMBO_ID);
            List<Map<String, Object>> config = (List<Map<String, Object>>) formBody.get(AuthAndComboTaskConstant.MERCHANT_CONFIG);
            String source = BeanUtil.getPropString(formBody, AuthAndComboTaskConstant.SOURCE);

            Map<String, String> applyFeeRateMap = CommonUtil.buildApplyFeeRateMap(config);
            // 独立行业变更只去设置微信的费率信息
            if (AuthAndComboTaskConstant.CHANGE_INDUSTRY_SOURCE.equals(source)) {
                applyFeeRateMap = CollectionUtil.hashMap(String.valueOf(PaywayEnum.WEIXIN.getValue()), applyFeeRateMap.get(String.valueOf(PaywayEnum.WEIXIN.getValue())));
            }
            ApplyFeeRateRequest applyFeeRateRequest = new ApplyFeeRateRequest()
                    .setMerchantSn(task.getMerchant_sn())
                    .setTradeComboId(tradeComboId)
                    .setAuditSn(message)
                    .setApplyPartialPayway(Boolean.TRUE)
                    .setApplyFeeRateMap(applyFeeRateMap);
            feeRateService.applyFeeRateOne(applyFeeRateRequest);
        }
    }

    private static final String AUTH_AND_COMBO_TOPIC = "events.cua.merchant-contract-job.auth-and-combo";

    public void changeStatusAndSendMessage(AuthAndComboTask task, int status, String result) {
        authAndComboTaskMapper.updateByPrimaryKeySelective(new AuthAndComboTask().setId(task.getId()).setStatus(status).setResult(result));
        Map bizParams = JSON.parseObject(task.getForm_body(), Map.class);

        int afterStatus;
        if (status == MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED) {
            afterStatus = MerchantChangeDataConstant.COMMON_STATUS_SUCCESS;
        } else if (status == MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL) {
            afterStatus = MerchantChangeDataConstant.COMMON_STATUS_FAIL;
        } else {
            afterStatus = MerchantChangeDataConstant.COMMON_STATUS_PROCESS;
        }
        AuthAndComboStatusChange statusChange = new AuthAndComboStatusChange();
        statusChange.setMerchantSn(task.getMerchant_sn());
        statusChange.setMerchantId(task.getMerchant_id());
        statusChange.setPreStatus(task.getStatus());
        statusChange.setStatus(afterStatus);
        statusChange.setMessage(result == null ? "" : result);
        statusChange.setSource(MapUtils.getString(bizParams, AuthAndComboTaskConstant.SOURCE, ""));
        log.info("sendAuthAndCombo : {}", statusChange);
        kafkaTemplate.send(AUTH_AND_COMBO_TOPIC, statusChange);
    }

    public boolean needApplyNewCombo(Map formBody) {
        return formBody.get(AuthAndComboTaskConstant.NEW_COMBO_ID) != null && WosaiCollectionUtils.isNotEmpty((List<Map<String, Object>>) formBody.get(AuthAndComboTaskConstant.MERCHANT_CONFIG));
    }
}
