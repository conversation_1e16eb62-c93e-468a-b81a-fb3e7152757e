package com.wosai.upay.job.refactor.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shouqianba.cua.enums.core.DeleteStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import com.wosai.upay.job.refactor.mapper.SubBizParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.SubBizParamsDO;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;


/**
 * 子业务交易参数记录表表数据库访问层 {@link SubBizParamsDO}
 * 对SubBizParamsMapper层做出简单封装 {@link SubBizParamsDynamicMapper}
 *
 * <AUTHOR>
 */
@Repository
public class SubBizParamsDAO extends AbstractBaseDAO<SubBizParamsDO, SubBizParamsDynamicMapper> {

    public SubBizParamsDAO(SqlSessionFactory sqlSessionFactory, SubBizParamsDynamicMapper entityMapper) {
        super(sqlSessionFactory, entityMapper);
    }

    /**
     * 根据商户号和支付方式获取子业务交易参数
     *
     * @param merchantSn 商户号
     * @param provider   支付方式
     * @return 子业务交易参数
     */
    public List<SubBizParamsDO> listByMerchantSnAndProvider(String merchantSn, String provider) {
        LambdaQueryWrapper<SubBizParamsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SubBizParamsDO::getMerchantSn, merchantSn);
        wrapper.eq(SubBizParamsDO::getProvider, provider);
        wrapper.eq(SubBizParamsDO::getDeleted, DeleteStatusEnum.NO_DELETED.getValue());
        return entityMapper.selectList(wrapper);
    }

    /**
     * 根据商户号和支付方式获取子业务交易参数
     *
     * @param merchantSn 商户号
     * @param provider   provider
     * @param appId      业务方
     */
    public void deleteByMerchantSnAndProviderAndAppId(String merchantSn, String provider, String appId) {
        if (StringUtils.isBlank(merchantSn) || StringUtils.isBlank(provider) || StringUtils.isBlank(appId)) {
            return;
        }
        entityMapper.delete(new LambdaQueryWrapper<SubBizParamsDO>()
                .eq(SubBizParamsDO::getMerchantSn, merchantSn)
                .eq(SubBizParamsDO::getProvider, provider)
                .eq(SubBizParamsDO::getTradeAppId, appId));
    }
}
