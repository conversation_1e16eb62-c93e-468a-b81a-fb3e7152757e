package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSONObject;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.AppInfoEvent;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.DataBusAppInfo;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.events.AppInfoInsertEvent;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.events.AppInfoUpdateEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import com.wosai.sales.merchant.business.model.AppInfoModel;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * @Description: 监听crm业务开通平台发出的所有消息
 * <AUTHOR>
 * @Date: 2023/1/3 2:33 下午
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class AppInfoConsumer extends AbstractDataBusConsumer {
    @Autowired
    SubBizParamsBiz subBizParamsBiz;
    @Autowired
    MerchantService merchantService;

    @KafkaListener(topics = {AppInfoEvent.TOPIC_NAME}, containerFactory = "smartKafkaListenerContainerFactory")
    @Transactional(rollbackFor = Exception.class)
    public void consume(ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    protected void doHandleEvent(AbstractEvent event) {
        if (!(event instanceof AppInfoUpdateEvent || event instanceof AppInfoInsertEvent)) {
            return;
        }
        //开通业务
        log.info("start handling appInfoEvent : {}", JSONObject.toJSONString(event));
        DataBusAppInfo data = new DataBusAppInfo();
        if (event instanceof AppInfoUpdateEvent) {
            AppInfoUpdateEvent appInfoUpdateEvent = (AppInfoUpdateEvent) event;
            data = appInfoUpdateEvent.getAfter();
        } else if (event instanceof AppInfoInsertEvent) {
            AppInfoInsertEvent appInfoInsertEvent = (AppInfoInsertEvent) event;
            data = appInfoInsertEvent.getData();
        }
        log.info("start handling smart detail appId:{},merchantId:{},storeId:{},data:{}", data.getAppId(), data.getMerchantId(), data.getStoreId(), JSONObject.toJSONString(data));
        //开通智慧经营并设置费率
        try {
            Integer status = data.getStatus();
            if (!Objects.equals(AppInfoModel.STATUS_SUCCESS, status)) {
                log.info("appInfoEvent,商户id:{},业务未开通成功 ", data.getMerchantId());
                return;
            }
            subBizParamsBiz.openSmart(data);
        } catch (Exception e) {
            //商户信息
            String merchantId = data.getMerchantId();
            MerchantInfo merchant = merchantService.getMerchantById(merchantId, null);
            String merchantSn = merchant.getSn();
            log.error("openSmart error 商户id:{},商户号:{}", data.getMerchantId(), merchantSn, e);
        }
    }

}
