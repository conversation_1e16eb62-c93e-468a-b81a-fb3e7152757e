package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.rholder.retry.RetryException;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.cua.brand.business.api.enums.PaymentModeEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.core.bean.model.TradeExtConfigContentModel;
import com.wosai.upay.core.bean.request.OpLogCreateRequestV2;
import com.wosai.upay.core.bean.request.TradeExtConfigCreateRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigQueryRequest;
import com.wosai.upay.core.bean.request.TradeExtConfigUpdateRequest;
import com.wosai.upay.core.bean.response.TradeExtConfigQueryResponse;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantConfig;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.enume.ProviderTerminalTaskTypeEnum;
import com.wosai.upay.job.externalservice.brand.BrandBusinessClient;
import com.wosai.upay.job.externalservice.brand.model.BrandDetailInfoQueryResp;
import com.wosai.upay.job.externalservice.brand.model.BrandMerchantInfoQueryResp;
import com.wosai.upay.job.externalservice.tag.TagClient;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.providers.AbstractProvider;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.providers.ProviderFactory;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.RetryerUtil;
import com.wosai.upay.job.util.ThreadLocalUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.fuyou.request.FuyouMerchantAffiliationRequest;
import com.wosai.upay.merchant.contract.model.haike.MerchantAffiliationRequest;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.biz.acquirer.ChangeToLklV3Biz.LKLORG_CHANNEL_NO_LIST;

/**
 * <AUTHOR>
 * @date 2024/9/12
 */
@Slf4j
@Component
public class PaymentModeChangeBiz {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TradeConfigService tradeConfigService;
    @Autowired
    private BrandBusinessClient brandBusinessClient;
    @Autowired
    private ContractStatusMapper contractStatusMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private ComposeAcquirerBiz composeAcquirerBiz;
    @Autowired
    private SupportService supportService;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private McContractRuleDAO mcContractRuleDAO;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private ProviderFactory providerFactory;
    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private HaikeService haikeService;
    @Autowired
    private FuyouService fuyouService;
    @Autowired
    private ContractParamsBiz contractParamsBiz;
    @Autowired
    private TagClient tagClient;
    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private ProviderTerminalTaskRepository providerTerminalTaskRepository;
    @Autowired
    private ProviderTerminalIdBiz providerTerminalIdBiz;
    @Autowired
    @Qualifier("paymentModeChangeThreadPoolTaskScheduler")
    private ThreadPoolTaskScheduler paymentModeChangeThreadPoolTaskScheduler;
    private static final List<PaywayEnum> BRAND_PAYWAYS = Arrays.asList(PaywayEnum.ALIPAY, PaywayEnum.WEIXIN);

    public CommonResult changePaymentMode(PaymentModeChangeReq req) {
        Map merchant = merchantService.getMerchantByMerchantId(req.getMerchantId());
        return changePaymentMode(req, merchant);
    }

    public CommonResult changePaymentMode(PaymentModeChangeReq req, Map merchant) {
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        try {
            PaymentModeChangeContext paymentModeChangeContext = preCheck(req, merchant);
            // 切换支付模式，不需要校验 品牌模式和子商户号的关系
            ThreadLocalUtil.setCheckBrandPaymentMode(false);
            // 切换参数
            doChangePaymentMode(paymentModeChangeContext);
            // 处理挂靠
            merchantAffiliation(paymentModeChangeContext);
            // 终端处理，延迟处理是因为 brand-business在调用完这个接口成功之后才改数据库payment mode
            doCreateMerchantProviderTerminal(paymentModeChangeContext);
            // 删除标签
            doRemoveTag(paymentModeChangeContext);
            // 处理 common switch
            doHandleCommonSwitch(req, paymentModeChangeContext);
            // 清除缓存
            supportService.removeCachedParams(paymentModeChangeContext.getMerchantSn());
            return new CommonResult(CommonResult.SUCCESS, paymentModeChangeContext.getWarnMessage());
        } catch (CommonInvalidParameterException | ContractBizException e) {
            log.error("切换支付模式异常 req:{}", JSON.toJSONString(req), e);
            chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("切换支付模式异常, 商户号:%s, 目标支付模式:%s 原因: %s", merchantSn, req.getTargetPaymentMode(), e.getMessage()));
            return new CommonResult(CommonResult.BIZ_FAIL, e.getMessage());
        } catch (Exception e) {
            log.error("切换支付模式异常 req:{}", JSON.toJSONString(req), e);
            chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("切换支付模式异常, 商户号:%s, 目标支付模式:%s 原因: %s", merchantSn, req.getTargetPaymentMode(), e.getMessage()));
            return new CommonResult(CommonResult.ERROR, e.getMessage());
        } finally {
            ThreadLocalUtil.clearCheckBrandPaymentMode();
        }
    }

    /**
     * CUA-11661
     * 由于小微升级会更换收单机构商户号，所以在小微升级之后对品牌挂靠商户进行重新的挂靠，挂靠到新的收单机构商户号下
     * @param merchantSn 商户号
     */
    public void tryChangePaymentModeAfterMicroUpgrade(String merchantSn) {
        try {
            Map merchantInfo = merchantService.getMerchantBySn(merchantSn);
            String merchantId = WosaiMapUtils.getString(merchantInfo, DaoConstants.ID);
            BrandMerchantInfoQueryResp brandMerchantInfo = brandBusinessClient.getBrandMerchantInfoByMerchantId(merchantId);
            if (brandMerchantInfo.isSubBrandMerchant() && brandMerchantInfo.isBrandPayMode()) {
                PaymentModeChangeReq paymentModeChangeReq = new PaymentModeChangeReq();
                paymentModeChangeReq.setMerchantId(merchantId);
                paymentModeChangeReq.setTargetPaymentMode(brandMerchantInfo.getPaymentMode().getCode());
                paymentModeChangeReq.setRemark("小微升级");
                paymentModeChangeReq.setOperatorId("小微升级");
                paymentModeChangeReq.setPlatform("进件服务");
                CommonResult commonResult = changePaymentMode(paymentModeChangeReq, merchantInfo);
                if (commonResult.isSuccess()) {
                    log.info("小微升级之后重新挂靠成功:{}", merchantSn);
                } else {
                    log.error("小微升级之后重新挂靠失败:{} {}", merchantSn, commonResult.getMsg());
                }
            }
        } catch (Exception e) {
            log.error("小微升级之后重新挂靠异常:{}", merchantSn, e);
        }
    }

    private void doHandleCommonSwitch(PaymentModeChangeReq req, PaymentModeChangeContext paymentModeChangeContext) {
        int commonSwitchStatus = tradeConfigService.getCommonSwitchStatus(CollectionUtil.hashMap("merchant_id", paymentModeChangeContext.getMerchantId(), "type", 21));
        int newCommonSwitchStatus = paymentModeChangeContext.isBrandPaymentMode() ? 1 : 0;
        if (commonSwitchStatus != newCommonSwitchStatus) {
            OpLogCreateRequestV2 opLogCreateRequestV2 = new OpLogCreateRequestV2();
            opLogCreateRequestV2.setPlatformCode(req.getPlatform());
            opLogCreateRequestV2.setOpUserId(req.getOperatorId());
            opLogCreateRequestV2.setRemark(req.getRemark());
            tradeConfigService.configCommonSwitchAndLog(CollectionUtil.hashMap("merchant_id", paymentModeChangeContext.getMerchantId(), "type", 21, "status", newCommonSwitchStatus),
                    opLogCreateRequestV2);
        }
    }

    private void doRemoveTag(PaymentModeChangeContext paymentModeChangeContext) {
        if (PaymentModeEnum.ALI_WX_BRAND_MODE.getCode().equals(paymentModeChangeContext.getTargetPaymentMode())) {
            tagClient.removeAliWaitAuthContract(paymentModeChangeContext.getMerchantId());
            tagClient.removeWxWaitAuthContract(paymentModeChangeContext.getMerchantId());
        } else if (PaymentModeEnum.ALI_BRAND_MODE.getCode().equals(paymentModeChangeContext.getTargetPaymentMode())) {
            tagClient.removeAliWaitAuthContract(paymentModeChangeContext.getMerchantId());
        } else if (PaymentModeEnum.WX_BRAND_MODE.getCode().equals(paymentModeChangeContext.getTargetPaymentMode())) {
            tagClient.removeWxWaitAuthContract(paymentModeChangeContext.getMerchantId());
        }
    }

    private PaymentModeChangeContext preCheck(PaymentModeChangeReq req, Map merchant) {
        if (WosaiMapUtils.isEmpty(merchant)) {
            throw new CommonInvalidParameterException("商户不存在");
        }
        BrandMerchantInfoQueryResp brandMerchantInfo = brandBusinessClient.getBrandMerchantInfoByMerchantId(req.getMerchantId());
        if (Objects.isNull(brandMerchantInfo) || !brandMerchantInfo.isSubBrandMerchant()) {
            throw new CommonInvalidParameterException("商户不是品牌子商户");
        }
        BrandDetailInfoQueryResp brandDetailInfo = brandBusinessClient.getBrandDetailInfoByBrandId(brandMerchantInfo.getBrandId());
        if (Objects.isNull(brandDetailInfo)) {
            throw new CommonInvalidParameterException("品牌不存在");
        }
        Map brandMerchant = merchantService.getMerchantBySn(brandDetailInfo.getMainMerchantSn());
        String merchantSn = WosaiMapUtils.getString(merchant, Merchant.SN);
        String brandMerchantSn = WosaiMapUtils.getString(brandMerchant, Merchant.SN);
        // 查询两者当前所在收单机构是否一致
        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        ContractStatus brandContractStatus = contractStatusMapper.selectByMerchantSn(brandMerchantSn);
        if (Objects.isNull(contractStatus) || Objects.isNull(brandContractStatus)) {
            throw new CommonInvalidParameterException("商户或品牌商户未进件");
        }
        if (ContractStatus.STATUS_SUCCESS != contractStatus.getStatus() || ContractStatus.STATUS_SUCCESS != brandContractStatus.getStatus()) {
            throw new ContractBizException("商户或品牌商户进件未成功");
        }
        if (!contractStatus.getAcquirer().equals(brandContractStatus.getAcquirer())) {
            throw new CommonInvalidParameterException("商户和品牌商户所在收单机构不一致");
        }
        if (!contractStatus.getAcquirer().equals(AcquirerTypeEnum.LKL_V3.getValue()) && !contractStatus.getAcquirer().equals(AcquirerTypeEnum.HAI_KE.getValue()) && !contractStatus.getAcquirer().equals(AcquirerTypeEnum.FU_YOU.getValue())) {
            throw new CommonInvalidParameterException("商户所在收单机构不支持品牌模式");
        }
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(contractStatus.getAcquirer());
        MerchantProviderParams acquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, Integer.valueOf(mcAcquirerDO.getProvider()));
        if (Objects.isNull(acquirerParams)) {
            throw new CommonInvalidParameterException("商户未进件成功");
        }
        PaymentModeChangeContext paymentModeChangeContext = new PaymentModeChangeContext()
                .setMerchantId(req.getMerchantId())
                .setMerchantSn(merchantSn)
                .setProviderMerchantId(acquirerParams.getProvider_merchant_id())
                .setBrandMerchantId(WosaiMapUtils.getString(brandMerchant, DaoConstants.ID))
                .setBrandMerchantSn(brandMerchantSn)
                .setTargetPaymentMode(req.getTargetPaymentMode())
                .setAcquirer(contractStatus.getAcquirer());
        // 校验云闪付开通状态
        // CUA-10080不校验云闪付开通状态，只返回警告文案
        if (paymentModeChangeContext.isBrandPaymentMode()) {
            UnionPayOpenStatusQueryResp unionPayOpenStatusQueryResp = composeAcquirerBiz.queryUnionPayOpenStatus(merchantSn, contractStatus.getAcquirer());
            if (!UnionPayOpenStatusQueryResp.SUCCESS_OPEN.equals(unionPayOpenStatusQueryResp.getStatus())) {
                paymentModeChangeContext.setWarnMessage("银联云闪付未开通成功");
            }
        }
        return paymentModeChangeContext;
    }

    private void doChangePaymentMode(PaymentModeChangeContext paymentModeChangeContext) {
        if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeChangeReq.TARGET_PAYMENT_MODE_MERCHANT)) {
            // 1.切换为商户模式
            // 支付宝微信参数是否都存在 不存在就要重新报备
            MerchantProviderParams wxParams = getParamsByMerchantSnAndPayway(paymentModeChangeContext.getMerchantSn(), PaywayEnum.WEIXIN, paymentModeChangeContext.getAcquirer());
            MerchantProviderParams aliParams = getParamsByMerchantSnAndPayway(paymentModeChangeContext.getMerchantSn(), PaywayEnum.ALIPAY, paymentModeChangeContext.getAcquirer());
            applicationContext.getBean(MerchantProviderParamsService.class).setDefaultMerchantProviderParams(wxParams.getId(), null, "微信切换为商家模式");
            applicationContext.getBean(MerchantProviderParamsService.class).setDefaultMerchantProviderParams(aliParams.getId(), null, "支付宝切换为商家模式");
        }
        Map aliBrandMerchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(paymentModeChangeContext.getBrandMerchantId(), PaywayEnum.ALIPAY.getValue());
        Map wexinBrandMerchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(paymentModeChangeContext.getBrandMerchantId(), PaywayEnum.WEIXIN.getValue());
        paymentModeChangeContext.setAliBrandMerchantConfig(aliBrandMerchantConfig);
        paymentModeChangeContext.setWeixinBrandMerchantConfig(wexinBrandMerchantConfig);

        if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeChangeReq.TARGET_PAYMENT_MODE_WECHAT)) {
            // 2.切换为微信品牌模式
            MerchantProviderParams aliParams = getParamsByMerchantSnAndPayway(paymentModeChangeContext.getMerchantSn(), PaywayEnum.ALIPAY, paymentModeChangeContext.getAcquirer());
            applicationContext.getBean(MerchantProviderParamsService.class).setDefaultMerchantProviderParams(aliParams.getId(), null, "支付宝切换为商家模式");
            setBrandMerchantConfig(paymentModeChangeContext, PaywayEnum.WEIXIN);
        }
        if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeChangeReq.TARGET_PAYMENT_MODE_ALIPAY)) {
            // 3.切换为支付宝品牌模式
            MerchantProviderParams wxParams = getParamsByMerchantSnAndPayway(paymentModeChangeContext.getMerchantSn(), PaywayEnum.WEIXIN, paymentModeChangeContext.getAcquirer());
            applicationContext.getBean(MerchantProviderParamsService.class).setDefaultMerchantProviderParams(wxParams.getId(), null, "微信切换为商家模式");
            setBrandMerchantConfig(paymentModeChangeContext, PaywayEnum.ALIPAY);
        }
        if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeChangeReq.TARGET_PAYMENT_MODE_ALIPAY_WECHAT)) {
            setBrandMerchantConfig(paymentModeChangeContext, PaywayEnum.WEIXIN);
            setBrandMerchantConfig(paymentModeChangeContext, PaywayEnum.ALIPAY);
        }
    }

    private void setBrandMerchantConfig(PaymentModeChangeContext paymentModeChangeContext, PaywayEnum paywayEnum) {
        String brandMerchantId = paymentModeChangeContext.getBrandMerchantId();
        String merchantId = paymentModeChangeContext.getMerchantId();
        Map brandMerchantConfig = paymentModeChangeContext.getMerchantConfigByPayway(paywayEnum);
        Map selfMerchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, paywayEnum.getValue());
        if (WosaiMapUtils.isNotEmpty(selfMerchantConfig) && Objects.nonNull(WosaiMapUtils.getInteger(selfMerchantConfig, MerchantConfig.PROVIDER)) && !Objects.equals(WosaiMapUtils.getInteger(brandMerchantConfig, MerchantConfig.PROVIDER), WosaiMapUtils.getInteger(selfMerchantConfig, MerchantConfig.PROVIDER))) {
            log.error("进件通过，品牌支付模式参数设置错误，provider不一致 商户：{}  品牌主商户：{}", merchantId, brandMerchantId);
            throw new CommonInvalidParameterException("进件通过，品牌支付模式参数设置错误，provider不一致");
        }
        Map brandParams = WosaiMapUtils.getMap(brandMerchantConfig, MerchantConfig.PARAMS);
        Map selfParams = WosaiMapUtils.getMap(selfMerchantConfig, MerchantConfig.PARAMS, new HashMap());
        Integer provider = WosaiMapUtils.getInteger(brandMerchantConfig, MerchantConfig.PROVIDER);
        // TODO 所有使用这个CommonModel.PROVIDER_KEY 的地方都要改成调用 core-b
        String providerTradeParamsKey = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
        Map brandTradeParams = WosaiMapUtils.getMap(brandParams, providerTradeParamsKey);
        if (ProviderEnum.PROVIDER_HAIKE.getValue().equals(provider)) {
            brandTradeParams.put(TransactionParam.IS_AFFILIATED, true);
            brandTradeParams.put(TransactionParam.ORIGINAL_PROVIDER_MCH_ID, paymentModeChangeContext.getProviderMerchantId());
        }
        if (ProviderEnum.PROVIDER_FUYOU.getValue().equals(provider)) {
            brandTradeParams.put(TransactionParam.PROVIDER_MCH_ID, paymentModeChangeContext.getProviderMerchantId());
        }
        selfParams.put(providerTradeParamsKey, brandTradeParams);
        if (WosaiMapUtils.isNotEmpty(selfMerchantConfig)) {
            tradeConfigService.updateMerchantConfig(CollectionUtil.hashMap(
                    DaoConstants.ID, WosaiMapUtils.getString(selfMerchantConfig, DaoConstants.ID),
                    MerchantConfig.PROVIDER, brandMerchantConfig.get(MerchantConfig.PROVIDER),
                    MerchantConfig.B2C_AGENT_NAME, brandMerchantConfig.get(MerchantConfig.B2C_AGENT_NAME),
                    MerchantConfig.C2B_AGENT_NAME, brandMerchantConfig.get(MerchantConfig.C2B_AGENT_NAME),
                    MerchantConfig.WAP_AGENT_NAME, brandMerchantConfig.get(MerchantConfig.WAP_AGENT_NAME),
                    MerchantConfig.MINI_AGENT_NAME, brandMerchantConfig.get(MerchantConfig.MINI_AGENT_NAME),
                    MerchantConfig.APP_AGENT_NAME, brandMerchantConfig.get(MerchantConfig.APP_AGENT_NAME),
                    MerchantConfig.PARAMS, selfParams
            ));
        } else {
            brandMerchantConfig.remove(DaoConstants.ID);
            brandMerchantConfig.put(MerchantConfig.MERCHANT_ID, merchantId);
            tradeConfigService.createMerchantConfig(brandMerchantConfig);
        }
    }

    private void merchantAffiliation(PaymentModeChangeContext paymentModeChangeContext) {
        // 如果是海科并且是是品牌模式，要去调用挂靠接口进行挂靠
        String merchantSn = paymentModeChangeContext.getMerchantSn();
        if (AcquirerTypeEnum.HAI_KE.getValue().equals(paymentModeChangeContext.getAcquirer()) && paymentModeChangeContext.isBrandPaymentMode()) {
            for (PaywayEnum paywayEnum : BRAND_PAYWAYS) {
                Map brandMerchantConfig = paymentModeChangeContext.getMerchantConfigByPayway(paywayEnum);
                Map brandParams = WosaiMapUtils.getMap(brandMerchantConfig, MerchantConfig.PARAMS);
                Integer provider = WosaiMapUtils.getInteger(brandMerchantConfig, MerchantConfig.PROVIDER);
                String providerTradeParamsKey = CommonModel.PROVIDER_KEY.get(String.valueOf(provider));
                Map brandTradeParams = WosaiMapUtils.getMap(brandParams, providerTradeParamsKey);
                String subMchId;
                if (PaywayEnum.ALIPAY.equals(paywayEnum)) {
                    subMchId = BeanUtil.getPropString(brandTradeParams, "alipay_sub_mch_id");
                } else {
                    subMchId = BeanUtil.getPropString(brandTradeParams, "weixin_sub_mch_id");
                }
                try {
                    // 增加重试机制
                    RetryerUtil.getHaikeBrandMerchantAffiliationRetryer().call(() -> {
                        ContractResponse contractResponse = haikeService.merchantAffiliation(new MerchantAffiliationRequest()
                                .setMerchantSn(merchantSn)
                                .setPayway(paywayEnum.getValue())
                                .setSubMchId(subMchId));
                        // 同步多次会出现记录已存在的返回值
                        if (contractResponse.isSuccess() || "记录已存在".equals(contractResponse.getMessage())) {
                            log.info("切换支付模式调用海科接口处理成功：{}", merchantSn);
                            contractResponse.setCode(200);
                        }
                        return contractResponse;
                    });
                } catch (ExecutionException e) {
                    log.error("切换支付模式调用海科接口处理失败：{}", merchantSn);
                    chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("切换支付模式异常,海科接口调用失败, 商户号:%s, 目标支付模式:%s 原因: %s", merchantSn, paymentModeChangeContext.getTargetPaymentMode(), e.getMessage()));
                } catch (RetryException e) {
                    ContractResponse result = (ContractResponse) e.getLastFailedAttempt().getResult();
                    log.error("切换支付模式调用海科接口处理失败：{}", merchantSn);
                    chatBotUtil.sendMessageToPaymentModeChangeChatBot(String.format("切换支付模式异常,海科接口调用失败, 商户号:%s, 目标支付模式:%s 原因: %s", merchantSn, paymentModeChangeContext.getTargetPaymentMode(), result.getMessage()));
                }
            }
        } else if (AcquirerTypeEnum.FU_YOU.getValue().equals(paymentModeChangeContext.getAcquirer())) {
            MerchantProviderParams brandAcquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(paymentModeChangeContext.getBrandMerchantSn(), ProviderEnum.PROVIDER_FUYOU.getValue());
            FuyouParam fuyouParam = contractParamsBiz.buildContractParamsByParams(brandAcquirerParams, FuyouParam.class);
            if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeEnum.ALI_BRAND_MODE.getCode())) {
                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.ALIPAY.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(),
                        brandAcquirerParams.getProvider_merchant_id(),
                        "支付宝品牌模式");

                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.WEIXIN.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(), null,
                        "微信商户模式");
            }
            if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeEnum.WX_BRAND_MODE.getCode())) {
                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.WEIXIN.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(),
                        brandAcquirerParams.getProvider_merchant_id(),
                        "微信品牌模式");

                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.ALIPAY.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(), null,
                        "支付宝商户模式");
            }
            if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeEnum.ALI_WX_BRAND_MODE.getCode())) {
                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.WEIXIN.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(),
                        brandAcquirerParams.getProvider_merchant_id(),
                        "微信品牌模式");

                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.ALIPAY.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(),
                        brandAcquirerParams.getProvider_merchant_id(),
                        "支付宝品牌模式");
            }
            if (paymentModeChangeContext.getTargetPaymentMode().equals(PaymentModeEnum.MERCHANT_MODE.getCode())) {
                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.ALIPAY.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(), null,
                        "支付宝商户模式");

                buildAndCallAffiliation(merchantSn, fuyouParam, PaywayEnum.WEIXIN.getValue(),
                        paymentModeChangeContext.getProviderMerchantId(), null,
                        "微信商户模式");
            }
        }
    }

    private void buildAndCallAffiliation(String merchantSn, FuyouParam fuyouParam,
                                         int payway, String fuyouMerchantId,
                                         String affiliationFuyouMerchantId, String modeDesc) {
        FuyouMerchantAffiliationRequest request = new FuyouMerchantAffiliationRequest();
        request.setFuyouMerchantId(fuyouMerchantId);
        request.setPayway(payway);
        if (affiliationFuyouMerchantId != null) {
            request.setAffiliationFuyouMerchantId(affiliationFuyouMerchantId);
        }

        try {
            ContractResponse contractResponse = fuyouService.merchantAffiliation(request, fuyouParam);
            if (!contractResponse.isSuccess()) {
                throw new ContractBizException(contractResponse.getMessage());
            }
        } catch (Exception e) {
            throw new ContractBizException(String.format("富友商户:%s,切换%s失败,异常:%s",
                    merchantSn, modeDesc, e.getMessage()), e);
        }
    }


    private void doCreateMerchantProviderTerminal(PaymentModeChangeContext paymentModeChangeContext) {
        McAcquirerDO mcAcquirerDO = mcAcquirerDAO.getByAcquirer(paymentModeChangeContext.getAcquirer());
        BasicProvider finalProvider = providerFactory.getProvider(String.valueOf(mcAcquirerDO.getProvider()));
        log.info("切换交易模式商户:{},开始同步终端和门店", paymentModeChangeContext.getMerchantSn());
        paymentModeChangeThreadPoolTaskScheduler.getScheduledExecutor().schedule(RunnableWrapper.of(() -> {
            try {
                doCreateMerchantProviderTerminal(paymentModeChangeContext, finalProvider);
                finalProvider.createProviderTerminal(paymentModeChangeContext.getMerchantSn(), Integer.valueOf(mcAcquirerDO.getProvider()));
            } catch (Exception e) {
                log.error("切换支付模式后置处理异常：{}", paymentModeChangeContext.getMerchantSn(), e);
            }
        }), 2, TimeUnit.SECONDS);
    }

    private MerchantProviderParams getParamsByMerchantSnAndPayway(String merchantSn, PaywayEnum paywayEnum, String acquirer) {
        Optional<McContractRuleDO> mcContractRule = mcContractRuleDAO.listAllEffectiveRule().stream().filter(r -> r.getIsDefault() && r.getPayway().equals(paywayEnum.getValue()) && r.getAcquirer().equals(acquirer)).findFirst();
        if (!mcContractRule.isPresent()) {
            throw new CommonInvalidParameterException(String.format("未找到默认的入网进件规则 %s %s", acquirer, paywayEnum.getText()));
        }
        McContractRuleDO contractRule = mcContractRule.get();
        List<MerchantProviderParams> params = Lists.newArrayList();
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = wxExample.or();
        criteria.andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(Integer.valueOf(contractRule.getProvider()))
                .andPaywayEqualTo(paywayEnum.getValue())
                .andDeletedEqualTo(false);
        if (AcquirerTypeEnum.LKL_V3.getValue().equals(acquirer) && PaywayEnum.WEIXIN.equals(paywayEnum)) {
            criteria.andChannel_noIn(LKLORG_CHANNEL_NO_LIST);
        }
        wxExample.setOrderByClause("ctime desc");
        params.addAll(merchantProviderParamsMapper.selectByExampleWithBLOBs(wxExample));
        params = params.stream().filter(r -> !r.isOnlineParams()).collect(Collectors.toList());
        if (WosaiCollectionUtils.isEmpty(params)) {
            CommonResult result = applicationContext.getBean(ContractApplicationService.class).contractByRule(
                    new RuleContractRequest()
                            .setRule(contractRule.getRule())
                            .setMerchantSn(merchantSn)
                            .setPlat("changePaymentMode")
                            .setReContract(true)
                            .setConfigParam(false)
            );
            if (!result.isSuccess()) {
                throw new ContractBizException(String.format("缺少可用%s子商户号", paywayEnum.getText()));
            }
            params = merchantProviderParamsMapper.selectByExampleWithBLOBs(wxExample);
            if (WosaiCollectionUtils.isEmpty(params)) {
                throw new ContractBizException(String.format("缺少可用%s子商户号", paywayEnum.getText()));
            }
            return params.get(0);
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams param : params) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(param.getAuth_status())) {
                return param;
            }
        }
        return params.get(0);
    }

    private void doCreateMerchantProviderTerminal(PaymentModeChangeContext paymentModeChangeContext, BasicProvider finalProvider) {
        String merchantSn = paymentModeChangeContext.getMerchantSn();
        // 如果是海科商户，得自己创建一个品牌商户级别的终端，然后将品牌子商户号写到core-b 创建一个绑定的任务
        if (paymentModeChangeContext.getAcquirer().equals(AcquirerTypeEnum.HAI_KE.getValue()) && paymentModeChangeContext.isBrandPaymentMode()) {
            MerchantProviderParams brandAcquirerParams = merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(paymentModeChangeContext.getBrandMerchantSn(), ProviderEnum.PROVIDER_HAIKE.getValue());
            final ProviderTerminal providerTerminal = providerTerminalBiz.existMerchantProviderTerminal(ProviderEnum.PROVIDER_HAIKE.getValue(), merchantSn, brandAcquirerParams.getPay_merchant_id());
            List<MerchantProviderParams> brandParams = ((AbstractProvider) finalProvider).getMerchantProviderParamsByProvider(ProviderEnum.PROVIDER_HAIKE.getValue(), paymentModeChangeContext.getBrandMerchantSn());
            String termNo = Objects.nonNull(providerTerminal) ? providerTerminal.getProvider_terminal_id() : providerTerminalIdBiz.nextProviderTerminalId();
            if (Objects.isNull(providerTerminal)) {
                providerTerminalBiz.createMerchantProviderTerminal(merchantSn, termNo, brandAcquirerParams.getPay_merchant_id(), ProviderEnum.PROVIDER_HAIKE.getValue());
                log.info("海科品牌商户绑定=>商户号:{},不存在商户级别,现在开始重新绑定,终端Id:{},交易参数:{}", merchantSn, termNo, JSONObject.toJSONString(brandParams));
                brandParams.forEach(param ->
                        providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                                param.getPay_merchant_id(),
                                param.getProvider(),
                                param.getPayway(),
                                ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                                termNo,
                                null,
                                null,
                                param.getMerchant_sn()));
            } else {
                //判断是否需要插入绑定任务
                final List<MerchantProviderParams> merchantProviderParams = providerTerminalBiz.getNotSyncMerchantProviderParams(providerTerminal, brandParams);
                //已经绑定了不用处理
                if (WosaiCollectionUtils.isNotEmpty(merchantProviderParams)) {
                    log.info("海科品牌商户绑定=>商户号:{},已经存在终端新增子商户号,终端Id:{},交易参数:{}", merchantSn, providerTerminal.getProvider_terminal_id(), JSONObject.toJSONString(merchantProviderParams));
                    //未绑定的子商户号插入task
                    merchantProviderParams.forEach(param ->
                            providerTerminalTaskRepository.addBoundTerminalTask(merchantSn,
                                    param.getPay_merchant_id(),
                                    param.getProvider(),
                                    param.getPayway(),
                                    ProviderTerminalTaskTypeEnum.BOUND_ALL_TERMINAL.getType(),
                                    providerTerminal.getProvider_terminal_id(),
                                    null,
                                    null,
                                    param.getMerchant_sn())
                    );
                }
            }
            for (MerchantProviderParams brandParam : brandParams) {
                String sn = String.format("%s:%s", merchantSn, brandParam.getPay_merchant_id());
                //查询是否存在
                final TradeExtConfigQueryRequest queryRequest = new TradeExtConfigQueryRequest();
                queryRequest.setSn(sn);
                queryRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_MERCHANT_SUB_MCH);
                queryRequest.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
                final TradeExtConfigQueryResponse tradeExtConfig = tradeConfigService.queryTradeExtConfig(queryRequest);
                if (!Objects.isNull(tradeExtConfig)) {
                    TradeExtConfigUpdateRequest updateRequest = new TradeExtConfigUpdateRequest();
                    updateRequest.setSn(sn);
                    updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_MERCHANT_SUB_MCH);
                    updateRequest.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
                    TradeExtConfigContentModel content = new TradeExtConfigContentModel();
                    content.setTermId(termNo);
                    updateRequest.setContent(content);
                    tradeConfigService.updateTradeExtConfig(updateRequest);
                } else {
                    //调用交易接口
                    TradeExtConfigCreateRequest updateRequest = new TradeExtConfigCreateRequest();
                    updateRequest.setSn(sn);
                    updateRequest.setSnType(TradeExtConfigCreateRequest.SN_TYPE_MERCHANT_SUB_MCH);
                    updateRequest.setProvider(ProviderEnum.PROVIDER_HAIKE.getValue());
                    TradeExtConfigContentModel content = new TradeExtConfigContentModel();
                    content.setTermId(termNo);
                    updateRequest.setContent(content);
                    tradeConfigService.createTradeExtConfig(updateRequest);
                }
            }
        }


    }


    @Data
    @Accessors(chain = true)
    private static class PaymentModeChangeContext {
        private String merchantId;
        private String merchantSn;
        private String brandMerchantId;
        private String brandMerchantSn;
        private String providerMerchantId;
        private Integer targetPaymentMode;
        private String acquirer;
        private Map aliBrandMerchantConfig;
        private Map weixinBrandMerchantConfig;
        private String warnMessage;

        public boolean isBrandPaymentMode() {
            return PaymentModeEnum.ALI_BRAND_MODE.getCode().equals(targetPaymentMode) || PaymentModeEnum.WX_BRAND_MODE.getCode().equals(targetPaymentMode) || PaymentModeEnum.ALI_WX_BRAND_MODE.getCode().equals(targetPaymentMode);
        }

        public Map getMerchantConfigByPayway(PaywayEnum paywayEnum) {
            if (PaywayEnum.ALIPAY.equals(paywayEnum)) {
                return aliBrandMerchantConfig;
            } else if (PaywayEnum.WEIXIN.equals(paywayEnum)) {
                return weixinBrandMerchantConfig;
            } else {
                throw new ContractBizException("不支持该支付方式");
            }
        }
    }
}
