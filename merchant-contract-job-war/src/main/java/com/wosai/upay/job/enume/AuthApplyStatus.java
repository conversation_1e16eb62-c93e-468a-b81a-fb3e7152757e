package com.wosai.upay.job.enume;

/**
 * Created by hzq on 19/8/26.
 */
public enum AuthApplyStatus {

    //开始状态
    UN_SUBMIT("未提交", 0),
    ON_SUBMIT("提交中", 1),
    //没有门店照片的小微商户 后续才会上传
    MICRO_WATI_SUBMIT("等待提交门店照片", 2),


    //中间状态
    SUBMIT("已提交", 10),
    WAITTING_FOR_AUDIT("审核中", 20),  //人工审核中
    WAITTING_FOR_CONFIRM_CONTACT("待联系人确认", 30),
    WAITTING_FOR_CONFIRM_LEGALPERSON("待账户验证", 40),

    //结束状态
    PASSED("审核通过", 50),
    PASSED_FREEZE("审核通过(已冻结)", 51),
    //实际这里 并没有审核通过
    PASSED_CANCEL("审核通过(已作废)", 52),

    SUBMIT_FAIL("提交失败驳回", 60),
    REJECTED("审核驳回", 61);


    private String context;
    private Integer val;

    AuthApplyStatus(String context, Integer val) {
        this.context = context;
        this.val = val;
    }

    public static AuthApplyStatus toStatus(Integer value) {
        if (value == null) {
            return null;
        }
        for (AuthApplyStatus status : AuthApplyStatus.values()) {
            if (status.getVal().equals(value)) {
                return status;
            }
        }
        return null;
    }

    public static boolean isFinish(Integer value) {
        AuthApplyStatus status = toStatus(value);
        return PASSED_CANCEL.equals(status) || PASSED.equals(status) || PASSED_FREEZE.equals(status) || SUBMIT_FAIL.equals(status) || REJECTED.equals(status);
    }

    public static boolean isPre(Integer value) {
        AuthApplyStatus status = toStatus(value);
        return UN_SUBMIT.equals(status) || ON_SUBMIT.equals(status) || MICRO_WATI_SUBMIT.equals(status);
    }

    public static boolean isSucceed(Integer value) {
        AuthApplyStatus status = toStatus(value);
        return PASSED.equals(status) || PASSED_FREEZE.equals(status);
    }

    public static boolean isFailed(Integer value) {
        AuthApplyStatus status = toStatus(value);
        return PASSED_CANCEL.equals(status) || REJECTED.equals(status) || SUBMIT_FAIL.equals(status);
    }

    public static boolean isProcess(Integer value) {
        AuthApplyStatus status = toStatus(value);
        return SUBMIT.equals(status) || WAITTING_FOR_AUDIT.equals(status) || WAITTING_FOR_CONFIRM_CONTACT.equals(status) || WAITTING_FOR_CONFIRM_LEGALPERSON.equals(status);
    }


    public String getContext() {
        return context;
    }


    public Integer getVal() {
        return val;
    }


}
