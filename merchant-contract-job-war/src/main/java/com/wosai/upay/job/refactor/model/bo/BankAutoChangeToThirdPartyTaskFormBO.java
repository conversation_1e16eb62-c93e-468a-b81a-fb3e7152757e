package com.wosai.upay.job.refactor.model.bo;

import lombok.Data;

/**
 * 银行自动切三方工单BO
 *
 * <AUTHOR>
 * @date 2024/6/19 15:03
 */
@Data
public class BankAutoChangeToThirdPartyTaskFormBO {

    /**
     * 商户号
     */
    private String merchantSn;


    /**
     * 银行商户号
     */
    private String bankMerchantId;


    /**
     * 交易报错银行通道
     */
    private String tradeFailBankName;


    /**
     * 回切三方通道
     */
    private String changeToTargetAcquirer;


    /**
     * 报错支付方式
     */
    private String payWay;


    /**
     * 交易报错原因
     */
    private String tradeFailReason;


    /**
     * 银行解决方法
     */
    private String bankRepairSolution;


    /**
     * 银行交易报错原始信息
     */
    private String bankOriginalErrorMsg;

    /**
     * 银行处理结果
     */
    private String bankProcessResult;


}
