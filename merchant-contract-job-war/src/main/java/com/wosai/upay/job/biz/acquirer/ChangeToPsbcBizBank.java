package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.merchant.contract.service.PsbcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 收单机构切换到拉卡拉
 *
 * <AUTHOR>
 * @date 2020-04-26
 */
@Component("psbc-AcquirerChangeBiz")
public class ChangeToPsbcBizBank extends AbstractBankDirectAcquirerChangeBiz {

    @Autowired
    private PsbcService psbcService;

    @Value("${psbc_business_dev_code}")
    public  String psbcBusinessDevCode;

    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_PSBC.getValue();
    }


    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_PSBC_RULE_GROUP;
    }

    @Override
    public String getDevCode(String acquirer) {
        return psbcBusinessDevCode;
    }

    @Override
    protected long getDefaultComboId(String acquirer) {
        return 125L;
    }

    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        super.sourceAcquirerPostBiz(change);
        psbcService.outMerInfoCutAisle(change.getMerchant_sn(), "01", "系统自动切换");
    }

    @Override
    protected void targetAcquirerPostBiz(McAcquirerChange change) {
        super.targetAcquirerPostBiz(change);
        psbcService.outMerInfoCutAisle(change.getMerchant_sn(), "00", "系统自动切换");
    }
}
