package com.wosai.upay.job.refactor.service.rpc.bank;

import com.shouqianba.cua.model.http.LogParamsDto;

import java.util.Map;

/**
 * merchant-bank-service BankAccountService
 */
public interface AccountBankAccountService {

    /**
     * autoUpdateVerifyStatus为false时候，id和merchant_id必须穿
     *
     * @param request                银行卡信息
     * @param logParamsDto           日志
     * @param autoUpdateVerifyStatus 是否自动更新审核状态
     */
    void updateMerchantBankAccountInfoWithLog(Map request, LogParamsDto logParamsDto, boolean autoUpdateVerifyStatus);

}
