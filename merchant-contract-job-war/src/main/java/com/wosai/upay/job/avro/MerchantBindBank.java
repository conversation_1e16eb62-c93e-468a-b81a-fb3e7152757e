/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.job.avro;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantBindBank extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 1309419443647178639L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantBindBank\",\"namespace\":\"com.wosai.upay.merchant.contract.avro\",\"fields\":[{\"name\":\"merchant_id\",\"type\":[\"string\",\"null\"]},{\"name\":\"merchant_sn\",\"type\":[\"string\",\"null\"]},{\"name\":\"bank_type\",\"type\":[\"int\",\"null\"]},{\"name\":\"bank_name\",\"type\":[\"string\",\"null\"]},{\"name\":\"status\",\"type\":[\"int\",\"null\"]},{\"name\":\"message\",\"type\":[\"string\",\"null\"]},{\"name\":\"ctime\",\"type\":[\"long\",\"null\"]},{\"name\":\"mtime\",\"type\":[\"long\",\"null\"]}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MerchantBindBank> ENCODER =
      new BinaryMessageEncoder<MerchantBindBank>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MerchantBindBank> DECODER =
      new BinaryMessageDecoder<MerchantBindBank>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MerchantBindBank> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MerchantBindBank> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MerchantBindBank>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MerchantBindBank to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MerchantBindBank from a ByteBuffer. */
  public static MerchantBindBank fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public CharSequence merchant_id;
  @Deprecated public CharSequence merchant_sn;
  @Deprecated public Integer bank_type;
  @Deprecated public CharSequence bank_name;
  @Deprecated public Integer status;
  @Deprecated public CharSequence message;
  @Deprecated public Long ctime;
  @Deprecated public Long mtime;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MerchantBindBank() {}

  /**
   * All-args constructor.
   * @param merchant_id The new value for merchant_id
   * @param merchant_sn The new value for merchant_sn
   * @param bank_type The new value for bank_type
   * @param bank_name The new value for bank_name
   * @param status The new value for status
   * @param message The new value for message
   * @param ctime The new value for ctime
   * @param mtime The new value for mtime
   */
  public MerchantBindBank(CharSequence merchant_id, CharSequence merchant_sn, Integer bank_type, CharSequence bank_name, Integer status, CharSequence message, Long ctime, Long mtime) {
    this.merchant_id = merchant_id;
    this.merchant_sn = merchant_sn;
    this.bank_type = bank_type;
    this.bank_name = bank_name;
    this.status = status;
    this.message = message;
    this.ctime = ctime;
    this.mtime = mtime;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public Object get(int field$) {
    switch (field$) {
    case 0: return merchant_id;
    case 1: return merchant_sn;
    case 2: return bank_type;
    case 3: return bank_name;
    case 4: return status;
    case 5: return message;
    case 6: return ctime;
    case 7: return mtime;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, Object value$) {
    switch (field$) {
    case 0: merchant_id = (CharSequence)value$; break;
    case 1: merchant_sn = (CharSequence)value$; break;
    case 2: bank_type = (Integer)value$; break;
    case 3: bank_name = (CharSequence)value$; break;
    case 4: status = (Integer)value$; break;
    case 5: message = (CharSequence)value$; break;
    case 6: ctime = (Long)value$; break;
    case 7: mtime = (Long)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'bank_type' field.
   * @return The value of the 'bank_type' field.
   */
  public Integer getBankType() {
    return bank_type;
  }

  /**
   * Sets the value of the 'bank_type' field.
   * @param value the value to set.
   */
  public void setBankType(Integer value) {
    this.bank_type = value;
  }

  /**
   * Gets the value of the 'bank_name' field.
   * @return The value of the 'bank_name' field.
   */
  public CharSequence getBankName() {
    return bank_name;
  }

  /**
   * Sets the value of the 'bank_name' field.
   * @param value the value to set.
   */
  public void setBankName(CharSequence value) {
    this.bank_name = value;
  }

  /**
   * Gets the value of the 'status' field.
   * @return The value of the 'status' field.
   */
  public Integer getStatus() {
    return status;
  }

  /**
   * Sets the value of the 'status' field.
   * @param value the value to set.
   */
  public void setStatus(Integer value) {
    this.status = value;
  }

  /**
   * Gets the value of the 'message' field.
   * @return The value of the 'message' field.
   */
  public CharSequence getMessage() {
    return message;
  }

  /**
   * Sets the value of the 'message' field.
   * @param value the value to set.
   */
  public void setMessage(CharSequence value) {
    this.message = value;
  }

  /**
   * Gets the value of the 'ctime' field.
   * @return The value of the 'ctime' field.
   */
  public Long getCtime() {
    return ctime;
  }

  /**
   * Sets the value of the 'ctime' field.
   * @param value the value to set.
   */
  public void setCtime(Long value) {
    this.ctime = value;
  }

  /**
   * Gets the value of the 'mtime' field.
   * @return The value of the 'mtime' field.
   */
  public Long getMtime() {
    return mtime;
  }

  /**
   * Sets the value of the 'mtime' field.
   * @param value the value to set.
   */
  public void setMtime(Long value) {
    this.mtime = value;
  }

  /**
   * Creates a new MerchantBindBank RecordBuilder.
   * @return A new MerchantBindBank RecordBuilder
   */
  public static Builder newBuilder() {
    return new Builder();
  }

  /**
   * Creates a new MerchantBindBank RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MerchantBindBank RecordBuilder
   */
  public static Builder newBuilder(Builder other) {
    return new Builder(other);
  }

  /**
   * Creates a new MerchantBindBank RecordBuilder by copying an existing MerchantBindBank instance.
   * @param other The existing instance to copy.
   * @return A new MerchantBindBank RecordBuilder
   */
  public static Builder newBuilder(MerchantBindBank other) {
    return new Builder(other);
  }

  /**
   * RecordBuilder for MerchantBindBank instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantBindBank>
    implements org.apache.avro.data.RecordBuilder<MerchantBindBank> {

    private CharSequence merchant_id;
    private CharSequence merchant_sn;
    private Integer bank_type;
    private CharSequence bank_name;
    private Integer status;
    private CharSequence message;
    private Long ctime;
    private Long mtime;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.bank_type)) {
        this.bank_type = data().deepCopy(fields()[2].schema(), other.bank_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bank_name)) {
        this.bank_name = data().deepCopy(fields()[3].schema(), other.bank_name);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.message)) {
        this.message = data().deepCopy(fields()[5].schema(), other.message);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.ctime)) {
        this.ctime = data().deepCopy(fields()[6].schema(), other.ctime);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.mtime)) {
        this.mtime = data().deepCopy(fields()[7].schema(), other.mtime);
        fieldSetFlags()[7] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MerchantBindBank instance
     * @param other The existing instance to copy.
     */
    private Builder(MerchantBindBank other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[0].schema(), other.merchant_id);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[1].schema(), other.merchant_sn);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.bank_type)) {
        this.bank_type = data().deepCopy(fields()[2].schema(), other.bank_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.bank_name)) {
        this.bank_name = data().deepCopy(fields()[3].schema(), other.bank_name);
        fieldSetFlags()[3] = true;
      }
      if (isValidValue(fields()[4], other.status)) {
        this.status = data().deepCopy(fields()[4].schema(), other.status);
        fieldSetFlags()[4] = true;
      }
      if (isValidValue(fields()[5], other.message)) {
        this.message = data().deepCopy(fields()[5].schema(), other.message);
        fieldSetFlags()[5] = true;
      }
      if (isValidValue(fields()[6], other.ctime)) {
        this.ctime = data().deepCopy(fields()[6].schema(), other.ctime);
        fieldSetFlags()[6] = true;
      }
      if (isValidValue(fields()[7], other.mtime)) {
        this.mtime = data().deepCopy(fields()[7].schema(), other.mtime);
        fieldSetFlags()[7] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public Builder setMerchantId(CharSequence value) {
      validate(fields()[0], value);
      this.merchant_id = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public Builder setMerchantSn(CharSequence value) {
      validate(fields()[1], value);
      this.merchant_sn = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'bank_type' field.
      * @return The value.
      */
    public Integer getBankType() {
      return bank_type;
    }

    /**
      * Sets the value of the 'bank_type' field.
      * @param value The value of 'bank_type'.
      * @return This builder.
      */
    public Builder setBankType(Integer value) {
      validate(fields()[2], value);
      this.bank_type = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'bank_type' field has been set.
      * @return True if the 'bank_type' field has been set, false otherwise.
      */
    public boolean hasBankType() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'bank_type' field.
      * @return This builder.
      */
    public Builder clearBankType() {
      bank_type = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'bank_name' field.
      * @return The value.
      */
    public CharSequence getBankName() {
      return bank_name;
    }

    /**
      * Sets the value of the 'bank_name' field.
      * @param value The value of 'bank_name'.
      * @return This builder.
      */
    public Builder setBankName(CharSequence value) {
      validate(fields()[3], value);
      this.bank_name = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'bank_name' field has been set.
      * @return True if the 'bank_name' field has been set, false otherwise.
      */
    public boolean hasBankName() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'bank_name' field.
      * @return This builder.
      */
    public Builder clearBankName() {
      bank_name = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    /**
      * Gets the value of the 'status' field.
      * @return The value.
      */
    public Integer getStatus() {
      return status;
    }

    /**
      * Sets the value of the 'status' field.
      * @param value The value of 'status'.
      * @return This builder.
      */
    public Builder setStatus(Integer value) {
      validate(fields()[4], value);
      this.status = value;
      fieldSetFlags()[4] = true;
      return this;
    }

    /**
      * Checks whether the 'status' field has been set.
      * @return True if the 'status' field has been set, false otherwise.
      */
    public boolean hasStatus() {
      return fieldSetFlags()[4];
    }


    /**
      * Clears the value of the 'status' field.
      * @return This builder.
      */
    public Builder clearStatus() {
      status = null;
      fieldSetFlags()[4] = false;
      return this;
    }

    /**
      * Gets the value of the 'message' field.
      * @return The value.
      */
    public CharSequence getMessage() {
      return message;
    }

    /**
      * Sets the value of the 'message' field.
      * @param value The value of 'message'.
      * @return This builder.
      */
    public Builder setMessage(CharSequence value) {
      validate(fields()[5], value);
      this.message = value;
      fieldSetFlags()[5] = true;
      return this;
    }

    /**
      * Checks whether the 'message' field has been set.
      * @return True if the 'message' field has been set, false otherwise.
      */
    public boolean hasMessage() {
      return fieldSetFlags()[5];
    }


    /**
      * Clears the value of the 'message' field.
      * @return This builder.
      */
    public Builder clearMessage() {
      message = null;
      fieldSetFlags()[5] = false;
      return this;
    }

    /**
      * Gets the value of the 'ctime' field.
      * @return The value.
      */
    public Long getCtime() {
      return ctime;
    }

    /**
      * Sets the value of the 'ctime' field.
      * @param value The value of 'ctime'.
      * @return This builder.
      */
    public Builder setCtime(Long value) {
      validate(fields()[6], value);
      this.ctime = value;
      fieldSetFlags()[6] = true;
      return this;
    }

    /**
      * Checks whether the 'ctime' field has been set.
      * @return True if the 'ctime' field has been set, false otherwise.
      */
    public boolean hasCtime() {
      return fieldSetFlags()[6];
    }


    /**
      * Clears the value of the 'ctime' field.
      * @return This builder.
      */
    public Builder clearCtime() {
      ctime = null;
      fieldSetFlags()[6] = false;
      return this;
    }

    /**
      * Gets the value of the 'mtime' field.
      * @return The value.
      */
    public Long getMtime() {
      return mtime;
    }

    /**
      * Sets the value of the 'mtime' field.
      * @param value The value of 'mtime'.
      * @return This builder.
      */
    public Builder setMtime(Long value) {
      validate(fields()[7], value);
      this.mtime = value;
      fieldSetFlags()[7] = true;
      return this;
    }

    /**
      * Checks whether the 'mtime' field has been set.
      * @return True if the 'mtime' field has been set, false otherwise.
      */
    public boolean hasMtime() {
      return fieldSetFlags()[7];
    }


    /**
      * Clears the value of the 'mtime' field.
      * @return This builder.
      */
    public Builder clearMtime() {
      mtime = null;
      fieldSetFlags()[7] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MerchantBindBank build() {
      try {
        MerchantBindBank record = new MerchantBindBank();
        record.merchant_id = fieldSetFlags()[0] ? this.merchant_id : (CharSequence) defaultValue(fields()[0]);
        record.merchant_sn = fieldSetFlags()[1] ? this.merchant_sn : (CharSequence) defaultValue(fields()[1]);
        record.bank_type = fieldSetFlags()[2] ? this.bank_type : (Integer) defaultValue(fields()[2]);
        record.bank_name = fieldSetFlags()[3] ? this.bank_name : (CharSequence) defaultValue(fields()[3]);
        record.status = fieldSetFlags()[4] ? this.status : (Integer) defaultValue(fields()[4]);
        record.message = fieldSetFlags()[5] ? this.message : (CharSequence) defaultValue(fields()[5]);
        record.ctime = fieldSetFlags()[6] ? this.ctime : (Long) defaultValue(fields()[6]);
        record.mtime = fieldSetFlags()[7] ? this.mtime : (Long) defaultValue(fields()[7]);
        return record;
      } catch (Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MerchantBindBank>
    WRITER$ = (org.apache.avro.io.DatumWriter<MerchantBindBank>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MerchantBindBank>
    READER$ = (org.apache.avro.io.DatumReader<MerchantBindBank>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
