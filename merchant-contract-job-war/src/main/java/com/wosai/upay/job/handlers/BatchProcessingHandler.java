package com.wosai.upay.job.handlers;


import com.wosai.data.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Component
@Slf4j
public class BatchProcessingHandler<T, R> implements BatchBaseHandler<T, R>, ApplicationContextAware {

    @Autowired
    private ApplicationContext applicationContext;


    private Map<String, BatchProcessingHandler<T, R>> handlers = new HashMap<>();

    public void beforeHandlerEvent(BatchContext batchContext) {
        String templateEvent = BeanUtil.getPropString(batchContext.getBatchTemplate(), "handlerService");
        if (handlers.get(templateEvent) == null) {
            handlers.put(templateEvent, applicationContext.getBean(templateEvent, BatchProcessingHandler.class));
        }
        handlers.get(templateEvent).doPreProcess(batchContext);
    }

    public R handlerEvent(T t, BatchContext batchContext) {
        String templateEvent = BeanUtil.getPropString(batchContext.getBatchTemplate(), "handlerService");
        BatchProcessingHandler<T, R> handler = handlers.get(templateEvent);
        try {
            return handler.handle(t, batchContext);
        } catch (Exception e) {
            return handler.handleError(t, batchContext, e);
        }
    }

    public void afterHandlerEvent(BatchContext batchContext) {
        String templateEvent = BeanUtil.getPropString(batchContext.getBatchTemplate(), "handlerService");
        handlers.get(templateEvent).doAfterProcess(batchContext);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void doPreProcess(BatchContext batchContext) {
    }

    @Override
    public R handle(T t, BatchContext batchContext) {
        return null;
    }

    @Override
    public void doAfterProcess(BatchContext batchContext) {

    }

    @Override
    public R handleError(T t, BatchContext batchContext, Exception e) {
        return null;
    }
}

