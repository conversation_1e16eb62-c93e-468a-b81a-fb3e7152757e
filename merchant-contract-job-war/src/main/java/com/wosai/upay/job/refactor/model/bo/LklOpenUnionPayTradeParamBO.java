package com.wosai.upay.job.refactor.model.bo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * lkl开放平台-云闪付交易参数BO
 * 注:不要乱改,序列化为json后直接写入merchant_config.params
 *
 * <AUTHOR>
 * @date 2024/1/16 16:05
 */
@Data
public class LklOpenUnionPayTradeParamBO {

    /**
     * 银联商户号
     */
    @JSONField(name = "merc_id")
    @JsonProperty("merc_id")
    private String providerMerchantId;
    /**
     * 终端号(银联,其实对应lkl返回的term_no,而不是term_id),
     * 交易那边提前命名为了termId,请不要混淆
     */
    @JSONField(name = "term_id")
    @JsonProperty("term_id")
    private String termId;

}
