package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.BankAccountTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.model.UnionPayOpenStatusQueryResp;
import com.wosai.upay.job.model.alipay.AlipayMchInfo;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import com.wosai.upay.merchant.contract.model.weixin.MchInfo;
import com.wosai.upay.merchant.contract.service.ChinaUmsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Description:
 * <AUTHOR>
 * @Date: 2021/4/23 4:40 下午
 */
@Component("ums-biz")
public class UmsAcquirerBiz implements IAcquirerBiz {

    @Autowired
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private ChinaUmsService chinaUmsService;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;

    @Autowired
    private IMerchantService iMerchantService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    MerchantBusinessLicenseService mcMerchantBusinessLicenseService;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private MerchantBankService merchantBankService;

    @Autowired
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private RuleContext ruleContext;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    private TradeConfigService tradeConfigService;


    @Override
    public String getDefaultRuleGroup(String acquirer) {
        return McConstant.RULE_GROUP_UMS;
    }

    @Override
    public String getAcquirerMchIdFromMerchantConfig(Map merchantConfig) {
        return null;
    }

    @Override
    public String getNormalWxRule() {
        return ContractRuleConstants.UMS_NORMAL_WEIXIN_RULE;
    }


    @Override
    public WxMchInfo getWxMchInfo(MerchantProviderParams providerParams) {
        String merchantName = wechatAuthBiz.getWechatAuthMerchantName(providerParams.getMerchant_sn());
        WxMchInfo wxMchInfo = new WxMchInfo().setMchInfo(new MchInfo().setMerchant_name(merchantName));
        try {
            ChinaUmsParam umsParam = contractParamsBiz.buildContractParams(String.valueOf(providerParams.getProvider()), providerParams.getPayway(), providerParams.getChannel_no(), ChinaUmsParam.class);
            wxMchInfo.setSubdevConfig(chinaUmsService.queryWechatSubDevConfig(umsParam, providerParams.getId()));
        }catch (Exception e){
            //不一定能找到，依赖于 merchant_config
        }
        return wxMchInfo;
    }

    @Override
    public AlipayMchInfo getAlipayMchInfo(MerchantProviderParams providerParams) {
        return new AlipayMchInfo().setName(null).setSub_merchant_id(providerParams.getPay_merchant_id());
    }

    @Override
    public UnionPayOpenStatusQueryResp queryUnionPayOpenStatus(String merchantSn) {
        Optional<MerchantProviderParamsDO> unionParam = merchantProviderParamsDAO.getMerchantProviderParamsByProviderAndPayway(merchantSn, ProviderEnum.PROVIDER_UMS.getValue(), PaywayEnum.UNIONPAY.getValue());
        if (unionParam.isPresent()) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.SUCCESS_OPEN)
                    .retry(false)
                    .build();
        }
        ContractTask netInTask = getNetInTask(merchantSn, AcquirerTypeEnum.UMS.getValue());
        if (Objects.nonNull(netInTask) && (TaskStatus.PROGRESSING.getVal().equals(netInTask.getStatus()) || TaskStatus.PENDING.getVal().equals(netInTask.getStatus()))) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                    .message("开通中，请稍后重试")
                    .retry(true)
                    .build();
        }
        if (Objects.nonNull(netInTask) && TaskStatus.FAIL.getVal().equals(netInTask.getStatus())) {
            return UnionPayOpenStatusQueryResp.builder()
                    .status(UnionPayOpenStatusQueryResp.FAIL_OPEN)
                    .message("开通失败")
                    .retry(false)
                    .build();
        }
        return UnionPayOpenStatusQueryResp.builder()
                .status(UnionPayOpenStatusQueryResp.NOT_OPEN)
                .message("暂时无法开通，联系销售支持")
                .retry(false)
                .build();
    }

    private ContractTask getNetInTask(String merchantSn, String acquirer) {
        List<ContractTask> contractTasks = contractTaskMapper.getContractsBySnAndType(merchantSn, ProviderUtil.CONTRACT_TYPE_INSERT);
        for (ContractTask contractTask : contractTasks) {
            RuleGroup ruleGroup = ruleContext.getRuleGroup(contractTask.getRule_group_id());
            if (ruleGroup.getAcquirer().equals(acquirer)) {
                return contractTask;
            }
        }
        return null;
    }

    private Map getBankAccount(String merchantId, String merchantSn) {
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", merchantId, "default_status", MerchantBankAccountPre.DEFAULT_STATUS_TRUE)
        );
        if (listResult == null || WosaiCollectionUtils.isEmpty(listResult.getRecords())) {
            throw new CommonPubBizException("获取商户卡信息为空merchantSn:" + merchantSn + "merchantId:" + merchantId);
        }
        return listResult.getRecords().get(0);
    }

    @Override
    public void updateClearanceProvider(String merchantId) {
        tradeConfigService.updateClearanceProvider(merchantId, TransactionParam.CLEARANCE_PROVIDER_YS);
    }
}
