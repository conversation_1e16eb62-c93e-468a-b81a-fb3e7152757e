package com.wosai.upay.job.biz;

import com.shouqianba.cua.enums.status.UseStatusEnum;
import com.wosai.common.exception.CommonDatabaseDuplicateKeyException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.bank.info.api.service.DistrictsServiceV2;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.biz.directparams.DirectParamsBizFactory;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.ProviderTerminalMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.MerchantParamReq;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.service.HaikeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-07-23
 */
@Component
@Slf4j
public class MerchantProviderParamsBiz {

    @Autowired
    private MerchantProviderParamsMapper paramsMapper;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    MerchantService merchantService;

    @Autowired
    private HaikeService haikeService;

    @Autowired
    ProviderTerminalMapper providerTerminalMapper;

    @Autowired
    ContractParamsBiz contractParamsBiz;

    @Autowired
    ParamContextBiz paramContextBiz;

    @Autowired
    DistrictsServiceV2 districtsServiceV2;

    public void saveDirectMerchantProviderParams(MerchantProviderParamsDto dto) {
        // 直连参数配置后直接设为使用中
        dto.setStatus(UseStatusEnum.IN_USE.getValue()).setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL);

        MerchantProviderParamsDto existParams = getDirectParams(dto.getMerchant_sn(), dto.getProvider(), dto.getPayway());
        if (existParams == null) {
            dto.setId(UUID.randomUUID().toString())
                    .setChannel_no(dto.getProvider().toString())
                    .setCtime(System.currentTimeMillis())
                    .setMtime(System.currentTimeMillis());

            paramsMapper.insertSelective(fromDO(dto));
        } else {
            Map<String, Object> extra = existParams.getExtra();
            extra.putAll(dto.getExtra());
            dto.setId(existParams.getId())
                    .setExtra(extra)
                    .setMtime(System.currentTimeMillis())
                    .setDeleted(false);
            paramsMapper.updateByPrimaryKeySelective(fromDO(dto));
        }
    }

    public MerchantProviderParamsDto getDirectParams(String merchantSn, int provider, int payway) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andProviderEqualTo(provider)
                .andPaywayEqualTo(payway);

        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isEmpty(params)) {
            return null;
        } else if (params.size() == 1) {
            return toDO(params.get(0));
        } else {
            throw new CommonDatabaseDuplicateKeyException(String.format("直连参数重复：%s %s %s", merchantSn, provider, payway));
        }

    }

    public MerchantProviderParamsDto getParamsById(String id) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andIdEqualTo(id).andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return toDO(params.get(0));
        }
        return null;
    }

    public void updateByPrimaryKeySelective(MerchantProviderParamsDto dto) {
        dto.setMtime(System.currentTimeMillis());
        paramsMapper.updateByPrimaryKeySelective(fromDO(dto));
    }

    public void deleteParamsById(String id) {
        paramsMapper.deleteByPrimaryKey(id);
    }

    public MerchantProviderParamsDto toDO(MerchantProviderParams params) {
        MerchantProviderParamsDto dto = new MerchantProviderParamsDto();
        BeanUtils.copyProperties(params, dto);
        dto.setExtra(CommonUtil.bytes2Map(params.getExtra()));
        return dto;
    }

    public MerchantProviderParams fromDO(MerchantProviderParamsDto dto) {
        MerchantProviderParams params = new MerchantProviderParams();
        BeanUtils.copyProperties(dto, params);
        params.setExtra(CommonUtil.map2Bytes(dto.getExtra()));
        return params;
    }

    /**
     * 处理直连交易参数
     * 1、直连交易参数虽然在数据库存储为1条，但是要在前端展示成多条
     *
     * @param dtos
     * @return
     */
    public List<MerchantProviderParamsCustomDto> handleDirectParams(List<MerchantProviderParamsCustomDto> dtos) {
        List<MerchantProviderParamsCustomDto> result = new ArrayList<>();
        List<MerchantProviderParamsCustomDto> directDtos = new ArrayList<>();

        for (MerchantProviderParamsCustomDto dto : dtos) {
            if (!dto.getProvider().equals(dto.getPayway())) {
                // 间连不处理
                result.add(dto);
            } else {
                directDtos.add(dto);
            }
        }
        for (MerchantProviderParamsCustomDto dto : directDtos) {
            result.addAll(DirectParamsBizFactory.getDirectParamsBiz(dto.getPayway()).handleDirectParams(dto));
        }
        return result;
    }

    public List<MerchantProviderParamsDto> getMerchantProviderParams(MerchantParamReq req) {
        if (WosaiStringUtils.isEmpty(req.getMerchant_sn()) && WosaiStringUtils.isEmpty(req.getPay_merchant_id())) {
            throw new CommonInvalidParameterException("merchant_sn 和 pay_merchant_id 不能同时为空");
        }
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        MerchantProviderParamsExample.Criteria criteria = example.or();

        if (WosaiStringUtils.isNotEmpty(req.getMerchant_sn())) {
            criteria.andMerchant_snEqualTo(req.getMerchant_sn());
        }
        if (WosaiStringUtils.isNotEmpty(req.getChannel_no())) {
            criteria.andChannel_noEqualTo(req.getChannel_no());
        }
        if (req.getProvider() != null) {
            criteria.andProviderEqualTo(req.getProvider());
        }
        if (req.getPayway() != null) {
            criteria.andPaywayEqualTo(req.getPayway());
        }
        if (WosaiStringUtils.isNotEmpty(req.getPay_merchant_id())) {
            criteria.andPay_merchant_idEqualTo(req.getPay_merchant_id());
        }

        criteria.andDeletedEqualTo(false);
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        return params.stream().map(param -> toDO(param)).collect(Collectors.toList());
    }

    /**
     * 保存收单机构信息
     * @param merchantSn
     * @param channelNo
     * @param provider
     * @param providerMerchantId
     * @param payMerchantId
     * @param contractRule
     * @param ruleGroupId
     */
    public void saveAcquirerParams(String merchantSn, String channelNo, int provider, String providerMerchantId, String payMerchantId, String contractRule, String ruleGroupId) {
        MerchantProviderParams params = new MerchantProviderParams().setId(UUID.randomUUID().toString())
                .setChannel_no(channelNo).setProvider(provider).setProvider_merchant_id(providerMerchantId).setPay_merchant_id(payMerchantId).setMerchant_sn(merchantSn)
                .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL).setContract_rule(contractRule).setRule_group_id(ruleGroupId)
                .setCtime(System.currentTimeMillis()).setMtime(System.currentTimeMillis());
        paramsMapper.insertSelective(params);
    }

    public void syncHaikeTerminal(MerchantProviderParams setDefault){
        CompletableFuture.runAsync(()-> {
            ContractResponse syncResponse = haikeService.syncTerminalAndMerchant(setDefault.getPay_merchant_id());
            if (!syncResponse.isSuccess()) {
                log.error(String.format("海科商户号绑定终端信息记录异常,商户SN:%s,子商户号:%s,原因:%s", setDefault.getMerchant_sn(), setDefault.getPay_merchant_id(), "海科同步终端信息失败"));
            }
        });
    }


    public boolean checkHaikeUnionSuccess(MerchantProviderParams params){
        HaikeParam haikeParam = contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(params.getMerchant_sn(), null);
        ContractResponse response = haikeService.queryMerchant(contextParam, haikeParam);
        if (response.isSuccess()){
            Map responseParam = response.getResponseParam();
            Map channelBiz = WosaiMapUtils.getMap(responseParam, "channel_biz_info");
            if(WosaiMapUtils.isEmpty(channelBiz)){
                return false;
            }
            Map bankInfo = WosaiMapUtils.getMap(channelBiz, "bank_info");
            String applyStatus = BeanUtil.getPropString(bankInfo, "apply_status");
            String reportStatus = BeanUtil.getPropString(bankInfo, "report_status");
            if (StringUtil.empty(applyStatus) || StringUtil.empty(reportStatus) || "0".equals(applyStatus) || "0".equals(reportStatus)){
                return false;
            }
            return "3".equals(BeanUtil.getPropString(bankInfo, "apply_status")) && "3".equals(BeanUtil.getPropString(bankInfo, "report_status"));
        }else {
            return false;
        }
    }
}
