package com.wosai.upay.job.biz;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信/支付宝行业分类目录
 *
 * <AUTHOR>
 * @date 2019-08-09 14:06
 */
@Component
@Data
@ToString
@ConfigurationProperties
@PropertySource("classpath:metadata/business-category.properties")
public class BusinessCategory {

    /**
     * 支付宝支付目录
     */
    private Map<String, String> alipayBusinessCategory;

    /**
     * 微信支付目录
     */
    private Map<String, String[]> weixinBusinessCategory;

}
