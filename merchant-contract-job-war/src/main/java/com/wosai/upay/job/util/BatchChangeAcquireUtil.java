package com.wosai.upay.job.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.config.OssAK;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

/**
 * @Description: 网络文件的读取与上传
 * <AUTHOR>
 * @Date 2021/9/6 13:56
 */
@Slf4j
public class BatchChangeAcquireUtil {

    public static final String ENDPOINT_URL = "http://oss-cn-hangzhou.aliyuncs.com/";
    public static String STATICS_BUCKET_NAME = "wosai-images";

    public static <T> List<T> getExcelInfoList(String url, T t) {
        ImportParams params = new ImportParams();
        InputStream inputStream = null;
        ExcelImportResult<T> res;
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                inputStream = connection.getInputStream();
            }
            res = ExcelImportUtil.importExcelMore(inputStream, t.getClass(), params);
        } catch (Exception e) {
            log.error("获取网络图片出现异常{}", e);
            throw new CommonPubBizException("获取网络文件出现异常，文件路径为：" + url);
        }
        return res.getList();
    }


    public static <T> String uploadToOss(List<T> sources, String baseDir) {
        File file = null;
        try {
            //上传结果
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            final Class<?> aClass = Class.forName(sources.get(0).getClass().getName());
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, aClass, sources);
            file = File.createTempFile(System.currentTimeMillis() + "", ".xlsx");
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            workbook.write(fileOutputStream);
            fileOutputStream.close();
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(file.length());
            OSSClient client = OssAK.buildOSSClient(ENDPOINT_URL);
            String key = baseDir + file.getName();
            client.putObject(STATICS_BUCKET_NAME, key, new FileInputStream(file), objectMeta);
            return ENDPOINT_URL + STATICS_BUCKET_NAME + "/" + key;
        } catch (Exception e) {
            log.error(" uploadToOss error {}", e);
        } finally {
            FileUtils.deleteQuietly(file);
        }
        return null;
    }
}
