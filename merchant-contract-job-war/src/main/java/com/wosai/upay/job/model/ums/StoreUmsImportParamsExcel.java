package com.wosai.upay.job.model.ums;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class StoreUmsImportParamsExcel implements IExcelModel, IExcelDataModel {

    @Excel(name = "收钱吧门店号", width = 20)
    @NotEmpty(message = "门店号不能为空")
    private String storeSn;

    @Excel(name = "BSC银商终端号", width = 20)
    @NotEmpty(message = "BSC银商终端号不能为空")
    private String bscTerm;

    @Excel(name = "CSB银商终端号", width = 20)
    @NotEmpty(message = "CSB银商终端号不能为空")
    private String csbTerm;

    // Excel导入结果信息
    @Excel(name = "导入结果")
    private String message;

    // 行号
    private int rowNum;

    // 错误信息
    private String errorMsg;

    @Override
    public int getRowNum() {
        return rowNum;
    }

    @Override
    public void setRowNum(int i) {
        this.rowNum = i;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String msg) {
        this.errorMsg = msg;
    }
}
