package com.wosai.upay.job.refactor.model.enums;

/**
 * 银行交易保障状态枚举
 *
 * <AUTHOR>
 */
public enum BankTradeProtectionStatusEnum implements ITextValueEnum {
    /**
     * 关闭保障
     */
    CLOSE(0, "关闭保障"),

    /**
     * 打开保障
     */
    OPEN(1, "打开保障");

    private final int value;
    private final String text;

    BankTradeProtectionStatusEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
