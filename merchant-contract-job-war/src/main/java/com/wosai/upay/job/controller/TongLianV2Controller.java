package com.wosai.upay.job.controller;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.ProviderTerminalIdBiz;
import com.wosai.upay.job.biz.*;
import com.wosai.upay.job.biz.acquirePos.TlT9HandleService;
import com.wosai.upay.job.enume.SubTaskStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.providers.TongLianV2Provider;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.job.util.CommonUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static javax.management.timer.Timer.ONE_DAY;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 通联收银宝 信息回调
 * <AUTHOR>
 * @Date 2023/6/7 15:50
 **/

@RestController
@RequestMapping("/tlv2")
@Slf4j
public class TongLianV2Controller {

    @Autowired
    TaskResultService taskResultService;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    ProviderTerminalIdBiz providerTerminalIdBiz;

    @Autowired
    ProviderTerminalBiz providerTerminalBiz;

    @Autowired
    TongLianV2Provider tongLianV2Provider;

    @Autowired
    private IndustryMappingCommonBiz industryMappingCommonBiz;

    @Autowired
    MerchantService merchantService;

    @Autowired
    TradeConfigService tradeConfigService;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    private ApplicationApolloConfig apolloConfig;

    @Value("${tl.channelno}")
    private String channelNo;

    @Value("${tl.public.key}")
    private String publicKey;

    @Autowired
    private TlT9HandleService tlV2T9HandleService;

    private static final String VALUE_EDITTYPE_BASIC = "basic";             //基本信息修改类型
    private static final String VALUE_EDITTYPE_CLEAR = "clear";             //结算信息类型
    private static final String VALUE_EDITTYPE_PROD = "prod";               //产品信息-> 费率
    private static final String VALUE_EDITTYPE_TERM = "term";               //产品信息-> 商户终端
    private static final String VALUE_EDITTYPE_IMAGE = "image";             //图片附件
    private static final String VALUE_NOTIFYTYPE_ADDMERCHANT = "addmerchant";     //新增商户
    private static final String VALUE_NOTIFYTYPE_EDITMERCHANT = "editmerchant";   //修改商户信息

    private static final String VALUE_AUDITSTATUS_SUCCESS = "02";                 //审核通过
    private static final String VALUE_AUDITSTATUS_FAIL = "03";                    //审核失败
    private static final String VALUE_CREATESTATUS_FAIl = "04";                 //提交失败

    private static final String SUBTASK_RESULT_SUCCESS = "通联收银宝返回成功";
    private static final String SUBTASK_RESULT_FAIL = "通联收银宝返回失败";

    private static final String CONTRACT_CALLBACK_MSG = "callback_msg";


    @RequestMapping(path = "/callback", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String contractCallBack(@RequestParam Map<String, String> param) {
        log.info("tlv2 callback : {}", JSON.toJSONString(param));
        if (apolloConfig.isIgnoreTLV2Callback()) {
            return "success";
        }
        AuditNotifyRequest auditNotifyRequest = JSON.parseObject(JSON.toJSONString(param), AuditNotifyRequest.class);
        if (VALUE_NOTIFYTYPE_ADDMERCHANT.equals(auditNotifyRequest.getNotifytype())) {
            handleAdd(auditNotifyRequest);
        } else {
            handleEdit(auditNotifyRequest);
        }
        return "success";
    }

    private void handleAdd(AuditNotifyRequest auditNotifyRequest) {
    }

    private void handleEdit(AuditNotifyRequest auditNotifyRequest) {
        MerchantProviderParams params = merchantProviderParamsMapper.getByPayMerchantId(auditNotifyRequest.getCusid());
        String merchantSn = params.getMerchant_sn();

        //由于开通刷卡,外卡,预授权没有生成task,所以会有不存在的场景,并且开通刷卡,外卡,预授权这些都是免审的所以通过使用redis记录来匹配,终端通过provider_terminal_bind_config绑定记录来匹配
        if(VALUE_NOTIFYTYPE_EDITMERCHANT.equals(auditNotifyRequest.getNotifytype())) {
            String editType = auditNotifyRequest.getEdittype();
            if (editType.equals(VALUE_EDITTYPE_PROD)) {
                //通联收银宝的产品信息修改成功回调
                final Boolean productCallback = tlV2T9HandleService.t9ProductCallback(merchantSn, auditNotifyRequest);
                //如果是新增刷卡的回调,则后续代码不需要执行
                if(productCallback) {
                    return;
                }

            }else if (editType.equals(VALUE_EDITTYPE_TERM)) {
                final Boolean termCallback = tlV2T9HandleService.t9TermCallback(merchantSn, auditNotifyRequest);
                if(termCallback) {
                    return;
                }
            }
        }


        List<ContractSubTask> subTasks = contractSubTaskMapper.selectMchTongLianV2UnCallbackTask(merchantSn, StringUtil.formatDate(System.currentTimeMillis() - ONE_DAY));

        ContractSubTask contractSubTask = null;
        String edittype = auditNotifyRequest.getEdittype();
        if (Objects.equals(edittype, VALUE_EDITTYPE_CLEAR)) {
            contractSubTask = subTasks.stream().filter(subTask ->
                    ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE.equals(subTask.getTask_type()) || ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS.equals(subTask.getTask_type())
            ).findAny().orElse(null);
        } else if (Objects.equals(edittype, VALUE_EDITTYPE_PROD)) {
            contractSubTask = subTasks.stream().filter(subTask ->
                    ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE.equals(subTask.getTask_type())
            ).findAny().orElse(null);
        } else if (Objects.equals(edittype, VALUE_EDITTYPE_BASIC)) {
            contractSubTask = subTasks.stream().filter(subTask ->
                    ProviderUtil.SUB_TASK_TASK_TYPE_UPDATE_BUSINESS_LICENSE.equals(subTask.getTask_type()) || ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION.equals(subTask.getTask_type())
            ).findAny().orElse(null);
        }
        if (Objects.isNull(contractSubTask)) {
            return;
        }
        if (VALUE_AUDITSTATUS_SUCCESS.equals(auditNotifyRequest.getAuditstatus())) {
            updateStatus(true, contractSubTask, auditNotifyRequest, null);
        } else if (VALUE_AUDITSTATUS_FAIL.equals(auditNotifyRequest.getAuditstatus()) || VALUE_CREATESTATUS_FAIl.equals(auditNotifyRequest.getAuditstatus())) {
            updateStatus(false, contractSubTask, auditNotifyRequest, auditNotifyRequest.getErrmsg());
        }
    }

    public void updateStatus(Boolean success, ContractSubTask contractSubTask, AuditNotifyRequest callbackMsg, String message) {
        Long taskId = contractSubTask.getP_task_id();
        if (TaskStatus.isFinish(contractSubTask.getStatus())) {
            return;
        }
        Map resp = JSON.parseObject(contractSubTask.getResponse_body(), Map.class);
        resp.put(CONTRACT_CALLBACK_MSG, callbackMsg);
        ContractSubTask update = new ContractSubTask()
                .setId(contractSubTask.getId())
                .setStatus(success ? SubTaskStatus.SUCCESS.getVal() : SubTaskStatus.FAIL.getVal())
                .setResponse_body(JSON.toJSONString(resp))
                .setResult(success ? SUBTASK_RESULT_SUCCESS : SUBTASK_RESULT_FAIL);
        contractSubTaskMapper.updateByPrimaryKey(update);
        if (contractSubTask.getStatus_influ_p_task() == 1) {
            if (success) {
                contractTaskMapper.addAffectStatusSuccessTaskCount(taskId);
                contractSubTaskMapper.setEnableScheduleByDepId(contractSubTask.getId());
                ContractTask task = contractTaskMapper.selectByPrimaryKey(taskId);
                if (task.getStatus() != 1) {
                    taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), task.getStatus(), null, false);
                }
            } else {
                Map reMsg = CollectionUtil.hashMap("channel", ProviderUtil.TONGLIAN_V2_CHANNEL, "message", message, "result", message);
                taskResultService.changeStatusAndResultV2(taskId, contractSubTask.getId(), 6, JSON.toJSONString(reMsg), false);
            }
        } else {
            if (success) {
                contractSubTaskMapper.setEnableScheduleByDepId(contractSubTask.getId());
            }
        }
    }

    private void handleContractTlV2Success(ContractSubTask subTask, String cusid) {
        String merchantSn = subTask.getMerchant_sn();
        MerchantInfo merchantInfo = merchantService.getMerchantBySn(merchantSn, null);

        Map extra = CollectionUtil.hashMap(CommonModel.MCH_ID, cusid,
                CommonModel.PROVIDER_MCH_ID, cusid,
                "mer_cat_code", industryMappingCommonBiz.getTongLianV2Code(merchantInfo.getIndustry())
        );
        MerchantProviderParams tonglianV2param = merchantProviderParamsMapper.selectByPayMerchantId(cusid);
        long now = System.currentTimeMillis();
        if (tonglianV2param == null) {
            MerchantProviderParams params = new MerchantProviderParams()
                    .setId(UUID.randomUUID().toString())
                    .setMerchant_sn(merchantSn)
                    .setOut_merchant_sn(merchantSn)
                    .setChannel_no(channelNo)
                    .setParent_merchant_id(cusid)
                    .setProvider(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())
                    .setProvider_merchant_id(cusid)
                    .setPayway(PaywayEnum.ACQUIRER.getValue())
                    .setParams_config_status(MerchantProviderParams.PARAMS_CONFIG_STATUS_NULL)
                    .setPay_merchant_id(cusid)
                    .setContract_rule(McConstant.RULE_GROUP_TONGLIAN_V2)
                    .setRule_group_id(McConstant.RULE_GROUP_TONGLIAN_V2)
                    .setExtra(CommonUtil.map2Bytes(CollectionUtil.hashMap("tradeParams", extra)))
                    .setCtime(now)
                    .setMtime(now)
                    .setStatus(1);
            merchantProviderParamsMapper.insertSelective(params);
        }
        String providerTerminalId = providerTerminalIdBiz.getProviderTerminalIdByMerchantSn();
        providerTerminalBiz.merchantConnectionProviderTerminal(subTask.getMerchant_sn(), providerTerminalId, cusid, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue());
        tradeConfigService.updateTLTradeParams(merchantInfo.getId(), CollectionUtil.hashMap(TransactionParam.TL_MCH_ID, cusid));
    }

    @Data
    public static class AuditNotifyRequest {
        /**
         * 平台分配的代理商号
         */
        private String orgid;

        /**
         * 平台分配的商户号
         */
        private String cusid;

        /**
         * 平台分配的机构APPID
         */
        private String appid;

        /**
         * 通知类型类型
         * addmerchant 新增商户
         * editmerchant 编辑商户
         */
        private String notifytype;

        /**
         * 编辑子类型
         * basic 基本信息
         * clear 结算信息
         * image 图片附件
         */
        private String edittype;

        /**
         * 代理商商户号
         */
        private String merchantid;

        /**
         * 进件请求流水号
         */
        private String reqid;

        /**
         * 审核状态
         * 01-待审核
         * 02-审核通过
         * 03-审核失败
         * 04-提交审核失败
         */
        private String auditstatus;

        /**
         * 审核失败错误信息
         */
        private String errmsg;

        /**
         * 签名信息
         */
        private String sign;

        /**
         * 通知时间
         */
        private long callback_time = System.currentTimeMillis();
    }

}