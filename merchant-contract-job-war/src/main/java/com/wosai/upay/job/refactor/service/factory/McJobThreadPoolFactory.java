package com.wosai.upay.job.refactor.service.factory;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.wosai.upay.merchant.contract.exception.ContractSysException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * 进件线程池工厂
 *
 * <AUTHOR>
 * @date 2023/10/13 17:37
 */
public final class McJobThreadPoolFactory {

    private static final Logger logger = LoggerFactory.getLogger(McJobThreadPoolFactory.class);

    private static final Integer CORE_THREAD_NUM = Runtime.getRuntime().availableProcessors() * 4;

    private static final Integer MAX_THREAD_NUM = 2 * CORE_THREAD_NUM;

    private static final RejectedExecutionHandler EXECUTION_HANDLER = new ThreadPoolExecutor.AbortPolicy();

    private static final int KEEP_ALIVE_TIME = 10;

    private static final int DEFAULT_THREAD_WORKER_QUEUE_CAPACITY = 100_000;


    private McJobThreadPoolFactory() {
    }

    public static ExecutorService getInstance() {
        return ThreadPoolHolder.INSTANCE;
    }

    private static class ThreadPoolHolder {
        private static final ExecutorService INSTANCE = new ThreadPoolExecutor(
                CORE_THREAD_NUM,
                MAX_THREAD_NUM,
                KEEP_ALIVE_TIME,
                TimeUnit.MILLISECONDS,
                new ArrayBlockingQueue<>(DEFAULT_THREAD_WORKER_QUEUE_CAPACITY),
                new ThreadFactoryBuilder()
                        .setUncaughtExceptionHandler((t, e) -> {
                            logger.error("[merchant contract job thread error], {}", e.getMessage());
                            throw new ContractSysException("merchant contract job thread error");
                        })
                        .setNameFormat("mc-thread-%d").build(),
                EXECUTION_HANDLER);
    }
}
