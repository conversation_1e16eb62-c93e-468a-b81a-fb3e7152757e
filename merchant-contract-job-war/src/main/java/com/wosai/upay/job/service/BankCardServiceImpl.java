package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.model.Request;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.RMQService;
import com.wosai.upay.job.constant.CommonConstants;
import com.wosai.upay.merchant.contract.avro.MerchantBankPreAvro;
import com.wosai.upay.merchant.contract.constant.LakalaBusinessFileds;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
/**
 * @Author: jerry
 * @date: 2019/5/28 17:40
 * @Description:job跟银行卡管理相关的耦合部分处理 暂定的修改逻辑。
 */
@Component
public class BankCardServiceImpl {

    @Value("${bank.message.send.topic}")
    private String avroTopic;
    @Resource(name = "kafkaTemplate")
    private KafkaTemplate<String, Object> kafkaTemplate;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private MerchantBankService merchantBankService;
    @Autowired
    private RMQService rmqService;
    @Autowired
    private BankService bankService;

    private final static Logger LOGGER = LoggerFactory.getLogger(BankCardServiceImpl.class);


    /**
     * 任务状态 ——》通知卡
     *
     * @param:
     * @return:
     * @date: 18:26
     */
    public void updateCardAfterTaskStatus(Map context, int verifyStatus, String memo) {
        Map source = (Map) context.get("cardRequestParam");
        Map merchant = (Map) context.get("merchant");
        if (CollectionUtils.isEmpty(source)) {
            String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
            Map<String, Object> merchantBankAccount = merchantService.getMerchantBankAccountByMerchantId(merchantId);
            updateBankByCoreB(merchant, merchantBankAccount, verifyStatus, memo);
        } else {
            String merchantSn = BeanUtil.getPropString(merchant, Merchant.SN);
            sendMesaageToBank(source, verifyStatus, merchantSn, memo);
        }
    }


    /**
     * 卡变更 发送卡最终验证状态
     *
     * @param:
     * @return:
     * @date: 18:08
     */
    public void sendMesaageToBank(Map cardRequestParam, int verifyStatus, String merchantSn, String memo) {
        if (verifyStatus == MerchantBankAccountPre.VERIFY_STATUS_INPROGRESS) {
            LOGGER.info("card {} merchantSn {} pre verifyStatus {} not send message", cardRequestParam, merchantSn, verifyStatus);
            return;
        }
        String merchantBankAccountPreId = BeanUtil.getPropString(cardRequestParam, DaoConstants.ID);
        MerchantBankPreAvro merchantBankPreAvro = new MerchantBankPreAvro();
        String requestParams = JSON.toJSONString(cardRequestParam);
        merchantBankPreAvro.setRequestParams(requestParams);
        merchantBankPreAvro.setId(merchantBankAccountPreId);
        merchantBankPreAvro.setMerchantSn(merchantSn);
        merchantBankPreAvro.setVerifyStatus(verifyStatus);
        merchantBankPreAvro.setContractMemo(memo);
        merchantBankPreAvro.setMtime(System.currentTimeMillis());
        merchantBankPreAvro.setContractId(null);
        ProducerRecord record = new ProducerRecord(avroTopic, merchantBankPreAvro);
        try {
            kafkaTemplate.send(record);
            LOGGER.info("send kafka complete record {} ", record);
        } catch (Exception e) {
            LOGGER.error("send kafka error record {} e", record, e);
        }

    }


    /**
     * 卡变更任务 重新提交的情况下需要进件修改卡为验证中
     *
     * @param:Map source 任务表的source内容
     * @return:
     * @date: 15:55
     */
    public void resubmitChangePreInProgressVerify(Map source) {
        String merchantBankAccountPreId = BeanUtil.getPropString(source, com.wosai.data.dao.DaoConstants.ID);
        Map request = CollectionUtil.hashMap(
                com.wosai.data.dao.DaoConstants.ID, merchantBankAccountPreId,
                MerchantBankAccountPre.VERIFY_STATUS, MerchantBankAccountPre.VERIFY_STATUS_INPROGRESS
        );
        merchantBankService.updateMerchantBankAccountPre(request);
        LOGGER.info("resubmit update bankPre request {}", request);
        String merchantId = BeanUtil.getPropString(source, MerchantBankAccountPre.MERCHANT_ID);
        merchantBankService.updateMerchantBankAccount(CollectionUtil.hashMap(MerchantBankAccount.MERCHANT_ID, merchantId, MerchantBankAccount.VERIFY_STATUS, MerchantBankAccount.VERIFY_STATUS_INPROGRESS));
        LOGGER.info("resubmit update bank inProgress merchantId {}", merchantId);
    }


    /**
     * 通过core-b接口修改卡状态  商户入网 银行账户变更调用
     *
     * @param:
     * @return:
     * @date: 18:07
     */
    private void updateBankByCoreB(Map merchant, Map merchantBankAccount, int verifyStatus, String memo) {
        if (CollectionUtils.isEmpty(merchantBankAccount)) {
            LOGGER.info(" merchantBankAccount empty");
            return;
        }
        if (!StringUtils.isEmpty(memo)) {
            Map extra = (Map) BeanUtil.getProperty(merchantBankAccount, MerchantBankAccount.EXTRA);
            if (extra == null) {
                extra = new HashMap();
            }
            extra.put(LakalaBusinessFileds.CONTRACT_MEMO, memo);
            merchantService.updateBankAccountInnerMethod(CollectionUtil.hashMap(DaoConstants.ID, BeanUtil.getPropString(merchantBankAccount, DaoConstants.ID), MerchantBankAccount.VERIFY_STATUS, verifyStatus, MerchantBankAccount.EXTRA, extra));
        } else {
            merchantService.updateBankAccountInnerMethod(CollectionUtil.hashMap(
                    DaoConstants.ID, BeanUtil.getPropString(merchantBankAccount, DaoConstants.ID),
                    MerchantBankAccount.VERIFY_STATUS, verifyStatus));
        }
        LOGGER.info(" updateBankByCoreB bankId {} verifyStatus {} merchant {} memo {}", BeanUtil.getPropString(merchantBankAccount, DaoConstants.ID), verifyStatus, merchant, memo);
        if (verifyStatus == MerchantBankAccount.VERIFY_STATUS_FAIL || verifyStatus == MerchantBankAccount.VERIFY_STATUS_SUCC) {
            notifyMerchantContractStatus(BeanUtil.getPropString(merchant, DaoConstants.ID), BeanUtil.getPropString(merchant, Merchant.SN),
                    BeanUtil.getPropString(merchant, Merchant.NAME), verifyStatus, memo);
        }

        //删除账户类型不同或者异名的卡(入网成功需要)
        if (verifyStatus == MerchantBankAccount.VERIFY_STATUS_SUCC) {
            try {
                String merchantId = BeanUtil.getPropString(merchant, DaoConstants.ID);
                Map source = CollectionUtil.hashMap(Request.KEY_OPERATOR, CommonConstants.OPERATOR_NAME, Request.KEY_PLATFORM, Request.KEY_PLATFORM_CRM, Request.KEY_REMARK, "商户入网");
                bankService.deleteDifferentTypeOrDifferentIdentityCard(merchantBankAccount, source, merchantId);
            } catch (Exception e) {
                LOGGER.error(" deleteDifferentTypeOrDifferentIdentityCard merchant {} bank {}  error", merchant, merchantBankAccount, e);
            }

        }
    }


    /**
     * 商户进件类型为入网，回调或者主动查询得到进件结果之后，对下游需要知道商户进件状态的服务进行通知
     *
     * @param merchantId
     * @param merchantSn
     * @param merchantName
     * @param bankAccountVerifyStatus
     * @param contractMemo
     */
    private void notifyMerchantContractStatus(String merchantId, String merchantSn, String merchantName, int bankAccountVerifyStatus, String contractMemo) {
        try {
            LOGGER.info("notify merchant {} contract  result, bank account verify status {}, contract memo is {}", merchantSn, bankAccountVerifyStatus, contractMemo);
            rmqService.writeMerchantBankAccountVerify(CollectionUtil.hashMap(ConstantUtil.KEY_MERCHANT_ID, merchantId,
                    ConstantUtil.KEY_MERCHANT_SN, merchantSn,
                    ConstantUtil.KEY_MERCHANT_NAME, merchantName,
                    MerchantBankAccount.VERIFY_STATUS, bankAccountVerifyStatus,
                    LakalaBusinessFileds.CONTRACT_MEMO, contractMemo));
        } catch (Exception t) {
            LOGGER.info("fail to notify merchant {} contract  result, bank account verify status {}, contract memo is {}, exception", merchantSn, bankAccountVerifyStatus, contractMemo, t);
        }
    }
}
