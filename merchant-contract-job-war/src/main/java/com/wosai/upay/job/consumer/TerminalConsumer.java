package com.wosai.upay.job.consumer;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.AbstractEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicInsertEvent;
import com.wosai.databus.event.terminal.basic.TerminalBasicStatusChangeEvent;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.LklV3ShopTermBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.LklV3Term;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.job.util.JacksonHelperUtils;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.service.CrmEdgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.generic.GenericRecord;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * @Description: 交易终端激活解绑的消息
 * <AUTHOR>
 * @Date 2021/4/27 3:23 下午
 **/

@Component
@Slf4j
@ConditionalOnProperty(name = "consumer.init", havingValue = "true")
public class TerminalConsumer extends AbstractDataBusConsumer {

    @Autowired
    LklV3ShopTermBiz lklV3ShopTermBiz;

    @Autowired
    StoreService storeService;

    @Autowired
    MerchantService merchantService;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    ContractTaskBiz contractTaskBiz;

    @Autowired
    TerminalService terminalService;

    @Autowired
    StoreCreateConsumer storeCreateConsumer;
    @Autowired
    @Lazy
    MerchantProviderParamsService merchantProviderParamsService;

    @Value("${indirect-pay.dev_code}")
    private String devCode;

    @Autowired
    ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    CrmEdgeService crmEdgeService;

    @KafkaListener(topics = "#{'${databus.consumer.terminal.topic}'.split(',')}", containerFactory = "dataBusKafkaListenerContainerFactoryV2")
    @Transactional(rollbackFor = Exception.class)
    public void consume(org.apache.kafka.clients.consumer.ConsumerRecord<String, GenericRecord> record) {
        handle(record);
    }

    @Override
    public void doHandleEvent(AbstractEvent event) {
        log.info("start handling terminalBasic,switcher:{}, event: {}", applicationApolloConfig.getLklV3ShopTermSwitch(), JacksonHelperUtils.toJsonString(event));
        if (!applicationApolloConfig.getLklV3ShopTermSwitch()) {
            return;
        }
        if (event instanceof TerminalBasicInsertEvent) {
            String merchantId = ((TerminalBasicInsertEvent) event).getData().getMerchantId();
            //商户校验
            MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
            if (Objects.isNull(merchant)) {
                return;
            }
            //门店校验
            String storeId = ((TerminalBasicInsertEvent) event).getData().getStoreId();
            StoreInfo store = storeService.getStoreById(storeId, devCode);
            if (Objects.isNull(store)) {
                return;
            }
            //终端校验
            String terminalId = ((TerminalBasicInsertEvent) event).getTerminalId();
            Map terminal = terminalService.getTerminalByTerminalId(terminalId);
            if (Objects.isNull(terminal)) {
                return;
            }
            final String vendorAppAppid = BeanUtil.getPropString(terminal, Terminal.VENDOR_APP_APPID);

            // 特殊终端按小终端报备
            if (applicationApolloConfig.getTerminalLevelVendorAppAppid().contains(vendorAppAppid)) {
                doInsert((TerminalBasicInsertEvent) event, merchant);
                //259文件终端级别绑定新需求
                do259TerminalBind((TerminalBasicInsertEvent) event, merchant, terminal);
            } else {
                // 其余默认按门店级别终端报备
                storeCreateConsumer.do259StoreBind(store.getSn());
            }
        } else if (event instanceof TerminalBasicStatusChangeEvent) {
            doUnbind((TerminalBasicStatusChangeEvent) event);
            //259文件终端解绑解绑新需求
            do259TerminalUnBind((TerminalBasicStatusChangeEvent) event);
        }
    }

    private void doInsert(TerminalBasicInsertEvent event, MerchantInfo merchant) {
        String terminalId = event.getTerminalId();
        String merchantId = event.getData().getMerchantId();
        List<AcquirerMerchantDto> acquirerList = merchantProviderParamsService.getAcquirerMerchantInfo(merchantId);
        //是否成功进件拉卡拉
        acquirerList.stream().forEach(info -> {
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(info.getProvider()));
            if (handleProvider == null) {
                log.info("商户ID:{},终端ID;{},未找到处理类", merchantId, terminalId);
                return;
            }
            //处理绑定
            handleProvider.produceInsertTerminalTaskByRule(event, merchant);
        });
    }


    public void do259TerminalBind(TerminalBasicInsertEvent event, MerchantInfo merchant, Map terminal) {
        String terminalId = event.getTerminalId();
        String merchantSn = merchant.getSn();
        //1,当前商户报过的所有收单机构
        final List<Integer> providerList = providerTerminalBiz.getProviderList(merchantSn);
        //根据不同provider处理
        providerList.parallelStream().filter(provider -> !Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue())).forEach(provider -> {
            BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(provider));
            if (handleProvider == null) {
                log.info("商户ID:{},终端ID;{},未找到处理类", merchant.getId(), terminalId);
                return;
            }
            //处理绑定
            handleProvider.handleSqbTerminalBind(merchant, terminal, provider);
        });
    }


    public void do259TerminalUnBind(TerminalBasicStatusChangeEvent event) {
        if (0 != event.getStatus()) {
            return;
        }
        String terminalId = event.getTerminalId();
        Map terminal = terminalService.getTerminalByTerminalId(terminalId);
        String merchantId = MapUtils.getString(terminal, "merchant_id");
        //终端校验
        if (Objects.isNull(terminal)) {
            return;
        }
        //商户校验
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
        if (Objects.isNull(merchant)) {
            return;
        }
        String merchantSn = merchant.getSn();
        //门店校验
        String storeId = MapUtils.getString(terminal, "store_id");
        StoreInfo store = storeService.getStoreById(storeId, devCode);
        if (Objects.isNull(store)) {
            return;
        }
        //1,当前商户报过的所有收单机构
        final List<Integer> providerList = providerTerminalBiz.getProviderList(merchantSn);
        //根据不同provider处理
        providerList.parallelStream()
                .filter(provider -> !Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue()))
                .forEach(provider -> {
                    BasicProvider handleProvider = providerFactory.getProvider(String.valueOf(provider));
                    if (handleProvider == null) {
                        log.info("商户ID:{},终端ID;{},未找到处理类", merchantId, terminalId);
                        return;
                    }
                    //处理绑定
                    handleProvider.handleSqbTerminalUnBind(merchant, terminal, provider);
                });

    }


    private void doUnbind(TerminalBasicStatusChangeEvent event) {
        if (0 != event.getStatus()) {
            return;
        }
        String terminalId = event.getTerminalId();
        Map terminal = terminalService.getTerminalByTerminalId(terminalId);
        String merchantId = MapUtils.getString(terminal, "merchant_id");
        MerchantInfo merchant = merchantService.getMerchantById(merchantId, devCode);
        String merchantSn = merchant.getSn();
//        if (!AcquirerTypeEnum.LKL_V3.getValue().equalsIgnoreCase(acquirerBiz.getMerchantAcquirer(merchantSn))) {
//            return;
//        }
        String storeId = MapUtils.getString(terminal, "store_id");
        StoreInfo store = storeService.getStoreById(storeId, devCode);
        LklV3Term termInfo = lklV3ShopTermBiz.getTermInfo(store.getSn(), terminalId);
        if (Objects.isNull(termInfo)) {
            return;
        }
        String termInfoStatus = termInfo.getStatus();
        if (LklV3Term.TERM_UNBIND.equalsIgnoreCase(termInfoStatus) || LklV3Term.TERM_WAIT_UNBIND.equalsIgnoreCase(termInfoStatus) || LklV3Term.TERM_UNBIND.equalsIgnoreCase(termInfoStatus)) {
            return;
        }
        Map context = CollectionUtil.hashMap(CommonModel.STORE_SN, store.getSn(), "terminalId", terminalId);
        ContractTask task = new ContractTask().setMerchant_sn(merchantSn).setMerchant_name(merchant.getName()).setStatus(0)
                .setType(ProviderUtil.CONTRACT_TYPE_ADDTERM).setAffect_status_success_task_count(0).setAffect_sub_task_count(1).setEvent_context(JSON.toJSONString(context));
        contractTaskBiz.insert(task);
        ContractSubTask subTask = new ContractSubTask().setTask_type(ProviderUtil.SUB_TASK_TASK_TYPE_UNBIND_TERM)
                .setStatus_influ_p_task(1)
                .setChannel(ProviderUtil.LKL_V3_PROVIDER_CHANNEL)
                .setMerchant_sn(merchantSn)
                .setContract_rule(McConstant.RULE_GROUP_LKLV3)
                .setRule_group_id(McConstant.RULE_GROUP_LKLV3)
                .setSchedule_dep_task_id(termInfo.getSubTaskId())
                .setSchedule_status(LklV3Term.TERM_SUCCESS.equals(termInfoStatus) ? 1 : 0)
                .setP_task_id(task.getId());
        contractSubTaskMapper.insert(subTask);
    }


}