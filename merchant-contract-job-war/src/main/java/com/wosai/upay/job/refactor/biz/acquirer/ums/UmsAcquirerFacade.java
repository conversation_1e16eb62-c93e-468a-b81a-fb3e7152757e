package com.wosai.upay.job.refactor.biz.acquirer.ums;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import org.springframework.stereotype.Service;

/**
 * 银联商务收单处理策略
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class UmsAcquirerFacade extends AbstractAcquirerHandler {

    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.UMS;
    }


}
