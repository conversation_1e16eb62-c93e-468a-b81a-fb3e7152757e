package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * (子)规则连接类型(嵌套子规则)枚举
 *
 * <AUTHOR>
 */
public enum RuleConnectionTypeEnum implements ITextValueEnum<String> {

    AND("AND", "等于"),

    OR("OR", "或者");

    private final String value;
    private final String text;

    RuleConnectionTypeEnum(String value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public String getValue() {
        return this.value;
    }
}
