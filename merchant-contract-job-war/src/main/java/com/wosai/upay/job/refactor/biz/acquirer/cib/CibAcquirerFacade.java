package com.wosai.upay.job.refactor.biz.acquirer.cib;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

/**
 * 上海兴业收单处理门面
 *
 * <AUTHOR>
 * @date 2023/12/11 14:34
 */
@Service
public class CibAcquirerFacade extends AbstractAcquirerHandler {


    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.CIB;
    }

}
