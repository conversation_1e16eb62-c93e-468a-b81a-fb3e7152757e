package com.wosai.upay.job.mapper;

import com.wosai.upay.job.model.DO.BlueSeaTask;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface BlueSeaTaskMapper {
    int deleteByPrimaryKey(Long id);

    int insert(BlueSeaTask record);

    int insertSelective(BlueSeaTask record);

    BlueSeaTask selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BlueSeaTask record);

    int updateByPrimaryKeyWithBLOBs(BlueSeaTask record);

    int updateByPrimaryKey(BlueSeaTask record);

    /**
     * 按状态查询任务
     *
     * @param status
     * @param limit
     * @param startTime
     * @param endTime
     * @return
     */
    List<BlueSeaTask> selectByStatus(@Param("status") List<Integer> status, @Param("type") List<Integer> type, @Param("limit") int limit, @Param("startTime") String startTime, @Param("endTime") String endTime);

    @Select("select * from bluesea_task where ali_mch_id=#{aliMchId} and type=#{type}")
    List<BlueSeaTask> selectRepetitiveness(@Param("aliMchId") String aliMchId, @Param("type") int type);

    @Select("select * from bluesea_task where merchant_sn=#{merchantSn} and type=#{type} and id!=#{id} and status = 20 order by create_at desc limit 1")
    BlueSeaTask selectRecentTaskBySnAndIdAndType(@Param("merchantSn") String merchantSn, @Param("type") int type, @Param("id") Long id);

    BlueSeaTask selectSuccessTaskBySnAndType(@Param("merchantSn") String merchantSn, @Param("type") List<Integer> type);

    @Select("select * from bluesea_task where merchant_sn=#{merchantSn} and type = 4 order by create_at desc limit 1")
    BlueSeaTask selectTerminalTask(@Param("merchantSn") String merchantSn);

    /**
     * 根据店铺申请单Id获取任务
     *
     * @param aliShopOrderId
     * @return
     */
    BlueSeaTask findTaskByAliShopOrderId(@Param("aliShopOrderId") String aliShopOrderId);

    /**
     * 根据门店申请单Id修改状态等
     *
     * @param newTask
     * @return
     */
    Integer updateTaskByAliShopOrderId(@Param("newTask") BlueSeaTask newTask);

    /**
     * 根据新蓝海申请单Id获取任务
     *
     * @param activityOrderId
     * @return
     */
    BlueSeaTask findTaskByActivityOrderId(@Param("activityOrderId") String activityOrderId);

    /**
     * 根据活动申请单Id修改状态等
     *
     * @param newTask
     * @return
     */
    Integer updateTaskByActivityOrderId(@Param("newTask") BlueSeaTask newTask);

    /**
     * 根据活动申请单Id修改状态等
     *
     * @param task
     * @return
     */
    Integer updateTaskByCondition(@Param("newTask") BlueSeaTask task);

    /**
     * 根据商户号获取最近一条记录
     *
     * @param merchantSn 商户号
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 14:39 2020/12/11
     */
    BlueSeaTask selectByMerchantSn(@Param("merchantSn") String merchantSn);

    /**
     * 根据商户号获取所有记录
     *
     * @param merchantSn 商户号
     * @return
     * @Author: zhmh
     * @Description:
     * @time: 14:39 2020/12/11
     */
    List<BlueSeaTask> selectListByMerchantSn(@Param("merchantSn") String merchantSn);


    @Select("select * from bluesea_task where merchant_sn=#{merchantSn} and type=#{type} order by create_at desc limit 1")
    BlueSeaTask selectTaskBySnAndType(@Param("merchantSn") String merchantSn, @Param("type") int type);

    @Select("select * from bluesea_task where merchant_sn=#{merchantSn} and type=#{type} and audit_id=#{auditId} order by create_at desc limit 1")
    BlueSeaTask selectTaskBySnAndTypeAndAudit(@Param("merchantSn") String merchantSn, @Param("type") int type, @Param("auditId") Long auditId);


    @Update("update bluesea_task set status = #{status},description = #{description} where id = #{id}")
    Integer updateStatusById(@Param("id") Long id, @Param("status") int status, @Param("description") String description);


    @Select("select * from bluesea_task where merchant_sn=#{merchantSn} and type=#{type} and audit_id=#{auditId}")
    List<BlueSeaTask> selectListByAuditId(@Param("merchantSn") String merchantSn, @Param("type") int type, @Param("auditId") Long auditId);

    @Select("select * from bluesea_task where store_sn=#{storeSn} and type=#{type} order by create_at desc limit 1")
    BlueSeaTask selectTaskByStoreSnAndType(@Param("storeSn") String storeSn, @Param("type") int type);

    @Select("select * from bluesea_task where change_order_id = #{change_order_id}")
    BlueSeaTask selectByChangeOrderId(@Param("change_order_id") String change_order_id);

}