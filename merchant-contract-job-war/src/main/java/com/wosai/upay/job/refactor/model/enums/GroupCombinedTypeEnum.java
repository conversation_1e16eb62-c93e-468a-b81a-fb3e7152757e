package com.wosai.upay.job.refactor.model.enums;

import com.shouqianba.cua.annotation.ITextValueEnum;

/**
 * 报备规则组策略组合类型枚举
 *
 * <AUTHOR>
 */
public enum GroupCombinedTypeEnum implements ITextValueEnum<Integer> {

    SINGLE_MASTER_NO_BACKUP(1, "一主无备"),

    SINGLE_MASTER_SINGLE_BACKUP(2, "一主一备"),

    SINGLE_MASTER_MULTIPLE_BACKUP(3, "一主多备"),

    SINGLE_MASTER_SINGLE_BACKUP_WITH_DAILY_QUOTA(3, "一主一备+每日额度"),

    LOAD_BALANCING_WITH_LOAD_RATIO(3, "负载比例");

    private final Integer value;

    private final String text;

    GroupCombinedTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @Override
    public Integer getValue() {
        return this.value;
    }
}
