package com.wosai.upay.job.refactor.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@Data
@TableName("mc_contract_rule")
@Accessors(chain = true)
public class McContractRuleDO {
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 报备规则唯一标识
     */
    @TableField(value = "rule")
    private String rule;

    /**
     * 规则名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 支付源
     */
    @TableField(value = "payway")
    private Integer payway;

    /**
     * 结算通道
     */
    @TableField(value = "provider")
    private String provider;

    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;

    /**
     * 报备渠道
     */
    @TableField(value = "channel")
    private String channel;

    /**
     * 0禁用 1启用
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 1收单机构报备 2结算通道报备
     */
    @TableField(value = "type")
    private Integer type;

    /**
     * 报备参数
     */
    @TableField(value = "metadata")
    private String metadata;

    /**
     * 系统异常后重试次数
     */
    @TableField(value = "retry")
    private Integer retry;

    /**
     * 是否默认通道
     */
    @TableField(value = "is_default")
    private Boolean isDefault;

    /**
     * 是否新增
     */
    @TableField(value = "is_insert")
    private Boolean isInsert;

    /**
     * 新增是否影响总任务状态
     */
    @TableField(value = "is_insert_influ_ptask")
    private Boolean isInsertInfluPtask;

    /**
     * 是否更新
     */
    @TableField(value = "is_update")
    private Boolean isUpdate;

    /**
     * 更新是否影响总任务状态
     */
    @TableField(value = "is_update_influ_ptask")
    private Boolean isUpdateInfluPtask;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    private Date createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    private Date updateAt;
}
