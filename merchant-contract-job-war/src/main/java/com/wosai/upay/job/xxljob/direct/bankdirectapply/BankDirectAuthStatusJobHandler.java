package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.AlipayAuthBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.biz.bankDirect.BankHandleService;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.ViewProcess;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.upay.job.Constants.BankDirectApplyConstant.Extra.ACQUIRE;
import static com.wosai.upay.job.Constants.BankDirectApplyConstant.Extra.PROVIDER;

@Slf4j
@Component("BankDirectAuthStatusJobHandler")
public class BankDirectAuthStatusJobHandler extends AbstractBankDirectJobHandler {

    @Autowired
    private ChatBotUtil chatBotUtil;
    @Autowired
    private BankDirectApplyMapper bankDirectApplyMapper;
    @Autowired
    private ContractTaskMapper contractTaskMapper;
    @Autowired
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Autowired
    private WechatAuthBiz wechatAuthBiz;
    @Autowired
    private McAcquirerDAO mcAcquirerDAO;
    @Autowired
    private BankHandleServiceFactory factory;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${wx_task_template_id}")
    private Long wxTaskTemplateId;

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;
    @Autowired
    private AlipayAuthBiz alipayAuthBiz;

    @Override
    public String getLockKey() {
        return "BankDirectAuthStatusJobHandler";
    }

    @Override
    public void execute(DirectJobParam param) {
        try {
            final List<BankDirectApply> applyList = bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(Lists.newArrayList(BankDirectApplyConstant.ProcessStatus.CONTRACT_SUCCESS), StringUtil.formatDate(System.currentTimeMillis() - param.getQueryTime()), param.getBatchSize());
            if (CollectionUtils.isEmpty(applyList)) {
                return;
            }
            applyList.forEach(apply -> {
                if (ShutdownSignal.isShuttingDown()) {
                    return;
                }
                final String merchantSn = apply.getMerchant_sn();
                try {
                    final Long taskId = apply.getTask_id();
                    ContractTask contractTask = null;
                    if (taskId != null) {
                        contractTask = contractTaskMapper.selectByPrimaryKey(taskId);
                    }
                    Map<String, Object> extraMap = apply.getExtraMap();
                    final MerchantProviderParams merchantProviderParam = getMerchantProviderParam(merchantSn, (Integer) extraMap.get(PROVIDER), 3);
                    if (Objects.isNull(merchantProviderParam)) {
                        return;
                    }
                    // 导入参数如果有些不需要校验微信认证状态但是需要校验支付宝，则需要设置该参数为false
                    boolean checkWeixinAuth = WosaiMapUtils.getBooleanValue(extraMap, BankDirectApplyConstant.Extra.CHECK_WEIXIN_AUTH, true);
                    Boolean authStatus = checkWeixinAuth ? wechatAuthBiz.getAuthStatus(merchantProviderParam, (Integer) extraMap.get(PROVIDER)) : true;
                    final Date updateAt = contractTask != null ? contractTask.getUpdate_at() : apply.getCreate_at();
                    //微信未实名且已经超过了30天将任务设为失败
                    if (!authStatus && DateUtils.addDays(updateAt, 30).before(new Date())) {
                        //修改direct_status(失败)表状态和bank_direct_apply(status-30失败,processStatus-99失败)表状态
                        modifyStatus(merchantSn, apply, BankDirectApplyConstant.DirectStatus.FAIL, BankDirectApplyConstant.Status.FAIL, BankDirectApplyConstant.AUTH_TIME_OUT_MEMO, apply.getDev_code(), BankDirectApplyConstant.ProcessStatus.FAIL);
                        final String bankPreId = BeanUtil.getPropString(JSONObject.parseObject(apply.getForm_body(), Map.class), BankDirectApplyConstant.BANK_PRE_ID);
                        deletedMerchantBankAccountPre(bankPreId, "银行进件实名认证超时");
                    }
                    //30天内没有派过工  task id 和申请单id数值差距很大,应该不会有相同的情况
                    final String redisKey = taskId != null ? taskId + ":" + "process_status:wxAuth" : apply.getId() + ":" + "process_status:wxAuth";
                    //微信未实名需要派工
                    if (!authStatus && !redisTemplate.hasKey(redisKey)) {
                        final String acquire = BeanUtil.getPropString(apply.getExtraMap(), BankDirectApplyConstant.Extra.ACQUIRE);
                        final String acquireName = mcAcquirerDAO.getAcquirerName(acquire);
                        //记录一下派工了几次,超过两次也不需要派工
                        final String countKey = "wxStatus" + ":" + apply.getId();
                        String count = redisTemplate.opsForValue().get(countKey);
                        if (StringUtils.isEmpty(count) || Long.parseLong(count) < 2) {
                            doStartTaskForRpc(acquireName, merchantSn, wxTaskTemplateId);
                            //添加key
                            redisTemplate.opsForValue().set(redisKey, String.valueOf(taskId), 10L, TimeUnit.DAYS);
                            //记录派工次数
                            redisTemplate.expire(countKey, 90, TimeUnit.DAYS);
                            redisTemplate.opsForValue().increment(countKey);
                        }
                    }
                    final boolean aliStatus = checkAlipayAuth(apply);
                    recordSubMessage(apply, authStatus, aliStatus);
                    //微信未实名
                    if (!authStatus) {
                        delayApply(apply, 6);
                        return;
                    }
                    //认为支付宝未认证成功
                    if (!aliStatus) {
                        delayApply(apply, 6);
                        return;
                    }
                    //实名成功将process_status字段状态改成30-商家认证成功
                    modifyBankDirectApply(apply, BankDirectApplyConstant.ProcessStatus.WX_AUTH_SUCCESS);
                    //商户已微信实名认证,待切换通道
                    recordViewProcess(apply, 50, new Date());
                } catch (Exception exception) {
                    log.error("银行直连商户:{}商家认证异常", merchantSn, exception);
                    chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户:%s,商家认证异常:%s", merchantSn, exception.getMessage()));
                }
            });
        } catch (Exception e) {
            log.error("wxAuthStatus exception", e);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("银行直连商户商家认证异常:%s", e.getMessage()));
        }
    }

    /**
     * 记录微信和支付宝认证状态
     *
     * @param apply     直连申请
     * @param wxStatus  微信子商户号实名状态
     * @param aliStatus 阿里子商户号实名状态
     */
    private void recordSubMessage(BankDirectApply apply, Boolean wxStatus, Boolean aliStatus) {
        final Map<String, Object> extraMap = apply.getExtraMap();
        final String processStr = BeanUtil.getPropString(extraMap, BankDirectApplyConstant.Extra.VIEW_PROCESS);
        if (StringUtils.isEmpty(processStr)) {
            //初始化所有信息
            final BankHandleService handleService = factory.getBankHandleService(apply.getDev_code());
            if(Objects.isNull(handleService)) {
                return;
            }
            final List<ViewProcess> viewProcesses = handleService.initViewProcess(apply.getMerchant_sn());
            if (CollectionUtils.isEmpty(viewProcesses)) {
                return;
            }
            //初始化 这一步也是为了兼容之前没有的数据这样就不需要清洗数据了
            viewProcesses.forEach(process -> {
                if (process.getViewStatus() <= 41) {
                    process.setFinish(Boolean.TRUE);
                    final String timeTemplate = process.getTime();
                    final String time = timeTemplate.replace("#", StringUtil.formatDate("yyyy-MM-dd HH:mm:ss", new Date()));
                    process.setTime(time);
                }
            });
            extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, viewProcesses);
            apply.setExtra(JSONObject.toJSONString(extraMap));
            bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
            return;
        }
        final List<ViewProcess> processes = JSONObject.parseArray(processStr, ViewProcess.class);
        //状态是否和数据库相同
        final boolean match = processes.stream().anyMatch(process -> Objects.equals(process.getAliStatus(), aliStatus) && Objects.equals(process.getWxStatus(), wxStatus));
        if (match) {
            return;
        }
        MerchantProviderParams merchantProviderParamAili = getMerchantProviderParam(apply.getMerchant_sn(), (Integer) extraMap.get(PROVIDER), PaywayEnum.ALIPAY.getValue());
        MerchantProviderParams merchantProviderParamWx = getMerchantProviderParam(apply.getMerchant_sn(), (Integer) extraMap.get(PROVIDER), PaywayEnum.WEIXIN.getValue());
        processes.forEach(process -> {
            if (StringUtils.isEmpty(process.getExtraMessage())) {
                return;
            }
            process.setAliMch(String.format("子商户号%s", merchantProviderParamAili.getPay_merchant_id()));
            process.setWxMch(String.format("子商户号%s", merchantProviderParamWx.getPay_merchant_id()));
            process.setAliStatus(aliStatus);
            process.setWxStatus(wxStatus);
        });
        extraMap.put(BankDirectApplyConstant.Extra.VIEW_PROCESS, processes);
        apply.setExtra(JSONObject.toJSONString(extraMap));
        bankDirectApplyMapper.updateByPrimaryKeySelective(apply);
    }

    private MerchantProviderParams getMerchantProviderParam(String merchantSn, Integer provider, int payWay) {
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn)
                .andPaywayEqualTo(payWay)
                .andProviderEqualTo(provider)
                .andDeletedEqualTo(false);
        final List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExample(example);
        //微信还没有报备成功
        if (CollectionUtils.isEmpty(merchantProviderParams)) {
            return null;
        }
        //按照创建时间排序使用最新的一个
        final List<MerchantProviderParams> providerParams = merchantProviderParams.parallelStream().sorted(Comparator.comparing(MerchantProviderParams::getCtime, Comparator.nullsFirst(Long::compareTo)).reversed()).collect(Collectors.toList());
        return providerParams.get(0);
    }

    /**
     * 检查支付宝子商户认证结果
     */
    private boolean checkAlipayAuth(BankDirectApply apply) {
        if (!applicationApolloConfig.getDirectApplyAliCheckSwitch()) {
            return true;
        }
        Map<String, Object> extraMap = apply.getExtraMap();
        String acquire = MapUtils.getString(extraMap, ACQUIRE);
        if (WosaiStringUtils.equalsIgnoreCase(acquire, AcquirerTypeEnum.HXB.getValue())) {
            //华夏通道不检查
            return true;
        }
        // 有些银行参数导入需要校验微信子商户号认证状态，不需要校验支付宝的认证状态，如果配置了 checkAliAuth=false，就直接返回 true
        if (!WosaiMapUtils.getBooleanValue(extraMap, BankDirectApplyConstant.Extra.CHECK_ALI_AUTH, true)) {
            return true;
        }
        MerchantProviderParams merchantProviderParamAili = getMerchantProviderParam(apply.getMerchant_sn(), (Integer) extraMap.get(PROVIDER), PaywayEnum.ALIPAY.getValue());
        if (Objects.isNull(merchantProviderParamAili)) {
            return false;
        }
        if (Objects.equals(PayMchAuthStatusEnum.YES.getValue(), merchantProviderParamAili.getAuth_status())) {
            return true;
        }
        //数据有误 或 时间在2022-09-15 00:00:00 之后的
        if (merchantProviderParamAili.getCtime() == null || merchantProviderParamAili.getCtime() >= 1663171200000L) {
            return alipayAuthBiz.queryAuthByMchId(merchantProviderParamAili);
        } else {
            //2022-09-15 00:00:00 之前的不检查
            return true;
        }
    }
}