package com.wosai.upay.job.refactor.model.entity;

import java.sql.Timestamp;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * 报备规则组表实体对象
 *
 * <AUTHOR>
 */
@TableName("mc_rule_group")
@Data
public class McRuleGroupDO {

    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 唯一标识
     */
    @TableField(value = "group_id")
    private String groupId;
    /**
     * 规则组名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 业务方
     */
    @TableField(value = "vendor")
    private String vendor;
    /**
     * 业务方应用
     */
    @TableField(value = "vendor_app")
    private String vendorApp;
    /**
     * 0禁用  1启用
     */
    @TableField(value = "status")
    private Integer status;
    /**
     * 是否默认 0-否 1-是
     */
    @TableField(value = "default_status")
    private Integer defaultStatus;
    /**
     * 规则列表
     */
    @TableField(value = "rules")
    private String rules;

    @TableField(value = "create_at")
    private Timestamp createAt;

    @TableField(value = "update_at")
    private Timestamp updateAt;
    /**
     * 收单机构
     */
    @TableField(value = "acquirer")
    private String acquirer;


}

