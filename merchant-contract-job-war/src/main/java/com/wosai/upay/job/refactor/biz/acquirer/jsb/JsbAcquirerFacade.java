package com.wosai.upay.job.refactor.biz.acquirer.jsb;

import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.refactor.biz.acquirer.AbstractAcquirerHandler;
import org.springframework.stereotype.Service;

@Service
public class JsbAcquirerFacade extends AbstractAcquirerHandler {
    @Override
    public AcquirerTypeEnum getType() {
        return AcquirerTypeEnum.JSB;
    }


}
