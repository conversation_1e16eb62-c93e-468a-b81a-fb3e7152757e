/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.upay.core.model.kafka;

import org.apache.avro.specific.SpecificData;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.SchemaStore;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class MerchantUpdateBusinessLicense extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
  private static final long serialVersionUID = 2614268714735476616L;
  public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"MerchantUpdateBusinessLicense\",\"namespace\":\"com.wosai.upay.core.model.kafka\",\"fields\":[{\"name\":\"merchant_sn\",\"type\":\"string\",\"meta\":\"商户号\"},{\"name\":\"merchant_id\",\"type\":\"string\",\"meta\":\"商户id\"},{\"name\":\"merchant_type\",\"type\":\"string\",\"meta\":\"商户类型\"},{\"name\":\"merchant_business_license_type\",\"type\":\"string\",\"meta\":\"商户营业执照类型\"}]}");
  public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

  private static SpecificData MODEL$ = new SpecificData();

  private static final BinaryMessageEncoder<MerchantUpdateBusinessLicense> ENCODER =
      new BinaryMessageEncoder<MerchantUpdateBusinessLicense>(MODEL$, SCHEMA$);

  private static final BinaryMessageDecoder<MerchantUpdateBusinessLicense> DECODER =
      new BinaryMessageDecoder<MerchantUpdateBusinessLicense>(MODEL$, SCHEMA$);

  /**
   * Return the BinaryMessageDecoder instance used by this class.
   */
  public static BinaryMessageDecoder<MerchantUpdateBusinessLicense> getDecoder() {
    return DECODER;
  }

  /**
   * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
   * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
   */
  public static BinaryMessageDecoder<MerchantUpdateBusinessLicense> createDecoder(SchemaStore resolver) {
    return new BinaryMessageDecoder<MerchantUpdateBusinessLicense>(MODEL$, SCHEMA$, resolver);
  }

  /** Serializes this MerchantUpdateBusinessLicense to a ByteBuffer. */
  public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
    return ENCODER.encode(this);
  }

  /** Deserializes a MerchantUpdateBusinessLicense from a ByteBuffer. */
  public static MerchantUpdateBusinessLicense fromByteBuffer(
      java.nio.ByteBuffer b) throws java.io.IOException {
    return DECODER.decode(b);
  }

  @Deprecated public java.lang.CharSequence merchant_sn;
  @Deprecated public java.lang.CharSequence merchant_id;
  @Deprecated public java.lang.CharSequence merchant_type;
  @Deprecated public java.lang.CharSequence merchant_business_license_type;

  /**
   * Default constructor.  Note that this does not initialize fields
   * to their default values from the schema.  If that is desired then
   * one should use <code>newBuilder()</code>.
   */
  public MerchantUpdateBusinessLicense() {}

  /**
   * All-args constructor.
   * @param merchant_sn The new value for merchant_sn
   * @param merchant_id The new value for merchant_id
   * @param merchant_type The new value for merchant_type
   * @param merchant_business_license_type The new value for merchant_business_license_type
   */
  public MerchantUpdateBusinessLicense(java.lang.CharSequence merchant_sn, java.lang.CharSequence merchant_id, java.lang.CharSequence merchant_type, java.lang.CharSequence merchant_business_license_type) {
    this.merchant_sn = merchant_sn;
    this.merchant_id = merchant_id;
    this.merchant_type = merchant_type;
    this.merchant_business_license_type = merchant_business_license_type;
  }

  public org.apache.avro.Schema getSchema() { return SCHEMA$; }
  // Used by DatumWriter.  Applications should not call.
  public java.lang.Object get(int field$) {
    switch (field$) {
    case 0: return merchant_sn;
    case 1: return merchant_id;
    case 2: return merchant_type;
    case 3: return merchant_business_license_type;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  // Used by DatumReader.  Applications should not call.
  @SuppressWarnings(value="unchecked")
  public void put(int field$, java.lang.Object value$) {
    switch (field$) {
    case 0: merchant_sn = (java.lang.CharSequence)value$; break;
    case 1: merchant_id = (java.lang.CharSequence)value$; break;
    case 2: merchant_type = (java.lang.CharSequence)value$; break;
    case 3: merchant_business_license_type = (java.lang.CharSequence)value$; break;
    default: throw new org.apache.avro.AvroRuntimeException("Bad index");
    }
  }

  /**
   * Gets the value of the 'merchant_sn' field.
   * @return The value of the 'merchant_sn' field.
   */
  public java.lang.CharSequence getMerchantSn() {
    return merchant_sn;
  }

  /**
   * Sets the value of the 'merchant_sn' field.
   * @param value the value to set.
   */
  public void setMerchantSn(java.lang.CharSequence value) {
    this.merchant_sn = value;
  }

  /**
   * Gets the value of the 'merchant_id' field.
   * @return The value of the 'merchant_id' field.
   */
  public java.lang.CharSequence getMerchantId() {
    return merchant_id;
  }

  /**
   * Sets the value of the 'merchant_id' field.
   * @param value the value to set.
   */
  public void setMerchantId(java.lang.CharSequence value) {
    this.merchant_id = value;
  }

  /**
   * Gets the value of the 'merchant_type' field.
   * @return The value of the 'merchant_type' field.
   */
  public java.lang.CharSequence getMerchantType() {
    return merchant_type;
  }

  /**
   * Sets the value of the 'merchant_type' field.
   * @param value the value to set.
   */
  public void setMerchantType(java.lang.CharSequence value) {
    this.merchant_type = value;
  }

  /**
   * Gets the value of the 'merchant_business_license_type' field.
   * @return The value of the 'merchant_business_license_type' field.
   */
  public java.lang.CharSequence getMerchantBusinessLicenseType() {
    return merchant_business_license_type;
  }

  /**
   * Sets the value of the 'merchant_business_license_type' field.
   * @param value the value to set.
   */
  public void setMerchantBusinessLicenseType(java.lang.CharSequence value) {
    this.merchant_business_license_type = value;
  }

  /**
   * Creates a new MerchantUpdateBusinessLicense RecordBuilder.
   * @return A new MerchantUpdateBusinessLicense RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder newBuilder() {
    return new com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder();
  }

  /**
   * Creates a new MerchantUpdateBusinessLicense RecordBuilder by copying an existing Builder.
   * @param other The existing builder to copy.
   * @return A new MerchantUpdateBusinessLicense RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder newBuilder(com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder other) {
    return new com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder(other);
  }

  /**
   * Creates a new MerchantUpdateBusinessLicense RecordBuilder by copying an existing MerchantUpdateBusinessLicense instance.
   * @param other The existing instance to copy.
   * @return A new MerchantUpdateBusinessLicense RecordBuilder
   */
  public static com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder newBuilder(com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense other) {
    return new com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder(other);
  }

  /**
   * RecordBuilder for MerchantUpdateBusinessLicense instances.
   */
  public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<MerchantUpdateBusinessLicense>
    implements org.apache.avro.data.RecordBuilder<MerchantUpdateBusinessLicense> {

    private java.lang.CharSequence merchant_sn;
    private java.lang.CharSequence merchant_id;
    private java.lang.CharSequence merchant_type;
    private java.lang.CharSequence merchant_business_license_type;

    /** Creates a new Builder */
    private Builder() {
      super(SCHEMA$);
    }

    /**
     * Creates a Builder by copying an existing Builder.
     * @param other The existing Builder to copy.
     */
    private Builder(com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder other) {
      super(other);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[2].schema(), other.merchant_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_business_license_type)) {
        this.merchant_business_license_type = data().deepCopy(fields()[3].schema(), other.merchant_business_license_type);
        fieldSetFlags()[3] = true;
      }
    }

    /**
     * Creates a Builder by copying an existing MerchantUpdateBusinessLicense instance
     * @param other The existing instance to copy.
     */
    private Builder(com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense other) {
            super(SCHEMA$);
      if (isValidValue(fields()[0], other.merchant_sn)) {
        this.merchant_sn = data().deepCopy(fields()[0].schema(), other.merchant_sn);
        fieldSetFlags()[0] = true;
      }
      if (isValidValue(fields()[1], other.merchant_id)) {
        this.merchant_id = data().deepCopy(fields()[1].schema(), other.merchant_id);
        fieldSetFlags()[1] = true;
      }
      if (isValidValue(fields()[2], other.merchant_type)) {
        this.merchant_type = data().deepCopy(fields()[2].schema(), other.merchant_type);
        fieldSetFlags()[2] = true;
      }
      if (isValidValue(fields()[3], other.merchant_business_license_type)) {
        this.merchant_business_license_type = data().deepCopy(fields()[3].schema(), other.merchant_business_license_type);
        fieldSetFlags()[3] = true;
      }
    }

    /**
      * Gets the value of the 'merchant_sn' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantSn() {
      return merchant_sn;
    }

    /**
      * Sets the value of the 'merchant_sn' field.
      * @param value The value of 'merchant_sn'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder setMerchantSn(java.lang.CharSequence value) {
      validate(fields()[0], value);
      this.merchant_sn = value;
      fieldSetFlags()[0] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_sn' field has been set.
      * @return True if the 'merchant_sn' field has been set, false otherwise.
      */
    public boolean hasMerchantSn() {
      return fieldSetFlags()[0];
    }


    /**
      * Clears the value of the 'merchant_sn' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder clearMerchantSn() {
      merchant_sn = null;
      fieldSetFlags()[0] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_id' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantId() {
      return merchant_id;
    }

    /**
      * Sets the value of the 'merchant_id' field.
      * @param value The value of 'merchant_id'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder setMerchantId(java.lang.CharSequence value) {
      validate(fields()[1], value);
      this.merchant_id = value;
      fieldSetFlags()[1] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_id' field has been set.
      * @return True if the 'merchant_id' field has been set, false otherwise.
      */
    public boolean hasMerchantId() {
      return fieldSetFlags()[1];
    }


    /**
      * Clears the value of the 'merchant_id' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder clearMerchantId() {
      merchant_id = null;
      fieldSetFlags()[1] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_type' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantType() {
      return merchant_type;
    }

    /**
      * Sets the value of the 'merchant_type' field.
      * @param value The value of 'merchant_type'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder setMerchantType(java.lang.CharSequence value) {
      validate(fields()[2], value);
      this.merchant_type = value;
      fieldSetFlags()[2] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_type' field has been set.
      * @return True if the 'merchant_type' field has been set, false otherwise.
      */
    public boolean hasMerchantType() {
      return fieldSetFlags()[2];
    }


    /**
      * Clears the value of the 'merchant_type' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder clearMerchantType() {
      merchant_type = null;
      fieldSetFlags()[2] = false;
      return this;
    }

    /**
      * Gets the value of the 'merchant_business_license_type' field.
      * @return The value.
      */
    public java.lang.CharSequence getMerchantBusinessLicenseType() {
      return merchant_business_license_type;
    }

    /**
      * Sets the value of the 'merchant_business_license_type' field.
      * @param value The value of 'merchant_business_license_type'.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder setMerchantBusinessLicenseType(java.lang.CharSequence value) {
      validate(fields()[3], value);
      this.merchant_business_license_type = value;
      fieldSetFlags()[3] = true;
      return this;
    }

    /**
      * Checks whether the 'merchant_business_license_type' field has been set.
      * @return True if the 'merchant_business_license_type' field has been set, false otherwise.
      */
    public boolean hasMerchantBusinessLicenseType() {
      return fieldSetFlags()[3];
    }


    /**
      * Clears the value of the 'merchant_business_license_type' field.
      * @return This builder.
      */
    public com.wosai.upay.core.model.kafka.MerchantUpdateBusinessLicense.Builder clearMerchantBusinessLicenseType() {
      merchant_business_license_type = null;
      fieldSetFlags()[3] = false;
      return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public MerchantUpdateBusinessLicense build() {
      try {
        MerchantUpdateBusinessLicense record = new MerchantUpdateBusinessLicense();
        record.merchant_sn = fieldSetFlags()[0] ? this.merchant_sn : (java.lang.CharSequence) defaultValue(fields()[0]);
        record.merchant_id = fieldSetFlags()[1] ? this.merchant_id : (java.lang.CharSequence) defaultValue(fields()[1]);
        record.merchant_type = fieldSetFlags()[2] ? this.merchant_type : (java.lang.CharSequence) defaultValue(fields()[2]);
        record.merchant_business_license_type = fieldSetFlags()[3] ? this.merchant_business_license_type : (java.lang.CharSequence) defaultValue(fields()[3]);
        return record;
      } catch (java.lang.Exception e) {
        throw new org.apache.avro.AvroRuntimeException(e);
      }
    }
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumWriter<MerchantUpdateBusinessLicense>
    WRITER$ = (org.apache.avro.io.DatumWriter<MerchantUpdateBusinessLicense>)MODEL$.createDatumWriter(SCHEMA$);

  @Override public void writeExternal(java.io.ObjectOutput out)
    throws java.io.IOException {
    WRITER$.write(this, SpecificData.getEncoder(out));
  }

  @SuppressWarnings("unchecked")
  private static final org.apache.avro.io.DatumReader<MerchantUpdateBusinessLicense>
    READER$ = (org.apache.avro.io.DatumReader<MerchantUpdateBusinessLicense>)MODEL$.createDatumReader(SCHEMA$);

  @Override public void readExternal(java.io.ObjectInput in)
    throws java.io.IOException {
    READER$.read(this, SpecificData.getDecoder(in));
  }

}
