/**
 * Autogenerated by Avro
 *
 * DO NOT EDIT DIRECTLY
 */
package com.wosai.pub.alipay.authinto.event;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class AlipayAppGatewayNotifyEvent extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
    private static final long serialVersionUID = -5989864769364336291L;
    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"AlipayAppGatewayNotifyEvent\",\"namespace\":\"com.wosai.pub.alipay.authinto.event\",\"fields\":[{\"name\":\"timestamp\",\"type\":[\"long\",\"null\"],\"meta\":\"消息时间戳\"},{\"name\":\"msg_method\",\"type\":[{\"type\":\"string\",\"avro.java.string\":\"String\"},\"null\"],\"meta\":\"消息接口名称\"},{\"name\":\"notify_params\",\"type\":[\"null\",\"bytes\"],\"meta\":\"支付宝回调参数透传\"}]}");
    public static org.apache.avro.Schema getClassSchema() { return SCHEMA$; }

    private static SpecificData MODEL$ = new SpecificData();

    private static final BinaryMessageEncoder<AlipayAppGatewayNotifyEvent> ENCODER =
            new BinaryMessageEncoder<AlipayAppGatewayNotifyEvent>(MODEL$, SCHEMA$);

    private static final BinaryMessageDecoder<AlipayAppGatewayNotifyEvent> DECODER =
            new BinaryMessageDecoder<AlipayAppGatewayNotifyEvent>(MODEL$, SCHEMA$);

    /**
     * Return the BinaryMessageDecoder instance used by this class.
     */
    public static BinaryMessageDecoder<AlipayAppGatewayNotifyEvent> getDecoder() {
        return DECODER;
    }

    /**
     * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
     * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
     */
    public static BinaryMessageDecoder<AlipayAppGatewayNotifyEvent> createDecoder(SchemaStore resolver) {
        return new BinaryMessageDecoder<AlipayAppGatewayNotifyEvent>(MODEL$, SCHEMA$, resolver);
    }

    /** Serializes this AlipayAppGatewayNotifyEvent to a ByteBuffer. */
    public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
        return ENCODER.encode(this);
    }

    /** Deserializes a AlipayAppGatewayNotifyEvent from a ByteBuffer. */
    public static AlipayAppGatewayNotifyEvent fromByteBuffer(
            java.nio.ByteBuffer b) throws java.io.IOException {
        return DECODER.decode(b);
    }

    @Deprecated public Long timestamp;
    @Deprecated public String msg_method;
    @Deprecated public java.nio.ByteBuffer notify_params;

    /**
     * Default constructor.  Note that this does not initialize fields
     * to their default values from the schema.  If that is desired then
     * one should use <code>newBuilder()</code>.
     */
    public AlipayAppGatewayNotifyEvent() {}

    /**
     * All-args constructor.
     * @param timestamp The new value for timestamp
     * @param msg_method The new value for msg_method
     * @param notify_params The new value for notify_params
     */
    public AlipayAppGatewayNotifyEvent(Long timestamp, String msg_method, java.nio.ByteBuffer notify_params) {
        this.timestamp = timestamp;
        this.msg_method = msg_method;
        this.notify_params = notify_params;
    }

    public org.apache.avro.Schema getSchema() { return SCHEMA$; }
    // Used by DatumWriter.  Applications should not call.
    public Object get(int field$) {
        switch (field$) {
            case 0: return timestamp;
            case 1: return msg_method;
            case 2: return notify_params;
            default: throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    // Used by DatumReader.  Applications should not call.
    @SuppressWarnings(value="unchecked")
    public void put(int field$, Object value$) {
        switch (field$) {
            case 0: timestamp = (Long)value$; break;
            case 1: msg_method = (String)value$; break;
            case 2: notify_params = (java.nio.ByteBuffer)value$; break;
            default: throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    /**
     * Gets the value of the 'timestamp' field.
     * @return The value of the 'timestamp' field.
     */
    public Long getTimestamp() {
        return timestamp;
    }

    /**
     * Sets the value of the 'timestamp' field.
     * @param value the value to set.
     */
    public void setTimestamp(Long value) {
        this.timestamp = value;
    }

    /**
     * Gets the value of the 'msg_method' field.
     * @return The value of the 'msg_method' field.
     */
    public String getMsgMethod() {
        return msg_method;
    }

    /**
     * Sets the value of the 'msg_method' field.
     * @param value the value to set.
     */
    public void setMsgMethod(String value) {
        this.msg_method = value;
    }

    /**
     * Gets the value of the 'notify_params' field.
     * @return The value of the 'notify_params' field.
     */
    public java.nio.ByteBuffer getNotifyParams() {
        return notify_params;
    }

    /**
     * Sets the value of the 'notify_params' field.
     * @param value the value to set.
     */
    public void setNotifyParams(java.nio.ByteBuffer value) {
        this.notify_params = value;
    }

    /**
     * Creates a new AlipayAppGatewayNotifyEvent RecordBuilder.
     * @return A new AlipayAppGatewayNotifyEvent RecordBuilder
     */
    public static Builder newBuilder() {
        return new Builder();
    }

    /**
     * Creates a new AlipayAppGatewayNotifyEvent RecordBuilder by copying an existing Builder.
     * @param other The existing builder to copy.
     * @return A new AlipayAppGatewayNotifyEvent RecordBuilder
     */
    public static Builder newBuilder(Builder other) {
        return new Builder(other);
    }

    /**
     * Creates a new AlipayAppGatewayNotifyEvent RecordBuilder by copying an existing AlipayAppGatewayNotifyEvent instance.
     * @param other The existing instance to copy.
     * @return A new AlipayAppGatewayNotifyEvent RecordBuilder
     */
    public static Builder newBuilder(AlipayAppGatewayNotifyEvent other) {
        return new Builder(other);
    }

    /**
     * RecordBuilder for AlipayAppGatewayNotifyEvent instances.
     */
    public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<AlipayAppGatewayNotifyEvent>
            implements org.apache.avro.data.RecordBuilder<AlipayAppGatewayNotifyEvent> {

        private Long timestamp;
        private String msg_method;
        private java.nio.ByteBuffer notify_params;

        /** Creates a new Builder */
        private Builder() {
            super(SCHEMA$);
        }

        /**
         * Creates a Builder by copying an existing Builder.
         * @param other The existing Builder to copy.
         */
        private Builder(Builder other) {
            super(other);
            if (isValidValue(fields()[0], other.timestamp)) {
                this.timestamp = data().deepCopy(fields()[0].schema(), other.timestamp);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.msg_method)) {
                this.msg_method = data().deepCopy(fields()[1].schema(), other.msg_method);
                fieldSetFlags()[1] = true;
            }
            if (isValidValue(fields()[2], other.notify_params)) {
                this.notify_params = data().deepCopy(fields()[2].schema(), other.notify_params);
                fieldSetFlags()[2] = true;
            }
        }

        /**
         * Creates a Builder by copying an existing AlipayAppGatewayNotifyEvent instance
         * @param other The existing instance to copy.
         */
        private Builder(AlipayAppGatewayNotifyEvent other) {
            super(SCHEMA$);
            if (isValidValue(fields()[0], other.timestamp)) {
                this.timestamp = data().deepCopy(fields()[0].schema(), other.timestamp);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.msg_method)) {
                this.msg_method = data().deepCopy(fields()[1].schema(), other.msg_method);
                fieldSetFlags()[1] = true;
            }
            if (isValidValue(fields()[2], other.notify_params)) {
                this.notify_params = data().deepCopy(fields()[2].schema(), other.notify_params);
                fieldSetFlags()[2] = true;
            }
        }

        /**
         * Gets the value of the 'timestamp' field.
         * @return The value.
         */
        public Long getTimestamp() {
            return timestamp;
        }

        /**
         * Sets the value of the 'timestamp' field.
         * @param value The value of 'timestamp'.
         * @return This builder.
         */
        public Builder setTimestamp(Long value) {
            validate(fields()[0], value);
            this.timestamp = value;
            fieldSetFlags()[0] = true;
            return this;
        }

        /**
         * Checks whether the 'timestamp' field has been set.
         * @return True if the 'timestamp' field has been set, false otherwise.
         */
        public boolean hasTimestamp() {
            return fieldSetFlags()[0];
        }


        /**
         * Clears the value of the 'timestamp' field.
         * @return This builder.
         */
        public Builder clearTimestamp() {
            timestamp = null;
            fieldSetFlags()[0] = false;
            return this;
        }

        /**
         * Gets the value of the 'msg_method' field.
         * @return The value.
         */
        public String getMsgMethod() {
            return msg_method;
        }

        /**
         * Sets the value of the 'msg_method' field.
         * @param value The value of 'msg_method'.
         * @return This builder.
         */
        public Builder setMsgMethod(String value) {
            validate(fields()[1], value);
            this.msg_method = value;
            fieldSetFlags()[1] = true;
            return this;
        }

        /**
         * Checks whether the 'msg_method' field has been set.
         * @return True if the 'msg_method' field has been set, false otherwise.
         */
        public boolean hasMsgMethod() {
            return fieldSetFlags()[1];
        }


        /**
         * Clears the value of the 'msg_method' field.
         * @return This builder.
         */
        public Builder clearMsgMethod() {
            msg_method = null;
            fieldSetFlags()[1] = false;
            return this;
        }

        /**
         * Gets the value of the 'notify_params' field.
         * @return The value.
         */
        public java.nio.ByteBuffer getNotifyParams() {
            return notify_params;
        }

        /**
         * Sets the value of the 'notify_params' field.
         * @param value The value of 'notify_params'.
         * @return This builder.
         */
        public Builder setNotifyParams(java.nio.ByteBuffer value) {
            validate(fields()[2], value);
            this.notify_params = value;
            fieldSetFlags()[2] = true;
            return this;
        }

        /**
         * Checks whether the 'notify_params' field has been set.
         * @return True if the 'notify_params' field has been set, false otherwise.
         */
        public boolean hasNotifyParams() {
            return fieldSetFlags()[2];
        }


        /**
         * Clears the value of the 'notify_params' field.
         * @return This builder.
         */
        public Builder clearNotifyParams() {
            notify_params = null;
            fieldSetFlags()[2] = false;
            return this;
        }

        @Override
        @SuppressWarnings("unchecked")
        public AlipayAppGatewayNotifyEvent build() {
            try {
                AlipayAppGatewayNotifyEvent record = new AlipayAppGatewayNotifyEvent();
                record.timestamp = fieldSetFlags()[0] ? this.timestamp : (Long) defaultValue(fields()[0]);
                record.msg_method = fieldSetFlags()[1] ? this.msg_method : (String) defaultValue(fields()[1]);
                record.notify_params = fieldSetFlags()[2] ? this.notify_params : (java.nio.ByteBuffer) defaultValue(fields()[2]);
                return record;
            } catch (Exception e) {
                throw new org.apache.avro.AvroRuntimeException(e);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumWriter<AlipayAppGatewayNotifyEvent>
            WRITER$ = (org.apache.avro.io.DatumWriter<AlipayAppGatewayNotifyEvent>)MODEL$.createDatumWriter(SCHEMA$);

    @Override public void writeExternal(java.io.ObjectOutput out)
            throws java.io.IOException {
        WRITER$.write(this, SpecificData.getEncoder(out));
    }

    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumReader<AlipayAppGatewayNotifyEvent>
            READER$ = (org.apache.avro.io.DatumReader<AlipayAppGatewayNotifyEvent>)MODEL$.createDatumReader(SCHEMA$);

    @Override public void readExternal(java.io.ObjectInput in)
            throws java.io.IOException {
        READER$.read(this, SpecificData.getDecoder(in));
    }

}