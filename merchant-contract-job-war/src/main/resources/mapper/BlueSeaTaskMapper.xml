<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.BlueSeaTaskMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.BlueSeaTask">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="audit_id" property="audit_id" jdbcType="BIGINT"/>
        <result column="ali_mch_id" property="ali_mch_id" jdbcType="VARCHAR"/>
        <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR"/>
        <result column="merchant_id" property="merchant_id" jdbcType="VARCHAR"/>
        <result column="store_sn" property="store_sn" jdbcType="VARCHAR"/>
        <result column="retry" property="retry" jdbcType="INTEGER"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_at" property="create_at" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="update_at" jdbcType="TIMESTAMP"/>
        <result column="priority" property="priority" jdbcType="TIMESTAMP"/>
        <result column="ali_shop_order_id" jdbcType="VARCHAR" property="ali_shop_order_id"/>
        <result column="change_order_id" jdbcType="VARCHAR" property="change_order_id"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.BlueSeaTask" extends="BaseResultMap">
        <result column="process" property="process" jdbcType="LONGVARCHAR"/>
        <result column="form_body" property="form_body" jdbcType="LONGVARCHAR"/>
        <result column="mch_snapshot" property="mch_snapshot" jdbcType="LONGVARCHAR"/>
        <result column="terminal_info" property="terminal_info" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , audit_id, ali_mch_id, merchant_sn, merchant_id,store_sn, retry, type, status, description,
    create_at, update_at, priority, ali_shop_order_id ,activity_order_id,change_order_id,apply_id
    </sql>
    <sql id="Blob_Column_List">
        process
        , form_body, mch_snapshot, terminal_info
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from bluesea_task
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from bluesea_task
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into bluesea_task (id, audit_id, ali_mch_id,
                                  merchant_sn, merchant_id, store_sn, retry,
                                  type, status, description,
                                  create_at, update_at, priority,
                                  ali_shop_order_id, process, form_body,
                                  mch_snapshot, terminal_info, change_order_id)
        values (#{id,jdbcType=BIGINT}, #{audit_id,jdbcType=BIGINT}, #{ali_mch_id,jdbcType=VARCHAR},
                #{merchant_sn,jdbcType=VARCHAR}, #{merchant_id,jdbcType=VARCHAR}, #{store_sn,jdbcType=VARCHAR},
                #{retry,jdbcType=INTEGER},
                #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR},
                #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, #{priority,jdbcType=TIMESTAMP},
                #{ali_shop_order_id,jdbcType=VARCHAR}, #{process,jdbcType=LONGVARCHAR},
                #{form_body,jdbcType=LONGVARCHAR},
                #{mch_snapshot,jdbcType=LONGVARCHAR}, #{terminal_info,jdbcType=LONGVARCHAR},
                #{change_order_id,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask" useGeneratedKeys="true"
            keyProperty="id">
        insert into bluesea_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="audit_id != null">
                audit_id,
            </if>
            <if test="apply_id != null">
                apply_id,
            </if>
            <if test="ali_mch_id != null">
                ali_mch_id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="merchant_id != null">
                merchant_id,
            </if>
            <if test="store_sn != null">
                store_sn,
            </if>
            <if test="retry != null">
                retry,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="priority != null">
                priority,
            </if>
            <if test="ali_shop_order_id != null">
                ali_shop_order_id,
            </if>
            <if test="process != null">
                process,
            </if>
            <if test="form_body != null">
                form_body,
            </if>
            <if test="mch_snapshot != null">
                mch_snapshot,
            </if>
            <if test="terminal_info != null">
                terminal_info,
            </if>
            <if test="change_order_id != null">
                change_order_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="audit_id != null">
                #{audit_id,jdbcType=BIGINT},
            </if>
            <if test="apply_id != null">
                #{apply_id,jdbcType=BIGINT},
            </if>
            <if test="ali_mch_id != null">
                #{ali_mch_id,jdbcType=VARCHAR},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_id != null">
                #{merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="store_sn != null">
                #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="retry != null">
                #{retry,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="priority != null">
                #{priority,jdbcType=TIMESTAMP},
            </if>
            <if test="ali_shop_order_id != null">
                #{ali_shop_order_id,jdbcType=VARCHAR},
            </if>
            <if test="process != null">
                #{process,jdbcType=LONGVARCHAR},
            </if>
            <if test="form_body != null">
                #{form_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="mch_snapshot != null">
                #{mch_snapshot,jdbcType=LONGVARCHAR},
            </if>
            <if test="terminal_info != null">
                #{terminal_info,jdbcType=LONGVARCHAR},
            </if>
            <if test="change_order_id != null">
                #{change_order_id,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task
        <set>
            <if test="audit_id != null">
                audit_id = #{audit_id,jdbcType=BIGINT},
            </if>
            <if test="ali_mch_id != null">
                ali_mch_id = #{ali_mch_id,jdbcType=VARCHAR},
            </if>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="merchant_id != null">
                merchant_id = #{merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="store_sn != null">
                store_sn = #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="retry != null">
                retry = #{retry,jdbcType=INTEGER},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TIMESTAMP},
            </if>
            <if test="ali_shop_order_id != null">
                ali_shop_order_id = #{ali_shop_order_id,jdbcType=VARCHAR},
            </if>
            <if test="process != null">
                process = #{process,jdbcType=LONGVARCHAR},
            </if>
            <if test="form_body != null">
                form_body = #{form_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="mch_snapshot != null">
                mch_snapshot = #{mch_snapshot,jdbcType=LONGVARCHAR},
            </if>
            <if test="terminal_info != null">
                terminal_info = #{terminal_info,jdbcType=LONGVARCHAR},
            </if>
            <if test="activity_order_id != null">
                activity_order_id = #{activity_order_id,jdbcType=VARCHAR},
            </if>
            <if test="change_order_id != null">
                change_order_id = #{change_order_id,jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task
        set audit_id          = #{audit_id,jdbcType=BIGINT},
            ali_mch_id        = #{ali_mch_id,jdbcType=VARCHAR},
            merchant_sn       = #{merchant_sn,jdbcType=VARCHAR},
            merchant_id       = #{merchant_id,jdbcType=VARCHAR},
            store_sn          = #{store_sn,jdbcType=VARCHAR},
            retry             = #{retry,jdbcType=INTEGER},
            type              = #{type,jdbcType=INTEGER},
            status            = #{status,jdbcType=INTEGER},
            description       = #{description,jdbcType=VARCHAR},
            create_at         = #{create_at,jdbcType=TIMESTAMP},
            update_at         = #{update_at,jdbcType=TIMESTAMP},
            priority          = #{priority,jdbcType=TIMESTAMP},
            ali_shop_order_id = #{ali_shop_order_id,jdbcType=VARCHAR},
            process           = #{process,jdbcType=LONGVARCHAR},
            form_body         = #{form_body,jdbcType=LONGVARCHAR},
            mch_snapshot      = #{mch_snapshot,jdbcType=LONGVARCHAR},
            terminal_info     = #{terminal_info,jdbcType=LONGVARCHAR},
            change_order_id   = #{change_order_id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task
        set audit_id          = #{audit_id,jdbcType=BIGINT},
            ali_mch_id        = #{ali_mch_id,jdbcType=VARCHAR},
            merchant_sn       = #{merchant_sn,jdbcType=VARCHAR},
            merchant_id       = #{merchant_id,jdbcType=VARCHAR},
            store_sn          = #{store_sn,jdbcType=VARCHAR},
            retry             = #{retry,jdbcType=INTEGER},
            type              = #{type,jdbcType=INTEGER},
            status            = #{status,jdbcType=INTEGER},
            description       = #{description,jdbcType=VARCHAR},
            create_at         = #{create_at,jdbcType=TIMESTAMP},
            update_at         = #{update_at,jdbcType=TIMESTAMP},
            priority          = #{priority,jdbcType=TIMESTAMP},
            ali_shop_order_id = #{ali_shop_order_id,jdbcType=VARCHAR},
            change_order_id   = #{change_order_id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByStatus" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from bluesea_task
        where priority &gt;= #{startTime}
        and priority &lt;=#{endTime}
        and status in
        <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
            #{item}
        </foreach>
        and type in
        <foreach item="item" index="index" collection="type" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by priority desc LIMIT #{limit}
    </select>

    <select id="selectSuccessTaskBySnAndType" resultType="com.wosai.upay.job.model.DO.BlueSeaTask">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Blob_Column_List"/>
        from bluesea_task where merchant_sn = #{merchantSn} and status = 19 and type in
        <foreach item="item" index="index" collection="type" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="findTaskByAliShopOrderId" resultMap="BaseResultMap">
        select *
        from bluesea_task
        where status = 5
          and ali_shop_order_id = #{aliShopOrderId}
    </select>

    <update id="updateTaskByAliShopOrderId" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task
        set status       = 6,
            mch_snapshot = #{newTask.mch_snapshot},
            process      = #{newTask.process}
        where status = 5
          and ali_shop_order_id = #{newTask.ali_shop_order_id}
    </update>

    <select id="findTaskByActivityOrderId" resultMap="BaseResultMap">
        select *
        from bluesea_task
        where status in (10, 11)
          and activity_order_id = #{activityOrderId}
    </select>

    <update id="updateTaskByActivityOrderId" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task set status = #{newTask.status},mch_snapshot = #{newTask.mch_snapshot},process =
        #{newTask.process}
        <if test="newTask.description != null and newTask.description != ''">
            ,description = #{newTask.description}
        </if>
        where status in (10,11) and activity_order_id = #{newTask.activity_order_id}
    </update>

    <update id="updateTaskByCondition" parameterType="com.wosai.upay.job.model.DO.BlueSeaTask">
        update bluesea_task set status = #{newTask.status},process = #{newTask.process}
        <if test="newTask.description != null and newTask.description != ''">
            ,description = #{newTask.description}
        </if>
        where status = 12 and activity_order_id = #{newTask.activity_order_id}
    </update>

    <select id="selectByMerchantSn" resultMap="BaseResultMap">
        select *
        from bluesea_task
        where merchant_sn = #{merchantSn}
          and status != 19 and ali_shop_order_id is not null
        order by priority desc limit 1
    </select>

    <select id="selectListByMerchantSn" resultMap="BaseResultMap">
        select *
        from bluesea_task
        where merchant_sn = #{merchantSn}
          and status not in (0, 2, 3)
        order by create_at desc limit 20
    </select>
</mapper>