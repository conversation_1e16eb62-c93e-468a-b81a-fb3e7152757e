<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.AntShopTaskMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.AntShopTask" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="merchant_id" property="merchant_id" jdbcType="VARCHAR" />
    <result column="store_sn" property="store_sn" jdbcType="VARCHAR" />
    <result column="ali_mch_id" property="ali_mch_id" jdbcType="VARCHAR" />
    <result column="business_type" property="business_type" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="ant_shop_order_id" property="ant_shop_order_id" jdbcType="VARCHAR" />
    <result column="ant_shop_id" property="ant_shop_id" jdbcType="VARCHAR" />
    <result column="retry" property="retry" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="priority" property="priority" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.AntShopTask" extends="BaseResultMap" >
    <result column="request_body" property="request_body" jdbcType="LONGVARCHAR" />
    <result column="response_body" property="response_body" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, merchant_id, store_sn, ali_mch_id, business_type, status, description, 
    ant_shop_order_id, ant_shop_id, retry, create_at, update_at, priority
  </sql>
  <sql id="Blob_Column_List" >
    request_body, response_body, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ant_shop_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ant_shop_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.AntShopTask" >
    insert into ant_shop_task (id, merchant_sn, merchant_id, 
      store_sn, ali_mch_id, business_type, 
      status, description, ant_shop_order_id, 
      ant_shop_id, retry, extra, create_at,
      update_at, priority, request_body, 
      response_body)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{merchant_id,jdbcType=VARCHAR}, 
      #{store_sn,jdbcType=VARCHAR}, #{ali_mch_id,jdbcType=VARCHAR}, #{business_type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{description,jdbcType=VARCHAR}, #{ant_shop_order_id,jdbcType=VARCHAR}, 
      #{ant_shop_id,jdbcType=VARCHAR}, #{retry,jdbcType=INTEGER}, #{extra,jdbcType=LONGVARCHAR}, #{create_at,jdbcType=TIMESTAMP},
      #{update_at,jdbcType=TIMESTAMP}, #{priority,jdbcType=TIMESTAMP}, #{request_body,jdbcType=LONGVARCHAR}, 
      #{response_body,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.AntShopTask" >
    insert into ant_shop_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="merchant_id != null" >
        merchant_id,
      </if>
      <if test="store_sn != null" >
        store_sn,
      </if>
      <if test="ali_mch_id != null" >
        ali_mch_id,
      </if>
      <if test="business_type != null" >
        business_type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="ant_shop_order_id != null" >
        ant_shop_order_id,
      </if>
      <if test="ant_shop_id != null" >
        ant_shop_id,
      </if>
      <if test="retry != null" >
        retry,
      </if>
      <if test="extra != null" >
        extra,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="request_body != null" >
        request_body,
      </if>
      <if test="response_body != null" >
        response_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null" >
        #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="store_sn != null" >
        #{store_sn,jdbcType=VARCHAR},
      </if>
      <if test="ali_mch_id != null" >
        #{ali_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="business_type != null" >
        #{business_type,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="ant_shop_order_id != null" >
        #{ant_shop_order_id,jdbcType=VARCHAR},
      </if>
      <if test="ant_shop_id != null" >
        #{ant_shop_id,jdbcType=VARCHAR},
      </if>
      <if test="retry != null" >
        #{retry,jdbcType=INTEGER},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="request_body != null" >
        #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.AntShopTask" >
    update ant_shop_task
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="merchant_id != null" >
        merchant_id = #{merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="store_sn != null" >
        store_sn = #{store_sn,jdbcType=VARCHAR},
      </if>
      <if test="ali_mch_id != null" >
        ali_mch_id = #{ali_mch_id,jdbcType=VARCHAR},
      </if>
      <if test="business_type != null" >
        business_type = #{business_type,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="ant_shop_order_id != null" >
        ant_shop_order_id = #{ant_shop_order_id,jdbcType=VARCHAR},
      </if>
      <if test="ant_shop_id != null" >
        ant_shop_id = #{ant_shop_id,jdbcType=VARCHAR},
      </if>
      <if test="retry != null" >
        retry = #{retry,jdbcType=INTEGER},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="request_body != null" >
        request_body = #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        response_body = #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.AntShopTask" >
    update ant_shop_task
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{merchant_id,jdbcType=VARCHAR},
      store_sn = #{store_sn,jdbcType=VARCHAR},
      ali_mch_id = #{ali_mch_id,jdbcType=VARCHAR},
      business_type = #{business_type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      ant_shop_order_id = #{ant_shop_order_id,jdbcType=VARCHAR},
      ant_shop_id = #{ant_shop_id,jdbcType=VARCHAR},
      retry = #{retry,jdbcType=INTEGER},
      extra = #{extra,jdbcType=LONGVARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP},
      request_body = #{request_body,jdbcType=LONGVARCHAR},
      response_body = #{response_body,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.AntShopTask" >
    update ant_shop_task
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      merchant_id = #{merchant_id,jdbcType=VARCHAR},
      store_sn = #{store_sn,jdbcType=VARCHAR},
      ali_mch_id = #{ali_mch_id,jdbcType=VARCHAR},
      business_type = #{business_type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      description = #{description,jdbcType=VARCHAR},
      ant_shop_order_id = #{ant_shop_order_id,jdbcType=VARCHAR},
      ant_shop_id = #{ant_shop_id,jdbcType=VARCHAR},
      retry = #{retry,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByStatus" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List"/>
    ,
    <include refid="Blob_Column_List"/>
    from ant_shop_task
    where priority &gt;= #{startTime}
    and priority &lt;=#{endTime}
    and status in
    <foreach item="item" index="index" collection="status" open="(" separator="," close=")">
      #{item}
    </foreach>
    and business_type in
    <foreach item="item" index="index" collection="type" open="(" separator="," close=")">
      #{item}
    </foreach>
    order by priority desc LIMIT #{limit}
  </select>

  <select id="selectByStoreSn" resultMap="BaseResultMap">
     select * from ant_shop_task where merchant_sn = #{merchantSn} and store_sn = #{storeSn} and ali_mch_id = #{aliMchId} order by priority desc limit 30
  </select>
    <select id="selectByMerchantSnAndALiMchId" resultType="com.wosai.upay.job.model.DO.AntShopTask">
      select * from ant_shop_task where merchant_sn = #{merchantSn} and ali_mch_id = #{aliMchId} and status = #{status}
      order by update_at desc limit 1
    </select>

  <delete id="deleteByMerchantSn" >
     delete from ant_shop_task  where merchant_sn = #{merchantSn} and status = 6
  </delete>
</mapper>