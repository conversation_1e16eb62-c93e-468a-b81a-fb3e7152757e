<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.job.mapper.CcbMerchantFeeRateMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.CcbMerchantFeeRate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="merchant_sn" jdbcType="VARCHAR" property="merchant_sn" />
    <result column="provider_merchant_id" jdbcType="VARCHAR" property="provider_merchant_id" />
    <result column="wh" jdbcType="VARCHAR" property="wh" />
    <result column="wd" jdbcType="VARCHAR" property="wd" />
    <result column="zh" jdbcType="VARCHAR" property="zh" />
    <result column="zd" jdbcType="VARCHAR" property="zd" />
    <result column="th" jdbcType="VARCHAR" property="th" />
    <result column="td" jdbcType="VARCHAR" property="td" />
    <result column="fd" jdbcType="VARCHAR" property="fd" />
    <result column="dj" jdbcType="VARCHAR" property="dj" />
    <result column="wh_ratio" jdbcType="VARCHAR" property="wh_ratio" />
    <result column="wd_ratio" jdbcType="VARCHAR" property="wd_ratio" />
    <result column="zh_ratio" jdbcType="VARCHAR" property="zh_ratio" />
    <result column="zd_ratio" jdbcType="VARCHAR" property="zd_ratio" />
    <result column="query_date" jdbcType="VARCHAR" property="query_date" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="mtime" jdbcType="BIGINT" property="mtime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, merchant_sn, provider_merchant_id, wh, wd, zh, zd, th, td, fd, dj, wh_ratio, 
    wd_ratio, zh_ratio, zd_ratio, query_date, ctime, mtime
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ccb_merchant_fee_rate
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByIdRange" resultType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    SELECT * FROM ccb_merchant_fee_rate WHERE id > #{param1} ORDER BY id LIMIT #{param2}
  </select>
  <select id="selectByMerchantSn" resultType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    select * from ccb_merchant_fee_rate where merchant_sn = #{merchantSn}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ccb_merchant_fee_rate
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    insert into ccb_merchant_fee_rate (id, merchant_sn, provider_merchant_id, 
      wh, wd, zh, zd, 
      th, td, fd, dj, 
      wh_ratio, wd_ratio, zh_ratio, 
      zd_ratio, query_date,
      ctime, mtime)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{provider_merchant_id,jdbcType=VARCHAR}, 
      #{wh,jdbcType=VARCHAR}, #{wd,jdbcType=VARCHAR}, #{zh,jdbcType=VARCHAR}, #{zd,jdbcType=VARCHAR}, 
      #{th,jdbcType=VARCHAR}, #{td,jdbcType=VARCHAR}, #{fd,jdbcType=VARCHAR}, #{dj,jdbcType=VARCHAR}, 
      #{wh_ratio,jdbcType=VARCHAR}, #{wd_ratio,jdbcType=VARCHAR}, #{zh_ratio,jdbcType=VARCHAR}, 
      #{zd_ratio,jdbcType=VARCHAR}, #{query_date,jdbcType=VARCHAR},
      #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    insert into ccb_merchant_fee_rate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="merchant_sn != null">
        merchant_sn,
      </if>
      <if test="provider_merchant_id != null">
        provider_merchant_id,
      </if>
      <if test="wh != null">
        wh,
      </if>
      <if test="wd != null">
        wd,
      </if>
      <if test="zh != null">
        zh,
      </if>
      <if test="zd != null">
        zd,
      </if>
      <if test="th != null">
        th,
      </if>
      <if test="td != null">
        td,
      </if>
      <if test="fd != null">
        fd,
      </if>
      <if test="dj != null">
        dj,
      </if>
      <if test="wh_ratio != null">
        wh_ratio,
      </if>
      <if test="wd_ratio != null">
        wd_ratio,
      </if>
      <if test="zh_ratio != null">
        zh_ratio,
      </if>
      <if test="zd_ratio != null">
        zd_ratio,
      </if>
      <if test="query_date != null">
        query_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null">
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="provider_merchant_id != null">
        #{provider_merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="wh != null">
        #{wh,jdbcType=VARCHAR},
      </if>
      <if test="wd != null">
        #{wd,jdbcType=VARCHAR},
      </if>
      <if test="zh != null">
        #{zh,jdbcType=VARCHAR},
      </if>
      <if test="zd != null">
        #{zd,jdbcType=VARCHAR},
      </if>
      <if test="th != null">
        #{th,jdbcType=VARCHAR},
      </if>
      <if test="td != null">
        #{td,jdbcType=VARCHAR},
      </if>
      <if test="fd != null">
        #{fd,jdbcType=VARCHAR},
      </if>
      <if test="dj != null">
        #{dj,jdbcType=VARCHAR},
      </if>
      <if test="wh_ratio != null">
        #{wh_ratio,jdbcType=VARCHAR},
      </if>
      <if test="wd_ratio != null">
        #{wd_ratio,jdbcType=VARCHAR},
      </if>
      <if test="zh_ratio != null">
        #{zh_ratio,jdbcType=VARCHAR},
      </if>
      <if test="zd_ratio != null">
        #{zd_ratio,jdbcType=VARCHAR},
      </if>
      <if test="query_date != null">
        #{query_date,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    update ccb_merchant_fee_rate
    <set>
      <if test="merchant_sn != null">
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="provider_merchant_id != null">
        provider_merchant_id = #{provider_merchant_id,jdbcType=VARCHAR},
      </if>
      <if test="wh != null">
        wh = #{wh,jdbcType=VARCHAR},
      </if>
      <if test="wd != null">
        wd = #{wd,jdbcType=VARCHAR},
      </if>
      <if test="zh != null">
        zh = #{zh,jdbcType=VARCHAR},
      </if>
      <if test="zd != null">
        zd = #{zd,jdbcType=VARCHAR},
      </if>
      <if test="th != null">
        th = #{th,jdbcType=VARCHAR},
      </if>
      <if test="td != null">
        td = #{td,jdbcType=VARCHAR},
      </if>
      <if test="fd != null">
        fd = #{fd,jdbcType=VARCHAR},
      </if>
      <if test="dj != null">
        dj = #{dj,jdbcType=VARCHAR},
      </if>
      <if test="wh_ratio != null">
        wh_ratio = #{wh_ratio,jdbcType=VARCHAR},
      </if>
      <if test="wd_ratio != null">
        wd_ratio = #{wd_ratio,jdbcType=VARCHAR},
      </if>
      <if test="zh_ratio != null">
        zh_ratio = #{zh_ratio,jdbcType=VARCHAR},
      </if>
      <if test="zd_ratio != null">
        zd_ratio = #{zd_ratio,jdbcType=VARCHAR},
      </if>
      <if test="query_date != null">
        query_date = #{query_date,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.CcbMerchantFeeRate">
    update ccb_merchant_fee_rate
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      provider_merchant_id = #{provider_merchant_id,jdbcType=VARCHAR},
      wh = #{wh,jdbcType=VARCHAR},
      wd = #{wd,jdbcType=VARCHAR},
      zh = #{zh,jdbcType=VARCHAR},
      zd = #{zd,jdbcType=VARCHAR},
      th = #{th,jdbcType=VARCHAR},
      td = #{td,jdbcType=VARCHAR},
      fd = #{fd,jdbcType=VARCHAR},
      dj = #{dj,jdbcType=VARCHAR},
      wh_ratio = #{wh_ratio,jdbcType=VARCHAR},
      wd_ratio = #{wd_ratio,jdbcType=VARCHAR},
      zh_ratio = #{zh_ratio,jdbcType=VARCHAR},
      zd_ratio = #{zd_ratio,jdbcType=VARCHAR},
      query_date = #{query_date,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>