<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.AlyDistrictCodeMapper" >
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.AlyDistrictCode" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="code" property="code" jdbcType="VARCHAR" />
        <result column="parent_code" property="parent_code" jdbcType="VARCHAR" />
        <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
        <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
        <result column="version" property="version" jdbcType="BIGINT" />
    </resultMap>
    <sql id="Base_Column_List" >
    id, name, code, parent_code, create_at, update_at, version
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select
        <include refid="Base_Column_List" />
        from aly_district_code
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from aly_district_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.AlyDistrictCode" >
    insert into aly_district_code (id, name, code,
      parent_code, create_at, update_at,
      version)
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
      #{parent_code,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP},
      #{version,jdbcType=BIGINT})
  </insert>
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.AlyDistrictCode" >
        insert into aly_district_code
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="code != null" >
                code,
            </if>
            <if test="parent_code != null" >
                parent_code,
            </if>
            <if test="create_at != null" >
                create_at,
            </if>
            <if test="update_at != null" >
                update_at,
            </if>
            <if test="version != null" >
                version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null" >
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="parent_code != null" >
                #{parent_code,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null" >
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null" >
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null" >
                #{version,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.AlyDistrictCode" >
        update aly_district_code
        <set >
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null" >
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="parent_code != null" >
                parent_code = #{parent_code,jdbcType=VARCHAR},
            </if>
            <if test="create_at != null" >
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null" >
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null" >
                version = #{version,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.AlyDistrictCode" >
    update aly_district_code
    set name = #{name,jdbcType=VARCHAR},
      code = #{code,jdbcType=VARCHAR},
      parent_code = #{parent_code,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="selectByNames" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from aly_district_code where  name in
        <foreach collection="list" item="item" open="(" close=")" separator="," >
            #{item}
        </foreach>
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from aly_district_code
        <where >
            <trim prefixOverrides="and">
                <if test=" param.id != null ">and id = #{param.id}</if>
                <if test=" param.name != null and param.name != '' ">and name = #{param.name}</if>
                <if test=" param.code != null  and param.code != '' ">and code = #{param.code}</if>
                <if test=" param.parent_code != null  and param.parent_code != '' ">and parent_code = #{param.parent_code}</if>
            </trim>
        </where>
    </select>
</mapper>