<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.WeiXinAuthEventLogMapper">
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.WeiXinAuthEventLog" useGeneratedKeys="true"
            keyProperty="seq">
        insert into weixin_auth_event_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="seq != null">
                seq,
            </if>
            <if test="ts != null">
                ts,
            </if>
            <if test="event != null">
                event,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="seq != null">
                #{seq,jdbcType=BIGINT},
            </if>
            <if test="ts != null">
                #{ts,jdbcType=BIGINT},
            </if>
            <if test="event != null">
                #{event,jdbcType=LONGVARBINARY},
            </if>
        </trim>
    </insert>

</mapper>