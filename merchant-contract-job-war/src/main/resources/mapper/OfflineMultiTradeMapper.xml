<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.OfflineMultiTradeMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.OfflineMultiTrade" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="bank_merchant_sn" property="bank_merchant_sn" jdbcType="VARCHAR" />
    <result column="provider" property="provider" jdbcType="VARCHAR" />
    <result column="trade_app_id" property="trade_app_id" jdbcType="VARCHAR" />
    <result column="terminal_id" property="terminal_id" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="TIMESTAMP" />
    <result column="mtime" property="mtime" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.OfflineMultiTrade" extends="BaseResultMap" >
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, bank_merchant_sn, provider, trade_app_id, terminal_id, ctime, mtime
  </sql>
  <sql id="Blob_Column_List" >
    form_body, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from offline_multi_trade
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from offline_multi_trade
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.OfflineMultiTrade" >
    insert into offline_multi_trade (id, merchant_sn, bank_merchant_sn, 
      provider, trade_app_id, terminal_id, 
      ctime, mtime, form_body, 
      extra)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{bank_merchant_sn,jdbcType=VARCHAR}, 
      #{provider,jdbcType=VARCHAR}, #{trade_app_id,jdbcType=VARCHAR}, #{terminal_id,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{form_body,jdbcType=LONGVARCHAR}, 
      #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.OfflineMultiTrade" useGeneratedKeys="true" keyProperty="id" >
    insert into offline_multi_trade
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="bank_merchant_sn != null" >
        bank_merchant_sn,
      </if>
      <if test="provider != null" >
        provider,
      </if>
      <if test="trade_app_id != null" >
        trade_app_id,
      </if>
      <if test="terminal_id != null" >
        terminal_id,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="bank_merchant_sn != null" >
        #{bank_merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        #{provider,jdbcType=VARCHAR},
      </if>
      <if test="trade_app_id != null" >
        #{trade_app_id,jdbcType=VARCHAR},
      </if>
      <if test="terminal_id != null" >
        #{terminal_id,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.OfflineMultiTrade" >
    update offline_multi_trade
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="bank_merchant_sn != null" >
        bank_merchant_sn = #{bank_merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        provider = #{provider,jdbcType=VARCHAR},
      </if>
      <if test="trade_app_id != null" >
        trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      </if>
      <if test="terminal_id != null" >
        terminal_id = #{terminal_id,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.OfflineMultiTrade" >
    update offline_multi_trade
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      bank_merchant_sn = #{bank_merchant_sn,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      terminal_id = #{terminal_id,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      form_body = #{form_body,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.OfflineMultiTrade" >
    update offline_multi_trade
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      bank_merchant_sn = #{bank_merchant_sn,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=VARCHAR},
      trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      terminal_id = #{terminal_id,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>