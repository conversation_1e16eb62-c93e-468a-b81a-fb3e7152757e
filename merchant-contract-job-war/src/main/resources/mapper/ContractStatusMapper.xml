<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ContractStatusMapper">
    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.ContractStatus"
            parameterType="java.lang.Long">
    select *
    from contract_status
    where id = #{id,jdbcType=BIGINT}
  </select>
    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ContractStatus" useGeneratedKeys="true"
            keyProperty="id">
        insert into contract_status
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="acquirer != null">
                acquirer,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="acquirer != null">
                #{acquirer,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ContractStatus">
        update contract_status
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="acquirer != null">
                acquirer = #{acquirer,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByMerchantSn" resultType="com.wosai.upay.job.model.ContractStatus"
            parameterType="java.lang.String">
    select * from contract_status
    where merchant_sn = #{merchant_sn,jdbcType=VARCHAR}
  </select>


    <select id="selectByUpdate" resultType="com.wosai.upay.job.model.ContractStatus">
        select * from contract_status
    where update_at >=  #{param1} limit 100
    </select>

    <update id="updateMtime">
    update contract_status set update_at=#{param1}
    where merchant_sn=#{param2}
    </update>

    <update id="replaceContractStatus">
        replace into contract_status (merchant_sn ,status) values  (#{param1},#{param2})
    </update>
</mapper>