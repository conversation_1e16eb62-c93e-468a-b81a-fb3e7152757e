<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.SubBizParamsMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.subBizParams.SubBizParams" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="trade_app_id" property="trade_app_id" jdbcType="VARCHAR" />
    <result column="provider" property="provider" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="deleted" property="deleted" jdbcType="BIT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.subBizParams.SubBizParams" extends="BaseResultMap" >
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, merchant_sn, trade_app_id, provider, create_at, update_at, deleted
  </sql>
  <sql id="Blob_Column_List" >
    extra
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParamsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sub_biz_params
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParamsExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'false' as QUERYID,
    <include refid="Base_Column_List" />
    from sub_biz_params
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from sub_biz_params
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from sub_biz_params
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParams" >
    insert into sub_biz_params
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="trade_app_id != null" >
        trade_app_id,
      </if>
      <if test="provider != null" >
        provider,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="trade_app_id != null" >
        #{trade_app_id,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        #{provider,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=BIT},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map" >
    update sub_biz_params
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.merchant_sn != null" >
        merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="record.trade_app_id != null" >
        trade_app_id = #{record.trade_app_id,jdbcType=VARCHAR},
      </if>
      <if test="record.provider != null" >
        provider = #{record.provider,jdbcType=INTEGER},
      </if>
      <if test="record.create_at != null" >
        create_at = #{record.create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.update_at != null" >
        update_at = #{record.update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null" >
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.extra != null" >
        extra = #{record.extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update sub_biz_params
    set id = #{record.id,jdbcType=BIGINT},
      merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      trade_app_id = #{record.trade_app_id,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=INTEGER},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      extra = #{record.extra,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update sub_biz_params
    set id = #{record.id,jdbcType=BIGINT},
      merchant_sn = #{record.merchant_sn,jdbcType=VARCHAR},
      trade_app_id = #{record.trade_app_id,jdbcType=VARCHAR},
      provider = #{record.provider,jdbcType=INTEGER},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParams" >
    update sub_biz_params
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="trade_app_id != null" >
        trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      </if>
      <if test="provider != null" >
        provider = #{provider,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParams" >
    update sub_biz_params
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.subBizParams.SubBizParams" >
    update sub_biz_params
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      trade_app_id = #{trade_app_id,jdbcType=VARCHAR},
      provider = #{provider,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      deleted = #{deleted,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>