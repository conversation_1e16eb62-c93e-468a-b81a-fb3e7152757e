<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.job.mapper.LklV3ShopTermMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.LklV3ShopTerm">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="merchant_sn" jdbcType="VARCHAR" property="merchantSn"/>
        <result column="store_sn" jdbcType="VARCHAR" property="storeSn"/>
        <result column="shopId" jdbcType="VARCHAR" property="shopId"/>
        <result column="mer_inner_no" jdbcType="VARCHAR" property="merInnerNo"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.LklV3ShopTerm">
        <result column="lkl_v3_term" jdbcType="LONGVARCHAR" property="lkl_v3_term"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , merchant_sn, store_sn, shopId, mer_inner_no
    </sql>
    <sql id="Blob_Column_List">
        lkl_v3_term
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from lkl_v3_shop_term
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.wosai.upay.job.model.LklV3ShopTerm">
        insert into lkl_v3_shop_term (id, merchant_sn, store_sn,
                                      shopId, mer_inner_no, lkl_v3_term)
        values (#{id,jdbcType=VARCHAR}, #{merchantSn,jdbcType=VARCHAR}, #{storeSn,jdbcType=VARCHAR},
                #{shopId,jdbcType=VARCHAR}, #{merInnerNo,jdbcType=VARCHAR}, #{lkl_v3_term,jdbcType=LONGVARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.LklV3ShopTerm">
        update lkl_v3_shop_term
        <set>
            <if test="merchantSn != null">
                merchant_sn = #{merchantSn,jdbcType=VARCHAR},
            </if>
            <if test="storeSn != null">
                store_sn = #{storeSn,jdbcType=VARCHAR},
            </if>
            <if test="shopId != null">
                shopId = #{shopId,jdbcType=VARCHAR},
            </if>
            <if test="merInnerNo != null">
                mer_inner_no = #{merInnerNo,jdbcType=VARCHAR},
            </if>
            <if test="lkl_v3_term != null">
                lkl_v3_term = #{lkl_v3_term,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="selectByMerchantSn" resultMap="ResultMapWithBLOBs">
        select * from lkl_v3_shop_term where merchant_sn = #{sn}
    </select>

    <select id="selectByShopId" resultMap="ResultMapWithBLOBs">
        select * from lkl_v3_shop_term where shopId= #{id}
    </select>
    <select id="selectByStoreSnAndMerInnerNo" resultMap="ResultMapWithBLOBs">
        select * from lkl_v3_shop_term where store_sn = #{sn} and mer_inner_no = #{merInnerNo} limit 1
    </select>
    <select id="selectByMerchantSnAndMerInnerNo" resultMap="ResultMapWithBLOBs">
        select * from lkl_v3_shop_term where merchant_sn = #{sn} and mer_inner_no = #{merInnerNo}
    </select>
    <select id="selectListByStoreSn" resultMap="ResultMapWithBLOBs">
        select * from lkl_v3_shop_term where store_sn = #{sn}
    </select>
    <select id="selectByStoreSn" resultType="com.wosai.upay.job.model.LklV3ShopTerm">
        select * from lkl_v3_shop_term where store_sn = #{sn} limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
        delete from lkl_v3_shop_term
        where id = #{id}
    </delete>
</mapper>