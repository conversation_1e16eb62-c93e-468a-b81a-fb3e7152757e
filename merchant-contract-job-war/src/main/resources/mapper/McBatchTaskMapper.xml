<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.McBatchTaskMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.McBatchTask" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="operator_id" property="operator_id" jdbcType="VARCHAR" />
    <result column="operator_name" property="operator_name" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="effect_time" property="effect_time" jdbcType="TIMESTAMP" />
    <result column="task_apply_log_id" property="task_apply_log_id" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.McBatchTask" extends="BaseResultMap" >
    <result column="payload" property="payload" jdbcType="LONGVARCHAR" />
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, operator_id, operator_name, type, effect_time, task_apply_log_id, create_at, 
    update_at, status
  </sql>
  <sql id="Blob_Column_List" >
    payload, result
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McBatchTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_batch_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.DO.McBatchTaskExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mc_batch_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_batch_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mc_batch_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.job.model.DO.McBatchTaskExample" >
    delete from mc_batch_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.McBatchTask" >
    insert into mc_batch_task (id, operator_id, operator_name, 
      type, effect_time, task_apply_log_id, 
      create_at, update_at, status, 
      payload, result)
    values (#{id,jdbcType=INTEGER}, #{operator_id,jdbcType=VARCHAR}, #{operator_name,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{effect_time,jdbcType=TIMESTAMP}, #{task_apply_log_id,jdbcType=VARCHAR}, 
      #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, #{status,jdbcType=INTEGER}, 
      #{payload,jdbcType=LONGVARCHAR}, #{result,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.McBatchTask" >
    insert into mc_batch_task
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="operator_id != null" >
        operator_id,
      </if>
      <if test="operator_name != null" >
        operator_name,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="effect_time != null" >
        effect_time,
      </if>
      <if test="task_apply_log_id != null" >
        task_apply_log_id,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="payload != null" >
        payload,
      </if>
      <if test="result != null" >
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="operator_id != null" >
        #{operator_id,jdbcType=VARCHAR},
      </if>
      <if test="operator_name != null" >
        #{operator_name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="effect_time != null" >
        #{effect_time,jdbcType=TIMESTAMP},
      </if>
      <if test="task_apply_log_id != null" >
        #{task_apply_log_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="payload != null" >
        #{payload,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.job.model.DO.McBatchTaskExample" resultType="java.lang.Integer" >
    select count(*) from mc_batch_task
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update mc_batch_task
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.operator_id != null" >
        operator_id = #{record.operator_id,jdbcType=VARCHAR},
      </if>
      <if test="record.operator_name != null" >
        operator_name = #{record.operator_name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.effect_time != null" >
        effect_time = #{record.effect_time,jdbcType=TIMESTAMP},
      </if>
      <if test="record.task_apply_log_id != null" >
        task_apply_log_id = #{record.task_apply_log_id,jdbcType=VARCHAR},
      </if>
      <if test="record.create_at != null" >
        create_at = #{record.create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.update_at != null" >
        update_at = #{record.update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.payload != null" >
        payload = #{record.payload,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.result != null" >
        result = #{record.result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update mc_batch_task
    set id = #{record.id,jdbcType=INTEGER},
      operator_id = #{record.operator_id,jdbcType=VARCHAR},
      operator_name = #{record.operator_name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      effect_time = #{record.effect_time,jdbcType=TIMESTAMP},
      task_apply_log_id = #{record.task_apply_log_id,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER},
      payload = #{record.payload,jdbcType=LONGVARCHAR},
      result = #{record.result,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update mc_batch_task
    set id = #{record.id,jdbcType=INTEGER},
      operator_id = #{record.operator_id,jdbcType=VARCHAR},
      operator_name = #{record.operator_name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      effect_time = #{record.effect_time,jdbcType=TIMESTAMP},
      task_apply_log_id = #{record.task_apply_log_id,jdbcType=VARCHAR},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.McBatchTask" >
    update mc_batch_task
    <set >
      <if test="operator_id != null" >
        operator_id = #{operator_id,jdbcType=VARCHAR},
      </if>
      <if test="operator_name != null" >
        operator_name = #{operator_name,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="effect_time != null" >
        effect_time = #{effect_time,jdbcType=TIMESTAMP},
      </if>
      <if test="task_apply_log_id != null" >
        task_apply_log_id = #{task_apply_log_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="payload != null" >
        payload = #{payload,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


</mapper>