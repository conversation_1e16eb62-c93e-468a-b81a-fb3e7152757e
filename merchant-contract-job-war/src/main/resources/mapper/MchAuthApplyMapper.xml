<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.MchAuthApplyMapper">

    <select id="selectByPrimaryKey" resultType="com.wosai.upay.job.model.MchAuthApply" parameterType="java.lang.Long">
        select *
        from mch_auth_apply
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="getAuthApplyByTypeAndAuthNumAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where type = #{type} and auth_num = #{auth_num}
        and status in
        <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        order by finish_at desc
        limit 1
    </select>
    <select id="getAuthApplyByAuthNumAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select * from mch_auth_apply
        where auth_num = #{auth_num}
        and status in
        <foreach collection="status" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        and cancel_status is null
        order by create_at desc
        limit 50;
    </select>
    <select id="getAppliesByCreateAtAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where priority >= #{priority}
        and priority <![CDATA[<]]> now()
        and status in
        <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        and cancel_status is null
        order by priority limit ${limit}
    </select>
    <select id="getAppliesByPriorityAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where priority > #{start,jdbcType=TIMESTAMP}
        and priority <![CDATA[<]]> #{end,jdbcType=TIMESTAMP}
        and status in
        <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        and cancel_status is null
        order by priority limit ${limit}
    </select>
    <select id="getAppliesByCommitAtAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where commit_at >= #{commit_at}
        and status in
        <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
        order by create_at desc limit ${limit}
    </select>
    <select id="allNeedCancelTask" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where cancel_status = 1
        and create_at >= #{createAt}
        order by create_at
        limit 100;
    </select>


    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.MchAuthApply">
        insert into mch_auth_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="auth_num != null">
                auth_num,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
            <if test="task_id != null">
                task_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="create_at != null">
                create_at,
            </if>
            <if test="commit_at != null">
                commit_at,
            </if>
            <if test="audit_at != null">
                audit_at,
            </if>
            <if test="finish_at != null">
                finish_at,
            </if>
            <if test="update_at != null">
                update_at,
            </if>
            <if test="request_body != null">
                request_body,
            </if>
            <if test="response_body != null">
                response_body,
            </if>
            <if test="qrcode_data_pre != null">
                qrcode_data_pre,
            </if>
            <if test="result != null">
                result,
            </if>
            <if test="qrcode_data_after != null">
                qrcode_data_after,
            </if>
            <if test="qr_code_data_channel != null">
                qr_code_data_channel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="auth_num != null">
                #{auth_num,jdbcType=VARCHAR},
            </if>

            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="task_id != null">
                #{task_id,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="commit_at != null">
                #{commit_at,jdbcType=TIMESTAMP},
            </if>
            <if test="audit_at != null">
                #{audit_at,jdbcType=TIMESTAMP},
            </if>
            <if test="finish_at != null">
                #{finish_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="request_body != null">
                #{request_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="response_body != null">
                #{response_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="qrcode_data_pre != null">
                #{qrcode_data_pre,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="qrcode_data_after != null">
                #{qrcode_data_after,jdbcType=LONGVARCHAR},
            </if>
            <if test="qr_code_data_channel != null">
                #{qr_code_data_channel,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.MchAuthApply">
        update mch_auth_apply
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="auth_num != null">
                auth_num = #{auth_num,jdbcType=VARCHAR},
            </if>

            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="task_id != null">
                task_id = #{task_id,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="commit_at != null">
                commit_at = #{commit_at,jdbcType=TIMESTAMP},
            </if>
            <if test="audit_at != null">
                audit_at = #{audit_at,jdbcType=TIMESTAMP},
            </if>
            <if test="finish_at != null">
                finish_at = #{finish_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="request_body != null">
                request_body = #{request_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="response_body != null">
                response_body = #{response_body,jdbcType=LONGVARCHAR},
            </if>
            <if test="qrcode_data_pre != null">
                qrcode_data_pre = #{qrcode_data_pre,jdbcType=LONGVARCHAR},
            </if>
            <if test="result != null">
                result = #{result,jdbcType=LONGVARCHAR},
            </if>
            <if test="qrcode_data_after != null">
                qrcode_data_after = #{qrcode_data_after,jdbcType=LONGVARCHAR},
            </if>
            <if test="qr_code_data_channel != null">
                qr_code_data_channel = #{qr_code_data_channel,jdbcType=LONGVARCHAR},
            </if>
            <if test="priority != null">
                priority = #{priority,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="submitResult">
        update mch_auth_apply
        set status        = #{status},
            request_body  = #{request},
            response_body = #{response},
            result        = #{result},
            commit_at     = now()
        where id = #{id}
    </update>
    <update id="updateStatusByIdAndPreStatus">
        update mch_auth_apply
        set status = #{result_status},
            result=#{result}
        where id = #{id}
          and status = #{pre_status}
    </update>
    <update id="updateQrcode">
        update mch_auth_apply
        set qrcode_data_pre  = #{qrcode_data_pre},
            qrcode_data_after= #{qrcode_data_after}
        where id = #{id}
    </update>
    <update id="updateFieldValueById">
        update mch_auth_apply
        set ${field} = #{value}
        where id = #{id}
    </update>

    <update id="updateCancelStatus">
        update mch_auth_apply
        set cancel_status = #{cancel_status}
        where id = #{id}
    </update>
    <update id="updatePayMerchantId">
        update mch_auth_apply
        set pay_merchant_id = #{pay_merchant_id}
        where id = #{id}
    </update>
    <select id="selectByTaskId" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where task_id = #{taskId}
        limit 1
    </select>
    <select id="selectMicroWait" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where merchant_sn = #{merchantSn}
          and status = 2
        limit 1
    </select>

    <select id="selectAuthApplyByMerchantSnAndStatus" resultType="com.wosai.upay.job.model.MchAuthApply">
        select *
        from mch_auth_apply
        where merchantSn = #{merchantSn}
        and status in
        <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
            #{item}
        </foreach>
    </select>
<!--    <select id="getTimeOutApply" resultType="com.wosai.upay.job.model.MchAuthApply">-->
<!--        select *-->
<!--        from mch_auth_apply-->
<!--        where status in (30, 40)-->
<!--          and cancel_status is null-->
<!--          and update_at <![CDATA[<]]> DATE_SUB(now(), INTERVAL 29 DAY)-->
<!--          and update_at > DATE_SUB(now(), INTERVAL 30 DAY)-->
<!--        order by update_at-->
<!--            desc-->
<!--        limit 100-->
<!--    </select>-->
</mapper>