<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.CcbDecpMerchantMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.CcbDecpMerchant" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="identity" property="identity" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="open_status" property="open_status" jdbcType="INTEGER" />
    <result column="associated_success_sn" property="associated_success_sn" jdbcType="VARCHAR" />
    <result column="activated" property="activated" jdbcType="INTEGER" />
    <result column="submitted" property="submitted" jdbcType="INTEGER" />
    <result column="result" property="result" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="mtime" property="mtime" jdbcType="BIGINT" />
    <result column="version" property="version" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.CcbDecpMerchant" extends="BaseResultMap" >
    <result column="request_body" property="request_body" jdbcType="LONGVARCHAR" />
    <result column="response_body" property="response_body" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, identity, status, open_status, associated_success_sn, activated, submitted, result,
    ctime, mtime, version
  </sql>
  <sql id="Blob_Column_List" >
    request_body, response_body
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ccb_decp_merchant
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ccb_decp_merchant
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.CcbDecpMerchant" useGeneratedKeys="true" keyProperty="id">
    insert into ccb_decp_merchant (id, merchant_sn, identity, 
      status, open_status, associated_success_sn, activated,
      submitted, result, ctime, 
      mtime, version, request_body, 
      response_body)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{identity,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{open_status,jdbcType=INTEGER}, #{associated_success_sn,jdbcType=VARCHAR}, #{activated,jdbcType=INTEGER},
      #{submitted,jdbcType=INTEGER}, #{result,jdbcType=VARCHAR}, #{ctime,jdbcType=BIGINT}, 
      #{mtime,jdbcType=BIGINT}, #{version,jdbcType=BIGINT}, #{request_body,jdbcType=LONGVARCHAR}, 
      #{response_body,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.CcbDecpMerchant" >
    insert into ccb_decp_merchant
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="identity != null" >
        identity,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="open_status != null" >
        open_status,
      </if>
      <if test="associated_success_sn != null" >
        associated_success_sn,
      </if>
      <if test="activated != null" >
        activated,
      </if>
      <if test="submitted != null" >
        submitted,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="request_body != null" >
        request_body,
      </if>
      <if test="response_body != null" >
        response_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="identity != null" >
        #{identity,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="open_status != null" >
        #{open_status,jdbcType=INTEGER},
      </if>
      <if test="associated_success_sn != null" >
        #{associated_success_sn,jdbcType=VARCHAR},
      </if>
      <if test="activated != null" >
        #{activated,jdbcType=INTEGER},
      </if>
      <if test="submitted != null" >
        #{submitted,jdbcType=INTEGER},
      </if>
      <if test="result != null" >
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.CcbDecpMerchant" >
    update ccb_decp_merchant
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="identity != null" >
        identity = #{identity,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="open_status != null" >
        open_status = #{open_status,jdbcType=INTEGER},
      </if>
      <if test="associated_success_sn != null" >
        associated_success_sn = #{associated_success_sn,jdbcType=VARCHAR},
      </if>
      <if test="activated != null" >
        activated = #{activated,jdbcType=INTEGER},
      </if>
      <if test="submitted != null" >
        submitted = #{submitted,jdbcType=INTEGER},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        request_body = #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        response_body = #{response_body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.CcbDecpMerchant" >
    update ccb_decp_merchant
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      identity = #{identity,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      open_status = #{open_status,jdbcType=INTEGER},
      associated_success_sn = #{associated_success_sn,jdbcType=VARCHAR},
      activated = #{activated,jdbcType=INTEGER},
      submitted = #{submitted,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT},
      request_body = #{request_body,jdbcType=LONGVARCHAR},
      response_body = #{response_body,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.CcbDecpMerchant" >
    update ccb_decp_merchant
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      identity = #{identity,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      open_status = #{open_status,jdbcType=INTEGER},
      associated_success_sn = #{associated_success_sn,jdbcType=VARCHAR},
      activated = #{activated,jdbcType=INTEGER},
      submitted = #{submitted,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByMerchantSnAndIdentity" resultType="com.wosai.upay.job.model.DO.CcbDecpMerchant">
    select * from ccb_decp_merchant
        where merchant_sn = #{param1}
            and identity = #{param2}
  </select>
  <select id="selectSuccessByIdentity" resultType="com.wosai.upay.job.model.DO.CcbDecpMerchant">
    select * from ccb_decp_merchant
    where identity = #{param2}
      and status = 2
  </select>
  <select id="selectByOpenStatus" resultType="com.wosai.upay.job.model.DO.CcbDecpMerchant">
    select * from ccb_decp_merchant where open_status = #{openStatus} order by mtime asc
  </select>
    <select id="selectActivatedByMerchantSn" resultType="com.wosai.upay.job.model.DO.CcbDecpMerchant">
      select * from ccb_decp_merchant where merchant_sn = #{merchantSn} and activated = 1 limit 1
    </select>

</mapper>