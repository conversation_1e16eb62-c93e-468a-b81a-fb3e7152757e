<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.McRuleGroupMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.McRuleGroup" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="group_id" property="group_id" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="vendor" property="vendor" jdbcType="VARCHAR" />
    <result column="vendor_app" property="vendor_app" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="acquirer" property="acquirer" jdbcType="VARCHAR" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.McRuleGroup" extends="BaseResultMap" >
    <result column="rules" property="rules" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, group_id, `name`, vendor, vendor_app, `status`, create_at, update_at, acquirer
  </sql>
  <sql id="Blob_Column_List" >
    rules
  </sql>
  <select id="selectByExampleWithBLOBs" resultMap="ResultMapWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McRuleGroupExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_rule_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.wosai.upay.job.model.DO.McRuleGroupExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mc_rule_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_rule_group
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByGroupId" resultMap="ResultMapWithBLOBs" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mc_rule_group
    where group_id = #{groupId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from mc_rule_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.job.model.DO.McRuleGroupExample" >
    delete from mc_rule_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
    insert into mc_rule_group (id, group_id, `name`, 
      vendor, vendor_app, `status`, 
      create_at, update_at, acquirer, 
      rules)
    values (#{id,jdbcType=INTEGER}, #{group_id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{vendor,jdbcType=VARCHAR}, #{vendor_app,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, #{acquirer,jdbcType=VARCHAR}, 
      #{rules,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
    insert into mc_rule_group
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="group_id != null" >
        group_id,
      </if>
      <if test="name != null" >
        `name`,
      </if>
      <if test="vendor != null" >
        vendor,
      </if>
      <if test="vendor_app != null" >
        vendor_app,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="acquirer != null" >
        acquirer,
      </if>
      <if test="rules != null" >
        rules,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="group_id != null" >
        #{group_id,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="vendor != null" >
        #{vendor,jdbcType=VARCHAR},
      </if>
      <if test="vendor_app != null" >
        #{vendor_app,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="acquirer != null" >
        #{acquirer,jdbcType=VARCHAR},
      </if>
      <if test="rules != null" >
        #{rules,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.job.model.DO.McRuleGroupExample" resultType="java.lang.Integer" >
    select count(*) from mc_rule_group
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update mc_rule_group
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.group_id != null" >
        group_id = #{record.group_id,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.vendor != null" >
        vendor = #{record.vendor,jdbcType=VARCHAR},
      </if>
      <if test="record.vendor_app != null" >
        vendor_app = #{record.vendor_app,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.create_at != null" >
        create_at = #{record.create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.update_at != null" >
        update_at = #{record.update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="record.acquirer != null" >
        acquirer = #{record.acquirer,jdbcType=VARCHAR},
      </if>
      <if test="record.rules != null" >
        rules = #{record.rules,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map" >
    update mc_rule_group
    set id = #{record.id,jdbcType=INTEGER},
      group_id = #{record.group_id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      vendor = #{record.vendor,jdbcType=VARCHAR},
      vendor_app = #{record.vendor_app,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      acquirer = #{record.acquirer,jdbcType=VARCHAR},
      rules = #{record.rules,jdbcType=LONGVARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update mc_rule_group
    set id = #{record.id,jdbcType=INTEGER},
      group_id = #{record.group_id,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      vendor = #{record.vendor,jdbcType=VARCHAR},
      vendor_app = #{record.vendor_app,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      create_at = #{record.create_at,jdbcType=TIMESTAMP},
      update_at = #{record.update_at,jdbcType=TIMESTAMP},
      acquirer = #{record.acquirer,jdbcType=VARCHAR}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
    update mc_rule_group
    <set >
      <if test="group_id != null" >
        group_id = #{group_id,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="vendor != null" >
        vendor = #{vendor,jdbcType=VARCHAR},
      </if>
      <if test="vendor_app != null" >
        vendor_app = #{vendor_app,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="acquirer != null" >
        acquirer = #{acquirer,jdbcType=VARCHAR},
      </if>
      <if test="rules != null" >
        rules = #{rules,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
    <update id="updateByGroupIdSelective" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
        update mc_rule_group
        <set >
            <if test="name != null" >
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="vendor != null" >
                vendor = #{vendor,jdbcType=VARCHAR},
            </if>
            <if test="vendor_app != null" >
                vendor_app = #{vendor_app,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                `status` = #{status,jdbcType=INTEGER},
            </if>
            <if test="create_at != null" >
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null" >
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="acquirer != null" >
                acquirer = #{acquirer,jdbcType=VARCHAR},
            </if>
            <if test="rules != null" >
                rules = #{rules,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where group_id = #{group_id,jdbcType=VARCHAR}
    </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
    update mc_rule_group
    set group_id = #{group_id,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      vendor = #{vendor,jdbcType=VARCHAR},
      vendor_app = #{vendor_app,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      acquirer = #{acquirer,jdbcType=VARCHAR},
      rules = #{rules,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.McRuleGroup" >
    update mc_rule_group
    set group_id = #{group_id,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      vendor = #{vendor,jdbcType=VARCHAR},
      vendor_app = #{vendor_app,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      acquirer = #{acquirer,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>