<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.PendingTasksMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.PendingTasks" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="sn" property="sn" jdbcType="VARCHAR" />
    <result column="event_type" property="event_type" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.PendingTasks" extends="BaseResultMap" >
    <result column="event_msg" property="event_msg" jdbcType="LONGVARCHAR" />
    <result column="result" property="result" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, sn, event_type, status, create_at, update_at, version
  </sql>
  <sql id="Blob_Column_List" >
    event_msg, result
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from pending_tasks
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.PendingTasks" >
    insert into pending_tasks (id, sn, event_type,
                               status, create_at, update_at,
                               version, event_msg, result
    )
    values (#{id,jdbcType=BIGINT}, #{sn,jdbcType=VARCHAR}, #{event_type,jdbcType=VARCHAR},
            #{status,jdbcType=INTEGER}, #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP},
            #{version,jdbcType=BIGINT}, #{event_msg,jdbcType=LONGVARCHAR}, #{result,jdbcType=LONGVARCHAR}
           )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.PendingTasks" >
    insert into pending_tasks
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="sn != null" >
        sn,
      </if>
      <if test="event_type != null" >
        event_type,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="event_msg != null" >
        event_msg,
      </if>
      <if test="result != null" >
        result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sn != null" >
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="event_type != null" >
        #{event_type,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=BIGINT},
      </if>
      <if test="event_msg != null" >
        #{event_msg,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.PendingTasks" >
    update pending_tasks
    <set >
      <if test="sn != null" >
        sn = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="event_type != null" >
        event_type = #{event_type,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=BIGINT},
      </if>
      <if test="event_msg != null" >
        event_msg = #{event_msg,jdbcType=LONGVARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMerchantSn" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks where sn = #{merchantSn,jdbcType=VARCHAR}
  </select>

  <select id="selectByMerchantSnAndEventType" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks where sn = #{merchantSn,jdbcType=VARCHAR}
    and  event_type =  #{eventType,jdbcType=VARCHAR}
    and status = 0
  </select>

  <select id="selectMinIdByCreateDateAndType" resultType="java.lang.Integer">
    select min(id) from pending_tasks
    where create_at <![CDATA[ > ]]> #{createAt,jdbcType=TIMESTAMP}
    and status = 0
    and event_type =  #{eventType,jdbcType=VARCHAR}
  </select>

  <select id="selectCountByCreateDateAndType" resultType="java.lang.Integer">
    select count(1) from pending_tasks where create_at <![CDATA[ > ]]> #{createAt,jdbcType=TIMESTAMP} and status = 0 and event_type =  #{eventType,jdbcType=VARCHAR}
  </select>

  <select id="selectBetweenIds" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks
    where id <![CDATA[ >= ]]> #{minId,jdbcType=VARCHAR}
     and id <![CDATA[ < ]]> #{maxId,jdbcType=VARCHAR}
     and event_type =  #{eventType,jdbcType=VARCHAR}
     and status = 0
  </select>

  <select id="selectAfterTargetDate" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks
    where create_at <![CDATA[ > ]]> #{createAt,jdbcType=TIMESTAMP}
     and event_type =  #{eventType,jdbcType=VARCHAR}
     and status = 0
     limit #{pageSize,jdbcType=INTEGER};
  </select>

  <select id="selectFeeRateAfterTargetDate" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks
    where create_at <![CDATA[ > ]]> #{createAt,jdbcType=TIMESTAMP}
     and create_at <![CDATA[ < ]]> #{endDate,jdbcType=TIMESTAMP}
     and event_type in (7,8)
     and status = 0
     limit #{pageSize,jdbcType=INTEGER};
  </select>
  <select id="selectPendingTasksByCreateAtAndType" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks
    where
      status=0
      and event_type = #{param2}
      and create_at > #{param1,jdbcType=TIMESTAMP}
      order by id asc
      LIMIT ${param3}
  </select>

  <delete id="deleteBeforeTargetDate">
    delete from pending_tasks where create_at <![CDATA[ < ]]> #{createAt,jdbcType=TIMESTAMP}
  </delete>

  <select id="selectPendingTasksByUpdateAtAndType" resultType="com.wosai.upay.job.model.DO.PendingTasks">
    select * from pending_tasks
    where
      status=0
      and event_type = #{param2}
      and update_at between #{param1,jdbcType=TIMESTAMP} and now()
    order by id asc
      LIMIT ${param3}
  </select>
</mapper>