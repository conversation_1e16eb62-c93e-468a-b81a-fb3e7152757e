<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.ProviderTerminalMapper">
    <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.ProviderTerminal">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR"/>
        <result column="store_sn" property="store_sn" jdbcType="VARCHAR"/>
        <result column="terminal_sn" property="terminal_sn" jdbcType="VARCHAR"/>
        <result column="terminal_appid" property="terminal_appid" jdbcType="VARCHAR"/>
        <result column="provider" property="provider" jdbcType="INTEGER"/>
        <result column="provider_terminal_id" property="provider_terminal_id" jdbcType="VARCHAR"/>
        <result column="acquirer_merchant_id" property="acquirer_merchant_id" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="create_at" property="create_at" jdbcType="TIMESTAMP"/>
        <result column="update_at" property="update_at" jdbcType="TIMESTAMP"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.ProviderTerminal" extends="BaseResultMap">
        <result column="bound_sub_mch_ids" property="bound_sub_mch_ids" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , merchant_sn, store_sn, terminal_sn, terminal_appid, provider, provider_terminal_id, acquirer_merchant_id, type,
    create_at, update_at
    </sql>
    <sql id="Blob_Column_List">
        bound_sub_mch_ids
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from provider_terminal
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from provider_terminal
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.wosai.upay.job.model.ProviderTerminal">
        insert into provider_terminal
        <trim prefix="(" suffix=")" suffixOverrides=",">
            merchant_sn,
            <if test="store_sn != null">
                store_sn,
            </if>
            <if test="terminal_sn != null">
                terminal_sn,
            </if>
            <if test="terminal_appid != null">
                terminal_appid,
            </if>
            provider,
            <if test="provider_terminal_id != null">
                provider_terminal_id,
            </if>
            <if test="acquirer_merchant_id != null">
                acquirer_merchant_id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="bound_sub_mch_ids != null">
                bound_sub_mch_ids,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{merchant_sn,jdbcType=VARCHAR},

            <if test="store_sn != null">
                #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_sn != null">
                #{terminal_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_appid != null">
                #{terminal_appid,jdbcType=VARCHAR},
            </if>

            #{provider,jdbcType=INTEGER},

            <if test="provider_terminal_id != null">
                #{provider_terminal_id,jdbcType=VARCHAR},
            </if>
            <if test="acquirer_merchant_id != null">
                #{acquirer_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="bound_sub_mch_ids != null">
                #{bound_sub_mch_ids,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.ProviderTerminal">
        update provider_terminal
        <set>
            <if test="merchant_sn != null">
                merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
            </if>
            <if test="store_sn != null">
                store_sn = #{store_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_sn != null">
                terminal_sn = #{terminal_sn,jdbcType=VARCHAR},
            </if>
            <if test="terminal_appid != null">
                terminal_appid = #{terminal_appid,jdbcType=VARCHAR},
            </if>
            <if test="provider != null">
                provider = #{provider,jdbcType=INTEGER},
            </if>
            <if test="provider_terminal_id != null">
                provider_terminal_id = #{provider_terminal_id,jdbcType=VARCHAR},
            </if>
            <if test="acquirer_merchant_id != null">
                acquirer_merchant_id = #{acquirer_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="create_at != null">
                create_at = #{create_at,jdbcType=TIMESTAMP},
            </if>
            <if test="update_at != null">
                update_at = #{update_at,jdbcType=TIMESTAMP},
            </if>
            <if test="bound_sub_mch_ids != null">
                bound_sub_mch_ids = #{bound_sub_mch_ids,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectByStoreSn" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from provider_terminal
        where store_sn = #{storeSn}
    </select>


    <select id="selectByMerchantSnAndProviderAndAcquirerMerchantId" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from provider_terminal
        where merchant_sn=#{merchantSn} and provider=#{provider} and acquirer_merchant_id = #{acquirerMerchantId}
    </select>


    <update id="updateBoundSubMchIdsById">
        update provider_terminal
        <set>
            bound_sub_mch_ids = #{boundSubMchIds},
        </set>
        where id = #{id}
    </update>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from provider_terminal
        <where>
            <trim  prefixOverrides="and" >
                <if test="providerTerminal.merchant_sn != null">
                   and merchant_sn = #{providerTerminal.merchant_sn}
                </if>
                <if test="providerTerminal.store_sn != null">
                   and store_sn = #{providerTerminal.store_sn}
                </if>
                <if test="providerTerminal.terminal_sn != null">
                    and terminal_sn = #{providerTerminal.terminal_sn}
                </if>
                <if test="providerTerminal.terminal_appid != null">
                   and terminal_appid = #{providerTerminal.terminal_appid}
                </if>
                <if test="providerTerminal.provider != null">
                   and provider = #{providerTerminal.provider}
                </if>
                <if test="providerTerminal.provider_terminal_id != null">
                   and provider_terminal_id = #{providerTerminal.provider_terminal_id}
                </if>
                <if test="providerTerminal.acquirer_merchant_id != null">
                    and acquirer_merchant_id = #{providerTerminal.acquirer_merchant_id}
                </if>
                <if test="providerTerminal.type != null">
                    and type = #{providerTerminal.type}
                </if>
                <if test="providerTerminal.bound_sub_mch_ids != null">
                   and bound_sub_mch_ids= #{providerTerminal.bound_sub_mch_ids}
                </if>
            </trim>
        </where>
    </select>
    <select id="selectByMerchantSnAndProvider" resultType="com.wosai.upay.job.model.ProviderTerminal">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from provider_terminal
        where merchant_sn=#{merchantSn} and provider=#{provider}
    </select>

    <delete id="deleteProviderTerminal">
        delete from provider_terminal
        <where>
            merchant_sn = #{merchantSn}
            <if test="storeSn != null and storeSn != ''">
                AND store_sn = #{storeSn}
            </if>
            <if test="terminalSn != null and terminalSn != ''">
                AND terminal_sn = #{terminalSn}
            </if>
            <if test="provider != null">
                AND provider = #{provider}
            </if>
            <if test="storeSn == null or storeSn == ''">
                AND store_sn is not null
            </if>
        </where>
    </delete>
</mapper>