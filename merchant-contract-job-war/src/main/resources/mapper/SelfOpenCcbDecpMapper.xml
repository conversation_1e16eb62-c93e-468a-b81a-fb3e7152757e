<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="open_status" property="open_status" jdbcType="INTEGER" />
    <result column="decp_id" property="decp_id" jdbcType="BIGINT" />
    <result column="result" property="result" jdbcType="VARCHAR" />
    <result column="ctime" property="ctime" jdbcType="BIGINT" />
    <result column="mtime" property="mtime" jdbcType="BIGINT" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" extends="BaseResultMap" >
    <result column="request_body" property="request_body" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, open_status, decp_id, result, ctime, mtime
  </sql>
  <sql id="Blob_Column_List" >
    request_body
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from self_open_ccb_decp
    where id = #{id,jdbcType=BIGINT}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from self_open_ccb_decp
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" useGeneratedKeys="true" keyProperty="id">
    insert into self_open_ccb_decp (id, merchant_sn, open_status, 
      decp_id, result, ctime, 
      mtime, request_body)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{open_status,jdbcType=INTEGER}, 
      #{decp_id,jdbcType=BIGINT}, #{result,jdbcType=VARCHAR}, #{ctime,jdbcType=BIGINT}, 
      #{mtime,jdbcType=BIGINT}, #{request_body,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" useGeneratedKeys="true" keyProperty="id">
    insert into self_open_ccb_decp
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="open_status != null" >
        open_status,
      </if>
      <if test="decp_id != null" >
        decp_id,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="ctime != null" >
        ctime,
      </if>
      <if test="mtime != null" >
        mtime,
      </if>
      <if test="request_body != null" >
        request_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="open_status != null" >
        #{open_status,jdbcType=INTEGER},
      </if>
      <if test="decp_id != null" >
        #{decp_id,jdbcType=BIGINT},
      </if>
      <if test="result != null" >
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        #{request_body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" >
    update self_open_ccb_decp
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="open_status != null" >
        open_status = #{open_status,jdbcType=INTEGER},
      </if>
      <if test="decp_id != null" >
        decp_id = #{decp_id,jdbcType=BIGINT},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null" >
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null" >
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="request_body != null" >
        request_body = #{request_body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" >
    update self_open_ccb_decp
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      open_status = #{open_status,jdbcType=INTEGER},
      decp_id = #{decp_id,jdbcType=BIGINT},
      result = #{result,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      request_body = #{request_body,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp" >
    update self_open_ccb_decp
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      open_status = #{open_status,jdbcType=INTEGER},
      decp_id = #{decp_id,jdbcType=BIGINT},
      result = #{result,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateOpenStatusById">
    update self_open_ccb_decp set open_status = #{param2} where id = #{param1}
  </update>
  <select id="selectByMerchantSn" resultType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp">
    select * from self_open_ccb_decp where merchant_sn = #{merchantSn} limit 1
  </select>
  <select id="selectProcessByMtime" resultType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp">
    select * from self_open_ccb_decp where mtime > #{mtime} and open_status = 1 order by mtime asc
  </select>
  <select id="selectSuccessByMtime" resultType="com.wosai.upay.job.model.DO.SelfOpenCcbDecp">
    select * from self_open_ccb_decp where mtime >= #{param1} and mtime &lt; #{param2} and open_status in (1,2) limit 1000
  </select>
  <delete id="cancelSelfOpenCcbDecp">
    update self_open_ccb_decp set open_status = 0,result = null,decp_id = null, mtime = #{param2} where id = #{param1}
  </delete>

</mapper>