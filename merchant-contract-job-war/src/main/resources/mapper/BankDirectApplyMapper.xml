<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.BankDirectApplyMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.BankDirectApply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="task_id" property="task_id" jdbcType="BIGINT" />
    <result column="dev_code" property="dev_code" jdbcType="VARCHAR" />
    <result column="bank_ref" property="bank_ref" jdbcType="INTEGER" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="result" property="result" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
    <result column="priority" property="priority" jdbcType="TIMESTAMP" />
    <result column="process_status" property="process_status" jdbcType="INTEGER" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.BankDirectApply" extends="BaseResultMap" >
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
    <result column="extra" property="extra" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, task_id, dev_code, bank_ref, status, result, create_at, update_at, 
    priority, process_status
  </sql>
  <sql id="Blob_Column_List" >
    form_body, extra
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bank_direct_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from bank_direct_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.BankDirectApply" >
    insert into bank_direct_apply (id, merchant_sn, task_id, 
      dev_code, bank_ref, status, 
      result, create_at, update_at, 
      priority, process_status, form_body, 
      extra)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{task_id,jdbcType=BIGINT}, 
      #{dev_code,jdbcType=VARCHAR}, #{bank_ref,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{result,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, 
      #{priority,jdbcType=TIMESTAMP}, #{process_status,jdbcType=INTEGER}, #{form_body,jdbcType=LONGVARCHAR}, 
      #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.BankDirectApply" useGeneratedKeys="true" keyProperty="id">
    insert into bank_direct_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="task_id != null" >
        task_id,
      </if>
      <if test="dev_code != null" >
        dev_code,
      </if>
      <if test="bank_ref != null" >
        bank_ref,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="process_status != null" >
        process_status,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
      <if test="extra != null" >
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="task_id != null" >
        #{task_id,jdbcType=BIGINT},
      </if>
      <if test="dev_code != null" >
        #{dev_code,jdbcType=VARCHAR},
      </if>
      <if test="bank_ref != null" >
        #{bank_ref,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="result != null" >
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="process_status != null" >
        #{process_status,jdbcType=INTEGER},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.BankDirectApply" >
    update bank_direct_apply
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="task_id != null" >
        task_id = #{task_id,jdbcType=BIGINT},
      </if>
      <if test="dev_code != null" >
        dev_code = #{dev_code,jdbcType=VARCHAR},
      </if>
      <if test="bank_ref != null" >
        bank_ref = #{bank_ref,jdbcType=INTEGER},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="process_status != null" >
        process_status = #{process_status,jdbcType=INTEGER},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null" >
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.BankDirectApply" >
    update bank_direct_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      task_id = #{task_id,jdbcType=BIGINT},
      dev_code = #{dev_code,jdbcType=VARCHAR},
      bank_ref = #{bank_ref,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP},
      process_status = #{process_status,jdbcType=INTEGER},
      form_body = #{form_body,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.BankDirectApply" >
    update bank_direct_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      task_id = #{task_id,jdbcType=BIGINT},
      dev_code = #{dev_code,jdbcType=VARCHAR},
      bank_ref = #{bank_ref,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      result = #{result,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP},
      process_status = #{process_status,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getApplyByTaskId" resultMap="ResultMapWithBLOBs">
     select * from bank_direct_apply  where task_id = #{taskId} order by create_at desc limit 1
    </select>

  <select id="getApplyBySnAndDevCode" resultMap="BaseResultMap">
      select * from bank_direct_apply  where merchant_sn = #{merchantSn} and dev_code = #{devCode} order by create_at desc limit 1
    </select>

  <select id="listByProcessStatusAndPriorityLimit" resultMap="ResultMapWithBLOBs">
     select * from bank_direct_apply
        where status in(0,10) and process_status in
    <foreach item="item" index="index" collection="processStatus" open="(" separator="," close=")">
      #{item}
    </foreach>
        and priority between  #{start,jdbcType=TIMESTAMP} and now() order by priority desc limit ${limit}
    </select>

  <select id="getApplyList" resultMap="BaseResultMap">
    select * from bank_direct_apply  where merchant_sn = #{merchantSn}
  </select>

  <select id="listByStatusAndMtimeLimit" resultMap="BaseResultMap">
      select * from bank_direct_apply where status = #{status} and priority &gt;= #{start,jdbcType=TIMESTAMP} and priority &lt; #{end,jdbcType=TIMESTAMP} limit ${limit}
    </select>

</mapper>