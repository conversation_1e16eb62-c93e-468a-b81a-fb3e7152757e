<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.AliDirectApplyMapper" >
  <resultMap id="BaseResultMap" type="com.wosai.upay.job.model.DO.AliDirectApply" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="merchant_sn" property="merchant_sn" jdbcType="VARCHAR" />
    <result column="task_id" property="task_id" jdbcType="BIGINT" />
    <result column="batch_no" property="batch_no" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="sign_url" property="sign_url" jdbcType="VARCHAR" />
    <result column="result" property="result" jdbcType="VARCHAR" />
    <result column="user_id" property="user_id" jdbcType="VARCHAR" />
    <result column="create_at" property="create_at" jdbcType="TIMESTAMP" />
    <result column="priority" property="priority" jdbcType="TIMESTAMP" />
    <result column="update_at" property="update_at" jdbcType="TIMESTAMP" />
  </resultMap>
  <resultMap id="ResultMapWithBLOBs" type="com.wosai.upay.job.model.DO.AliDirectApply" extends="BaseResultMap" >
    <result column="request_body" property="request_body" jdbcType="LONGVARCHAR" />
    <result column="response_body" property="response_body" jdbcType="LONGVARCHAR" />
    <result column="form_body" property="form_body" jdbcType="LONGVARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, merchant_sn, task_id, batch_no, status, sign_url, result, user_id, create_at, 
    priority, update_at
  </sql>
  <sql id="Blob_Column_List" >
    request_body, response_body, form_body
  </sql>
  <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from ali_direct_apply
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from ali_direct_apply
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.job.model.DO.AliDirectApply" >
    insert into ali_direct_apply (id, merchant_sn, task_id, 
      batch_no, status, sign_url, 
      result, user_id, create_at, 
      priority, update_at, request_body, 
      response_body, form_body)
    values (#{id,jdbcType=BIGINT}, #{merchant_sn,jdbcType=VARCHAR}, #{task_id,jdbcType=BIGINT}, 
      #{batch_no,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{sign_url,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{user_id,jdbcType=VARCHAR}, #{create_at,jdbcType=TIMESTAMP}, 
      #{priority,jdbcType=TIMESTAMP}, #{update_at,jdbcType=TIMESTAMP}, #{request_body,jdbcType=LONGVARCHAR}, 
      #{response_body,jdbcType=LONGVARCHAR}, #{form_body,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.job.model.DO.AliDirectApply" >
    insert into ali_direct_apply
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="merchant_sn != null" >
        merchant_sn,
      </if>
      <if test="task_id != null" >
        task_id,
      </if>
      <if test="batch_no != null" >
        batch_no,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="sign_url != null" >
        sign_url,
      </if>
      <if test="result != null" >
        result,
      </if>
      <if test="user_id != null" >
        user_id,
      </if>
      <if test="create_at != null" >
        create_at,
      </if>
      <if test="priority != null" >
        priority,
      </if>
      <if test="update_at != null" >
        update_at,
      </if>
      <if test="request_body != null" >
        request_body,
      </if>
      <if test="response_body != null" >
        response_body,
      </if>
      <if test="form_body != null" >
        form_body,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="merchant_sn != null" >
        #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="task_id != null" >
        #{task_id,jdbcType=BIGINT},
      </if>
      <if test="batch_no != null" >
        #{batch_no,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="sign_url != null" >
        #{sign_url,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="user_id != null" >
        #{user_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="request_body != null" >
        #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        #{response_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        #{form_body,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.job.model.DO.AliDirectApply" >
    update ali_direct_apply
    <set >
      <if test="merchant_sn != null" >
        merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      </if>
      <if test="task_id != null" >
        task_id = #{task_id,jdbcType=BIGINT},
      </if>
      <if test="batch_no != null" >
        batch_no = #{batch_no,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="sign_url != null" >
        sign_url = #{sign_url,jdbcType=VARCHAR},
      </if>
      <if test="result != null" >
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="user_id != null" >
        user_id = #{user_id,jdbcType=VARCHAR},
      </if>
      <if test="create_at != null" >
        create_at = #{create_at,jdbcType=TIMESTAMP},
      </if>
      <if test="priority != null" >
        priority = #{priority,jdbcType=TIMESTAMP},
      </if>
      <if test="update_at != null" >
        update_at = #{update_at,jdbcType=TIMESTAMP},
      </if>
      <if test="request_body != null" >
        request_body = #{request_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="response_body != null" >
        response_body = #{response_body,jdbcType=LONGVARCHAR},
      </if>
      <if test="form_body != null" >
        form_body = #{form_body,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.job.model.DO.AliDirectApply" >
    update ali_direct_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      task_id = #{task_id,jdbcType=BIGINT},
      batch_no = #{batch_no,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sign_url = #{sign_url,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      user_id = #{user_id,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP},
      request_body = #{request_body,jdbcType=LONGVARCHAR},
      response_body = #{response_body,jdbcType=LONGVARCHAR},
      form_body = #{form_body,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.job.model.DO.AliDirectApply" >
    update ali_direct_apply
    set merchant_sn = #{merchant_sn,jdbcType=VARCHAR},
      task_id = #{task_id,jdbcType=BIGINT},
      batch_no = #{batch_no,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      sign_url = #{sign_url,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      user_id = #{user_id,jdbcType=VARCHAR},
      create_at = #{create_at,jdbcType=TIMESTAMP},
      priority = #{priority,jdbcType=TIMESTAMP},
      update_at = #{update_at,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectProcessApplyByMerchantSn" resultType="com.wosai.upay.job.model.DO.AliDirectApply">
    select * from ali_direct_apply
    where merchant_sn = #{merchantSn}
    and status not in (60,70) limit 1
  </select>
  <select id="selectLatestApplyByMerchantSn" resultType="com.wosai.upay.job.model.DO.AliDirectApply">
    select * from ali_direct_apply
    where merchant_sn = #{merchantSn}
    order by create_at desc limit 1
  </select>
  <select id="getAppliesByPrioirtyAndStatus" resultType="com.wosai.upay.job.model.DO.AliDirectApply">
    select * from ali_direct_apply
    where priority &gt;= #{startTime}
    and priority &lt;= #{endTime}
    and status in
    <foreach collection="statuses" item="item" open="(" separator="," close=")" index="">
      #{item}
    </foreach>
    order by priority desc limit #{limit}
  </select>
  <select id="selectProcessApplyByBatchNo" resultType="com.wosai.upay.job.model.DO.AliDirectApply">
    select * from ali_direct_apply
    where batch_no = #{batchNo}
    and status not in (60,70)
    order by priority desc limit 1
  </select>
    <select id="selectApplyByTaskId" resultType="com.wosai.upay.job.model.DO.AliDirectApply">
      select * from ali_direct_apply
      where task_id = #{param1}
    </select>

</mapper>