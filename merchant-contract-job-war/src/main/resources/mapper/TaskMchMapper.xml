<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wosai.upay.job.mapper.TaskMchMapper">

<!--    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.TaskMch" useGeneratedKeys="true" keyProperty="id">-->
<!--        insert into task_mch (task_id, sub_task_id, auth_apply_id, pay_merchant_id, merchant_sn)-->
<!--        values (#{task_id,jdbcType=BIGINT}, #{sub_task_id,jdbcType=BIGINT}, #{auth_apply_id,jdbcType=BIGINT},-->
<!--                #{pay_merchant_id,jdbcType=VARCHAR}, #{merchant_sn,jdbcType=VARCHAR})-->
<!--    </insert>-->

    <insert id="insert" parameterType="com.wosai.upay.job.model.DO.TaskMch" useGeneratedKeys="true"
            keyProperty="id">
        insert into task_mch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="task_id != null">
                task_id,
            </if>
            <if test="sub_task_id != null">
                sub_task_id,
            </if>
            <if test="auth_apply_id != null">
                auth_apply_id,
            </if>
            <if test="pay_merchant_id != null">
                pay_merchant_id,
            </if>
            <if test="merchant_sn != null">
                merchant_sn,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="task_id != null">
                #{task_id,jdbcType=BIGINT},
            </if>
            <if test="sub_task_id != null">
                #{sub_task_id,jdbcType=BIGINT},
            </if>
            <if test="auth_apply_id != null">
                #{auth_apply_id,jdbcType=BIGINT},
            </if>
            <if test="pay_merchant_id != null">
                #{pay_merchant_id,jdbcType=VARCHAR},
            </if>
            <if test="merchant_sn != null">
                #{merchant_sn,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>