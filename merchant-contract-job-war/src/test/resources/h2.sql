SET MODE MYSQL;
CREATE TABLE IF NOT EXISTS mc_acquirer_change (
id int(11) unsigned NOT NULL AUTO_INCREMENT,
apply_id varchar(37) ,
merchant_sn varchar(40) ,
merchant_id varchar(37) ,
source_acquirer varchar(64) ,
target_acquirer varchar(64) ,
status int(11) NOT NULL DEFAULT '1' ,
memo varchar(256) ,
process text ,
extra text ,
create_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NULL  AS CURRENT_TIMESTAMP,
immediately tinyint(1) DEFAULT '0',
PRIMARY KEY (id),
KEY idx_mch_sn0 (merchant_sn),
KEY idx_create_at1 (create_at),
KEY idx_apply_id2 (apply_id),
KEY idx_update_at3 (update_at)
);

CREATE TABLE IF NOT EXISTS contract_status (
id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
merchant_sn varchar(40) NOT NULL DEFAULT '' ,
status int(2) DEFAULT '0' ,
create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
version bigint(20) DEFAULT '0' ,
acquirer varchar(64) NOT NULL DEFAULT 'lkl' ,
PRIMARY KEY (id),
UNIQUE KEY idx_merchant_sn4 (merchant_sn),
KEY idx_create_at5 (create_at),
KEY idx_update_at6 (update_at)
);
CREATE TABLE IF NOT EXISTS mc_contract_rule (
id int(11) unsigned NOT NULL AUTO_INCREMENT,
rule varchar(128) ,
name varchar(64) ,
payway int(11) DEFAULT NULL ,
provider varchar(64) ,
acquirer varchar(64) ,
channel varchar(64) ,
status int(11) NOT NULL ,
type int(11) NOT NULL DEFAULT '2' ,
metadata text ,
retry int(11) DEFAULT '0' ,
is_default tinyint(1) DEFAULT '0' ,
is_insert tinyint(1) DEFAULT '1' ,
is_insert_influ_ptask tinyint(1) DEFAULT '0' ,
is_update tinyint(1) DEFAULT '0' ,
is_update_influ_ptask tinyint(1) DEFAULT '0' ,
create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
PRIMARY KEY (id),
UNIQUE KEY idx_rule4 (rule)
);
CREATE TABLE IF NOT EXISTS mc_channel (
id int(11) unsigned NOT NULL AUTO_INCREMENT,
channel varchar(64) ,
name varchar(64) ,
subject varchar(64) ,
payway int(11) NOT NULL ,
payway_channel_no varchar(64) ,
channel_no varchar(64) ,
private_key text ,
provider varchar(64) ,
provider_metadata text ,
acquirer varchar(64) ,
acquirer_metadata text ,
create_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NULL  AS CURRENT_TIMESTAMP,
PRIMARY KEY (id),
UNIQUE KEY idx_channel5 (channel)
);
CREATE TABLE IF NOT EXISTS mc_rule_group (
id int(11) unsigned NOT NULL AUTO_INCREMENT,
group_id varchar(64) ,
name varchar(64) ,
vendor varchar(64) ,
vendor_app varchar(64) ,
status int(11) DEFAULT '0' ,
rules text ,
create_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NULL  AS CURRENT_TIMESTAMP,
acquirer varchar(64) ,
PRIMARY KEY (id),
UNIQUE KEY idx_group_id6 (group_id)
);
CREATE TABLE IF NOT EXISTS pay_for_task (
id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
sub_task_id bigint(20) unsigned NOT NULL DEFAULT '0' ,
merchant_sn varchar(40) ,
hash_req int(20) NOT NULL DEFAULT '0' ,
create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
version bigint(20) DEFAULT '0' ,
context_param text ,
status int(2) DEFAULT '0' ,
request_param text ,
response text ,
submit_remit_order_id varchar(50) ,
request_flow_no varchar(64) ,
PRIMARY KEY (id),
KEY idx_sn7 (merchant_sn),
KEY idx_create_at8 (create_at),
KEY idx_submit_remit_order_id9 (submit_remit_order_id),
KEY idx_request_flow_no10 (request_flow_no),
KEY idx_status11 (status)
);

CREATE TABLE IF NOT EXISTS contract_sub_task (
id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
p_task_id bigint(20) unsigned NOT NULL ,
merchant_sn varchar(40) ,
channel varchar(40) ,
default_channel int(2) DEFAULT NULL ,
change_config int(2) DEFAULT NULL ,
change_body int(2) DEFAULT NULL ,
task_type int(2) NOT NULL DEFAULT '-1' ,
contract_id varchar(100) ,
payway int(2) DEFAULT NULL ,
request_body text ,
response_body text ,
schedule_status int(2) DEFAULT '0' ,
schedule_dep_task_id bigint(20) DEFAULT '0' ,
status int(2) DEFAULT '0' ,
status_influ_p_task int(2) DEFAULT '0' ,
priority timestamp NOT NULL  AS CURRENT_TIMESTAMP,
result text ,
create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
version bigint(20) DEFAULT '0',
contract_rule varchar(64) ,
rule_group_id varchar(64) ,
retry int(11) NOT NULL DEFAULT '0' ,
PRIMARY KEY (id),
KEY sn12 (merchant_sn),
KEY idx_status13 (status),
KEY idx_p_task_id14 (p_task_id),
KEY idx_channel15 (channel),
KEY idx_schedule_dep_task_id16 (schedule_dep_task_id),
KEY idx_contract_sub_task_contract_id17 (contract_id)
);
CREATE TABLE IF NOT EXISTS contract_task (
id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
merchant_sn varchar(40) NOT NULL DEFAULT '' ,
merchant_name varchar(40) NOT NULL DEFAULT '' ,
type varchar(40) NOT NULL DEFAULT '' ,
event_msg text ,
event_context text ,
status int(2) DEFAULT '0' ,
affect_sub_task_count int(2) NOT NULL ,
affect_status_success_task_count int(2) NOT NULL ,
result text ,
create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
version bigint(20) DEFAULT '0' ,
priority timestamp NOT NULL  AS CURRENT_TIMESTAMP,
complete_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
rule_group_id varchar(64) NOT NULL DEFAULT '' ,
PRIMARY KEY (id),
KEY idx_create_status18 (create_at,status),
KEY idx_priority_status19 (priority,status),
KEY idx_sn_status20 (merchant_sn,status)
);

CREATE TABLE IF NOT EXISTS merchant_provider_params (
id varchar(36) ,
merchant_sn varchar(36) ,
out_merchant_sn varchar(56) ,
channel_no varchar(36) ,
parent_merchant_id varchar(36) ,
provider int(10) DEFAULT '0' ,
provider_merchant_id varchar(36) ,
payway int(10) DEFAULT '0' ,
params_config_status int(10) DEFAULT '0' ,
pay_merchant_id varchar(36) ,
weixin_sub_appid varchar(36) ,
weixin_subscribe_appid varchar(36) ,
weixin_sub_mini_appid varchar(36) ,
weixin_receipt_appid varchar(36) ,
status int(10) DEFAULT '0' ,
extra blob ,
ctime bigint(20) DEFAULT NULL ,
mtime bigint(20) DEFAULT NULL ,
deleted tinyint(1) NOT NULL DEFAULT '0' ,
version bigint(20) unsigned NOT NULL DEFAULT '1' ,
contract_rule varchar(64) ,
rule_group_id varchar(64) ,
update_status int(11) NOT NULL DEFAULT '-1' ,
auth_status int(10) DEFAULT '0' ,
PRIMARY KEY (id),
KEY idx_merchant_sn25 (merchant_sn),
KEY idx_ctime26 (ctime),
KEY idx_pay_merchant_id27 (pay_merchant_id),
KEY idx_provider_merchant_id28 (provider_merchant_id),
KEY index_mtime29 (mtime)
);

CREATE TABLE IF NOT EXISTS merchant_provider_params_ext (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    param_id varchar(40) NOT NULL DEFAULT '' ,
    type int(10) NOT NULL ,
    ext_field_1 varchar(200) DEFAULT '0' ,
    ext_field_2 varchar(200) DEFAULT '0' ,
    extra text ,
    create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
    version bigint(20) unsigned NOT NULL DEFAULT '1' ,
    PRIMARY KEY (id),
    KEY idx_param_id30 (param_id),
    KEY idx_ext_field_131 (ext_field_1),
    KEY idx_ext_field_232 (ext_field_2),
    KEY idx_type_ext_133 (type,ext_field_1),
    KEY create_at34 (create_at)
);

CREATE TABLE IF NOT EXISTS contract_event_log (
seq bigint(20) NOT NULL AUTO_INCREMENT ,
ts bigint(20) NOT NULL ,
event blob NOT NULL ,
PRIMARY KEY (seq)
);

CREATE TABLE IF NOT EXISTS contract_opinion_event_log (
seq bigint(20) NOT NULL AUTO_INCREMENT ,
ts bigint(20) NOT NULL ,
event blob NOT NULL ,
PRIMARY KEY (seq)
);

CREATE TABLE IF NOT EXISTS weixin_auth_event_log (
 seq bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
 ts bigint(20) NOT NULL ,
 event blob ,
 PRIMARY KEY (seq)
);

-- 支付宝直连申请单
CREATE TABLE IF NOT EXISTS ali_direct_apply (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  merchant_sn varchar(40) NOT NULL DEFAULT '' ,
  task_id bigint(20) DEFAULT NULL ,
  batch_no varchar(30) DEFAULT NULL ,
  status int(2) DEFAULT '0' ,
  request_body text ,
  response_body text ,
  sign_url varchar(256) DEFAULT NULL ,
  result varchar(256) DEFAULT NULL ,
  form_body text ,
  user_id varchar(30) DEFAULT NULL ,
  create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  priority timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP,
  update_at timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_sn0 (merchant_sn),
  KEY idx_user_id1 (user_id),
  KEY idx_create_at_status2 (create_at,status),
  KEY idx_priority_status3 (priority,status)
);
-- 微信直连申请单
CREATE TABLE IF NOT EXISTS weixin_direct_apply (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  merchant_sn varchar(40) NOT NULL DEFAULT '' ,
  task_id bigint(20) DEFAULT NULL ,
  dev_code varchar(16) DEFAULT NULL ,
  submit_type int(2) DEFAULT '1' ,
  status int(2) DEFAULT '0' ,
  request_body text ,
  response_body text ,
  sign_url varchar(256) DEFAULT NULL ,
  result varchar(256) DEFAULT NULL ,
  qrcode_data text ,
  form_body text ,
  create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
  update_at timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP,
  priority timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_sn01 (merchant_sn),
  KEY idx_create_at_status1 (create_at,status),
  KEY idx_update_at2 (update_at,status)
);
-- 直连总状态
CREATE TABLE IF NOT EXISTS direct_status (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  merchant_sn varchar(40) NOT NULL ,
  dev_code varchar(16) NOT NULL ,
  status int(2) DEFAULT '0' ,
  create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_at timestamp NOT NULL  DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY idx_merchant_sn_dev_code0 (merchant_sn,dev_code)
);
-- 蚂蚁门店
CREATE TABLE IF NOT EXISTS ant_shop_task
(
    id                bigint(20) unsigned  NOT NULL AUTO_INCREMENT ,
    merchant_sn       varchar(36) NOT NULL ,
    merchant_id       varchar(36) NOT NULL,
    store_sn          varchar(32) NOT NULL,
    ali_mch_id        varchar(36) NOT NULL,
    business_type     int(11)          DEFAULT NULL ,
    status            int(11)          NOT NULL DEFAULT '0' ,
    description       varchar(256) DEFAULT '',
    request_body      text ,
    response_body     text ,
    ant_shop_order_id varchar(64) DEFAULT NULL,
    ant_shop_id       varchar(64) DEFAULT NULL,
    retry             int(11)          NOT NULL DEFAULT '0' ,
    extra             text ,
    create_at         timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    update_at         timestamp        NOT NULL  DEFAULT CURRENT_TIMESTAMP ,
    priority          timestamp        NOT NULL  DEFAULT CURRENT_TIMESTAMP ,
    PRIMARY KEY (id),
    KEY idx_ant_merchant_id0 (merchant_id) ,
    KEY idx_ant_merchant_sn1 (merchant_sn) ,
    KEY idx_ali_mch_id2 (ali_mch_id) ,
    KEY idx_ant_priority3 (priority) ,
    KEY idx_ant_shop_order_id4 (ant_shop_order_id) ,
    KEY idx_ant_shop_id5 (ant_shop_id)
);
CREATE TABLE IF NOT EXISTS bluesea_task (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
    audit_id bigint(20) NOT NULL ,
    ali_mch_id varchar(36) ,
    merchant_sn varchar(36) ,
    merchant_id varchar(36) ,
    retry int(11) NOT NULL DEFAULT '0' ,
    type int(11) DEFAULT NULL ,
    status int(11) NOT NULL DEFAULT '0' ,
    description varchar(256) ,
    process text ,
    form_body text ,
    mch_snapshot text ,
    terminal_info text ,
    create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
    priority timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
    ali_shop_order_id varchar(64) ,
    activity_order_id varchar(32) ,
    PRIMARY KEY (id),
    KEY idx_merchant_id0 (merchant_id),
    KEY idx_create_at3 (create_at),
    KEY idx_update_at4 (update_at),
    KEY idx_audit_id3 (audit_id),
    KEY idx_merchant_sn5 (merchant_sn),
    KEY idx_priority5 (priority),
    KEY idx_ali_shop_order_id6 (ali_shop_order_id) USING BTREE
);
CREATE TABLE IF NOT EXISTS ccb_decp_merchant (
     id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
     merchant_sn varchar(40) ,
     identity varchar(40) ,
     status int(11) NOT NULL ,
     open_status int(11) NOT NULL ,
     associated_success_sn varchar(40) ,
     activated int(2) DEFAULT '0' ,
     submitted int(2) DEFAULT '0' ,
     request_body text ,
     response_body text ,
     result varchar(100) ,
     ctime bigint(20) DEFAULT NULL ,
     mtime bigint(20) DEFAULT NULL ,
     version bigint(20) DEFAULT '0' ,
     PRIMARY KEY (id),
     KEY idx_merchant_sn0 (merchant_sn),
     KEY idx_identity0 (identity)
);
CREATE TABLE IF NOT EXISTS multi_provider_contract_event (
     id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
     merchant_sn varchar(40) NOT NULL DEFAULT '' ,
     primary_task_id bigint(20) DEFAULT NULL ,
     secondary_task_id bigint(20) DEFAULT NULL ,
     event_msg text ,
     status int(2) DEFAULT '0' ,
     result text ,
     create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
     update_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
     version bigint(20) DEFAULT '0' ,
     primary_group_id varchar(20) DEFAULT NULL ,
     secondary_group_id varchar(20) DEFAULT NULL ,
     PRIMARY KEY (id),
     KEY idx_merchant_sn3 (merchant_sn),
     KEY idx_primary_task_id1 (primary_task_id),
     KEY idx_secondary_task_id2 (secondary_task_id),
     KEY idx_create_status3 (create_at,status)
);
CREATE TABLE IF NOT EXISTS contract_event (
  id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  task_id bigint(20) DEFAULT NULL ,
  merchant_sn varchar(40) NOT NULL DEFAULT '' ,
  event_type int(2) DEFAULT '0' ,
  event_msg text ,
  status int(2) DEFAULT '0' ,
  result text ,
  create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
  version bigint(20) DEFAULT '0' ,
  rule_group_id varchar(64) NOT NULL DEFAULT '' ,
  retry int(11) DEFAULT '0' ,
  priority timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
  PRIMARY KEY (id),
  KEY idx_task_id0 (task_id),
  KEY idx_create_at_status3 (create_at,status),
  KEY idx_merchant_sn_status3 (merchant_sn,status)
);
CREATE TABLE IF NOT EXISTS payway_config_change (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    merchant_sn varchar(40) NOT NULL DEFAULT '' ,
    payway int(2) NOT NULL ,
    channel varchar(40),
    body text NOT NULL ,
    status int(2) DEFAULT '0' ,
    create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP,
    version bigint(20) DEFAULT '0' ,
    PRIMARY KEY (id),
    KEY idx_merchant_sn6 (merchant_sn),
    KEY idx_create_at6 (create_at),
    KEY idx_update_at8 (update_at)
);
CREATE TABLE IF NOT EXISTS self_open_ccb_decp (
      id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
      merchant_sn varchar(40) ,
      open_status int(2) NOT NULL ,
      decp_id bigint(20) DEFAULT NULL ,
      request_body text ,
      result varchar(200) ,
      ctime bigint(20) DEFAULT NULL ,
      mtime bigint(20) DEFAULT NULL ,
      PRIMARY KEY (id),
      KEY idx_merchant_sn1 (merchant_sn),
      KEY idx_mtime1 (mtime)
);
CREATE TABLE IF NOT EXISTS open_ccb_decp (
     id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
     identity varchar(60)  NOT NULL DEFAULT '' ,
     number varchar(60)  NOT NULL DEFAULT '' ,
     status int(2) NOT NULL ,
     request_body text ,
     response_body text ,
     ctime bigint(20) DEFAULT NULL ,
     mtime bigint(20) DEFAULT NULL ,
     version bigint(20) DEFAULT '0' ,
     PRIMARY KEY (id),
     KEY idx_identity_number0 (identity,number),
     KEY idx_mtime2 (mtime)
);
CREATE TABLE IF NOT EXISTS ccb_config (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
    district_code varchar(10) ,
    province varchar(32) ,
    city varchar(32) ,
    private_min_price varchar(10) ,
    public_min_price varchar(10) ,
    ins_no varchar(20) ,
    account varchar(40) ,
    ctime bigint(20) DEFAULT NULL ,
    mtime bigint(20) DEFAULT NULL ,
    deleted tinyint(1) NOT NULL DEFAULT '0',
    version bigint(20) DEFAULT '0' ,
    PRIMARY KEY (id)
);
CREATE TABLE IF NOT EXISTS ccb_config_change_history (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
    ccb_config_id bigint(20) NOT NULL ,
    op_type int(2) NOT NULL ,
    private_min_price varchar(10) ,
    public_min_price varchar(10) ,
    ins_no varchar(20) ,
    account varchar(40) ,
    operator varchar(20) ,
    update_time bigint(20) ,
    PRIMARY KEY (id),
    KEY idx_ccb_config_id0 (ccb_config_id)
);
CREATE TABLE IF NOT EXISTS bank_direct_apply (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
    merchant_sn varchar(40) NOT NULL DEFAULT '' ,
    task_id bigint(20) DEFAULT NULL ,
    dev_code varchar(16) DEFAULT NULL ,
    bank_ref int(2) DEFAULT '1' ,
    status int(2) DEFAULT '0' ,
    result varchar(256) DEFAULT NULL ,
    form_body text ,
    create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
    update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
    priority timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
    process_status int(2) DEFAULT '0' ,
    extra text ,
    PRIMARY KEY (id),
    KEY idx_sn59 (merchant_sn),
    KEY idx_create_at_status60 (create_at,status),
    KEY idx_update_at61 (update_at,status),
    KEY idx_priority_status62 (priority,status),
    KEY idx_task_id_create_at63 (task_id,create_at)
);
CREATE TABLE IF NOT EXISTS auth_and_combo_task (
   id bigint(20) unsigned NOT NULL AUTO_INCREMENT ,
   merchant_id varchar(37) ,
   merchant_sn varchar(32) ,
   status int(2) NOT NULL DEFAULT '0' ,
   form_body text ,
   sub_mch_id varchar(16) ,
   result mediumtext ,
   extra text ,
   create_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ,
   update_at timestamp NOT NULL  AS CURRENT_TIMESTAMP ,
   PRIMARY KEY (id),
   KEY idx_sn60 (merchant_sn)
);



INSERT INTO `pay_for_task`(`id`, `sub_task_id`, `merchant_sn`, `hash_req`, `create_at`, `update_at`, `version`, `context_param`, `status`, `request_param`, `response`, `submit_remit_order_id`, `request_flow_no`) VALUES (81556, 4580201, '**************', -*********, '2020-06-19 17:16:22', '2020-06-22 16:49:35', 0, '{\"bankAccount\":{\"city\":\"上海市 市辖区\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"solicitor_sn\":\"sctc-**********\",\"default_status\":1,\"solicitor_name\":\"测试你好啊1\",\"holder_id_back_ocr_status\":0,\"merchant_id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"merchant_sn\":\"**************\",\"type\":1,\"holder_id_back_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"mtime\":*************,\"holder_id_front_ocr_status\":0,\"number\":\"****************\",\"opening_number\":\"************\",\"identity\":\"*********\",\"bank_card_status\":1,\"branch_name\":\"招商银行上海分行张杨支行\",\"bank_name\":\"招商银行\",\"ctime\":*************,\"id_type\":2,\"holder_id_front_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"verify_status\":0,\"id\":\"77fab7a4-b73c-41dd-9104-01d6f1a1f5c3\",\"holder_id_status\":0,\"bank_card_image\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"letter_of_authorization\":\"\",\"merchant_name\":\"入网自动化40966923bf75\",\"vendor_name\":\"shouqianba223\",\"holder\":\"入网自动化法人护照\",\"version\":1,\"card_validity\":\"08/28\",\"deleted\":false,\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"vendor_sn\":\"1\",\"id_validity\":\"********-********\",\"clearing_number\":\"************\"},\"bankInfo\":{\"opening_number\":\"************\",\"deleted\":false,\"branch_name\":\"招商银行上海分行张杨支行\",\"bank_name\":\"招商银行\",\"ctime\":*************,\"id\":\"9126c471-f23d-4a48-ac77-c6ac418ab406\",\"mtime\":*************,\"bank_city_code\":\"2900\",\"version\":1,\"clearing_number\":\"************\",\"status\":1},\"merchantFeeRates\":[{\"wechatType\":\"WECHAT_PAY_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"ALIPAY_WALLET_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"JINGDONG_PURCHASE_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"QQ_PURCHASE_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"LAKALA_WALLET_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"UNIONPAY_WALLET_DEBIT_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"UNIONPAY_WALLET_CREDIT_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"BESTPAY_PURCHASE_FEE\",\"wechatRate\":\"0.0038\"}],\"merchantBusinessLicense\":{\"legal_person_id_card_back_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"solicitor_sn\":\"sctc-**********\",\"solicitor_name\":\"测试你好啊1\",\"merchant_id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"merchant_sn\":\"**************\",\"type\":1,\"mtime\":1592558179322,\"number\":\"***************\",\"legal_person_id_type\":2,\"ctime\":1592558179322,\"id\":********,\"legal_person_id_card_front_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"address\":\"入网自动化营业执照注册地址abc123\",\"letter_of_authorization\":\"\",\"photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"merchant_name\":\"入网自动化40966923bf75\",\"vendor_name\":\"shouqianba223\",\"legal_person_id_number\":\"*********\",\"version\":1,\"deleted\":false,\"trade_license\":\"\",\"legal_person_name\":\"入网自动化法人护照\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"vendor_sn\":\"1\",\"name\":\"入网自动化营业证照\",\"validity\":\"********-********\",\"id_validity\":\"********-********\"},\"merchant\":{\"withdraw_mode\":1,\"country\":\"CHN\",\"street_address\":\"入网自动化商户详细地址abc123\",\"bank_account_verify_status\":-1,\"contact_phone\":\"***********\",\"city\":\"苏州市\",\"latitude\":\"31.307336\",\"business_license_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"solicitor_sn\":\"sctc-**********\",\"solicitor_name\":\"测试你好啊1\",\"industry\":\"747f3e28-312d-11e6-aebb-ecf4bbdee2f0\",\"owner_cellphone\":\"***********\",\"mtime\":*************,\"platform\":0,\"legal_person_id_type\":2,\"province\":\"江苏省\",\"alias\":\"入网自动化40966923bf75\",\"ctime\":*************,\"currency\":\"CNY\",\"id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"sn\":\"**************\",\"concat_id_card_front_photo\":\"\",\"longitude\":\"120.776146\",\"business_name\":\"入网自动化0765a7b39653\",\"contact_name\":\"入网自动化\",\"owner_name\":\"入网自动化\",\"customer_phone\":\"***********\",\"vendor_name\":\"shouqianba223\",\"contact_cellphone\":\"***********\",\"version\":2,\"legal_person_register_no\":\"***************\",\"deleted\":false,\"legal_person_name\":\"入网自动化法人护照\",\"district\":\"姑苏区\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"vendor_sn\":\"1\",\"name\":\"入网自动化40966923bf75\",\"status\":1}}', 1, '{\"account_type\":1,\"bank_address\":\"上海市 市辖区\",\"bank_holder\":\"入网自动化法人护照\",\"bank_name\":\"招商银行\",\"bank_number\":\"****************\",\"branch_name\":\"招商银行上海分行张杨支行\",\"business_id\":\"195218741972172800\",\"holder_type\":2,\"plat_form\":\"merchant-contract-job\",\"uniqKey\":-**********,\"verify_retry\":3}', '{\"business_id\":\"195216779453136896\",\"msg\":\"已生成待处理核验单\",\"need_verify\":false,\"syns\":false}', '195216779453136896', '195216779453136896');
INSERT INTO `contract_task`(`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`) VALUES (2571832, '**************', '入网自动化40966923bf75', '新增商户入网', NULL, '{\"bankAccount\":{\"id\":\"77fab7a4-b73c-41dd-9104-01d6f1a1f5c3\",\"merchant_id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"type\":1,\"holder\":\"入网自动化法人护照\",\"id_type\":2,\"identity\":\"*********\",\"holder_id_front_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"holder_id_back_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"holder_id_front_ocr_status\":0,\"holder_id_back_ocr_status\":0,\"holder_id_status\":0,\"number\":\"****************\",\"verify_status\":0,\"bank_name\":\"招商银行\",\"branch_name\":\"招商银行上海分行张杨支行\",\"card_validity\":\"08/28\",\"clearing_number\":\"************\",\"opening_number\":\"************\",\"city\":\"上海市 市辖区\",\"bank_card_image\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"id_validity\":\"********-********\",\"letter_of_authorization\":\"\",\"bank_card_status\":1,\"default_status\":1,\"ctime\":*************,\"mtime\":*************,\"deleted\":false,\"version\":1,\"merchant_sn\":\"**************\",\"merchant_name\":\"入网自动化40966923bf75\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"solicitor_sn\":\"sctc-**********\",\"solicitor_name\":\"测试你好啊1\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba223\"},\"bankInfo\":{\"id\":\"9126c471-f23d-4a48-ac77-c6ac418ab406\",\"opening_number\":\"************\",\"clearing_number\":\"************\",\"bank_name\":\"招商银行\",\"branch_name\":\"招商银行上海分行张杨支行\",\"ctime\":*************,\"mtime\":*************,\"deleted\":false,\"version\":1,\"status\":1,\"bank_city_code\":\"2900\"},\"merchantFeeRates\":[{\"wechatType\":\"WECHAT_PAY_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"ALIPAY_WALLET_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"JINGDONG_PURCHASE_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"QQ_PURCHASE_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"LAKALA_WALLET_FEE\",\"wechatRate\":\"0.006\"},{\"wechatType\":\"UNIONPAY_WALLET_DEBIT_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"UNIONPAY_WALLET_CREDIT_FEE\",\"wechatRate\":\"0.0038\"},{\"wechatType\":\"BESTPAY_PURCHASE_FEE\",\"wechatRate\":\"0.0038\"}],\"merchantBusinessLicense\":{\"id\":********,\"merchant_id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"type\":1,\"photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"number\":\"***************\",\"name\":\"入网自动化营业证照\",\"validity\":\"********-********\",\"address\":\"入网自动化营业执照注册地址abc123\",\"letter_of_authorization\":\"\",\"trade_license\":\"\",\"legal_person_id_type\":2,\"legal_person_id_card_front_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"legal_person_id_card_back_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"legal_person_name\":\"入网自动化法人护照\",\"legal_person_id_number\":\"*********\",\"id_validity\":\"********-********\",\"ctime\":1592558179322,\"mtime\":1592558179322,\"deleted\":false,\"version\":1,\"merchant_sn\":\"**************\",\"merchant_name\":\"入网自动化40966923bf75\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"solicitor_sn\":\"sctc-**********\",\"solicitor_name\":\"测试你好啊1\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba223\"},\"merchant\":{\"id\":\"7f0e65c9-ab5c-45f7-b80c-d5da5a5af297\",\"sn\":\"**************\",\"name\":\"入网自动化40966923bf75\",\"alias\":\"入网自动化40966923bf75\",\"industry\":\"747f3e28-312d-11e6-aebb-ecf4bbdee2f0\",\"status\":1,\"withdraw_mode\":1,\"longitude\":\"120.776146\",\"latitude\":\"31.307336\",\"country\":\"CHN\",\"province\":\"江苏省\",\"city\":\"苏州市\",\"district\":\"姑苏区\",\"street_address\":\"入网自动化商户详细地址abc123\",\"contact_name\":\"入网自动化\",\"contact_phone\":\"***********\",\"contact_cellphone\":\"***********\",\"concat_id_card_front_photo\":\"\",\"legal_person_name\":\"入网自动化法人护照\",\"legal_person_id_type\":2,\"legal_person_register_no\":\"***************\",\"business_license_photo\":\"http://images.wosaimg.com/ca/ff81ead879921aaaffe9a1a9ac14a78b985198.png\",\"currency\":\"CNY\",\"owner_name\":\"入网自动化\",\"owner_cellphone\":\"***********\",\"customer_phone\":\"***********\",\"bank_account_verify_status\":-1,\"vendor_id\":\"859d9f5f-af99-11e5-9ec3-00163e00625b\",\"solicitor_id\":\"c97fcf9b-b300-11e5-9987-6c92bf21bb99\",\"platform\":0,\"ctime\":*************,\"mtime\":*************,\"deleted\":false,\"version\":2,\"business_name\":\"入网自动化0765a7b39653\",\"solicitor_sn\":\"sctc-**********\",\"solicitor_name\":\"测试你好啊1\",\"vendor_sn\":\"1\",\"vendor_name\":\"shouqianba223\"}}', 3, 5, 0, '{\"channel\":\"pay_for\",\"message\":\"对私+非身份证，收钱吧代付通路返回验证中\"}', '2020-06-19 17:16:21', '2020-06-19 17:16:22', 0, '2020-06-19 17:16:22', '2020-06-23 00:00:00', 'default');
INSERT INTO `contract_sub_task`(`id`, `p_task_id`, `merchant_sn`, `channel`, `default_channel`, `change_config`, `change_body`, `task_type`, `contract_id`, `payway`, `request_body`, `response_body`, `schedule_status`, `schedule_dep_task_id`, `status`, `status_influ_p_task`, `priority`, `result`, `create_at`, `update_at`, `version`, `contract_rule`, `rule_group_id`, `retry`) VALUES (4580201, 2571832, '**************', 'lkl', NULL, NULL, NULL, 5, '', NULL, NULL, NULL, 0, 81556, 0, 1, '2020-06-19 17:16:22', '{\"channel\":\"pay_for\",\"message\":\"代付验证中\"}', '2020-06-19 17:16:21', '2020-06-19 17:16:22', 0, 'lkl', 'default', 0);
INSERT INTO `pay_for_task` (`id`, `sub_task_id`, `merchant_sn`, `hash_req`, `create_at`, `update_at`, `version`, `context_param`, `status`, `request_param`, `response`, `submit_remit_order_id`, `request_flow_no`)
VALUES (79503, 4510722, '**************', *********, '2020-06-07 02:05:10', '2020-06-07 02:05:11', 0,'{"bankAccount":{"city":"江苏省 苏州市","type":2,"number":"6228480405965827372","bank_card_status":0,"branch_name":"中国农业银行苏州平江新城支行","bank_name":"中国农业银行","ctime":*************,"id_type":1,"verify_status":0,"id":"33d702e1-fe56-42e0-994f-8a8e107d618f","holder":"自助入网营业执照账户"}}' , 4,'', '', '189559771492253696', '189559771492253696');
INSERT INTO `contract_sub_task` (`id`, `p_task_id`, `merchant_sn`, `channel`, `default_channel`, `change_config`, `change_body`, `task_type`, `contract_id`, `payway`, `request_body`, `response_body`, `schedule_status`, `schedule_dep_task_id`, `status`, `status_influ_p_task`, `priority`, `result`, `create_at`, `update_at`, `version`, `contract_rule`, `rule_group_id`, `retry`)
VALUES (4510722, 2585382, '**************', '', NULL, NULL, NULL, 99, '', NULL, '{"sn":"**************","lkl_mer_id":"","contract_id":"*************","pic_type":"BUSINESS_LICENCE","pic_url":"http://images.wosaimg.com/a5/b0648e6f150ca39e769c7e5880aa07f42639e2.jpg"}', NULL, 0, 0, 3, 0, '2020-07-09 22:21:26', '{"sn":"**************","lkl_mer_id":"","contract_id":"*************","pic_type":"BUSINESS_LICENCE","pic_url":"http://images.wosaimg.com/a5/b0648e6f150ca39e769c7e5880aa07f42639e2.jpg"}', '2020-07-09 22:21:26', '2020-07-09 22:21:26', 0, '', '', 0);
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585382, '**************', '测试商户镖脯', '新增商户入网', NULL, '{"bankAccount":{"type":"2"}}', 0, 5, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', 'default');
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585383, 'fail_verify_merchant_sn', '测试商户镖脯', '新增商户入网', NULL, '{"bankAccount":{"type":"2"}}', 6, 6, 0, '{"message":"对公，收钱吧代付通路返回金额验证不通过"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', 'default');
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585384, 'weixin_merchant_sn', '测试商户镖脯', '微信商家认证', NULL, '{"bankAccount":{"type":"2"}}', 5, 5, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', 'default');

-- 支付宝直连相关
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585385, 'processAliTaskSn', '测试商户镖脯', '支付宝直连', NULL, '{"bankAccount":{"type":"2"}}', 1, 0, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '');
INSERT INTO ali_direct_apply (`id`, `merchant_sn`, `task_id`, `batch_no`, `status`, `sign_url`, `result`, `user_id`, `create_at`, `priority`, `update_at`, `request_body`, `response_body`, `form_body`)
VALUES (1, 'processAliApplySn', 2585385, '***************', 30, 'url', NULL, NULL, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '2020-07-09 20:07:32', NULL, NULL, NULL);
INSERT INTO direct_status (id, merchant_sn, dev_code, status, create_at, update_at)
VALUES (1, 'successAliSn', 'MMNMVMGFWSJD',2, '2020-07-09 20:07:32', '2020-07-09 20:07:32');

INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585386, 'aliAuditApplySn', '测试商户镖脯', '支付宝直连', NULL, '{"bankAccount":{"type":"2"}}', 1, 0, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '');
INSERT INTO ali_direct_apply (`id`, `merchant_sn`, `task_id`, `batch_no`, `status`, `sign_url`, `result`, `user_id`, `create_at`, `priority`, `update_at`, `request_body`, `response_body`, `form_body`)
VALUES (2, 'aliAuditApplySn', 2585386, '**************', 30, 'url', NULL, 'user_id', '2020-07-09 20:07:32', '2020-07-09 20:07:32', '2020-07-09 20:07:32', NULL, NULL, '{"app_info":{"fee_rate":"0.38"},"contact_info":{}}');

-- 微信直连相关
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585387, 'processWeixinOnLineTaskSn', '测试商户镖脯', '微信直连线下', NULL, '{"bankAccount":{"type":"2"}}', 1, 0, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '');
insert into weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (1, 'processWeixinOnLineApplySn', 2585387, 'PJOWY29OWSZI', 1, 0, NULL, NULL, '2020-07-09 20:07:32', '2020-07-09 20:07:32', NULL, NULL, NULL, NULL);VALUES (1, 'processWeixinApplySn', 2585387, '***************', 30, 'url', NULL, NULL, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '2020-07-09 20:07:32', NULL, NULL, NULL);
INSERT INTO direct_status (id, merchant_sn, dev_code, status, create_at, update_at)
VALUES (2, 'successWeixinOnlineSn', 'PJOWY29OWSZI',2, '2020-07-09 20:07:32', '2020-07-09 20:07:32');

INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585388, 'weixinOffAuditSn', '测试商户镖脯', '微信直连线下', NULL, '{"bankAccount":{"type":"2"}}', 1, 0, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-07-09 20:07:32', '2020-07-09 20:07:32', 0, '2020-07-09 20:07:32', '2020-07-09 20:07:32', '');
INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (2, 'weixinOffAuditSn', 2585388, 'PJOWY29OWSZI', 1, 20, NULL, NULL, '2020-07-09 20:07:32', '2020-07-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"fee_rate":"0.38","app_id":"123"},"contact_info":{}}');

-- 微信直连转人工
INSERT INTO `contract_task` (`id`, `merchant_sn`, `merchant_name`, `type`, `event_msg`, `event_context`, `status`, `affect_sub_task_count`, `affect_status_success_task_count`, `result`, `create_at`, `update_at`, `version`, `priority`, `complete_at`, `rule_group_id`)
VALUES (2585389, 'manualTaskSn', '测试商户镖脯', '微信直连线下', NULL, '{"bankAccount":{"type":"2"}}', 6, 0, 0, '{"message":"对公，收钱吧代付通路验证中"}', '2020-11-09 20:07:32', '2020-11-09 20:07:32', 0, '2020-11-09 20:07:32', '2020-11-09 20:07:32', '');
INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (3, 'manualTaskSn', 2585389, 'PJOWY29OWSZI', 1, 70, NULL, NULL, '2020-11-09 20:07:32', '2020-11-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"fee_rate":"0.38","app_id":"123"},"contact_info":{}}');
INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (4, 'manualTaskSn', 2585389, 'PJOWY29OWSZI', 1, 70, NULL, NULL, '2020-10-09 20:07:32', '2020-10-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"fee_rate":"0.38","app_id":"123"},"contact_info":{}}');
INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (5, 'manualTaskSn', 2585389, 'PJOWY29OWSZI', 1, 70, NULL, NULL, '2020-10-09 20:07:32', '2020-10-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"fee_rate":"0.38","app_id":"123"},"contact_info":{}}');

INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (6, 'spAuditingSn', 2585389, 'PJOWY29OWSZI', 2, 10, NULL, NULL, '2020-10-09 20:07:32', '2020-10-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"fee_rate":"0.38","app_id":"123"},"contact_info":{}}');
INSERT INTO weixin_direct_apply (`id`, `merchant_sn`, `task_id`, `dev_code`, `submit_type`, `status`, `sign_url`,`result`, `create_at`, `update_at`, `request_body`, `response_body`, `qrcode_data`,`form_body`)
VALUES (7, 'bindApplySn', 2585389, 'PJOWY29OWSZI', 3, 10, NULL, NULL, '2020-10-09 20:07:32', '2020-10-09 20:07:32', NULL, NULL, NULL, '{"app_info":{"bind_merchant_sn":"bind_merchant_sn","app_id":"123"}}');




