package com.wosai.upay.job.xxljob.direct.antshop;

import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.biz.AntShopBiz;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.model.bluesea.AliMchLevel;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AntShopUpgradeLevelJobHandlerTest {

    @Mock
    private AntShopBiz antShopBiz;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private BlueSeaService blueSeaService;

    @Mock
    private ChatBotUtil chatBotUtil;

    @InjectMocks
    private AntShopUpgradeLevelJobHandler antShopUpgradeLevelJobHandler;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        // 调用被测试的方法
        String lockKey = antShopUpgradeLevelJobHandler.getLockKey();

        // 验证返回的锁键是否正确
        Assert.assertEquals("AntShopUpgradeLevelJobHandler", lockKey);
    }

    @Test
    public void execute_FuYouProvider_UpdatesToQualify() {
        AntShopTask task = new AntShopTask();
        task.setAli_mch_id("12345");
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        MerchantProviderParamsDO params = new MerchantProviderParamsDO();
        params.setProvider(PayParamsModel.PROVIDER_FUYOU);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.of(params));

        antShopUpgradeLevelJobHandler.execute(param);

        verify(antShopBiz).updateAntShopTask(argThat(t -> t.getStatus() == AntShopTaskConstant.TaskStatus.QUALIFY));
    }

    @Test
    public void execute_QueryAlipayMchLevelFailure_UpdatesToRetry() {
        AntShopTask task = new AntShopTask();
        task.setAli_mch_id("12345");
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        AliMchLevel aliMchLevel = new AliMchLevel();
        aliMchLevel.setSuccess(false);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());
        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(aliMchLevel);

        antShopUpgradeLevelJobHandler.execute(param);

        verify(antShopBiz).updateReTry(eq(task), isNull(), isNull());
    }

    @Test
    public void execute_LevelInList_UpdatesToM3() {
        AntShopTask task = new AntShopTask();
        task.setAli_mch_id("12345");
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        AliMchLevel aliMchLevel = new AliMchLevel();
        aliMchLevel.setSuccess(true);
        aliMchLevel.setLevel("INDIRECT_LEVEL_M1");

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());
        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(aliMchLevel);

        antShopUpgradeLevelJobHandler.execute(param);

        verify(antShopBiz).updateMerchantToM3(task);
    }

    @Test
    public void execute_LevelNotInList_UpdatesToQualify() {
        AntShopTask task = new AntShopTask();
        task.setAli_mch_id("12345");
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        AliMchLevel aliMchLevel = new AliMchLevel();
        aliMchLevel.setSuccess(true);
        aliMchLevel.setLevel("OTHER_LEVEL");

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenReturn(Optional.empty());
        when(blueSeaService.queryAlipayMchLevel("12345")).thenReturn(aliMchLevel);

        antShopUpgradeLevelJobHandler.execute(param);

        verify(antShopBiz).updateAntShopTask(argThat(t -> t.getStatus() == AntShopTaskConstant.TaskStatus.QUALIFY));
    }

    @Test
    public void execute_ExceptionOccurs_UpdatesToFailAndSendsMessage() {
        AntShopTask task = new AntShopTask();
        task.setAli_mch_id("12345");
        task.setBusiness_type(AntShopTaskConstant.BusinessType.SCAN_CODE);

        when(antShopBiz.getAntShopTasks(anyList(), anyList(), anyInt(), anyLong()))
                .thenReturn(Collections.singletonList(task));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantId("12345"))
                .thenThrow(new RuntimeException("Test exception"));

        antShopUpgradeLevelJobHandler.execute(param);

        verify(antShopBiz).updateAntShopTask(argThat(t -> t.getStatus() == AntShopTaskConstant.TaskStatus.FAIL));
        verify(chatBotUtil).sendMessageToContractWarnChatBot(anyString());
    }
}
