package com.wosai.upay.job.mapper;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ChannelActivity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ChannelActivityMapperTest extends BaseTest {

    @Autowired
    ChannelActivityMapper channelActivityMapper;

    @Test
    public void insertTest() {
        ChannelActivity channelActivity = new ChannelActivity();
        channelActivity.setApply_id(null);
        channelActivity.setAudit_id("9238742");
        channelActivity.setMch_id("test");
        channelActivity.setMerchant_sn("test");
        channelActivity.setMerchant_id("test-test");
        channelActivity.setType(1);
        channelActivity.setStatus(1);
        channelActivity.setForm_body("testtetstetst");
        channelActivityMapper.insert(channelActivity);
        System.out.println("ok");
    }

    @Test
    public void selectByPrimaryKeyTest() {
        ChannelActivity cc = channelActivityMapper.selectByPrimaryKey(13L);
        System.out.println("OK");
    }

}
