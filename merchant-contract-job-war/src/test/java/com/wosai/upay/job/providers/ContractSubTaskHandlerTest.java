package com.wosai.upay.job.providers;


import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.handlers.ContractSubTaskHandler;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.service.BankCardServiceImpl;
import com.wosai.upay.job.service.RedisService;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;


public class ContractSubTaskHandlerTest extends BaseTest {

    @MockBean
    TaskResultService taskResultService;
    @MockBean
    MonitorLog monitorLog;
    @MockBean
    RuleContext ruleContext;
    @MockBean
    BankCardServiceImpl bankCardService;
    @MockBean
    List<AbstractProvider> abstractProvider;
    @Autowired
    ContractSubTaskHandler contractSubTaskHandler;
    @MockBean
    private AuthApplyFlowService authApplyFlowService;
    @MockBean
    private RedisService redisService;
    @MockBean
    private TradeConfigService tradeConfigService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(contractSubTaskHandler, "redisService", redisService);
        ReflectionTestUtils.setField(contractSubTaskHandler, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(contractSubTaskHandler, "authApplyFlowService", authApplyFlowService);
    }


    @Test
    public void handleTask() throws Exception {
        ContractTask task = new ContractTask();
        task.setStatus(0);
        ContractSubTask subTask = new ContractSubTask();
        subTask.setChannel("lkl").setContract_rule("lkl");
        Mockito.doReturn(new ContractRule()).when(ruleContext).getContractRule(Mockito.anyString());
        contractSubTaskHandler.handle(task, subTask);
        ContractResponse response = new ContractResponse();
        abstractProvider.forEach(r -> {
                    Mockito.doReturn(response).when(r)
                            .processTaskByRule(Mockito.anyObject(), Mockito.anyObject(), Mockito.anyObject());
                }
        );
        subTask.setContract_rule("lkl");
        contractSubTaskHandler.handle(task, subTask);
    }

    @Test
    public void handlerAuthUpgrade() {
    }
}
