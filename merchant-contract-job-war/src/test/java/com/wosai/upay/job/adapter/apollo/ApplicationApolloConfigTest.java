package com.wosai.upay.job.adapter.apollo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.biz.AlipayAuthBiz;
import com.wosai.upay.job.config.ApolloConfig;
import com.wosai.upay.job.config.ApolloParamsConfig;
import com.wosai.upay.job.scheduler.ScheduleUtil;
import com.wosai.upay.job.util.ProviderUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.wosai.upay.job.service.MerchantProviderParamsServiceImpl.USE_SPECIAL_CHANNEL;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.OFFLINE_EDU_TRAIN;
import static com.wosai.upay.job.service.WeixinFeeRateActivityServiceImpl.SCHOOL_CANTEEN;


public class ApplicationApolloConfigTest extends BaseTest {

    @Autowired
    private ApolloParamsConfig apolloParamsConfig;
    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Test
    public void getAppModuleWhiteType() {
        int appModuleWhiteType1 = apolloParamsConfig.getInt("app_module_white_type", 0);
        int appModuleWhiteType2 = applicationApolloConfig.getAppModuleWhiteType();
        Assert.assertEquals(appModuleWhiteType1, appModuleWhiteType2);
    }

    @Test
    public void getWeixinActivity() {
        List<String> channels1 = apolloParamsConfig.getList("weixin_activity", "[]");
        List<String> channels2 = applicationApolloConfig.getWeixinActivity();
        Assert.assertEquals(JSON.toJSONString(channels1), JSON.toJSONString(channels2));
    }

    @Test
    public void getBlacklistWords() {
        List<String> words1 = apolloParamsConfig.getList("blacklist-words", "[\"黑名单\",\"商户存在风险\"]");
        List<String> words2 = applicationApolloConfig.getBlacklistWords();
        Assert.assertEquals(JSON.toJSONString(words1), JSON.toJSONString(words2));
    }

    @Test
    public void getAgentAppidConfig() {
        Map content1 = apolloParamsConfig.getMap("agentAppidConfig", null);
        Map content2 = applicationApolloConfig.getAgentAppidConfig();
        Assert.assertEquals(JSON.toJSONString(content1), JSON.toJSONString(content2));

    }

    @Test
    public void getAntShopIndustryMcc() {
        final Map<String, String> categoryMap1 = apolloParamsConfig.getMap("ant_shop_industry_mcc", "");
        final Map<String, String> categoryMap2 = applicationApolloConfig.getAntShopIndustryMcc();
        Assert.assertEquals(JSON.toJSONString(categoryMap1), JSON.toJSONString(categoryMap2));
    }

    @Test
    public void getAcFees() {
        Map fees1 = apolloParamsConfig.getMap("ac_fees", "{}");
        Map fees2 = applicationApolloConfig.getAcFees();
        Assert.assertEquals(JSON.toJSONString(fees1), JSON.toJSONString(fees2));
    }

    @Test
    public void getAlyThirdCategory() {
        final Map<String, String> categoryMap1 = apolloParamsConfig.getMap("aly_third_category", "aly_third_category");
        final Map<String, String> categoryMap2 = applicationApolloConfig.getAlyThirdCategory();
        Assert.assertEquals(JSON.toJSONString(categoryMap1), JSON.toJSONString(categoryMap2));
    }

    @Test
    public void getBlueSeaSupply() {
        Map map1 = apolloParamsConfig.getMap(BlueSeaConstant.SUPPLY_KEY, JSON.toJSONString(BlueSeaConstant.SUPPLY_ID));
        Map map2 = applicationApolloConfig.getBlueSeaSupply();
        Assert.assertEquals(JSON.toJSONString(map1), JSON.toJSONString(map2));
    }

    @Test
    public void getWechatGoldRoute() {
        Map route1 = apolloParamsConfig.getMap("wechat-gold-route", null);
        Map route2 = applicationApolloConfig.getWechatGoldRoute();
        Assert.assertEquals(JSON.toJSONString(route1), JSON.toJSONString(route2));
    }

    @Test
    public void getPrimaryContractTimeOut() {
        Map primaryContractTimeOut1 = apolloParamsConfig.getMap("primary_contract_time_out", "{}");
        Map primaryContractTimeOut2 = applicationApolloConfig.getPrimaryContractTimeOut();
        Assert.assertEquals(JSON.toJSONString(primaryContractTimeOut1), JSON.toJSONString(primaryContractTimeOut2));
    }

    @Test
    public void getCcbDecpCity() {
        Map ccbDecpCity1 = apolloParamsConfig.getMap("ccb_decp_city", null);
        Map ccbDecpCity2 = applicationApolloConfig.getCcbDecpCity();
        Assert.assertEquals(JSON.toJSONString(ccbDecpCity1), JSON.toJSONString(ccbDecpCity2));
    }

    @Test
    public void getAppIdSubBiz() {
        Map appIdSubBizMap1 = apolloParamsConfig.getMap("appId_subBiz", "{}");
        Map appIdSubBizMap2 = applicationApolloConfig.getAppIdSubBiz();
        Assert.assertEquals(JSON.toJSONString(appIdSubBizMap1), JSON.toJSONString(appIdSubBizMap2));
    }

    @Test
    public void getWeixinOnlineSettlementId() {
        Map onlineSettlementIdConfig1  = apolloParamsConfig.getMap("weixin_online_settlement_id", "{}");
        Map onlineSettlementIdConfig2  = applicationApolloConfig.getWeixinOnlineSettlementId();
        Assert.assertEquals(JSON.toJSONString(onlineSettlementIdConfig1), JSON.toJSONString(onlineSettlementIdConfig2));
    }

    @Test
    public void getChangeAcquirerCheck() {
        Map checkConfig1 = apolloParamsConfig.getMap("change-acquirer-check", "{}");
        Map checkConfig2 = applicationApolloConfig.getChangeAcquirerCheck();
        Assert.assertEquals(JSON.toJSONString(checkConfig1), JSON.toJSONString(checkConfig2));
    }

    @Test
    public void getFuyouBusinessMapping() {
        Map<String, Object> fuyouScopeMapping1 = apolloParamsConfig.getMap("fuyou_business_mapping", "{}");
        Map<String, Object> fuyouScopeMapping2 = applicationApolloConfig.getFuyouBusinessMapping();
        Assert.assertEquals(JSON.toJSONString(fuyouScopeMapping1), JSON.toJSONString(fuyouScopeMapping2));
    }

    @Test
    public void getAliDirectIndustry() {
        Map aliDirectIndustry1 = apolloParamsConfig.getMap("ali_direct_industry", null);
        Map aliDirectIndustry2 = applicationApolloConfig.getAliDirectIndustry();
        Assert.assertEquals(JSON.toJSONString(aliDirectIndustry1), JSON.toJSONString(aliDirectIndustry2));
    }

    @Test
    public void getProcessingDirectMessage() {
        Map processingContractMap1 = apolloParamsConfig.getMap(ScheduleUtil.TIPS_FOR_DIRECT_PROCESSING_KEY, null);
        Map processingContractMap2 = applicationApolloConfig.getProcessingDirectMessage();
        Assert.assertEquals(JSON.toJSONString(processingContractMap1), JSON.toJSONString(processingContractMap2));
    }

    @Test
    public void getProcessingContractMessage() {
        Map errorMessage1 = apolloParamsConfig.getMap(ScheduleUtil.TIPS_FOR_WEB_PROCESSING_KEY, null);
        Map errorMessage2 = applicationApolloConfig.getProcessingContractMessage();
        Assert.assertEquals(JSON.toJSONString(errorMessage1), JSON.toJSONString(errorMessage2));
    }

    @Test
    public void getFuyouTerminalMode() {
        Map<String, String> terminalMap1 = apolloParamsConfig.getMap("fuyou_terminal_mode", "{}");
        Map<String, String> terminalMap2 = applicationApolloConfig.getFuyouTerminalMode();
        Assert.assertEquals(JSON.toJSONString(terminalMap1), JSON.toJSONString(terminalMap2));
    }

    @Test
    public void getUmsToLkl() {
        Map map1 = apolloParamsConfig.getMap("ums_to_lkl", null);
        Map map2 = applicationApolloConfig.getUmsToLkl();
        Assert.assertEquals(JSON.toJSONString(map1), JSON.toJSONString(map2));
    }

    @Test
    public void getMccConverter() {
        Map mccConverter1 = apolloParamsConfig.getMap("mcc_converter", "{}");
        Map mccConverter2 = applicationApolloConfig.getMccConverter();
        Assert.assertEquals(JSON.toJSONString(mccConverter1), JSON.toJSONString(mccConverter2));
    }

    @Test
    public void getSameTermDelayTime() {
        Map sameTermDelayTimeMap1 = apolloParamsConfig.getMap("same_term_delay_time", "{\"1028\":1000}");
        Map sameTermDelayTimeMap2 = applicationApolloConfig.getSameTermDelayTime();
        Assert.assertEquals(JSON.toJSONString(sameTermDelayTimeMap1), JSON.toJSONString(sameTermDelayTimeMap2));
    }

    @Test
    public void getReBindTerminalForbiddenTime() {
        Map forbiddenTimeMap1 = apolloParamsConfig.getMap("reBindTerminal_forbidden_time", "{}");
        Map forbiddenTimeMap2 = applicationApolloConfig.getReBindTerminalForbiddenTime();
        Assert.assertEquals(JSON.toJSONString(forbiddenTimeMap1), JSON.toJSONString(forbiddenTimeMap2));
    }

    @Test
    public void getChannelAuthUrl() {
        Map map1 = apolloParamsConfig.getMap(ScheduleUtil.CHANNEL_AUTH_URL, "{}");
        Map map2 = applicationApolloConfig.getChannelAuthUrl();
        Assert.assertEquals(JSON.toJSONString(map1), JSON.toJSONString(map2));
    }

    @Test
    public void getPayLaterIndustryFee() {
        Map<String,String> industryFeeMap1  = apolloParamsConfig.getMap("pay_later_industry_fee", null);
        Map<String,String> industryFeeMap2  = applicationApolloConfig.getPayLaterIndustryFee();
        Assert.assertEquals(JSON.toJSONString(industryFeeMap1), JSON.toJSONString(industryFeeMap2));
    }

    @Test
    public void getTradeCombIds() {
        Map tradeCombIdMap1 = apolloParamsConfig.getMap("trade_comb_ids", JSONObject.toJSONString(CollectionUtil.hashMap(SCHOOL_CANTEEN, -1L, OFFLINE_EDU_TRAIN, -2L)));
        Map tradeCombIdMap2 = applicationApolloConfig.getTradeCombIds();
        Assert.assertEquals(JSON.toJSONString(tradeCombIdMap1), JSON.toJSONString(tradeCombIdMap2));
    }

    @Test
    public void getReflectCodeMap() {
        Map reflectCode1 = apolloParamsConfig.getMap("reflectCodeMap", "{}");
        Map<String, String> reflectCodeMap2 = applicationApolloConfig.getReflectCodeMap();
        Assert.assertEquals(JSON.toJSONString(reflectCode1), JSON.toJSONString(reflectCodeMap2));
    }

    @Test
    public void getSqbDistrictCode() {
        List sqbDistrictCode1 = apolloParamsConfig.getList("sqbDistrictCode", "{}");
        List<String> sqbDistrictCode2 = applicationApolloConfig.getSqbDistrictCode();
        Assert.assertEquals(JSON.toJSONString(sqbDistrictCode1), JSON.toJSONString(sqbDistrictCode2));
    }

    @Test
    public void getAutoReceiptAppid() {
        boolean autoReceiptAppid1 = apolloParamsConfig.getBoolean("auto-receipt-appid", true);
        Boolean autoReceiptAppid2 = applicationApolloConfig.getAutoReceiptAppid();
        Assert.assertEquals(autoReceiptAppid1, autoReceiptAppid2);
    }

    @Test
    public void getHxOrgNoAppId() {
        Map<String, String> map1 = apolloParamsConfig.getMap("hx_orgNo_appId", "{}");
        Map<String, String> map2 = applicationApolloConfig.getHxOrgNoAppId();
        Assert.assertEquals(JSON.toJSONString(map1), JSON.toJSONString(map2));
    }

    @Test
    public void getHxParentServerOrgno() {
        String hxParentServerOrgno1 = apolloParamsConfig.getString("hx-parentServerorgno", "");
        String hxParentServerOrgno2 = applicationApolloConfig.getHxParentServerOrgno();
        Assert.assertEquals(hxParentServerOrgno1, hxParentServerOrgno2);
    }

    @Test
    public void getIcbcWxConfig() {
    }

    @Test
    public void getSpecialIndustry() {
        List<String> specialIndustry1 = apolloParamsConfig.getList("special_industry", "[]");
        List<String> specialIndustry2 = applicationApolloConfig.getSpecialIndustry();
        Assert.assertEquals(JSON.toJSONString(specialIndustry1), JSON.toJSONString(specialIndustry2));
    }

    @Test
    public void getForbidPay() {
        List<String> forbidPay1 = apolloParamsConfig.getList("forbid-pay", "[]");
        List<String> forbidPay2 = applicationApolloConfig.getForbidPay();
        Assert.assertEquals(JSON.toJSONString(forbidPay1), JSON.toJSONString(forbidPay2));
    }

    @Test
    public void getAuthAuditTime() {
        int authAuditTime1 = apolloParamsConfig.getInt("auth_audit_time", 10);
        int authAuditTime2 = applicationApolloConfig.getAuthAuditTime();
        Assert.assertEquals(authAuditTime1, authAuditTime2);
    }

    @Test
    public void getTipsForApplyingAddMinutes() {
        String string1 = apolloParamsConfig.getString(ScheduleUtil.TIPS_FOR_APPLYING_MINUTES_KEY, null);
        String string2 = applicationApolloConfig.getTipsForApplyingAddMinutes();
        Assert.assertEquals(string1, string2);
    }

    @Test
    public void getBlueseaTerminalVenderAppAPPid() {
        List<String> vendorAppids1 = apolloParamsConfig.getList(BlueSeaConstant.BLUESEA_TERMINAL_VENDER_APP_APPID, JSON.toJSONString(BlueSeaConstant.blueSeaTerminalVenderAppAppId));
        List<String> vendorAppids2 = applicationApolloConfig.getBlueseaTerminalVenderAppAPPid();
        Assert.assertEquals(JSON.toJSONString(vendorAppids1), JSON.toJSONString(vendorAppids2));
    }

    @Test
    public void getBlueseaPcTerminalVenderAppAPPid() {
        List<String> vendorAppids1 = apolloParamsConfig.getList(BlueSeaConstant.BLUESEA_PCTERMINAL_VENDER_APP_APPID, JSON.toJSONString(BlueSeaConstant.blueSeaPcTerminalVenderAppAppid));
        List<String> vendorAppids2 = applicationApolloConfig.getBlueseaPcTerminalVenderAppAPPid();
        Assert.assertEquals(JSON.toJSONString(vendorAppids1), JSON.toJSONString(vendorAppids2));
    }

    @Test
    public void getSimpleSuperPos() {
        List<String> simpleSuperPosList1 = Lists.newArrayList(apolloParamsConfig.getList("simple_super_pos", "[\"2022071100004879\",\"2022110300005136\"]"));
        List<String> simpleSuperPosList2 = Lists.newArrayList(applicationApolloConfig.getSimpleSuperPos());
        Assert.assertEquals(JSON.toJSONString(simpleSuperPosList1), JSON.toJSONString(simpleSuperPosList2));
    }

    @Test
    public void getOnlineVendorAppids() {
        List onlineVendorAppids1 = apolloParamsConfig.getList("online_vendor_appids", "[]");
        List<String> onlineVendorAppids2 = applicationApolloConfig.getOnlineVendorAppids();
        Assert.assertEquals(JSON.toJSONString(onlineVendorAppids1), JSON.toJSONString(onlineVendorAppids2));
    }

    @Test
    public void getFySimpleSuperPos() {
        List<String> fyT9PosList1 = apolloParamsConfig.getList("fy_simple_super_pos", "[\"2024022100006650\",\"2024022100006651\"]");
        List<String> fyT9PosList2 = applicationApolloConfig.getFySimpleSuperPos();
        Assert.assertEquals(JSON.toJSONString(fyT9PosList1), JSON.toJSONString(fyT9PosList2));
    }

    @Test
    public void getReContractWx() {
        List<String> reContractWxList1 = apolloParamsConfig.getList("reContractWx", "[\"lkl\", \"haike\"]");
        List<String> reContractWxList2 = applicationApolloConfig.getReContractWx();
        Assert.assertEquals(JSON.toJSONString(reContractWxList1), JSON.toJSONString(reContractWxList2));
    }

    @Test
    public void getCcbDecpOldIds() {
        List<String> ccbDecpOldIds1 = apolloParamsConfig.getList("ccb_decp_old_ids", null);
        List<String> ccbDecpOldIds2 = applicationApolloConfig.getCcbDecpOldIds();
        Assert.assertEquals(JSON.toJSONString(ccbDecpOldIds1), JSON.toJSONString(ccbDecpOldIds2));
    }



    @Test
    public void getMultiBusinessWhitelist() {
        List whitelist1 = apolloParamsConfig.getList("multi_business_whitelist", "[]");
        List whitelist2 = applicationApolloConfig.getMultiBusinessWhitelist();
        Assert.assertEquals(JSON.toJSONString(whitelist1), JSON.toJSONString(whitelist2));
    }

    @Test
    public void getAcquireOrder() {
        final List<String> PRIORITY_ORDER1 =  apolloParamsConfig.getList("acquire_order", "[\"haike\",\"lklV3\",\"fuyou\"]");
        final List<String> PRIORITY_ORDER2 =  applicationApolloConfig.getAcquireOrder();
        Assert.assertEquals(JSON.toJSONString(PRIORITY_ORDER1), JSON.toJSONString(PRIORITY_ORDER2));
    }

    @Test
    public void changeAll() {
        final List changeAll1 = apolloParamsConfig.getList("change_all", "[1020,1032,1033,1017,1016,1037,1038,1034]");
        final List changeAll2 = applicationApolloConfig.getChangeAll();
        Assert.assertEquals(JSON.toJSONString(changeAll1), JSON.toJSONString(changeAll2));
    }

    @Test
    public void getWxChannelActivityIndustry() {
        List<Map> industryConfigs1 = apolloParamsConfig.getList("wx-channel-activity-industry", "[]");
        List<Map> industryConfigs2 = applicationApolloConfig.getWxChannelActivityIndustry();
        Assert.assertEquals(JSON.toJSONString(industryConfigs1), JSON.toJSONString(industryConfigs2));
    }

    @Test
    public void getIsvOrgPath() {
        List list1 = apolloParamsConfig.getList("isv-org-path", "[\"00069\",\"00052\"]");
        List<String> list2 = applicationApolloConfig.getIsvOrgPath();
        Assert.assertEquals(JSON.toJSONString(list1), JSON.toJSONString(list2));
    }

    @Test
    public void getChangeAcquirerBizCheck() {
        List<Map> list1 = apolloParamsConfig.getList("change-acquirer-biz-check", "[]");
        List<Map> list2 = applicationApolloConfig.getChangeAcquirerBizCheck();
        Assert.assertEquals(JSON.toJSONString(list1), JSON.toJSONString(list2));
    }

    @Test
    public void getUmsForbidOrg() {
        List<String> forbidOrgs1 = apolloParamsConfig.getList("ums-forbid-org", "[\"00069\",\"00052\"]");
        List<String> forbidOrgs2 = applicationApolloConfig.getUmsForbidOrg();
        Assert.assertEquals(JSON.toJSONString(forbidOrgs1), JSON.toJSONString(forbidOrgs2));
    }

    @Test
    public void getIgnoreBankPreCheck() {
        List list1 = apolloParamsConfig.getList("ignore_bankPre_check", "[\"SGJWMX7JFINP\"]");
        List<String> list2 = applicationApolloConfig.getIgnoreBankPreCheck();
        Assert.assertEquals(JSON.toJSONString(list1), JSON.toJSONString(list2));
    }

    @Test
    public void getLklCallBack() {
        List<String> lklCallBack1 = apolloParamsConfig.getList("lklCallBack", "[]");
        List<String> lklCallBack2 = applicationApolloConfig.getLklCallBack();
        Assert.assertEquals(JSON.toJSONString(lklCallBack1), JSON.toJSONString(lklCallBack2));
    }

    @Test
    public void getSkipDingMsg() {
        List<String> list1 = apolloParamsConfig.getList("skip-ding-msg", "[]");
        List<String> list2 = applicationApolloConfig.getSkipDingMsg();
        Assert.assertEquals(JSON.toJSONString(list1), JSON.toJSONString(list2));
    }

    @Test
    public void getMiniOrganizationId() {
        String id1 = apolloParamsConfig.getString("mini-organization-id", "");
        String id2 = applicationApolloConfig.getMiniOrganizationId();
        Assert.assertEquals(id1, id2);
    }

    @Test
    public void getRuleGroup() {
        String ruleConfig1 = apolloParamsConfig.getString("rule-group", null);
        String ruleConfig2 = applicationApolloConfig.getRuleGroup();
        Assert.assertEquals(ruleConfig1, ruleConfig2);
    }

    @Test
    public void getMerchantLklNew() {
        String merchantLklNew1 = apolloParamsConfig.getString("merchant_lkl_new", "");
        String merchantLklNew2 = applicationApolloConfig.getMerchantLklNew();
        Assert.assertEquals(merchantLklNew1, merchantLklNew2);
    }

    @Test
    public void getPayTradeAppId() {
        String name1 = apolloParamsConfig.getString("payTradeAppId", "支付业务");
        String name2 = applicationApolloConfig.getPayTradeAppId();
        Assert.assertEquals(name1, name2);
    }

    @Test
    public void getT9TradeAppId() {
        String name1 = apolloParamsConfig.getString("t9TradeAppId", "刷卡收款");
        String name2 = applicationApolloConfig.getT9TradeAppId();
        Assert.assertEquals(name1, name2);
    }

    @Test
    public void getBankTradeAppId() {
        String name1 = apolloParamsConfig.getString("bankTradeAppId", "银行合作");
        String name2 = applicationApolloConfig.getBankTradeAppId();
        Assert.assertEquals(name1, name2);
    }

    @Test
    public void getOnlinePayment() {
        String name1 = apolloParamsConfig.getString("onlinePayment", "线上");
        String name2 = applicationApolloConfig.getOnlinePayment();
        Assert.assertEquals(name1, name2);
    }

    @Test
    public void getWeixinIndirectIndustry() {
        String weixinIndirectIndustry1 = apolloParamsConfig.getString("weixin_indirect_industry", "{}");
        String weixinIndirectIndustry2 = applicationApolloConfig.getWeixinIndirectIndustry();
        Assert.assertEquals(weixinIndirectIndustry1, weixinIndirectIndustry2);
    }

    @Test
    public void getNotHandleCombo() {
        String notHandleCombo1 = apolloParamsConfig.getString("not-handle-combo", "");
        String notHandleCombo2 = applicationApolloConfig.getNotHandleCombo();
        Assert.assertEquals(notHandleCombo1, notHandleCombo2);
    }

    @Test
    public void getLklV3AddTermType() {
        final String termType1 = apolloParamsConfig.getString("lklV3_add_term_type", "50.20.10.11.60.118.119.120.40.30");
        final String termType2 = applicationApolloConfig.getLklV3AddTermType();
        Assert.assertEquals(termType1, termType2);
    }

    @Test
    public void getMonitorWarn() {
        String monitorWarn1 = apolloParamsConfig.getString("monitor_warn", null);
        String monitorWarn2 = applicationApolloConfig.getMonitorWarn();
        Assert.assertEquals(monitorWarn1, monitorWarn2);
    }

    @Test
    public void getQueryTime() {
        String queryTime1 = apolloParamsConfig.getString(ScheduleUtil.QUERY_APPLO_KEY, null);
        String queryTime2 = applicationApolloConfig.getQueryTime();
        Assert.assertEquals(queryTime1, queryTime2);
    }

    @Test
    public void getQueryLimit() {
        String queryLimit1 = apolloParamsConfig.getString(ScheduleUtil.LIMIT_APPLO_KEY, null);
        String queryLimit2 = applicationApolloConfig.getQueryLimit();
        Assert.assertEquals(queryLimit1, queryLimit2);
    }

    @Test
    public void getDynamicCron() {
        String dynamicCron1 = apolloParamsConfig.getString("dynamic_cron", "0 0/10 * * * ?");
        String dynamicCron2 = applicationApolloConfig.getDynamicCron();
        Assert.assertEquals(dynamicCron1, dynamicCron2);
    }

    @Test
    public void getCheckUmsLkl() {
        String n1 = apolloParamsConfig.getString("check_ums_lkl", "N");
        String n2 = applicationApolloConfig.getCheckUmsLkl();
        Assert.assertEquals(n1, n2);
    }

    @Test
    public void getGrayScaleLklNew() {
        int gray1 = apolloParamsConfig.getInt("gray_scale_lkl_new", 30);
        int gray2 = applicationApolloConfig.getGrayScaleLklNew();
        Assert.assertEquals(gray1, gray2);
    }

    @Test
    public void getDelayTerminalTask() {
        int delaySeconds1 = apolloParamsConfig.getInt("delay_terminal_task", 40);
        int delaySeconds2 = applicationApolloConfig.getDelayTerminalTask();
        Assert.assertEquals(delaySeconds1, delaySeconds2);
    }

    @Test
    public void getAcquirerChangeScheduleSemaphore() {
        int config1 = apolloParamsConfig.getInt("acquirer-change-schedule-semaphore", 3);
        int config2 = applicationApolloConfig.getAcquirerChangeScheduleSemaphore();
        Assert.assertEquals(config1, config2);
    }

    @Test
    public void getHxWxAuthCount() {
        final int hxWxAuthCount1 = apolloParamsConfig.getInt("hx_wx_auth_count", 200);
        final int hxWxAuthCount2 = applicationApolloConfig.getHxWxAuthCount();
        Assert.assertEquals(hxWxAuthCount1, hxWxAuthCount2);
    }

    @Test
    public void getQueryUnionOpenLimit() {
        int limit1 = apolloParamsConfig.getInt("query_union_open_limit", ScheduleUtil.DEFAULT_QUERY_LIMIT);
        int limit2 = applicationApolloConfig.getQueryUnionOpenLimit();
        Assert.assertEquals(limit1, limit2);
    }

    @Test
    public void getQueryUnionOpenTime() {
        long time1 = apolloParamsConfig.getLong("query_union_open_time", ScheduleUtil.DEFAULT_THREE_HOURS_MILLIS_QUERY);
        long time2 = applicationApolloConfig.getQueryUnionOpenTime();
        Assert.assertEquals(time1, time2);
    }

    @Test
    public void getDynamicLimit() {
        int queryLimit1 = apolloParamsConfig.getInt("dynamic_limit", ScheduleUtil.DEFAULT_QUERY_LIMIT);
        int queryLimit2 = applicationApolloConfig.getDynamicLimit();
        Assert.assertEquals(queryLimit1, queryLimit2);
    }

    @Test
    public void getHxBindTaskDelay() {
        long delay1 = apolloParamsConfig.getLong("hx_bind_task_delay", 1000L);
        long delay2 = applicationApolloConfig.getHxBindTaskDelay();
        Assert.assertEquals(delay1, delay2);
    }

    @Test
    public void getDynamicRedis() {
        long keyExpire1 = apolloParamsConfig.getLong("dynamic_redis", ProviderUtil.REDIS_KEY_EXPIRE_SECONDS);
        long keyExpire2 = applicationApolloConfig.getDynamicRedis();
        Assert.assertEquals(keyExpire1, keyExpire2);
    }

    @Test
    public void getChannelGroupV2Flag() {
        boolean channelGroupV2Flag1 = apolloParamsConfig.getBoolean("getChannelGroupV2Flag", true);
        boolean channelGroupV2Flag2 = applicationApolloConfig.getChannelGroupV2Flag();
        Assert.assertEquals(channelGroupV2Flag1, channelGroupV2Flag2);
    }

    @Test
    public void getNingboMapping() {
        boolean ningboMapping1 = apolloParamsConfig.getBoolean("ningbo_mapping", false);
        boolean ningboMapping2 = applicationApolloConfig.getNingboMapping();
        Assert.assertEquals(ningboMapping1, ningboMapping2);
    }

    @Test
    public void getChangeToLklBizCheckPreCreate() {
        boolean aBoolean1 = apolloParamsConfig.getBoolean("ChangeToLklBiz-checkPreCreate", false);
        boolean aBoolean2 = applicationApolloConfig.getChangeToLklBizCheckPreCreate();
        Assert.assertEquals(aBoolean1, aBoolean2);
    }

    @Test
    public void getCheckAliAuth() {
        boolean checkAliAuth1 = apolloParamsConfig.getBoolean("check_ali_Auth", false);
        boolean checkAliAuth2 = applicationApolloConfig.getCheckAliAuth();
        Assert.assertEquals(checkAliAuth1, checkAliAuth2);
    }

    @Test
    public void getAllMerchantLklOld() {
        boolean allMerchantOld1 = apolloParamsConfig.getBoolean("all_merchant_lkl_old", true);
        boolean allMerchantOld2 = applicationApolloConfig.getAllMerchantLklOld();
        Assert.assertEquals(allMerchantOld1, allMerchantOld2);
    }

    @Test
    public void getAllMerchantLklNew() {
        boolean allMerchantNew1 = apolloParamsConfig.getBoolean("all_merchant_lkl_new", false);
        boolean allMerchantNew2 = applicationApolloConfig.getAllMerchantLklNew();
        Assert.assertEquals(allMerchantNew1, allMerchantNew2);
    }

    @Test
    public void getTradeAppSwitch() {
        boolean tradeAppSwitch1 = apolloParamsConfig.getBoolean("tradeApp_switch", Boolean.TRUE);
        boolean tradeAppSwitch2 = applicationApolloConfig.getTradeAppSwitch();
        Assert.assertEquals(tradeAppSwitch1, tradeAppSwitch2);
    }

    @Test
    public void getNingboMicro() {
        boolean ningBoMicro1 = apolloParamsConfig.getBoolean("ningbo-micro", true);
        boolean ningBoMicro2 = applicationApolloConfig.getNingboMicro();
        Assert.assertEquals(ningBoMicro1, ningBoMicro2);
    }

    @Test
    public void getUseSpecialChannel() {
        boolean aBoolean1 = apolloParamsConfig.getBoolean(USE_SPECIAL_CHANNEL, Boolean.TRUE);
        boolean aBoolean2 = applicationApolloConfig.getUseSpecialChannel();
        Assert.assertEquals(aBoolean1, aBoolean2);
    }

    @Test
    public void getSyncFeeRate() {
        boolean syncFeeRate1 = apolloParamsConfig.getBoolean("sync-feerate", true);
        boolean syncFeeRate2 = applicationApolloConfig.getSyncFeeRate();
        Assert.assertEquals(syncFeeRate1, syncFeeRate2);
    }

    @Test
    public void getIsDelayCreateFeeRateTask() {
        boolean isDelayCreateFeeRateTask1 = apolloParamsConfig.getBoolean("isDelayCreateFeeRateTask", true);
        boolean isDelayCreateFeeRateTask2 = applicationApolloConfig.getIsDelayCreateFeeRateTask();
        Assert.assertEquals(isDelayCreateFeeRateTask1, isDelayCreateFeeRateTask2);
    }

    @Test
    public void getHandleSettlementIdAfterChangeWxParam() {
        boolean change1 = apolloParamsConfig.getBoolean("handle-settlementId-after-change-wx-param", true);
        boolean change2 = applicationApolloConfig.getHandleSettlementIdAfterChangeWxParam();
        Assert.assertEquals(change1, change2);
    }

    @Test
    public void getHandleBizLicenseUpdate() {
        boolean isHandle1 = apolloParamsConfig.getBoolean("handleBizLicenseUpdate", false);
        boolean isHandle2 = applicationApolloConfig.getHandleBizLicenseUpdate();
        Assert.assertEquals(isHandle1, isHandle2);
    }

    @Test
    public void getLklV3ShopTermSwitch() {
        boolean switch1 = apolloParamsConfig.getBoolean("lklv3_shop_term_switch", false);
        boolean switch2 = applicationApolloConfig.getLklV3ShopTermSwitch();
        Assert.assertEquals(switch1, switch2);
    }

    @Test
    public void getPublicPayForRule() {
        boolean publicPayForRule1 = apolloParamsConfig.getBoolean("publicPayForRule", false);
        boolean publicPayForRule2 = applicationApolloConfig.getPublicPayForRule();
        Assert.assertEquals(publicPayForRule1, publicPayForRule2);
    }

    @Test
    public void getDirectApplyAliCheckSwitch() {
        boolean aBoolean1 = apolloParamsConfig.getBoolean(AlipayAuthBiz.ALI_SWITCH, false);
        boolean aBoolean2 = applicationApolloConfig.getDirectApplyAliCheckSwitch();
        Assert.assertEquals(aBoolean1, aBoolean2);
    }

    @Test
    public void getLklV3CheckMultiMerchantId() {
        boolean switcher1 = apolloParamsConfig.getBoolean("lklv3_check_multi_merchantId", true);
        boolean switcher2 = applicationApolloConfig.getLklV3CheckMultiMerchantId();
        Assert.assertEquals(switcher1, switcher2);
    }

    @Test
    public void getNewBlueSeaAppId() {
        String appId1 = ApolloConfig.getNewBlueSeaAppId();
        String appId2 = applicationApolloConfig.getNewBlueSeaAppId();
        Assert.assertEquals(appId1, appId2);
    }

    @Test
    public void getNewBlueSeaPrivateKey() {
        String appPrivateKey1 = ApolloConfig.getNewBlueSeaPrivateKey();
        String appPrivateKey2 = applicationApolloConfig.getNewBlueSeaPrivateKey();
        Assert.assertEquals(appPrivateKey1, appPrivateKey2);
    }

    @Test
    public void getNewBlueSeaPublicKey() {
        String alipayPublicKey1 = ApolloConfig.getNewBlueSeaPublicKey();
        String alipayPublicKey2 = applicationApolloConfig.getNewBlueSeaPublicKey();
        Assert.assertEquals(alipayPublicKey1, alipayPublicKey2);
    }

    @Test
    public void getNewBlueSignType() {
        String signType1 = ApolloConfig.getNewBlueSignType();
        String signType2 = applicationApolloConfig.getNewBlueSignType();
        Assert.assertEquals(signType1, signType2);
    }

    @Test
    public void getWechatActivityStatus() {
        final Boolean status1 = ApolloConfig.getWechatActivityStatus();
        final Boolean status2 = applicationApolloConfig.getWechatActivityStatus();
        Assert.assertEquals(status1, status2);
    }

    @Test
    public void getNewBlueSeaErrorMessage() {
        Map<String, Map<String, String>> map1 = ApolloConfig.getList();
        Map<String, Map<String, String>> map2 = applicationApolloConfig.getNewBlueSeaErrorMessage();
        Assert.assertEquals(map1, map2);
    }

    @Test
    public void getBankSupportPayWay() {
        Map<String, List<Integer>> bankSupportPayWay1 = ApolloConfig.getBankSupportPayWay();
        Map<String, List<Integer>> bankSupportPayWay2 = applicationApolloConfig.getBankSupportPayWay();
        Assert.assertEquals(bankSupportPayWay1, bankSupportPayWay2);
    }

    @Test
    public void getBatchThreshold() {
        final Integer batchThreshold1 = ApolloConfig.getBatchThreshold();
        final Integer batchThreshold2 = applicationApolloConfig.getBatchThreshold();
        Assert.assertEquals(batchThreshold1, batchThreshold2);
    }

    @Test
    public void getBankViewProcess() {
        Map bankViewProcess1 = apolloParamsConfig.getMap("bank_view_process", "{}");
        Map<String, List<Map<String, String>>> bankViewProcess2 = applicationApolloConfig.getBankViewProcess();
        Assert.assertEquals(bankViewProcess1, bankViewProcess2);
    }

}