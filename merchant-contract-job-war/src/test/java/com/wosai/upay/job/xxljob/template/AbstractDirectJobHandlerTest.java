package com.wosai.upay.job.xxljob.template;

import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractDirectJobHandlerTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private RLock lock;

    @InjectMocks
    private AbstractDirectJobHandler handler = new AbstractDirectJobHandler() {
        @Override
        public String getLockKey() {
            return "testLockKey";
        }

        @Override
        public void execute(DirectJobParam param) {
            // 无需实现，因为测试中不需要
        }
    };

    @Before
    public void setUp() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
    }

    @Test
    public void handle_SyncExecType_CallsDoHandle() {
        DirectJobParam param = new DirectJobParam();
        param.setExecType(DirectExecTypeEnum.SYNC);

        handler.handle(param);

        verify(lock, times(1)).tryLock();
    }
}
