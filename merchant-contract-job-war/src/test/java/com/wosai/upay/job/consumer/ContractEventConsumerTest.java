package com.wosai.upay.job.consumer;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.avro.AvroEventEntry;
import com.wosai.databus.event.merchant.basic.MerchantBasicEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicStatusChangeEvent;
import com.wosai.databus.event.merchant.basic.MerchantBasicUpdateEvent;
import com.wosai.databus.event.merchant.config.FeeRateEvent;
import com.wosai.databus.event.merchant.config.Field;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import com.wosai.upay.job.util.RecordEventUtil;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.ByteBuffer;

public class ContractEventConsumerTest extends BaseTest {

    private static SerializeConfig serializeConfig = new SerializeConfig();

    static {
        serializeConfig.setPropertyNamingStrategy(PropertyNamingStrategy.SnakeCase);
    }

    @Autowired
    ContractEventConsumer contractEventConsumer;
    @MockBean
    ContractStatusMapper statusMapper;
    @SpyBean
    DataSyncHandler dataSyncHandler;
    @SpyBean
    RedisLock redisLock;
    @MockBean
    SelfHelpNetInEventService selfHelpNetInEventService;
    @MockBean
    RecordEventUtil recordEventUtil;
    @SpyBean
    ApplicationApolloConfig applicationApolloConfig;
    @MockBean
    MerchantService merchantService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(contractEventConsumer, "selfHelpNetInEventService", selfHelpNetInEventService);
        ReflectionTestUtils.setField(contractEventConsumer, "merchantService", merchantService);

    }

    @Test
    public void consume() {
        MerchantBasicEvent event = new MerchantBasicStatusChangeEvent();
        GenericRecord genericRecord = new AvroEventEntry();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event, serializeConfig).getBytes()));
        ConsumerRecord<String, GenericRecord> record = new ConsumerRecord<String, GenericRecord>("单元测试", 1, 1, "key", genericRecord);
        contractEventConsumer.consume(record);
        Mockito.doReturn(new ContractStatus().setStatus(2)).when(statusMapper).selectByMerchantSn(Mockito.anyString());
        Mockito.doReturn(true).when(redisLock).lock(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
        contractEventConsumer.consume(record);
        event = new MerchantBasicUpdateEvent();
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(event, serializeConfig).getBytes()));
        contractEventConsumer.consume(record);
        Mockito.doReturn(Boolean.TRUE).when(applicationApolloConfig).getSyncFeeRate();
        Field field = new Field("0.2", "0.2");
        FeeRateEvent feeRateEvent = new FeeRateEvent()
                .setB2cFeeRate(field).setC2bFeeRate(field).setWapFeeRate(field).setMiniFeeRate(field);
        genericRecord.put("event", ByteBuffer.wrap(JSON.toJSONString(feeRateEvent, serializeConfig).getBytes()));
        Mockito.doReturn(CollectionUtil.hashMap("isNetIn", true)).when(dataSyncHandler)
                .needToSyncMerchantFeerateChange(Mockito.anyInt(), Mockito.anyMap(), Mockito.anyList());
        contractEventConsumer.consume(record);
    }


}
