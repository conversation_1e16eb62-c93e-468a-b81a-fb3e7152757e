package com.wosai.upay.job.providers;


import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.service.NewLakalaService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class LklProviderTest extends BaseTest {

    private static ContractRule rule;

    static {
        rule = new ContractRule();
        rule.setIs_insert_influ_ptask(true);
        rule.setIs_update_influ_ptask(true);
    }

    @Autowired
    LklProvider lklProvider;
    @MockBean
    PayForProvider payForProvider;
    @MockBean
    NewLakalaService newLakalaService;
    @MockBean
    AcquirerService acquirerService;
    @MockBean
    TradeConfigService tradeConfigService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(lklProvider, "newLakalaService", newLakalaService);
        ReflectionTestUtils.setField(lklProvider, "acquirerService", acquirerService);
        ReflectionTestUtils.setField(lklProvider, "tradeConfigService", tradeConfigService);
    }

    @Test
    public void produceInsertTaskByRule() {
        lklProvider.produceInsertTaskByRule("sn", new ContractEvent(), new HashMap<>(), rule);
    }

    @Test
    public void produceUpdateTaskByRule() {
        ContractEvent event = new ContractEvent().setEvent_type(4);
        lklProvider.produceUpdateTaskByRule("sn", event, new HashMap<>(), rule);
        event.setEvent_type(1);
        Map context = new HashMap<>();
        Map bank = CollectionUtil.hashMap("bank", "bank");
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("cardRequestParam", bank);
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(0);
        List<String> msg = Arrays.asList("msg111");
        Map eventContext = CollectionUtil.hashMap("msg", msg);
        event.setEvent_msg(JSON.toJSONString(eventContext));
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(2);
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(9);
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "0");
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "1");
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "2");
        lklProvider.produceUpdateTaskByRule("sn", event, context, rule);
    }

    @Test
    public void processInsertTaskByRule() {
        //不需要代付
        ContractTask task = new ContractTask();
        Map context = new HashMap();
        task.setEvent_context(JSON.toJSONString(context));
        Mockito.doReturn(AcquirerTypeEnum.LKL.getValue()).when(acquirerService).getMerchantAcquirer(Mockito.anyString());
        lklProvider.processInsertTaskByRule(task, new ContractChannel(), new ContractSubTask());
        //需要代付
        Map bank = new HashMap();
        context.put("bankAccount", bank);
        bank.put("type", 1);
        bank.put("id_type", 3);
        task.setEvent_context(JSON.toJSONString(context));
        Mockito.doReturn(AcquirerTypeEnum.TONG_LIAN.getValue()).when(acquirerService).getMerchantAcquirer(Mockito.anyString());
        lklProvider.processInsertTaskByRule(task, new ContractChannel(), new ContractSubTask());
        Mockito.doReturn(new PayForTask()).when(payForProvider).produceTask(Mockito.anyMap(), Mockito.anyObject());
        task.setType(ProviderUtil.CONTRACT_TYPE_INSERT);
        lklProvider.processInsertTaskByRule(task, new ContractChannel(), new ContractSubTask());

    }

    @Test
    public void processUpdateTaskByRule() {
        ContractTask task = new ContractTask();
        task.setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT);
        lklProvider.processUpdateTaskByRule(task, new ContractChannel(), new ContractSubTask());
        ContractSubTask sub = new ContractSubTask();
        task.setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT);
        List<Integer> subTypes = Arrays.asList(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT, ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS
                , ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION, ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE, ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH
                , ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE, ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE);
        subTypes.forEach(r -> {
            sub.setTask_type(r);
            lklProvider.processUpdateTaskByRule(task, new ContractChannel(), sub);
        });
    }
}
