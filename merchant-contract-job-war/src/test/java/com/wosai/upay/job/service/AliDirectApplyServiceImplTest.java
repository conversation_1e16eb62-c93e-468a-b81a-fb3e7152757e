package com.wosai.upay.job.service;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.direct.AliDirectBiz;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.direct.AliAuthorizeCallBack;
import com.wosai.upay.job.model.direct.AliDirectApplyDto;
import com.wosai.upay.job.model.direct.AliDirectReq;
import com.wosai.upay.job.model.direct.ApplyStatusResp;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import static org.junit.Assert.*;

public class AliDirectApplyServiceImplTest extends BaseTest {

    @InjectMocks
    private AliDirectApplyServiceImpl aliDirectApplyService;

    @Mock
    private AliDirectBiz aliDirectBiz;

    @Mock
    private ParamContextBiz paramContextBiz;

    @Mock
    private RedisLock redisLock;

    @Test
    public void applyAliDirectPay() {
        AliDirectReq aliDirectReq = new AliDirectReq();
        aliDirectReq.setMerchant_sn("merchant_sn").setDev_code("dev_code");
        aliDirectReq.setContact_info(new AliDirectReq.Contact_info().setContact_name("name")
                    .setContact_phone("phone").setContact_email("<EMAIL>"));
        aliDirectReq.setApp_info(new AliDirectReq.App_info().setAccount("*********").setAccount_pic("http://www.baidu.com").setFee_rate("0.38"));
        ContractResponse contractResponse = aliDirectApplyService.applyAliDirectPay(aliDirectReq);
        assertTrue(contractResponse.isSuccess());
        Mockito.doThrow(new CommonInvalidParameterException("模拟校验失败")).when(aliDirectBiz).preCheck(aliDirectReq.getMerchant_sn(), aliDirectReq.getDev_code());
        contractResponse = aliDirectApplyService.applyAliDirectPay(aliDirectReq);
        assertFalse(contractResponse.isSuccess());
    }

    @Test
    public void queryApplyStatus() {
        thrown.expect(CommonPubBizException.class);
        aliDirectApplyService.queryApplyStatus("merchant_sn","crm_ap");
    }

    @Test
    public void getApplyStatusByType() {
        ApplyStatusResp statusResp = aliDirectApplyService.getApplyStatusByType("merchant_sn", "crm_app");
        assertNull(statusResp);
    }

    @Test
    public void getDirectApplyByTaskId() {
        AliDirectApplyDto directApplyDto = aliDirectApplyService.getDirectApplyByTaskId(2585386L);
        assertNotNull(directApplyDto);
        assertEquals(directApplyDto.getTask_id().intValue(), 2585386);
    }

    @Test
    public void authorizeMerchant() {
        aliDirectApplyService.authorizeMerchant(new AliAuthorizeCallBack());
    }
}