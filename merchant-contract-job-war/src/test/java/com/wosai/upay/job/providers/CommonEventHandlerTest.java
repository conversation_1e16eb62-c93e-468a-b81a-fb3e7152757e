package com.wosai.upay.job.providers;

import com.alibaba.fastjson.JSON;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.ConstantsEvent;
import com.wosai.upay.job.biz.DataBusBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.service.BankCardServiceImpl;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;


public class CommonEventHandlerTest extends BaseTest {

    @MockBean
    WeixinAuthApplyBiz weixinAuthApplyBiz;
    @Autowired
    CommonEventHandler commonEventHandler;
    @Autowired
    private RuleContext ruleContext;
    @MockBean
    private ContractEventMapper contractEventMapper;
    @MockBean
    private ContractTaskMapper contractTaskMapper;
    @MockBean
    private ContractSubTaskMapper contractSubTaskMapper;
    @MockBean
    private ContractStatusMapper contractStatusMapper;
    @MockBean
    private ParamContextBiz paramContextBiz;
    @MockBean
    private DataBusBiz dataBusBiz;
    @MockBean
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @MockBean
    private BankCardServiceImpl bankCardService;


    @Test
    public void handleEvent() throws Exception {
        Map merchant = CollectionUtil.hashMap("ctime", System.currentTimeMillis());
        Map context = CollectionUtil.hashMap("merchant", merchant);
        Mockito.doReturn(context).when(paramContextBiz).getParamContextByMerchantSn(Mockito.anyString(), Mockito.anyObject());
        ContractEvent event = new ContractEvent();
        event.setVersion(1L).setMerchant_sn("sn").setRule_group_id("default");
        Map eventMsg = new HashMap<>();
        eventMsg.put(ConstantsEvent.EVENT_TYPE_TABLE_NAME, "table_name");
        event.setEvent_msg(JSON.toJSONString(event));
        event.setEvent_type(ContractEvent.OPT_TYPE_NET_IN);
        commonEventHandler.handle(event);
        ContractStatus contractStatus = new ContractStatus();
        contractStatus.setStatus(ContractStatus.STATUS_BIZ_FAIL);
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(Mockito.anyString());
        eventMsg.put(ConstantsEvent.EVENT_TYPE_TABLE_NAME, "merchant_bank_account_pre");
        //黑名单
        event.setEvent_type(ContractEvent.OPT_TYPE_FAIL);
        event.setEvent_msg(JSON.toJSONString(event));
        commonEventHandler.handle(event);
        //crm重新入网 实名重新提交
        event.setEvent_type(ContractEvent.OPT_TYPE_NET_CRM_UPDATE);
        commonEventHandler.handle(event);
        //更新事件处理
        event.setEvent_type(ContractEvent.OPT_TYPE_MERCHANT_FEERATE);
        contractStatus.setStatus(ContractStatus.STATUS_SUCCESS);
        commonEventHandler.handle(event);


        MerchantProviderParams params = new MerchantProviderParams();
        params.setPayway(3).setProvider(1016).setMerchant_sn("sn")
                .setRule_group_id("default").setContract_rule("lkl").setStatus(1);
        Mockito.doReturn(Arrays.asList(params)).when(merchantProviderParamsMapper).selectByExample(Mockito.anyObject());
        commonEventHandler.handle(event);

    }
}
