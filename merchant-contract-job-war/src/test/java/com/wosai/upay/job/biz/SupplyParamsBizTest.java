package com.wosai.upay.job.biz;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.SubBizParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.service.ContractApplicationService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class SupplyParamsBizTest {

    @InjectMocks
    private SupplyParamsBiz supplyParamsBiz;
    @Mock
    private SubBizParamsMapper subBizParamsMapper;
    @Mock
    private ContractStatusMapper contractStatusMapper;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private SubBizParamsBiz subBizParamsBiz;
    @Mock
    private ChangeTradeParamsBiz changeTradeParamsBiz;
    @Mock
    private ContractApplicationService contractApplicationService;

    /**
     * 1.没有非支付业务的拉卡拉多业务多通道参数 && 当前收单机构也不在lkl
     */
    @Test
    public void supplyLklParams01() {
        String merchantSn = "merchantSn";
        Mockito.doReturn("1").when(subBizParamsBiz).getPayTradeAppId();
        Mockito.doReturn(new ArrayList<>()).when(subBizParamsMapper).selectByExample(any());
        Mockito.doReturn(new ContractStatus().setAcquirer("ccb")).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        boolean needDispatchWorkers = supplyParamsBiz.supplyLklParams(merchantSn);
        Assert.assertFalse(needDispatchWorkers);

        Mockito.verify(merchantProviderParamsMapper, Mockito.times(0)).selectByExampleWithBLOBs(any());
    }

    /**
     * 2.有非支付业务的拉卡拉多业务多通道参数 && 当前收单机构不在lkl
     * 不需要重新报备
     */
    @Test
    public void supplyLklParams02() {
        String merchantSn = "merchantSn";
        Mockito.doReturn("1").when(subBizParamsBiz).getPayTradeAppId();
        SubBizParams subBizParams = new SubBizParams()
                .setTrade_app_id("2")
                .setProvider(ProviderEnum.PROVIDER_LAKALA_V3.getValue())
                .setExtra("{\"1032\":[\"fd2b6569-c6c9-450e-ba6b-3eb36666c296\",\"86a616fa-709b-47c1-bf62-bdb3bf700896\"]}");
        Mockito.doReturn(Collections.singletonList(subBizParams)).when(subBizParamsMapper).selectByExample(any());
        Mockito.doReturn(new ContractStatus().setAcquirer("ccb")).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        Mockito.doReturn(new MerchantProviderParams().setId("fd2b6569-c6c9-450e-ba6b-3eb36666c296").setPayway(2).setDeleted(true)).when(merchantProviderParamsMapper).selectByPrimaryKey("fd2b6569-c6c9-450e-ba6b-3eb36666c296");
        Mockito.doReturn(new MerchantProviderParams().setId("86a616fa-709b-47c1-bf62-bdb3bf700896").setPayway(3).setDeleted(true)).when(merchantProviderParamsMapper).selectByPrimaryKey("86a616fa-709b-47c1-bf62-bdb3bf700896");
        Mockito.doReturn(Collections.singletonList(new MerchantProviderParams().setAuth_status(1))).when(merchantProviderParamsMapper).selectByExampleWithBLOBs(any());
        boolean needDispatchWorkers = supplyParamsBiz.supplyLklParams(merchantSn);
        Assert.assertFalse(needDispatchWorkers);

        Mockito.verify(merchantProviderParamsMapper, Mockito.times(0)).getUseWeiXinParam(merchantSn);
        Mockito.verify(contractApplicationService, Mockito.times(0)).contractByRule(any());
    }

    /**
     * 2.没有有非支付业务的拉卡拉多业务多通道参数 && 当前收单机构在lkl
     * 不需要重新报备
     */
    @Test
    public void supplyLklParams03() {
        String merchantSn = "merchantSn";
        Mockito.doReturn("1").when(subBizParamsBiz).getPayTradeAppId();

        Mockito.doReturn(new ArrayList<>()).when(subBizParamsMapper).selectByExample(any());
        Mockito.doReturn(new ContractStatus().setAcquirer("lklV3")).when(contractStatusMapper).selectByMerchantSn(merchantSn);
        MerchantProviderParams param = new MerchantProviderParams().setAuth_status(1);
        Mockito.doReturn(Collections.singletonList(param)).when(merchantProviderParamsMapper).selectByExampleWithBLOBs(any());
        boolean needDispatchWorkers = supplyParamsBiz.supplyLklParams(merchantSn);
        Assert.assertFalse(needDispatchWorkers);

        Mockito.verify(merchantProviderParamsMapper, Mockito.times(1)).getUseWeiXinParam(merchantSn);
        Mockito.verify(merchantProviderParamsMapper, Mockito.times(1)).getUseAlipayParam(merchantSn);
        Mockito.verify(changeTradeParamsBiz, Mockito.times(2)).changeTradeParams(param, null, false, "1");
        Mockito.verify(contractApplicationService, Mockito.times(0)).contractByRule(any());
    }
}