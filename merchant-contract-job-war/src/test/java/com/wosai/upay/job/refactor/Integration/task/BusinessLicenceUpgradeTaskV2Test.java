package com.wosai.upay.job.refactor.Integration.task;

import com.alibaba.fastjson.JSON;

import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.utils.object.DateExtensionUtils;
import com.wosai.data.util.CollectionUtil;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;

import com.wosai.upay.job.biz.AopBiz;
import com.wosai.upay.job.model.dto.crm.CrmFormFieldInfoDTO;
import com.wosai.upay.job.model.dto.crm.CrmInformationManagementApplyFormDTO;
import com.wosai.upay.job.model.dto.request.LicenseUpdateAmountVerifyReqDTO;
import com.wosai.upay.job.model.dto.response.LicenseUpdateAmountVerifyResDTO;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.dao.InternalScheduleMainTaskDAO;
import com.wosai.upay.job.refactor.dao.InternalScheduleSubTaskDAO;
import com.wosai.upay.job.refactor.model.bo.MerchantBankAccountBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.enums.PersonalCertificateTypeEnum;
import com.wosai.upay.job.refactor.task.license.entity.BankAccountCertificateBO;
import com.wosai.upay.job.refactor.task.license.entity.BankAccountDTO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseDTO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.model.enums.crm.CrmApplyFormAuditStatusEnum;
import com.wosai.upay.job.refactor.service.impl.task.BusinessLicenceTaskServiceImpl;

import com.wosai.upay.job.refactor.service.rpc.risk.req.RiskEntryResult;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.account.BankAccountVerifyTaskHandler;
import com.wosai.upay.job.refactor.task.license.account.ChangeAccountWithLicenseUpdate;
import com.wosai.upay.job.refactor.task.license.crm.CrmLicenseApplyUpdate;
import com.wosai.upay.job.refactor.task.license.update.UpdateBusinessNameTaskHandler;
import com.wosai.upay.job.refactor.utils.DtoConverter;
import com.wosai.upay.job.refactor.utils.TimeExpirationUtil;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;



/**
 * 收单机构测试
 *
 * <AUTHOR>
 */
@Slf4j
public class BusinessLicenceUpgradeTaskV2Test extends BaseTest {

    @Resource
    private BusinessLicenceTaskServiceImpl businessLicenceTaskService;

    @Resource
    private CrmLicenseApplyUpdate crmLicenseApplyUpdate;

    @Resource
    private BusinessLicenceCertificationV2Task businessLicenceCertificationV2Task;


    @Test
    public void testUpdateLicenseApplyFail() {
        Map<String, String> msgMap = new HashMap<>();
        msgMap.put(RiskEntryResult.APP, "认证失败-app");
        msgMap.put(RiskEntryResult.CRM, "认证失败-crm");
        msgMap.put(RiskEntryResult.SPA, "认证失败-spa");
        crmLicenseApplyUpdate.updateCrmLicenseApplyStatus(36482,
                CrmApplyFormAuditStatusEnum.AUDIT_FAIL.getValue(),
                JSON.toJSONString(msgMap));

    }

    @Test
    public void testGetCrmApply() {
        int id = 37356;
        Map<String, Object> fieldAppInfoById = crmLicenseApplyUpdate.getFieldAppInfoById(id);
    }

    @Test
    public void testUpdateLicenseApplySuccess() {
        crmLicenseApplyUpdate.updateCrmLicenseApplyStatus(36623,
                CrmApplyFormAuditStatusEnum.AUDIT_SUCCESS.getValue(), null);

    }

    @Autowired
    private MerchantBankService merchantBankService;

    @Test
    public void testGetAccountPre() {
        String merchantId = "05ededbb4fce-d5eb-7e11-bfd0-bcca2f81";
        Map existedBankAccount = merchantBankService
                .getMerchantBankAccountPreByMerchantIdAndNumber("05ededbb4fce-d5eb-7e11-bfd0-bcca2f81", "621224948470109516");
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId));
        log.info("extra type : {}", existedBankAccount.get("extra").getClass().getTypeName());
    }


    @Test
    public void testGetFieldAppInfoById() {
        crmLicenseApplyUpdate.getFieldAppInfoById(36621);
    }


    @Test
    public void testSubmitTask() {
        String request = "{\n" +
                "    \"merchant_id\": \"628a46c6-385a-418e-be54-664184187c10\",\n" +
                "    \"dev_code\": \"\",\n" +
                "    \"user_id\": \"3685adda-0c78-4236-b6f0-cbc2477b6789\",\n" +
                "    \"organization_id\": \"\",\n" +
                "    \"platform\": \"crm_app\",\n" +
                "    \"field_app_info_id\": 37718,\n" +
                "    \"scene\": \"business_license2\",\n" +
                "    \"dev_param\": {\n" +
                "      \"field_list\": [\n" +
                "        {\n" +
                "          \"field_key\": \"type\",\n" +
                "          \"field_code\": \"XATGHRBMHQZXWZG\",\n" +
                "          \"field_value\": \"1\",\n" +
                "          \"field_name\": \"营业执照类型\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"photo\",\n" +
                "          \"field_code\": \"TOUDHHGVZEDLODT\",\n" +
                "          \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "          \"field_name\": \"营业执照\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"number\",\n" +
                "          \"field_code\": \"QHKIKNJXXHMCRFM\",\n" +
                "          \"field_value\": \"92BJNMMJUPU5XWTM1J\",\n" +
                "          \"field_name\": \"统一社会信用代码\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"name\",\n" +
                "          \"field_code\": \"SSKMHPFFFQVMDOA\",\n" +
                "          \"field_value\": \"商户个体工商户营业执照\",\n" +
                "          \"field_name\": \"营业执照名称\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_name\",\n" +
                "          \"field_code\": \"UXFUUDCHXBSKTHH\",\n" +
                "          \"field_value\": \"荣帆\",\n" +
                "          \"field_name\": \"姓名\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"address\",\n" +
                "          \"field_code\": \"TCDAKWGUNAYJQJD\",\n" +
                "          \"field_value\": \"商户个体工商户营业执照注册地址\",\n" +
                "          \"field_name\": \"注册地址\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"validity\",\n" +
                "          \"field_code\": \"YHZFCJBQMONUERR\",\n" +
                "          \"field_value\": \"20180304-20150304\",\n" +
                "          \"field_name\": \"有效期\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_type\",\n" +
                "          \"field_code\": \"IKXMMLWJOLOLZVR\",\n" +
                "          \"field_value\": \"\",\n" +
                "          \"field_name\": \"证件类型\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_number\",\n" +
                "          \"field_code\": \"UUXSBAQZVZCHDFQ\",\n" +
                "          \"field_value\": \"371722196503070413\",\n" +
                "          \"field_name\": \"证件号码\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_card_front_photo\",\n" +
                "          \"field_code\": \"MTPHLKXOAIIBVVX\",\n" +
                "          \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "          \"field_name\": \"证件正面照\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_card_back_photo\",\n" +
                "          \"field_code\": \"EXOKFKNUIGCGTEV\",\n" +
                "          \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "          \"field_name\": \"证件反面照\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"id_validity\",\n" +
                "          \"field_code\": \"CPLZGRRUJUOIVGW\",\n" +
                "          \"field_value\": \"20180304-20150304\",\n" +
                "          \"field_name\": \"有效期\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"business_name\",\n" +
                "          \"field_code\": \"FLCVVQJMLYULTVA\",\n" +
                "          \"field_value\": \"商户0304zurr2\",\n" +
                "          \"field_name\": \"商户经营名称\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"registered_legal_person_name\",\n" +
                "          \"field_code\": \"FAZMQGWEDWTVQMM\",\n" +
                "          \"field_value\": \"孙伟东\",\n" +
                "          \"field_name\": \"法人姓名\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"auxiliary_proof_materials\",\n" +
                "          \"field_code\": \"KJOWJTGNMJBXJLT\",\n" +
                "          \"field_value\": \"\",\n" +
                "          \"field_name\": \"身份辅助证明\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_card_address\",\n" +
                "          \"field_code\": \"YFUOQLVUSGKYAFN\",\n" +
                "          \"field_value\": \"\",\n" +
                "          \"field_name\": \"法人证件地址\",\n" +
                "          \"field_main_type\": 0\n" +
                "        },\n" +
                "        {\n" +
                "          \"field_key\": \"legal_person_id_card_issuing_authority\",\n" +
                "          \"field_code\": \"QDUAXWYFADHONQI\",\n" +
                "          \"field_value\": \"\",\n" +
                "          \"field_name\": \"法人证件签发机关\",\n" +
                "          \"field_main_type\": 0\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  }";
        CrmInformationManagementApplyFormDTO dto = JSON.parseObject(request, CrmInformationManagementApplyFormDTO.class);
        List<CrmFormFieldInfoDTO> crmFormFieldInfoDTOS = dto.extractCrmFormFieldInfoDTOsFromDevParams();
        BusinessLicenseDTO businessLicenseDTO = DtoConverter.convertToDTOBySourceKeyValueList(crmFormFieldInfoDTOS, BusinessLicenseDTO.class);
        businessLicenceCertificationV2Task.insertTask(dto);
    }


    @Resource
    private AcquirerService acquirerService;

    @Test
    public void testChangeAcquirer() {
        acquirerService.applyChangeAcquirer("**************", "lklV3", true);
    }

    private BusinessLicenseAuditApplyDTO getBusinessLicenseAuditApplyDTO(String merchantSn, Integer fieldAppInfoId) {
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = new BusinessLicenseAuditApplyDTO();
        BusinessLicenseDTO businessLicenseDTO = new BusinessLicenseDTO();
        businessLicenseDTO.setBusinessName("test-001");
        businessLicenseDTO.setType(1);
        businessLicenseDTO.setLicensePhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        businessLicenseDTO.setNumber("92PP0JJUKXNQGTXMJT");
        businessLicenseDTO.setName("test商户营业执照");
        businessLicenseDTO.setLegalPersonName("花璐");
        businessLicenseDTO.setRegisterAddress("商户个体工商户营业执照注册地址");
        businessLicenseDTO.setValidity("********-********");
        businessLicenseDTO.setLegalPersonCertificateType(1);
        businessLicenseDTO.setLegalPersonCertificateFrontPhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        businessLicenseDTO.setLegalPersonCertificateBackPhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        businessLicenseDTO.setLegalPersonCertificateNumber("******************");
        businessLicenseDTO.setLegalPersonCertificateValidity("********-********");
        BankAccountDTO bankAccountDTO = new BankAccountDTO();
        bankAccountDTO.setSettlementAccountType(1);
        bankAccountDTO.setBankCardPhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        bankAccountDTO.setHolder("花璐");
        bankAccountDTO.setAccountNumber("****************");
        bankAccountDTO.setOpeningBank("平安银行");
        bankAccountDTO.setOpeningCity("苏州市");
        bankAccountDTO.setOpeningBranch("平安银行昆明东风支行");
        bankAccountDTO.setCardValidity("04/2030");
        bankAccountDTO.setCertificateType(1);
        bankAccountDTO.setCertificateFrontPhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        bankAccountDTO.setCertificateBackPhoto("https://private-images.shouqianba.com/d1/a1b62b89996817e87ed427c033707266e38350.jpeg");
        bankAccountDTO.setCertificateNumber("******************");
        bankAccountDTO.setCertificateValidity("********-********");
        bankAccountDTO.setCertificateName("test-花璐");
        businessLicenseAuditApplyDTO.setBusinessLicense(businessLicenseDTO);
        businessLicenseAuditApplyDTO.setBankAccount(bankAccountDTO);
        businessLicenseAuditApplyDTO.setSubmitUserId("7641bcad-c733-4255-af33-8bd7dc73d2db");
        businessLicenseAuditApplyDTO.setFieldAppInfoId(fieldAppInfoId);
        businessLicenseAuditApplyDTO.setPlatform("CRM");
        return businessLicenseAuditApplyDTO;
    }


    @Test
    public void testDoInsertTaskByLicenseAuditApply() {
        String merchantSn = "**************";
        Integer fieldAppInfoId = 36552;
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = getBusinessLicenseAuditApplyDTO(merchantSn, fieldAppInfoId);
        businessLicenceCertificationV2Task.doInsertTaskByLicenseAuditApply(merchantSn, fieldAppInfoId, businessLicenseAuditApplyDTO);
    }

    @Autowired
    private com.wosai.mc.service.MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    private com.wosai.upay.core.service.MerchantBusinessLicenseService coreBMerchantBusinessLicenseService;

    @Test
    public void testRemoveBusinessLicenseVerifyMark() {
        String merchantId = "e33a08e0-4271-411b-b033-28d8a9f94a91";
        MerchantBusinessLicenseInfo licenseInfo = merchantBusinessLicenseService.getMerchantBusinessLicenseByMerchantId(merchantId, null);
        if (Objects.isNull(licenseInfo)) {
            return;
        }
        Map extra = licenseInfo.getExtra();
        if (MapUtils.isEmpty(extra)) {
            return;
        }
        boolean existMark = extra.containsKey("business_license_verify_v2");
        if (existMark) {
            HashMap<String, Object> map = Maps.newHashMap();
            map.put("id", licenseInfo.getId());
            map.put("merchant_id", licenseInfo.getMerchant_id());
            extra.remove("business_license_verify_v2");
            map.put("extra", extra);
            coreBMerchantBusinessLicenseService.updateMerchantBusinessLicense(map);
        }
    }

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Autowired
    private MerchantService merchantService;

    @Test
    public void testInsertMicroUpgrade() {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("id", ********);
        map.put("merchant_id", "50fc8e1f-c418-4c26-959b-756a2dd3dcaa");
        map.put("type", 0);
        coreBMerchantBusinessLicenseService.updateMerchantBusinessLicense(map);
        Map account = merchantService.getMerchantBankAccountByMerchantId("50fc8e1f-c418-4c26-959b-756a2dd3dcaa");
        String merchantSn = "**************";
        Integer fieldAppInfoId = 36552;
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = getBusinessLicenseAuditApplyDTO(merchantSn, fieldAppInfoId);
        businessLicenceCertificationV2Task.doInsertTaskByLicenseAuditApply(merchantSn, fieldAppInfoId, businessLicenseAuditApplyDTO);
    }

    @Test
    public void testDoTas1k() {
        businessLicenceCertificationV2Task.batchHandleTasksByMainTaskIds(Lists.newArrayList(9121L));
    }


    @Test
    public void testDoTask() {
        String merchantSn = "**************";
        List<InternalScheduleMainTaskDO> internalScheduleMainTaskDOS = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2.getValue());
        Optional<InternalScheduleMainTaskDO> maxIdInternalScheduleMainTaskDO = internalScheduleMainTaskDOS.stream().max(Comparator.comparing(InternalScheduleMainTaskDO::getId));
        Long id = maxIdInternalScheduleMainTaskDO.get().getId();
        businessLicenceCertificationV2Task.batchHandleTasksByMainTaskIds(Lists.newArrayList(id));
    }



    @Test
    public void testLicenseUpdate() {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("id", ********);
        map.put("merchant_id", "50fc8e1f-c418-4c26-959b-756a2dd3dcaa");
        map.put("type", 1);
        coreBMerchantBusinessLicenseService.updateMerchantBusinessLicense(map);
        String merchantSn = "**************";
        Integer fieldAppInfoId = 36552;
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = getBusinessLicenseAuditApplyDTO(merchantSn, fieldAppInfoId);
        businessLicenceCertificationV2Task.doInsertTaskByLicenseAuditApply(merchantSn, fieldAppInfoId, businessLicenseAuditApplyDTO);
    }

    @Test
    public void testUpdateBankAccounts() {
    }


    @Test
    public void testUpdateBusinessNameInLicenseCertificate() {

    }

    @Resource
    private ChangeAccountWithLicenseUpdate changeAccountWithLicenseUpdate;

    @Test
    public void testCoreBUpdateAccount() {
        String bankAccount = "{\n" +
                "      \"bank_card_image\": \"https://private-images.shouqianba.com/d5/151b2bada491b59d586c32285f0d2834c61385.jpg\",\n" +
                "      \"bank_name\": \"平安银行221\",\n" +
                "      \"branch_name\": \"平安银行北京十里河支行\",\n" +
                "      \"card_validity\": \"12/2025\",\n" +
                "      \"city\": \"北京市 北京市\",\n" +
                "      \"corporate_account_certificate_photo\": \"https://private-images.shouqianba.com/e1/13a51a4437b3796ec115bfa868cac6654015f8.jpg\",\n" +
                "      \"extraMap\": {\n" +
                "        \"settlement_account_type\": 1,\n" +
                "        \"corporate_account_certificate_photo\": \"https://private-images.shouqianba.com/e1/13a51a4437b3796ec115bfa868cac6654015f8.jpg\"\n" +
                "      },\n" +
                "      \"holder\": \"梅帅2\",\n" +
                "      \"number\": \"6230583000011065218\",\n" +
                "      \"opening_number\": \"************\",\n" +
                "      \"settlement_account_type\": 1\n" +
                "    }";
        String merchantId = "a1d43507-20e9-4b4d-a386-02e569922c33";
        Map<String, Object> stringObjectMap = JSON.parseObject(bankAccount, new TypeReference<Map<String, Object>>() {
        });
        changeAccountWithLicenseUpdate.updateDefaultAccount(merchantId, stringObjectMap,
                2, "927d343c-ae82-434b-9074-233ce8e9cf50", "test", "msp");
    }

    @Resource
    private UpdateBusinessNameTaskHandler updateBusinessNameTaskHandler;


    @Test
    public void testUpdateBusinessName() {
        String merchantSn = "50fc8e1f-c418-4c26-959b-756a2dd3dcaa";
        String newBusinessName = "test-001";
        updateBusinessNameTaskHandler.handleUpdateBusinessNameUpdate(merchantSn, newBusinessName);
    }

    @Test
    public void testSyncCrmLicenseApplyAccountVerifyStatus() {
        BankAccountVerifyTaskHandler.AccountVerifyRecord accountVerifyRecord = new BankAccountVerifyTaskHandler.
                AccountVerifyRecord("816696154149355520", 2);
        crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(37613, accountVerifyRecord);
    }


    // verifyAmountForAppUpdateLicense
    @Test
    public void testVerifyAmountForAppUpdateLicense() {
        LicenseUpdateAmountVerifyReqDTO verifyReqDTO = new LicenseUpdateAmountVerifyReqDTO();
        verifyReqDTO.setMerchantId("a6de420e-ad98-4cb4-a3d1-6677c0712a58");
        verifyReqDTO.setBusinessId("820256210732187648");
        verifyReqDTO.setAmount(new BigDecimal(12));
        LicenseUpdateAmountVerifyResDTO verifyAmountForAppUpdateLicense = businessLicenceTaskService.verifyAmountForAppUpdateLicense(verifyReqDTO);

    }


    @Test
    public void testCheckSubmitBusinessLicenseApplyToContractSideAudit() {
        String input = "{\n" +
                "      \"merchant_id\": \"bbcbe056-b8e3-4a35-8ab4-0254144ae17b\",\n" +
                "      \"dev_code\": \"\",\n" +
                "      \"user_id\": \"e1f1aa4f-f25b-44c0-92af-1432508e1ca4\",\n" +
                "      \"organization_id\": \"\",\n" +
                "      \"platform\": \"app\",\n" +
                "      \"field_app_info_id\": 38582,\n" +
                "      \"scene\": \"business_license2\",\n" +
                "      \"dev_param\": {\n" +
                "        \"field_list\": [\n" +
                "          {\n" +
                "            \"field_key\": \"type\",\n" +
                "            \"field_code\": \"XATGHRBMHQZXWZG\",\n" +
                "            \"field_value\": 1,\n" +
                "            \"field_name\": \"营业执照类型\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"photo\",\n" +
                "            \"field_code\": \"TOUDHHGVZEDLODT\",\n" +
                "            \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "            \"field_name\": \"营业执照\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"number\",\n" +
                "            \"field_code\": \"QHKIKNJXXHMCRFM\",\n" +
                "            \"field_value\": \"929B11TU6CMEE900WJ\",\n" +
                "            \"field_name\": \"统一社会信用代码\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"name\",\n" +
                "            \"field_code\": \"SSKMHPFFFQVMDOA\",\n" +
                "            \"field_value\": \"商户个体工商户营业执照\",\n" +
                "            \"field_name\": \"营业执照名称\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_name\",\n" +
                "            \"field_code\": \"UXFUUDCHXBSKTHH\",\n" +
                "            \"field_value\": \"淳霞\",\n" +
                "            \"field_name\": \"姓名\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"address\",\n" +
                "            \"field_code\": \"TCDAKWGUNAYJQJD\",\n" +
                "            \"field_value\": \"商户个体工商户营业执照注册地址\",\n" +
                "            \"field_name\": \"注册地址\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"validity\",\n" +
                "            \"field_code\": \"YHZFCJBQMONUERR\",\n" +
                "            \"field_value\": \"20220927-99991231\",\n" +
                "            \"field_name\": \"有效期\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_type\",\n" +
                "            \"field_code\": \"IKXMMLWJOLOLZVR\",\n" +
                "            \"field_value\": 1,\n" +
                "            \"field_name\": \"证件类型\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_number\",\n" +
                "            \"field_code\": \"UUXSBAQZVZCHDFQ\",\n" +
                "            \"field_value\": \"611025198211095248\",\n" +
                "            \"field_name\": \"证件号码\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_card_front_photo\",\n" +
                "            \"field_code\": \"MTPHLKXOAIIBVVX\",\n" +
                "            \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "            \"field_name\": \"证件正面照\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_card_back_photo\",\n" +
                "            \"field_code\": \"EXOKFKNUIGCGTEV\",\n" +
                "            \"field_value\": \"https://images.wosaimg.com/14/0fb51c9815c5abc0bde9a8f11407e1.jpg\",\n" +
                "            \"field_name\": \"证件反面照\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"id_validity\",\n" +
                "            \"field_code\": \"CPLZGRRUJUOIVGW\",\n" +
                "            \"field_value\": \"20221120-20271120\",\n" +
                "            \"field_name\": \"有效期\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"registered_legal_person_name\",\n" +
                "            \"field_code\": \"FAZMQGWEDWTVQMM\",\n" +
                "            \"field_value\": \"淳霞\",\n" +
                "            \"field_name\": \"法人姓名\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"auxiliary_proof_materials\",\n" +
                "            \"field_code\": \"KJOWJTGNMJBXJLT\",\n" +
                "            \"field_value\": \"\",\n" +
                "            \"field_name\": \"身份辅助证明\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_card_address\",\n" +
                "            \"field_code\": \"YFUOQLVUSGKYAFN\",\n" +
                "            \"field_value\": \"\",\n" +
                "            \"field_name\": \"法人证件地址\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"legal_person_id_card_issuing_authority\",\n" +
                "            \"field_code\": \"QDUAXWYFADHONQI\",\n" +
                "            \"field_value\": \"\",\n" +
                "            \"field_name\": \"法人证件签发机关\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"business_name\",\n" +
                "            \"field_code\": \"XCMQUILGEGCWXEC\",\n" +
                "            \"field_value\": \"商户0303fzcm\",\n" +
                "            \"field_name\": \"商户经营名称\",\n" +
                "            \"field_main_type\": 0\n" +
                "          },\n" +
                "          {\n" +
                "            \"field_key\": \"remark\",\n" +
                "            \"field_code\": \"JSBUTDYHVGZHNMA\",\n" +
                "            \"field_value\": \"\",\n" +
                "            \"field_name\": \"备注\",\n" +
                "            \"field_main_type\": 0\n" +
                "          }\n" +
                "        ]\n" +
                "      }\n" +
                "    }";
        CrmInformationManagementApplyFormDTO apply = JSON.parseObject(input, CrmInformationManagementApplyFormDTO.class);
        businessLicenceTaskService.checkSubmitBusinessLicenseApplyToContractSideAudit(apply);
    }


    @Test
    public void testUpdateBankAccount() {
        String merchantId = "e33a08e0-4271-411b-b033-28d8a9f94a91";
        changeAccountWithLicenseUpdate.updateDefaultAccountVerifyStatus(merchantId, MerchantBankAccount.VERIFY_STATUS_FAIL, "system", "system", "APP");
    }

    @Autowired
    private CommonFieldService commonFieldService;

    // 40994
    @Test
    public void testGetApplyFromCrm() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(100);
        pageInfo.setPage(1);
        Map<String, Object> queryFilterMap = Maps.newHashMap();
        queryFilterMap.put("id", 41007);
        queryFilterMap.put("field_type", "business_license2");
        ListResult fieldAppInfos = commonFieldService.findFieldAppInfos(pageInfo, queryFilterMap);
        if (Objects.isNull(fieldAppInfos)
                || CollectionUtils.isEmpty(fieldAppInfos.getRecords())
                || MapUtils.isEmpty(fieldAppInfos.getRecords().get(0))) {
            return;
        }
        Map fieldAppInfo = fieldAppInfos.getRecords().get(0);
    }

    @Test
    public void isExistedLicenseUpdateV2ChangeAccountApply() {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(100);
        pageInfo.setPage(1);
        Map<String, Object> queryFilterMap = Maps.newHashMap();
        queryFilterMap.put("merchant_id", "ac4e5806-65b3-4a2e-b0a8-41c1009b7602");
        queryFilterMap.put("field_type", "business_license2");
        ListResult fieldAppInfos = commonFieldService.findFieldAppInfos(pageInfo, queryFilterMap);
        if (Objects.isNull(fieldAppInfos)
                || CollectionUtils.isEmpty(fieldAppInfos.getRecords())
                || MapUtils.isEmpty(fieldAppInfos.getRecords().get(0))) {
            return;
        }
        Map fieldAppInfo = fieldAppInfos.getRecords().get(0);
        Map businessAppInfo = MapUtils.getMap(fieldAppInfo, "business_app_info", Maps.newHashMap());

        // businessAppInfo是否存在key  bankAccount 存在 返回true

    }


    @Test
    public void forceProcessTask() {
        Optional<InternalScheduleMainTaskDO> taskOpt = internalScheduleMainTaskDAO.getByPrimaryKey(9461);
        if (taskOpt.isPresent()) {
            InternalScheduleMainTaskDO internalScheduleMainTaskDO = taskOpt.get();
            internalScheduleMainTaskDO.setStatus(2);
            businessLicenceCertificationV2Task.batchHandleMainTasks(Lists.newArrayList(internalScheduleMainTaskDO));
        }
    }

    @Resource
    private InternalScheduleSubTaskDAO internalScheduleSubTaskDAO;

    @Test
    public void processSingleTask() {
        Optional<InternalScheduleMainTaskDO> taskOpt = internalScheduleMainTaskDAO.getByPrimaryKey(9718);
        if (taskOpt.isPresent()) {
            InternalScheduleMainTaskDO internalScheduleMainTaskDO = taskOpt.get();
            internalScheduleMainTaskDO.setStatus(2);
            businessLicenceCertificationV2Task.batchHandleMainTasks(Lists.newArrayList(internalScheduleMainTaskDO));
        }
    }

    @Test
    public void testScheduledTask() {
        businessLicenceCertificationV2Task.batchHandleTasksInSequence(3);
    }

    @Test
    public void testExpireTime() throws ParseException {
        Timestamp timestamp = DateExtensionUtils.parseTimestamp("2025-03-02");
        Assert.assertTrue(TimeExpirationUtil.isTimeExpired(timestamp, 2));
        Timestamp timestamp1 = DateExtensionUtils.parseTimestamp("2025-03-06");
        Assert.assertTrue(!TimeExpirationUtil.isTimeExpired(timestamp1, 5));
    }

    @Test
    public void testIsLicenseUpdateExistBusinessNameChange() {
        Boolean licenseUpdateExistBusinessNameChange = businessLicenceTaskService.isLicenseUpdateExistSuccessBusinessNameChange("**************");
        Assert.assertTrue(licenseUpdateExistBusinessNameChange);
    }


    @Test
    public void testSerializer() {
        MerchantBankAccountBO merchantBankAccountBO = new MerchantBankAccountBO();
        merchantBankAccountBO.setId("*********");
        Map map = merchantBankAccountBO.toMap();
    }

    @Resource
    private BankAccountVerifyTaskHandler bankAccountVerifyTaskHandler;

    @Test
    public void notifyMerchantToVerifyAmount() {
        String merchantSn = "**************";
        bankAccountVerifyTaskHandler.sendNoticeVerifyAmount(merchantSn);
    }

    @Test
    public void testChangeAcquirerWithoutUpdateCard() {
        ThreadLocalUtil.setAcquirerChangeSkipSyncBankAccount(true);
        acquirerService.applyChangeAcquirer("**************", "lklV3", true);
    }

    @Test
    public void testSyncBankAccountCertificateUnnecessaryInfo() {
        String merchantId = "d97c3855-9ba8-494a-accf-154ad1ac135b";
        BankAccountCertificateBO bankAccountCertificateBO = new BankAccountCertificateBO();
        bankAccountCertificateBO.setCertificateType(PersonalCertificateTypeEnum.ID_CARD.getValue());
        bankAccountCertificateBO.setCertificateNum("******************");
        bankAccountCertificateBO.setCertificateName("test-888888");
        bankAccountCertificateBO.setCertificateAddress("湖南省郴州市-888888");
        bankAccountCertificateBO.setCertificateValidity("********-********");
        bankAccountCertificateBO.setCertificateIssuingAuthority("湖南省郴州市公安局-888888");
        bankAccountCertificateBO.setCertificateFrontPhoto("https://private-images.shouqianba.com/28/1ba22699140096b2ef5948bad1d7f141c9972c.jpg");
        bankAccountCertificateBO.setCertificateBackPhoto("https://private-images.shouqianba.com/28/1ba22699140096b2ef5948bad1d7f141c9972c.jpg");
        changeAccountWithLicenseUpdate.syncBankAccountCertificateUnnecessaryInfo(merchantId, bankAccountCertificateBO, "MSP");
    }

}
