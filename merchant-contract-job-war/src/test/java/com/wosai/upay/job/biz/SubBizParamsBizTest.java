package com.wosai.upay.job.biz;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.ValidStatusEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.crmdatabus.merchantBusinessOpen.appInfo.DataBusAppInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.TradeAppService;
import com.wosai.trade.service.request.TradeAppQueryRequest;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.trade.service.result.ListResult;
import com.wosai.trade.service.result.TradeAppResult;
import com.wosai.upay.common.exception.CommonPubBizException;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.acquirer.AbstractAcquirerChangeBiz;
import com.wosai.upay.job.handlers.CommonEventHandler;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.SubBizParamsMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.model.subBizParams.SubBizConfig;
import com.wosai.upay.job.model.subBizParams.SubBizParams;
import com.wosai.upay.job.providers.DefaultChangeTradeParamsBiz;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.McContractRuleDAO;
import com.wosai.upay.job.refactor.model.entity.McAcquirerDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.service.AcquirerService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;

import java.util.*;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SubBizParamsBizTest {

    @InjectMocks
    private SubBizParamsBiz subBizParamsBiz;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Mock
    private MerchantService merchantService;

    @Mock
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private McAcquirerDAO acquirerDAO;

    @Mock
    private AcquirerService acquirerService;

    @Mock
    private ChangeTradeParamsBiz tradeParamsBiz;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private TradeAppService tradeAppService;

    @Mock
    private FeeRateService feeRateService;

    @Mock
    private OnlinePaymentBiz onlinePaymentBiz;

    @Mock
    private SubBizParamsMapper subBizParamsMapper;

    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private RedisLock redisLock;

    @Mock
    MerchantProviderParamsMapper merchantProviderParamsMapper;


    public static final String info = "{\"5c8e9f62-25c4-42fa-8d03-893b57c7fa87\":{\"tradeName\":\"储值\",\"mappingTradeName\":\"储值\",\"mappingTradeAppId\":\"8\",\"mappingComboId\":\"5703\",\"feeRate\":{\"2\":\"0.38\",\"3\":\"0.38\",\"17\":\"0.38\"}},\"3765750f-4ad9-4b80-a6ab-c5562d712a38\":{\"tradeName\":\"自营外卖\",\"mappingTradeName\":\"自营外卖\",\"mappingTradeAppId\":\"7\",\"mappingComboId\":\"\",\"feeRate\":{\"2\":\"0.38\",\"3\":\"0.38\",\"17\":\"0.38\"}},\"b5e9ecc5-525c-439a-bdac-fc5b8e4878f4\":{\"tradeName\":\"扫码点单\",\"mappingTradeName\":\"扫码点单\",\"mappingTradeAppId\":\"6\",\"mappingComboId\":\"6025\",\"feeRate\":{\"2\":\"0.38\",\"3\":\"0.38\",\"17\":\"0.38\"}},\"61fc74c7-d86f-4f13-b872-712e884a86cf\":{\"tradeName\":\"校园外卖\",\"mappingTradeName\":\"校园外卖\",\"mappingTradeAppId\":\"4\",\"mappingComboId\":\"8618\",\"feeRate\":{\"2\":\"0.38\",\"3\":\"0.38\",\"17\":\"0.38\"}},\"huabei\":{\"tradeName\":\"花呗分期\",\"mappingTradeName\":\"花呗分期\",\"mappingTradeAppId\":\"6221\",\"mappingComboId\":\"9764\",\"feeRate\":{\"2\":\"0.38\"}},\"onlinePayment\":{\"tradeName\":\"线上收款\",\"mappingTradeName\":\"线上收款\",\"mappingTradeAppId\":\"7083\",\"mappingComboId\":\"12207\",\"feeRate\":{\"3\":\"0.78\"},\"forceCombo\":true},\"crossCityPayment\":{\"tradeName\":\"跨城收款\",\"mappingTradeName\":\"跨城收款\",\"mappingTradeAppId\":\"7084\",\"mappingComboId\":\"12208\",\"feeRate\":{\"3\":\"0.78\"},\"forceCombo\":true},\"刷卡收款\":{\"tradeName\":\"刷卡收款\",\"mappingTradeName\":\"刷卡收款\",\"mappingTradeAppId\":\"5\"}}\n";

    Map<String,Object> subBizMap;
    @Before
    public void setUp() {
        subBizMap = JSONObject.parseObject(info,Map.class);
    }

    @Test
    public void openSmartTest() {
        // Arrange
        String appId = "appId";
        String merchantId = "merchantId";
        String merchantSn = "merchantSn";
        String acquirer = "acquirer";
        String tradeAppId = "tradeAppId";

        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);

        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setSn(merchantSn);
        when(merchantService.getMerchantById(merchantId, null)).thenReturn(merchantInfo);

        ContractStatus contractStatus = new ContractStatus();
        contractStatus.setStatus(ContractStatus.STATUS_SUCCESS);
        contractStatus.setAcquirer(acquirer);
        when(contractStatusMapper.selectByMerchantSn(merchantSn)).thenReturn(contractStatus);

        when(acquirerDAO.getByAcquirer(acquirer)).thenReturn(new McAcquirerDO().setProvider("provider"));

        SubBizConfig subBizConfig = new SubBizConfig();
        subBizConfig.setMappingTradeAppId(tradeAppId);
        when(applicationApolloConfig.getAppIdSubBiz()).thenReturn(Collections.singletonMap(appId, BeanUtils.beanToMap(subBizConfig)));

        AbstractAcquirerChangeBiz changeBiz = mock(AbstractAcquirerChangeBiz.class);
        when(applicationContext.getBean(any(String.class), eq(AbstractAcquirerChangeBiz.class))).thenReturn(changeBiz);

        List<MerchantProviderParams> paramsList = Collections.emptyList();
        when(changeBiz.getDefaultChangeParamsForSubBiz(merchantSn)).thenReturn(paramsList);
        when(tradeParamsBiz.openSmartTradeParams(any(MerchantProviderParams.class), any(), anyBoolean(), anyString())).thenReturn(true);

        // Act
        subBizParamsBiz.openSmart(data);

        // Assert
        verify(redisLock, times(1)).lock(appId, anyString(), anyLong());
        verify(redisLock, times(1)).unlock(anyString(), anyString());
        verify(tradeParamsBiz, times(paramsList.size())).openSmartTradeParams(any(MerchantProviderParams.class), any(), anyBoolean(), eq(tradeAppId));
        verify(kafkaTemplate, times(1)).send(anyString(), anyString());
        verify(acquirerService, times(1)).syncMchStatusToTargetAcquirer(eq(merchantSn), eq(ValidStatusEnum.VALID.getValue()), eq(acquirer));
    }

    @Test
    public void openSmartTestWithRedisLockException() {
        // Arrange
        String appId = "appId";
        String merchantId = "merchantId";

        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);

        when(redisLock.lock(any(),any(),anyLong())).thenReturn(Boolean.FALSE);

        // Act & Assert
        final CommonPubBizException commonPubBizException = assertThrows(CommonPubBizException.class, () -> {
            subBizParamsBiz.openSmart(data);
        });

        // Assert
        Assert.equals(commonPubBizException.getMessage(),"重复消费");
        verify(redisLock, times(1)).lock(appId, anyString(), anyLong());
    }


    @Test
    public void openSmartTestWithExistException() {
        // Arrange
        String appId = "appId";
        String merchantId = "merchantId";

        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);

        when(redisLock.lock(any(),any(),anyLong())).thenReturn(Boolean.TRUE);
        Map<String,Object> appIdSubBizMap = Maps.newHashMap();
        when(applicationApolloConfig.getAppIdSubBiz()).thenReturn(appIdSubBizMap);

        // Act & Assert
        final CommonPubBizException commonPubBizException = assertThrows(CommonPubBizException.class, () -> {
            subBizParamsBiz.openSmart(data);
        });
        final String format = String.format("appId:%s,没有找到对应业务,appIdSubBizMap:%s",
                appId,
                JSONObject.toJSONString(appIdSubBizMap));
        // Assert
        Assert.equals(commonPubBizException.getMessage(),format);
        verify(redisLock, times(1)).lock(appId, anyString(), anyLong());
        verify(merchantService,never()).getMerchantById(any(),any());
    }

    @Test
    public void openSmartTestWithWitheListException() {
        // Arrange
        String appId = "5c8e9f62-25c4-42fa-8d03-893b57c7fa87";
        String merchantId = "merchantId";

        //开通信息
        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);
        //商户信息
        final MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setId(merchantId);
        merchantInfo.setSn("sn");
        List whitelist = Lists.newArrayList("sn");
        when(redisLock.lock(any(),any(),anyLong())).thenReturn(Boolean.TRUE);
        when(applicationApolloConfig.getAppIdSubBiz()).thenReturn(subBizMap);
        when(merchantService.getMerchantById(any(),any())).thenReturn(merchantInfo);
        when(applicationApolloConfig.getMultiBusinessWhitelist()).thenReturn(whitelist);

        // Act & Assert
        final CommonPubBizException commonPubBizException = assertThrows(CommonPubBizException.class, () -> {
            subBizParamsBiz.openSmart(data);
        });
        String message = String.format("当前商户在白名单中whitelist:%s",JSONObject.toJSONString(whitelist));
        // Assert
        Assert.equals(commonPubBizException.getMessage(),message);
    }

    @Test
    public void openSmartTestContractException() {
        // Arrange
        String appId = "5c8e9f62-25c4-42fa-8d03-893b57c7fa87";
        String merchantId = "merchantId";

        //开通信息
        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);
        //商户信息
        final MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setId(merchantId);
        merchantInfo.setSn("sn");
        List whitelist = Lists.newArrayList("111");
        when(redisLock.lock(any(),any(),anyLong())).thenReturn(Boolean.TRUE);
        when(applicationApolloConfig.getAppIdSubBiz()).thenReturn(subBizMap);
        when(merchantService.getMerchantById(any(),any())).thenReturn(merchantInfo);
        when(applicationApolloConfig.getMultiBusinessWhitelist()).thenReturn(whitelist);
        when(contractStatusMapper.selectByMerchantSn(merchantInfo.getSn())).thenReturn(null);

        // Act & Assert
        final CommonPubBizException commonPubBizException = assertThrows(CommonPubBizException.class, () -> {
            subBizParamsBiz.openSmart(data);
        });
        // Assert
        Assert.equals(commonPubBizException.getMessage(),"当前间连扫码未开通成功");
    }

    @Test
    public void openSmartTestNotSupportException() {
        // Arrange
        String appId = "5c8e9f62-25c4-42fa-8d03-893b57c7fa87";
        String merchantId = "merchantId";
        String merchantSn = "merchantSn";
        String acquirer = "acquirer";
        String tradeAppId = "tradeAppId";
        //开通信息
        DataBusAppInfo data = new DataBusAppInfo();
        data.setAppId(appId);
        data.setMerchantId(merchantId);
        //商户信息
        final MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setId(merchantId);
        merchantInfo.setSn(merchantSn);
        List whitelist = Lists.newArrayList("111");
        ContractStatus contractStatus = new ContractStatus().setStatus(ContractStatus.STATUS_SUCCESS).setAcquirer(acquirer);

        when(redisLock.lock(any(),any(),anyLong())).thenReturn(Boolean.TRUE);
        when(applicationApolloConfig.getAppIdSubBiz()).thenReturn(subBizMap);
        when(merchantService.getMerchantById(any(),any())).thenReturn(merchantInfo);
        when(applicationApolloConfig.getMultiBusinessWhitelist()).thenReturn(whitelist);
        when(contractStatusMapper.selectByMerchantSn(merchantInfo.getSn())).thenReturn(contractStatus);

        when(acquirerDAO.getByAcquirer(acquirer)).thenReturn(new McAcquirerDO().setProvider("1028"));
//        when(this.merchantProviderParamsMapper.selectByExample(any())).thenReturn(Lists.newArrayList());
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(merchantSn).andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue()).andDeletedEqualTo(false);
        Mockito.spy(merchantProviderParamsMapper).selectByExample(example);
        // Act & Assert
        final CommonPubBizException commonPubBizException = assertThrows(CommonPubBizException.class, () -> {
            subBizParamsBiz.openSmart(data);
        });
        // Assert
        Assert.equals(commonPubBizException.getMessage(),"当前移动支付业务所在收单机构不支持智慧经营且没有找到可支持的间连支付通道");
    }

    /**
     * 开通了支付宝和微信的，只删除支付宝的
     */
    @Test
    public void closeCrossCityPayment01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setExtra("{\"ali\":{\"payMerchantId\":\"2081718330850354\"}}");
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.SUCCESS);
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(onlinePaymentBiz).queryCurrentAcquirerSuccessApply("merchantSn", PaywayEnum.ALIPAY.getValue());
        TradeAppQueryRequest tradeAppQueryRequest1 = new TradeAppQueryRequest();
        tradeAppQueryRequest1.setPage(1);
        tradeAppQueryRequest1.setPageSize(5);
        tradeAppQueryRequest1.setName("跨城收款");
        Mockito.doReturn(JSON.parseObject("{\"total\":1,\"records\":[{\"id\":17,\"name\":\"跨城收款\"}]}", new TypeReference<ListResult<TradeAppResult>>(){})).when(tradeAppService).queryTradeApps(tradeAppQueryRequest1);
        Mockito.doReturn("跨城收款").when(applicationApolloConfig).getCrossCityPayment();

        TradeAppQueryRequest tradeAppQueryRequest2 = new TradeAppQueryRequest();
        tradeAppQueryRequest2.setPage(1);
        tradeAppQueryRequest2.setPageSize(5);
        tradeAppQueryRequest2.setName("线上收款");
        Mockito.doReturn(JSON.parseObject("{\"total\":1,\"records\":[{\"id\":22,\"name\":\"线上收款\"}]}", new TypeReference<ListResult<TradeAppResult>>(){})).when(tradeAppService).queryTradeApps(tradeAppQueryRequest2);
        Mockito.doReturn("线上收款").when(applicationApolloConfig).getOnlinePayment();

        Mockito.doReturn(JSON.parseObject("{\"onlinePayment\":{\"tradeName\":\"线上收款\"},\"crossCityPayment\":{\"tradeName\":\"跨城收款\"}}")).when(applicationApolloConfig).getAppIdSubBiz();
        Mockito.doReturn(Arrays.asList(new ListMchFeeRateResult().setTradeComboId(474L).setBscFeeRate("0.6").setPayWay(PaywayEnum.ALIPAY.getValue()).setTradeAppName("跨城收款")
                , new ListMchFeeRateResult().setTradeComboId(474L).setBscFeeRate("0.78").setPayWay(PaywayEnum.WEIXIN.getValue()).setTradeAppName("跨城收款")
                , new ListMchFeeRateResult().setTradeComboId(475L).setBscFeeRate("0.6").setPayWay(PaywayEnum.ALIPAY.getValue()).setTradeAppName("线上收款")
                , new ListMchFeeRateResult().setTradeComboId(475L).setBscFeeRate("0.78").setPayWay(PaywayEnum.WEIXIN.getValue()).setTradeAppName("线上收款")))
                .when(feeRateService).listMchEffectFeeRates("merchantSn");

        SubBizParams subBizParams1 = new SubBizParams()
                .setId(1L)
                .setTrade_app_id("17")
                .setExtra(JSON.toJSONString(CollectionUtil.hashMap(
                        "1033", Arrays.asList("paramId1", "paramId2")
                )));
        SubBizParams subBizParams2 = new SubBizParams()
                .setId(2L)
                .setTrade_app_id("22")
                .setExtra(JSON.toJSONString(CollectionUtil.hashMap(
                        "1033", Arrays.asList("paramId1", "paramId2")
                )));
        Mockito.doReturn(Arrays.asList(subBizParams1, subBizParams2)).when(subBizParamsMapper).selectByExampleWithBLOBs(any());
        Mockito.doReturn(new MerchantProviderParams().setPayway(PaywayEnum.ALIPAY.getValue()))
                .when(merchantProviderParamsMapper).selectByPrimaryKey("paramId1");
        Mockito.doReturn(new MerchantProviderParams().setPayway(PaywayEnum.WEIXIN.getValue()))
                .when(merchantProviderParamsMapper).selectByPrimaryKey("paramId2");

        subBizParamsBiz.closeCrossCityPayment("merchantSn", PaywayEnum.ALIPAY.getValue());
        Mockito.verify(subBizParamsMapper, Mockito.times(2)).updateByPrimaryKeySelective(any());
        Mockito.verify(feeRateService, Mockito.times(2)).cancelFeeRate(any());
        Mockito.verify(onlinePaymentBiz, Mockito.times(1)).saveFeeRateAndInvalidApply(any(), any());
    }
}
