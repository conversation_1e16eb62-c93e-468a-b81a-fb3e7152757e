package com.wosai.upay.job.xxljob.batch.providerterminal;

import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static com.wosai.upay.job.constant.ProviderTerminalConstants.FAIL;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class AddStoreBindAllSubMchJobHandlerTest {

    @InjectMocks
    private AddStoreBindAllSubMchJobHandler handler;

    @Mock
    private ProviderTerminalTaskMapper taskMapper;

    @Mock
    private ProviderTerminalBiz providerTerminalBiz;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ProviderTerminalTaskRepository taskRepository;

    private ProviderTerminalTask task;

    @Before
    public void setUp() {
        task = new ProviderTerminalTask();
        task.setId(1L);
        task.setMerchant_sn("testMerchant");
        task.setStatus(0); // 假设初始状态为0
        task.setContext("{\"provider\":1}"); // 假设提供者为1
    }

    @Test
    public void getLockKey_ValidId_ReturnsCorrectKey() {
        task.setId(123L);
        String expectedKey = "AddStoreBindAllSubMchJobHandler:123";
        String actualKey = handler.getLockKey(task);
        assertEquals(expectedKey, actualKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsKeyWithNull() {
        task.setId(null);
        String expectedKey = "AddStoreBindAllSubMchJobHandler:null";
        String actualKey = handler.getLockKey(task);
        assertEquals(expectedKey, actualKey);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsTaskList() {
        BatchJobParam param = new BatchJobParam();
        param.setQueryTime(1000L); // 1秒
        param.setBatchSize(10);

        List<ProviderTerminalTask> expectedTasks = new ArrayList<>();
        ProviderTerminalTask task = new ProviderTerminalTask();
        task.setId(1L);
        expectedTasks.add(task);

        when(taskMapper.selectMerchantSnByPriorityAndType(Mockito.anyString(), Mockito.anyString(), Mockito.eq(4), Mockito.eq(10)))
                .thenReturn(expectedTasks);

        List<ProviderTerminalTask> result = handler.queryTaskItems(param);

        assertEquals(expectedTasks, result);
    }

    @Test
    public void queryTaskItems_EmptyResult_ReturnsEmptyList() {
        BatchJobParam param = new BatchJobParam();
        param.setQueryTime(1000L);
        param.setBatchSize(10);

        when(taskMapper.selectMerchantSnByPriorityAndType(Mockito.anyString(), Mockito.anyString(), Mockito.eq(4), Mockito.eq(10)))
                .thenReturn(new ArrayList<>());

        List<ProviderTerminalTask> result = handler.queryTaskItems(param);

        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void queryTaskItems_ZeroBatchSize_ReturnsEmptyList() {
        BatchJobParam param = new BatchJobParam();
        param.setQueryTime(1000L);
        param.setBatchSize(0);

        when(taskMapper.selectMerchantSnByPriorityAndType(Mockito.anyString(), Mockito.anyString(), Mockito.eq(4), Mockito.eq(0)))
                .thenReturn(new ArrayList<>());

        List<ProviderTerminalTask> result = handler.queryTaskItems(param);

        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void doHandleSingleData_SuccessfulProcessing_UpdatesStatusToSuccess() {
        when(taskMapper.selectByPrimaryKey(task.getId())).thenReturn(task);
        doNothing().when(providerTerminalBiz).boundTerminal(task);

        handler.doHandleSingleData(task);

        verify(taskRepository, never()).updateTaskStatusById(anyLong(), eq(FAIL), anyString());
    }

    @Test
    public void doHandleSingleData_ContractBizException_UpdatesStatusToFail() {
        when(taskMapper.selectByPrimaryKey(task.getId())).thenReturn(task);
        doThrow(new ContractBizException("Test Exception")).when(providerTerminalBiz).boundTerminal(task);

        handler.doHandleSingleData(task);

        verify(taskRepository).updateTaskStatusById(task.getId(), FAIL, "Test Exception");
    }

    @Test
    public void doHandleSingleData_RunTimeException_UpdatesStatusToFail() {
        when(taskMapper.selectByPrimaryKey(task.getId())).thenReturn(task);
        doThrow(new RuntimeException("RunTime Exception")).when(providerTerminalBiz).boundTerminal(task);

        handler.doHandleSingleData(task);

        verify(taskRepository, times(0)).updateTaskStatusById(anyLong(), anyInt(), anyString());
    }

    @Test
    public void doHandleSingleData_ProviderHaike_LockAcquisitionFailure_Returns() {
        task.setContext("{\"provider\":1037}"); // 假设提供者为2（Haike）
        when(taskMapper.selectByPrimaryKey(task.getId())).thenReturn(task);
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(false);

        handler.doHandleSingleData(task);

        verify(providerTerminalBiz, never()).boundTerminal(task);
    }

    @Test
    public void doHandleSingleData_ProviderHaike_LockAcquisitionSuccess_ProcessesTask() {
        task.setContext("{\"provider\":1037}"); // 假设提供者为2（Haike）
        when(taskMapper.selectByPrimaryKey(task.getId())).thenReturn(task);
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(true);
        doNothing().when(providerTerminalBiz).boundTerminal(task);

        handler.doHandleSingleData(task);

        verify(providerTerminalBiz).boundTerminal(task);
        verify(redisLock).unlock(anyString(), anyString());
    }
}