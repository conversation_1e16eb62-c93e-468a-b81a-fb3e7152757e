package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.merchant.audit.api.service.MerchantAuditService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Map;


/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/9/10 6:15 PM
 **/

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class UnionOpenJobServiceTest {

    @Autowired
    UnionOpenJobService unionOpenJobService;

    @Test
    public void test(){
        unionOpenJobService.getUnionOpenParamByMerchantSn("mch-1680002561325");
    }

    @Test
    public void subTest(){
        unionOpenJobService.contractUnionMerchant("21690003039582");
    }


    @Autowired
    MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Autowired
    MerchantAuditService merchantAuditService;

    @Test
    public void lincense(){
        Map<String, Object> license = merchantBusinessLicenseService.getBusinessLicenseByMerchantId("0001098d1064-7498-b574-f752-e1e88fc9");
        System.out.println(JSON.toJSONString(license));
        Map audit = merchantAuditService.getAuditByMerchantId("0001098d1064-7498-b574-f752-e1e88fc9");
        System.out.println(JSON.toJSONString(audit));
        Map audit1 = merchantAuditService.getAuditByMerchantSn("mch-1680002033871");
        System.out.println(audit1);
    }
}