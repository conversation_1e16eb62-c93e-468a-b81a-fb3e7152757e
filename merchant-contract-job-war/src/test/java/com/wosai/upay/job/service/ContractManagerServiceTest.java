package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.dto.*;
import com.wosai.web.api.ListResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ContractManagerServiceTest for ContractManagerService
 *
 * <AUTHOR>
 * @date 2019-07-11 11:20
 * @Update by jerry:改用mock重写测试类代码
 */
@Slf4j
public class ContractManagerServiceTest extends BaseTest {

    @Autowired
    private ContractManagerService contractManagerService;

    @Before
    public void before() {
        log.info("--------------------- 开始测试：---------------------");
    }

    @After
    public void after() {
        log.info("--------------------- 测试结束 ---------------------");
    }

    @Test
    public void findRuleGroupList() {
        PageInfo pageInfo = new PageInfo(1, 10);
        RuleGroupDto ruleGroupDto = new RuleGroupDto();

        ListResult<RuleGroupDto> result = this.contractManagerService.findRuleGroupList(pageInfo, ruleGroupDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void addRuleGroup() {
        List<RuleGroupDto.Rule> rules = new ArrayList<>(2);
        RuleGroupDto.Rule rule1 = new RuleGroupDto.Rule();
        rule1.setContract_rule("100000001");
        rule1.setDepend_on("task1000001");
        RuleGroupDto.Rule rule2 = new RuleGroupDto.Rule();
        rule2.setContract_rule("200000002");
        rule2.setDepend_on("task2000002");
        rules.add(rule1);
        rules.add(rule2);
        RuleGroupDto ruleGroupDto = new RuleGroupDto();
        ruleGroupDto.setName("拉卡拉自动报备组");
        ruleGroupDto.setVendor("业务方001");
        ruleGroupDto.setVendor_app("业务方001");
        ruleGroupDto.setVendor("商户入网");
        ruleGroupDto.setRules(rules);
        RuleGroupDto result = this.contractManagerService.addRuleGroup(ruleGroupDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void modifyRuleGroup() {
        RuleGroupDto ruleGroupDto = new RuleGroupDto();
        List<RuleGroupDto.Rule> rules = new ArrayList<>(2);
        RuleGroupDto.Rule rule1 = new RuleGroupDto.Rule();
        rule1.setContract_rule("100000001");
        rule1.setDepend_on("task1000001");
        rules.add(rule1);
        ruleGroupDto.setGroup_id("c6b16aaf9f9246ecbaa5e3d283188d72");
        ruleGroupDto.setStatus(1);
        ruleGroupDto.setRules(rules);

        RuleGroupDto result = this.contractManagerService.modifyRuleGroup(ruleGroupDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void enableRuleGroup() {
        String groupId = "f4d7a36646ac41ba84488b826d9732ae111";
        Integer enable = 1;
        String remark = "临时禁用";
        RuleGroupDto result = this.contractManagerService.enableRuleGroup(groupId, enable);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findRuleGroupDetailByGroupId() {
        String groupId = "f4d7a36646ac41ba84488b826d9732ae";

        RuleGroupDto result = this.contractManagerService.findRuleGroupDetailByGroupId(groupId);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findContractRuleList() {
        PageInfo pageInfo = new PageInfo(1, 10);
        ContractRuleDto contractRuleDto = new ContractRuleDto();
        contractRuleDto.setName("银联");
        contractRuleDto.setType(2);
        contractRuleDto.setPayway(null);
        contractRuleDto.setProvider(null);
        contractRuleDto.setAcquirer(null);
        contractRuleDto.setChannel(null);

        ListResult<ContractRuleDto> result = this.contractManagerService.findContractRuleList(pageInfo, contractRuleDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findContractRuleDetail() {
        String rule = "lkl-1016-3-32631798";
        ContractRuleDto result = this.contractManagerService.findContractRuleDetailByRule(rule);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void addContractRule() {
        ContractRuleDto contractRuleDto = new ContractRuleDto();
        contractRuleDto.setName("拉卡拉9999999999999");
        contractRuleDto.setChannel("lkl-1017-17-S20180919151112EA3E6");
        contractRuleDto.setStatus(1);
        contractRuleDto.setType(2);
        contractRuleDto.setMetadata(null);
        ContractRuleDto result = this.contractManagerService.addContractRule(contractRuleDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void modifyContractRule() {
        ContractRuleDto contractRuleDto = new ContractRuleDto();
        contractRuleDto.setRule("lkl-1016-3-32631798");

        Map<String, Object> weixin_subdev_config = new HashMap<>(1);
        Map<String, Object> 杭州 = new HashMap<>(1);
        杭州.put("sub_appid", "wxc6ca2c84581752bb");
        杭州.put("subscribe_appid", "wxc6ca2c84581752bb");
        Map<String, Object> 南京 = new HashMap<>(1);
        南京.put("sub_appid", "wxf9114ca5d9b45bd5");
        南京.put("subscribe_appid", "wxf9114ca5d9b45bd5");
        weixin_subdev_config.put("杭州", 杭州);
        weixin_subdev_config.put("南京", 南京);

        Map<String, Object> metadata = new HashMap<>(1);
        metadata.put("weixin_subdev_config", weixin_subdev_config);
        contractRuleDto.setMetadata(metadata);

        ContractRuleDto result = this.contractManagerService.modifyContractRule(contractRuleDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void enableContractRule() {
        String rule = "0c41665a4cfe4445b4a9fb6f9274caf6";
        Integer enable = 0;
        ContractRuleDto result = this.contractManagerService.enableContractRule(rule, enable);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findChannelList() {
        ChannelCustomDto channelCustomDto = new ChannelCustomDto();
        channelCustomDto.setPayway(3);
        channelCustomDto.setChannel("lkl-1016-3-32631798");
        channelCustomDto.setProvider("1016");
        channelCustomDto.setAcquirer("lkl");
        channelCustomDto.setName("111");
        ListResult<ChannelCustomDto> listResult = this.contractManagerService.findChannelList(new PageInfo(1, 10), channelCustomDto);
        System.out.println(JSON.toJSONString(listResult));
    }

    @Test
    public void findChannelDetailByChannel() {
        String channel = "lkl-1016-3-32631798";
        ChannelCustomDto result = this.contractManagerService.findChannelDetailByChannel(channel);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void updateChannel() {
        ChannelDto param = new ChannelDto();
        param.setChannel("lkl-1016-3-32631798");
        param.setName("拉卡拉-内蒙古银联-微信");
        ChannelDto result = this.contractManagerService.updateChannel(param);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findAcquirerList() {
        AcquirerDto acquirerDto = new AcquirerDto();
        ListResult<AcquirerDto> result = this.contractManagerService.findAcquirerList(new PageInfo(1, 10), acquirerDto);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void findProviderList() {
        ProviderDto providerDto = new ProviderDto();

        ListResult<ProviderDto> result = this.contractManagerService.findProviderList(new PageInfo(1, 10), providerDto);
        System.out.println(JSON.toJSONString(result));
    }
}
