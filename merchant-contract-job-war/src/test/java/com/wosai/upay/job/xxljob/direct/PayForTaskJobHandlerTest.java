package com.wosai.upay.job.xxljob.direct;

import com.wosai.upay.job.mapper.PayForTaskMapper;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.providers.PayForProvider;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PayForTaskJobHandlerTest {

    @InjectMocks
    private PayForTaskJobHandler payForTaskJobHandler;

    @Mock
    private PayForTaskMapper payForTaskMapper;

    @Mock
    private PayForProvider payForProvider;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(30000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "PayForTaskJobHandler";
        String actualLockKey = payForTaskJobHandler.getLockKey();

        Assert.assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_EmptyPayForTasksList_NoProcessing() {
        when(payForTaskMapper.selectByCreate(anyString(), anyString(), anyInt())).thenReturn(new ArrayList<>());

        payForTaskJobHandler.execute(param);

        verify(payForProvider, never()).processTask(any(PayForTask.class));
    }

    @Test
    public void execute_NonEmptyPayForTasksList_ProcessTasks() {
        List<PayForTask> payForTasks = new ArrayList<>();
        PayForTask task1 = new PayForTask();
        task1.setId(1L);
        payForTasks.add(task1);

        when(payForTaskMapper.selectByCreate(anyString(), anyString(), anyInt())).thenReturn(payForTasks);

        payForTaskJobHandler.execute(param);

        verify(payForProvider, times(1)).processTask(task1);
    }

    @Test
    public void execute_ExceptionDuringProcessing_LogsError() {
        List<PayForTask> payForTasks = new ArrayList<>();
        PayForTask task1 = new PayForTask();
        task1.setId(1L);
        payForTasks.add(task1);

        when(payForTaskMapper.selectByCreate(anyString(), anyString(), anyInt())).thenReturn(payForTasks);
        doThrow(new RuntimeException("Test exception")).when(payForProvider).processTask(any(PayForTask.class));

        payForTaskJobHandler.execute(param);

        verify(payForProvider, times(1)).processTask(task1);
    }
}
