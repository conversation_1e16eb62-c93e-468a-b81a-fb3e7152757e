package com.wosai.upay.job.refactor.Integration.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.mapper.IndustryCodeV2Mapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.*;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.providers.BasicProvider;
import com.wosai.upay.job.refactor.dao.IndustryCodeV2DAO;
import com.wosai.upay.job.refactor.mapper.IndustryCodeV2DynamicMapper;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.entity.IndustryCodeV2DO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

/**
 * 版本升级测试
 * mybatis-plus,pageHelper,redisson,caffeine
 *
 * <AUTHOR>
 * @date 2023/11/20 13:36
 */
@Slf4j
public class VersionUpgradedTest extends BaseTest {

    @Resource
    private IndustryCodeV2DAO industryCodeV2DAO;

    @Resource
    private IndustryCodeV2DynamicMapper industryCodeV2DynamicMapper;

    @Resource
    private IndustryCodeV2Mapper industryCodeV2Mapper;


    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Resource
    private MerchantProviderParamsDynamicMapper merchantProviderParamsDynamicMapper;

    @Test
    public void testMybatisPlusQuery() {
        Optional<IndustryCodeV2DO> industryOptional = industryCodeV2DAO.getIndustryCodeV2ByIndustryId("0260c061-c759-47c7-ac2d-dddd353f1649");
        assertEquals("2032", industryOptional.get().getECnyCode());
    }


    @Test
    public void testMybatisPlusListAll() {
        List<IndustryCodeV2DO> industryCodeV2DOS = industryCodeV2DAO.listAllIndustryCodeV2s();
        assertEquals(industryCodeV2DOS.size(), industryCodeV2DynamicMapper.selectCount(null).intValue());
    }

    @Test
    public void testMybatisPlusUpdate() {
        String id = "dde9f979-2edd-4655-8c35-0e115248359a";
        assertThat(industryCodeV2DynamicMapper
                .updateById(new IndustryCodeV2DO().setId(id).setECnyCode("8888"))).isPositive();
        //        UPDATE industry_code_v2
        //        SET
        //                union_code_weixin_private = '5948'
        //        WHERE
        //                id = '0d7d3cbe-64b8-43f0-95fd-d1eac90b339f'
        //        AND industry_id = 'dde9f979-2edd-4655-8c35-0e115248359a';
        assertThat(
                industryCodeV2DynamicMapper.update(
                        new IndustryCodeV2DO().setIndustryId("dde9f979-2edd-4655-8c35-0e115248359a"),
                        Wrappers.<IndustryCodeV2DO>lambdaUpdate()
                                .set(IndustryCodeV2DO::getUnionCodeWeixinPrivate, "5948")
                                .eq(IndustryCodeV2DO::getId, id)
                )
        ).isPositive();
    }

    @Test
    public void testMybatisPlusInsert() {
        IndustryCodeV2DO industryCodeV2DO = new IndustryCodeV2DO();
        UUID uuid = UUID.randomUUID();
        industryCodeV2DO.setId(uuid.toString());
        assertThat(industryCodeV2DynamicMapper.insert(industryCodeV2DO)).isPositive();
        IndustryCodeV2DO industryCodeV2DO1 = industryCodeV2DynamicMapper.selectById(uuid.toString());
        log.info("IndustryCodeV2: {}", JSON.toJSONString(industryCodeV2DO1));
        assertThat(industryCodeV2DO1).isNotNull();
    }

    @Test
    public void testMybatisPlusDelete() {
        IndustryCodeV2DO industryCodeV2DO = new IndustryCodeV2DO();
        UUID uuid = UUID.randomUUID();
        industryCodeV2DO.setId(uuid.toString());
        industryCodeV2DynamicMapper.insert(industryCodeV2DO);
        assertThat(industryCodeV2DynamicMapper.deleteById(industryCodeV2DO.getId())).isPositive();
    }

    @Test
    public void testMybatisPlusOrderBy() {
        List<IndustryCodeV2DO> industryCodeV2DOS = industryCodeV2DynamicMapper.selectList(Wrappers.<IndustryCodeV2DO>query().orderByAsc("e_cny_code"));
        List<IndustryCodeV2DO> industryCodeV2DOS1 = industryCodeV2DynamicMapper
                .selectList(Wrappers.<IndustryCodeV2DO>lambdaQuery().orderByAsc(IndustryCodeV2DO::getIndustryId, IndustryCodeV2DO::getECnyCode));
        assertThat(industryCodeV2DOS).isNotEmpty();
        // 多字段排序
        List<IndustryCodeV2DO> industryCodeV2DOS2 = industryCodeV2DynamicMapper
                .selectList(Wrappers.<IndustryCodeV2DO>query().orderByAsc("id", "industry_id"));
        assertThat(industryCodeV2DOS2).isNotEmpty();
        List<IndustryCodeV2DO> industryCodeV2DOS3 = industryCodeV2DynamicMapper
                .selectList(Wrappers.<IndustryCodeV2DO>query().orderByAsc("industry_id").orderByDesc("e_cny_code"));
        assertThat(industryCodeV2DOS3).isNotEmpty();
    }

    @Test
    public void testMybatisPlusOrderByLambda() {
        List<IndustryCodeV2DO> industryCodeV2DOS = industryCodeV2DynamicMapper
                .selectList(Wrappers.<IndustryCodeV2DO>lambdaQuery().orderByAsc(IndustryCodeV2DO::getECnyCode));
        assertThat(industryCodeV2DOS).isNotEmpty();
        // 多字段排序
        List<IndustryCodeV2DO> industryCodeV2DOS1 = industryCodeV2DynamicMapper
                .selectList(Wrappers.<IndustryCodeV2DO>lambdaQuery().orderByAsc(IndustryCodeV2DO::getIndustryId, IndustryCodeV2DO::getLakalaCode));
        assertThat(industryCodeV2DOS1).isNotEmpty();
        List<IndustryCodeV2DO> industryCodeV2DOS2 = industryCodeV2DynamicMapper.selectList(Wrappers.<IndustryCodeV2DO>lambdaQuery()
                .orderByAsc(IndustryCodeV2DO::getUnionCodeWeixinPrivate).orderByDesc(IndustryCodeV2DO::getDirectConnectWeixinCode));
        assertThat(industryCodeV2DOS2).isNotEmpty();
    }

    @Test
    public void testMybatisPlusGroup() {
        QueryWrapper<IndustryCodeV2DO> wrapper = new QueryWrapper<>();
        wrapper.select("e_cny_code, count(*) as num")
                .groupBy("e_cny_code");
        List<Map<String, Object>> maplist = industryCodeV2DynamicMapper.selectMaps(wrapper);
        for (Map<String, Object> mp : maplist) {
            log.info(JSON.toJSONString(mp));
        }
        // lambdaQueryWrapper groupBy orderBy
        LambdaQueryWrapper<IndustryCodeV2DO> lambdaQueryWrapper = new QueryWrapper<IndustryCodeV2DO>().lambda()
                .select(IndustryCodeV2DO::getECnyCode)
                .groupBy(IndustryCodeV2DO::getECnyCode)
                .orderByAsc(IndustryCodeV2DO::getECnyCode);
        List<IndustryCodeV2DO> industryCodeV2DOS = industryCodeV2DynamicMapper.selectList(lambdaQueryWrapper);
        for (IndustryCodeV2DO industryCodeV2DO : industryCodeV2DOS) {
            log.info(JSON.toJSONString(industryCodeV2DO));
        }
    }

    @Test
    public void testMybatisPlusWithPageHelper() {
        int pageSize = 15;
        Integer total = Math.toIntExact(merchantProviderParamsDynamicMapper
                .selectCount(new LambdaQueryWrapper<MerchantProviderParamsDO>()
                        .eq(MerchantProviderParamsDO::getProvider, 1028)
                        .eq(MerchantProviderParamsDO::getPayway, 0)));
        PageInfo<MerchantProviderParamsDO> info = PageHelper.startPage(1, pageSize)
                .doSelectPageInfo(() -> merchantProviderParamsDynamicMapper
                        .selectList(new LambdaQueryWrapper<MerchantProviderParamsDO>()
                                .eq(MerchantProviderParamsDO::getProvider, 1028)
                                .eq(MerchantProviderParamsDO::getPayway, 0)
                                .orderByAsc(MerchantProviderParamsDO::getMerchantSn)));
        assertThat(info.getTotal()).isEqualTo(total);
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = info.getList();
        assertThat(merchantProviderParamsDOS).hasSize(pageSize);
    }
    

    /**
     * 验证添加mybatis-plus后是否影响已有查询
     */
    @Test
    public void testExistedMapper() {
        String industryId = "0ba80d89-3b6e-4782-92fa-4bb49c669fe6";
        String id = "0ba80d89-3b6e-4782-92fa-4bb49c669fe6";
        IndustryCodeV2 IndustryCodeV2 = industryCodeV2Mapper.getIndustryCodeV2ByIndustryId(industryId);
        assertEquals(id, IndustryCodeV2.getIndustry_id());
    }

    @Test
    public void testExistedPageHelper() {
        int pageSize = 20;
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andProviderEqualTo(1028).andPaywayEqualTo(0);
        PageHelper.startPage(1, pageSize, "merchant_sn");
        assertEquals(pageSize, merchantProviderParamsMapper.selectByExample(example).size());
    }

    @Resource
    private RuleContext ruleContext;

    @Test
    public void testRedisConnection() {
        RuleGroup hxb = ruleContext.getRuleGroup("hxb");
        assertNotNull(hxb);
    }

    @Resource
    private RedissonClient redissonClient;

    @Test
    @SuppressWarnings("all")
    public void testRedisson() {
        String lockKey = "merchant-contract-job:test:redisson";
        ThreadPoolWorker<Object> worker = ThreadPoolWorker.of();
        for (int i = 0; i < 5; i++) {
            worker.addWork(() -> {
                try {
                    RLock lock = redissonClient.getLock(lockKey);
                    if (lock.tryLock()) {
                        // 抢到了锁,执行逻辑
                        for (int j = 0; j < 5; j++) {
                            Thread.sleep(1000);
                            log.info("thread: {}, 执行工作中, {}",Thread.currentThread().getName(), j );
                        }
                    } else {
                        log.warn("加锁失败, current thread: {}", Thread.currentThread().getName());
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(e);
                }
            });
        }
        worker.doWorks(); // join
    }
}
