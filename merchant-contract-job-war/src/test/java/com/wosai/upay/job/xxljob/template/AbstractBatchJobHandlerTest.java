package com.wosai.upay.job.xxljob.template;

import com.wosai.upay.job.xxljob.model.BatchExecTypeEnum;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractBatchJobHandlerTest {

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor batchJobHandlerThreadPool;

    @Spy
    private TestBatchJobHandler handler;

    private BatchJobParam param;

    @Before
    public void setUp() {
        param = new BatchJobParam();
        param.setExecType(BatchExecTypeEnum.SYNC_SERIAL);
        RLock mock = mock(RLock.class);
        Mockito.doReturn(mock).when(redissonClient).getLock(anyString());
        Mockito.doReturn(true).when(mock).tryLock();
        ReflectionTestUtils.setField(handler, "redissonClient", redissonClient);
        ReflectionTestUtils.setField(handler, "batchJobHandlerThreadPool", batchJobHandlerThreadPool);
    }

    @Test
    public void handle_SyncSerial_ExecutesTasksInOrder() {
        handler.handle(param);

        verify(handler, times(1)).doHandleSingleData("task1");
        verify(handler, times(1)).doHandleSingleData("task2");
    }

    @Test
    public void handle_SyncParallel_ExecutesTasksInParallel() {
        param.setExecType(BatchExecTypeEnum.SYNC_PARALLEL);

        handler.handle(param);

        verify(handler, times(1)).doHandleSingleData("task1");
        verify(handler, times(1)).doHandleSingleData("task2");
    }

    @Test
    public void handle_AsyncSerial_ExecutesTasksAsynchronously() {
        param.setExecType(BatchExecTypeEnum.ASYNC_SERIAL);

        handler.handle(param);

        verify(batchJobHandlerThreadPool, times(1)).submit(any(Runnable.class));
    }

    @Test
    public void handle_AsyncParallel_ExecutesTasksAsynchronouslyInParallel() {
        param.setExecType(BatchExecTypeEnum.ASYNC_PARALLEL);

        handler.handle(param);

        verify(batchJobHandlerThreadPool, times(1)).submit(any(Runnable.class));
    }

    @Test
    public void handle_InvalidExecType_ThrowsIllegalArgumentException() {
        param.setExecType(null);

        assertThrows(IllegalArgumentException.class, () -> handler.handle(param));
    }

    // Added from AbstractBatchJobHandlerTestB
    @Test
    public void handle_SyncSerial_ExecutesTasksInOrder_B() {
        handler.handle(param);

        verify(handler, times(1)).doHandleSingleData("task1");
        verify(handler, times(1)).doHandleSingleData("task2");
    }

    @Test
    public void handle_SyncParallel_ExecutesTasksInParallel_B() {
        param.setExecType(BatchExecTypeEnum.SYNC_PARALLEL);
        handler.handle(param);

        verify(handler, times(1)).doHandleSingleData("task1");
        verify(handler, times(1)).doHandleSingleData("task2");
    }

    @Test
    public void handle_AsyncSerial_ExecutesTasksAsynchronously_B() {
        param.setExecType(BatchExecTypeEnum.ASYNC_SERIAL);

        handler.handle(param);

        verify(batchJobHandlerThreadPool, times(1)).submit(any(Runnable.class));
    }

    @Test
    public void handle_AsyncParallel_ExecutesTasksAsynchronouslyInParallel_B() {
        param.setExecType(BatchExecTypeEnum.ASYNC_PARALLEL);

        handler.handle(param);

        verify(batchJobHandlerThreadPool, times(1)).submit(any(Runnable.class));
    }

    @Test
    public void handle_InvalidExecType_ThrowsIllegalArgumentException_B() {
        param.setExecType(null);

        assertThrows(IllegalArgumentException.class, () -> handler.handle(param));
    }

    private static class TestBatchJobHandler extends AbstractBatchJobHandler<String> {
        @Override
        public List<String> queryTaskItems(BatchJobParam param) {
            return Arrays.asList("task1", "task2");
        }

        @Override
        public String getLockKey(String t) {
            return "lock:" + t;
        }

        @Override
        public void doHandleSingleData(String t) {
            // 处理单个数据
        }
    }
}
