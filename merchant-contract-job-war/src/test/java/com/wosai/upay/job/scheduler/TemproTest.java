package com.wosai.upay.job.scheduler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.request.AlipayMerchantIotDeviceBindRequest;
import com.alipay.api.response.AlipayMerchantIotDeviceBindResponse;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.crow.api.service.TagIngestService;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.BlueSeaBiz;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.messageStrategy.BlueSeaActivityChangedBiz;
import com.wosai.upay.job.enume.MerchantType;
import com.wosai.upay.job.handlers.MultiProviderEventHandler;
import com.wosai.upay.job.handlers.SupplyFileSubTaskHandler;
import com.wosai.upay.job.mapper.*;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.DO.TaskMch;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.ContractApplicationService;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.ContractTaskResultService;
import com.wosai.upay.job.service.SubtaskResultService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.util.WechatQrCodeUtils;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.newBlueSea.request.AlipayMerchantIotDeviceBindReq;
import com.wosai.upay.merchant.contract.model.weixin.ApplymentParam;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/9/21 6:26 PM
 **/
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TemproTest {

    @Autowired
    MerchantService merchantService;

    @Autowired
    TradeConfigService tradeConfigService;

    @Test
    public void merchantTest() {
        Map merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway("67555feb-7b5e-49d4-aa13-e541b6bb775a", 3);
        System.out.println(BeanUtil.getNestedProperty(merchantConfig, "up_direct_trade_params."));

    }

    @Autowired
    BlueSeaBiz blueSeaBiz;

    @Autowired
    BlueSeaTaskMapper blueSeaTaskMapper;

    @Test
    public void bind() {
        BlueSeaTask blueSeaTask = blueSeaTaskMapper.selectByPrimaryKey(400L);
        blueSeaBiz.handleMerchantIotDeviceBind(blueSeaTask);
    }

    @Test
    public void method(){
        BlueSeaTask blueSeaTask = blueSeaTaskMapper.selectByPrimaryKey(129L);
        String form_body = blueSeaTask.getForm_body();
        String aliAccount = MapUtils.getString(JSONObject.parseObject(blueSeaTask.getForm_body(), Map.class), BlueSeaConstant.ALIACCOUNT);
        System.out.println(form_body + aliAccount);
        System.out.println(aliAccount);
    }

    @Autowired
    NewBlueSeaService newBlueSeaService;

    @Test
    public void contractbind() {
        AlipayMerchantIotDeviceBindReq build = AlipayMerchantIotDeviceBindReq.builder().build();
        AliCommResponse<AlipayMerchantIotDeviceBindRequest, AlipayMerchantIotDeviceBindResponse> response = newBlueSeaService.alipayMerchantIotDeviceBind(build);
        System.out.println(response);
    }

    @Autowired
    private TerminalService terminalService;

    @Test
    public void queryterminal() {
        Map filter = CollectionUtil.hashMap("vendor_app_appid", "****************", "status", 1);
        ListResult terminals = terminalService.getTerminals("8618a453-6597-4a4e-a434-06dfd1f4b345", null, null, filter);
        if (terminals.getTotal() < 1) {
            return;
        }
        System.out.println(terminals);
    }

    @Autowired
    ContractApplicationService contractApplicationService;

    @Test
    public void apply(){
        String merchantSn = "**************";
        Long auditId = 27786L;
        Map form_body =CollectionUtil.hashMap("aliAccount","aaa","operator","0af4121d-6cf5-19cc-816c-f66ffddc0000","platform","SP","indoor","http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/ding-c7caefa94951be5e2eb39459247a6dec.jpeg?type=image");
        contractApplicationService.applyNewBlueSea(merchantSn, auditId, form_body);
    }

    @Test
    public void applyTerminal(){
//        String merchantSn = "*************";         //没有成功的记录
//        String merchantSn = "**************";       //有成功的记录,无终端
        String merchantSn = "**************";       //有成功记录，有终端
        Long auditId = 27786L;
        Map form_body =CollectionUtil.hashMap("aliAccount","aaa","operator","0af4121d-6cf5-19cc-816c-f66ffddc0000","platform","SP","indoor","http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/ding-c7caefa94951be5e2eb39459247a6dec.jpeg?type=image");
        CommonResult commonResult = contractApplicationService.refreshTerminal(merchantSn, auditId, form_body);
        System.out.println(commonResult);
    }

    @Test
    public void mapper(){
        List<BlueSeaTask> blueSeaTasks = blueSeaBiz.getBlueSeaTasks(Arrays.asList(BlueSeaConstant.SHOP_CREATED), Arrays.asList(BlueSeaConstant.blueSeaType, BlueSeaConstant.kx), 50, ********L);
        System.out.println(blueSeaTasks);
//        Arrays.asList(BlueSeaConstant.M4), Arrays.asList(BlueSeaConstant.blueSeaType, BlueSeaConstant.kx), 50, "********");
    }

    @Test
    public void config(){
        Map config = tradeConfigService.getTerminalConfigByTerminalIdAndPayway("7e0b5520-e0eb-4a2b-9833-6b7f5b798d44", PaywayEnum.ALIPAY.getValue());
        System.out.println(config);
    }

    @Test
    public void applyKx(){
        String merchantSn = "**************";
        Long auditId = 29998L;
        Map form_body = CollectionUtil.hashMap("aliAccount","aaa","operator","0af4121d-6cf5-19cc-816c-f66ffddc0000","platform","SP","indoor","http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/ding-c7caefa94951be5e2eb39459247a6dec.jpeg?type=image","templateId","11093","tobacco_url","http://wosai-sales.oss-cn-hangzhou.aliyuncs.com/workflow/ding-c7caefa94951be5e2eb39459247a6dec.jpeg?type=image");
        contractApplicationService.applyKx(merchantSn, auditId, form_body);
    }

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    @Test
    public void methods4(){
        Map map = applicationApolloConfig.getBlueSeaSupply();
        System.out.println(map);
    }

    @Autowired
    ContractTaskResultService contractTaskResultService;

    @Test
    public void getWechatAuthSucceedTask(){
        ContractTask wechatAuthSucceedTask = contractTaskResultService.getWechatAuthSucceedTask("**************", "*************");
    }

    @Autowired
    SubtaskResultService subtaskResultService;

    @Test
    public void getAuthApplyByTaskId() {
        MchAuthApply authApplyByTaskId = subtaskResultService.getAuthApplyByTaskId(2693485L);
        Date update_at = authApplyByTaskId.getUpdate_at();
        System.out.println(update_at);
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(update_at));
    }

    @Test
    public void methoddate(){
        System.out.println(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(1607706061000L));
    }

    @Autowired
    BlueSeaActivityChangedBiz blueSeaActivityChangedBiz;

    @Test
    public void updateTerminalConfig(){
        blueSeaActivityChangedBiz.updateTerminalConfig("**************","123","4654934a-c4a9-492a-8e31-d744800e5497");
    }

    @Autowired
    TagIngestService tagIngestService;

    @Test
    public void taginjest(){
        Integer a = tagIngestService.ingest(Arrays.asList("aa"), "e72e7547-1918-4849-b06d-81e6f5c74a21", BlueSeaConstant.KX_TAG_OPEN);
        System.out.println(a);
    }

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Test
    public void mchmapper(){
        ContractSubTask contractSubTask = contractSubTaskMapper.selectByPrimaryKey(4989907L);
        contractSubTask.setStatus(100);
        contractSubTaskMapper.insert(contractSubTask);
    }

    @Autowired
    ContractStatusService contractStatusService;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    WechatQrCodeUtils wechatQrCodeUtils;

    @Test
    public void packageCode(){
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(24814987L);
        String messageByContractTask = contractStatusService.getMessageByContractTask(contractTask);
        System.out.println(messageByContractTask);
    }

    @Test
    public void getweixinupgrade(){
        Map crm = contractStatusService.getWeixinUpgrade("21690003572417", "sp", ProviderUtil.CONTRACT_TYPE_AUTH);
        System.out.println(crm);
    }

    @Autowired
    TaskMchMapper taskMchMapper;

    @Test
    public void insertTaskMch(){
//        TaskMch build = TaskMch.builder().taskId(1L).subTaskId(1L).authApplyId(1L).payMerchantId("abc").merchantSn("sd").build();
        TaskMch build=null;
//        taskMchMapper.insert(build);
        System.out.println("abc");
        System.out.println("abc");
//        TaskMch taskMch = taskMchMapper.selectRecentByTaskId(2739310L);
//        System.out.println(taskMch);
    }

    @Autowired
    ContractTaskBiz contractTaskBiz;


    @Autowired
    WeixinAuthApplyBiz weixinAuthApplyBiz;

    @Autowired
    MchAuthApplyMapper mchAuthApplyMapper;

    @Autowired
    MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Autowired
    MerchantProviderParamsBiz merchantProviderParamsBiz;


    @Test
    public void updateParamsV2() {
        List<String> mchIds = Arrays.asList("1596128735003");
        Long taskId = 2722944L;
        MchAuthApply mchAuthApply = mchAuthApplyMapper.selectByTaskId(taskId);
//        Map<String, List> map = mchAuthApply.getPayMerchantId();
        Map<String, List<String>> map = mchAuthApply.getPayMerchantId();
        mchIds.forEach(mchId -> {
            //微信实名认证成功标记
            MerchantProviderParamsDto merchantProviderParamsDto = merchantProviderParamsMapper.getByPayMerchantId(mchId).toProviderParamsModule();
            merchantProviderParamsDto.getExtra().put(WeixinParamsUpdateApply.AUTH_TIME, System.currentTimeMillis());
            merchantProviderParamsDto.setAuth_status(1);
            merchantProviderParamsMapper.updateByPrimaryKeySelective(merchantProviderParamsBiz.fromDO(merchantProviderParamsDto));
            //申请单关联子商户号注入
            String rule = merchantProviderParamsDto.getContract_rule().split("-")[0];
            if (MapUtils.isEmpty(map)) {
                map.put(rule, Arrays.asList(mchId));
            } else {
                List<String> payMerchantIds = map.get(rule);
                if (StringUtil.listEmpty(payMerchantIds)){
                    map.put(rule, Arrays.asList(mchId));
                }else{
                    payMerchantIds.add(mchId);
                    map.put(rule, payMerchantIds);
                }
            }
        });
        mchAuthApplyMapper.updatePayMerchantId(mchAuthApply.getId(), JSON.toJSONString(map));
    }


    private static final String APPLYMENT_STATE_WAITTING_FOR_AUDIT = "APPLYMENT_STATE_WAITTING_FOR_AUDIT"; //【审核中】--请耐心等待1~2个工作日，微信支付将会完成审核
    private static final String APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT = "APPLYMENT_STATE_WAITTING_FOR_CONFIRM_CONTACT"; //【待确认联系信息】--请扫描微信支付返回的小程序码确认联系信息(此过程可修改超级管理员手机号)
    private static final String APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON = "APPLYMENT_STATE_WAITTING_FOR_CONFIRM_LEGALPERSON"; //请扫描微信支付返回的小程序码在小程序端完成账户验证
    private static final String APPLYMENT_STATE_PASSED = "APPLYMENT_STATE_PASSED"; //【审核通过】--请扫描微信支付返回的小程序码在小程序端完成授权流程
    private static final String APPLYMENT_STATE_REJECTED = "APPLYMENT_STATE_REJECTED"; //【审核驳回】--请按照驳回原因修改申请资料，并更换业务申请编码，重新提交申请
    private static final String APPLYMENT_STATE_FREEZED = "APPLYMENT_STATE_FREEZED"; //【已冻结】可能是该主体已完成过入驻，请查看驳回原因，并通知驳回原因中指定的联系人扫描微信支付返回的小程序码在小程序端完成授权流程
    private static final String APPLYMENT_STATE_CANCELED = "APPLYMENT_STATE_CANCELED"; //【已作废】--表示申请单已被撤销，无需再对其进行操作



    @Test
    public void syncQrCodeIfNecessary() throws Exception {
        MchAuthApply apply = mchAuthApplyMapper.selectByPrimaryKey(32397L);
        ApplymentParam applymentParam = JSON.parseObject(apply.getRequest_body(), ApplymentParam.class);
        Map info = new HashMap<>();
        if (applymentParam != null && applymentParam.getContact_info() != null && !StringUtils.isEmpty(applymentParam.getContact_info().getName())) {
            info.put("concatName", applymentParam.getContact_info().getName());
        }
        if (MerchantType.TYPE_MICRO.getVal().equals(Integer.valueOf(apply.getType()))) {
            info.put("merchantName", String.format("商户_%s", applymentParam.getIdentification_info().getIdentification_name()));
        }else{
            info.put("merchantName", applymentParam.getSubject_info().getBusiness_licence_info().getMerchant_name());
        }
//        info.put("qrcode", resp.getQrcode_data());
//        qrCodeAfter = wechatQrCodeUtils.confirmationCodeUrl(info);
    }


    @Autowired
    LklV3ShopTermMapper lklV3ShopTermMapper;

    @Test
    public void lklv3term(){
//        LklV3ShopTerm lklV3ShopTerm = lklV3ShopTermMapper.selectByPrimaryKey("45845eec-b2e7-425e-8a3c-87af2d88b0d6");
//        System.out.println(lklV3ShopTerm);
//        List<LklV3ShopTerm> sn = lklV3ShopTermMapper.selectByMerchantSn("sn");
//        System.out.println(sn);
//        LklV3ShopTerm shopId = lklV3ShopTermMapper.selectByShopId("shopId");
//        System.out.println(shopId);
//        LklV3ShopTerm str = lklV3ShopTermMapper.selectByStoreSn("str");
//        System.out.println(str);
        ContractSubTaskReq lkl = new ContractSubTaskReq().setMerchantSn("21690003041367").setChannel("lkl");
        List<ContractSubTask> taskByRecord = contractSubTaskMapper.findTaskByRecord(lkl);
        System.out.println(taskByRecord);
    }

    @Autowired
    SupplyFileSubTaskHandler supplyFileTaskHandler;

    @Test
    public void handResult(){
        ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(4932221L);
        com.wosai.upay.merchant.contract.model.ContractResponse response = new com.wosai.upay.merchant.contract.model.ContractResponse().setCode(500).setMessage("aaa");
        supplyFileTaskHandler.handleResult(response, subTask);
    }

    @Autowired
    MultiProviderEventHandler multiProviderEventHandler;

    @Autowired
    MultiProviderContractEventMapper multiProviderContractEventMapper;
    @Test
    public void hanlde() throws Exception {
        MultiProviderContractEvent event = multiProviderContractEventMapper.selectByPrimaryKey(138730L);
        multiProviderEventHandler.handle(event);
    }

}