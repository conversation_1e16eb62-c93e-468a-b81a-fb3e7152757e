package com.wosai.upay.job.controller;

import org.springframework.data.redis.connection.BitFieldSubCommands;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/16
 */
public class MyValueOperations<K, V> implements ValueOperations<K, V> {
    @Override
    public void set(K k, V v) {

    }

    @Override
    public void set(K k, V v, long l, TimeUnit timeUnit) {

    }

    @Override
    public Boolean setIfAbsent(K k, V v) {
        return null;
    }

    @Override
    public Boolean setIfAbsent(K k, V v, long l, TimeUnit timeUnit) {
        return null;
    }

    @Override
    public Boolean setIfPresent(K k, V v) {
        return null;
    }

    @Override
    public Boolean setIfPresent(K k, V v, long l, TimeUnit timeUnit) {
        return null;
    }

    @Override
    public void multiSet(Map<? extends K, ? extends V> map) {

    }

    @Override
    public Boolean multiSetIfAbsent(Map<? extends K, ? extends V> map) {
        return null;
    }

    @Override
    public V get(Object o) {
        return null;
    }

    @Override
    public V getAndSet(K k, V v) {
        return null;
    }

    @Override
    public List<V> multiGet(Collection<K> collection) {
        return null;
    }

    @Override
    public Long increment(K k) {
        return null;
    }

    @Override
    public Long increment(K k, long l) {
        return null;
    }

    @Override
    public Double increment(K k, double v) {
        return null;
    }

    @Override
    public Long decrement(K k) {
        return null;
    }

    @Override
    public Long decrement(K k, long l) {
        return null;
    }

    @Override
    public Integer append(K k, String s) {
        return null;
    }

    @Override
    public String get(K k, long l, long l1) {
        return null;
    }

    @Override
    public void set(K k, V v, long l) {

    }

    @Override
    public Long size(K k) {
        return null;
    }

    @Override
    public Boolean setBit(K k, long l, boolean b) {
        return null;
    }

    @Override
    public Boolean getBit(K k, long l) {
        return null;
    }

    @Override
    public List<Long> bitField(K k, BitFieldSubCommands bitFieldSubCommands) {
        return null;
    }

    @Override
    public RedisOperations<K, V> getOperations() {
        return null;
    }
}
