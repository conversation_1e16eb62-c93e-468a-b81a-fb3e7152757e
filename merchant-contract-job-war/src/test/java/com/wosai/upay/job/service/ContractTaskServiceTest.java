package com.wosai.upay.job.service;

import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.WeixinAuthApplyBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.MchAuthApplyMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.merchant.contract.service.AuthApplyFlowService;
import com.wosai.upay.merchant.contract.service.WeixinService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;


public class ContractTaskServiceTest extends BaseTest {

    @SpyBean
    private ContractTaskService contractTaskService;

    @MockBean
    ContractStatusMapper contractStatusMapper;
    @MockBean
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @MockBean
    ParamContextBiz paramContextBiz;
    @MockBean
    MerchantService merchantService;
    @MockBean
    MchAuthApplyMapper mchAuthApplyMapper;
    @MockBean
    AuthApplyFlowService authApplyFlowService;
    @MockBean
    TaskResultService taskResultService;
    @MockBean
    RedisLock redisLock;
    @MockBean
    RuleContext ruleContext;
    @MockBean
    WeixinService weixinService;
    @MockBean
    WeixinAuthApplyBiz weixinAuthApplyBiz;


    @Before
    public void before() {
        ReflectionTestUtils.setField(contractTaskService, "merchantService", merchantService);
        ReflectionTestUtils.setField(contractTaskService, "authApplyFlowService", authApplyFlowService);
        Mockito.doReturn(true).when(redisLock).lock(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
    }

    @Test
    public void createSuccessTaskTest() {
        List<MerchantProviderParams> paramsList = new ArrayList<>();
        MerchantProviderParams param = new MerchantProviderParams();
        param.setRule_group_id("lkl");
        paramsList.add(param);
        Mockito.doReturn(paramsList).when(merchantProviderParamsMapper).selectByExample(new MerchantProviderParamsExample());
        contractTaskService.createSuccessTask("21690003181407");
    }
}
