package com.wosai.upay.job.biz.acquirePos;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.*;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.merchant.contract.model.fuyou.response.TermQueryResponse;
import com.wosai.upay.merchant.contract.service.FuyouService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class FyT9HandleServiceTest {

    @Mock
    protected T9HandleFactory factory;
    @Mock
    private TerminalService terminalService;
    @Mock
    private MerchantService merchantService;
    @Mock
    private TradeConfigService tradeConfigService;

    @Mock
    private FeeRateService feeRateService;
    @Mock
    private SupportService supportService;

    @Mock
    private SubBizParamsBiz subBizParamsBiz;
    @Mock
    private FuyouService fuyouService;

    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;
    @Mock
    private ContractStatusService contractStatusService;
    @InjectMocks
    private FyT9HandleService fyT9HandleService;

    @Mock
    private McAcquirerDAO mcAcquirerDAO;


    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;


    private CrmOpenCheckDTO dto;

    @Before
    public void setUp() {
        dto = new CrmOpenCheckDTO();
        dto.setMerchantSn("123456");
        dto.setAcquire("ACQUIRER_CODE");
    }


    @Test
    public void testInnerBindCheckSuccess() {
        // 校验通过
        InnerBindCheckDTO dto = new InnerBindCheckDTO();
        dto.setVendorAppAppid("test_vendorAppAppid");
        dto.setMerchantSn("test_merchantSn");


        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn("testMerchantSn");
        merchant.setId("testMerchantID");
        when(merchantService.getMerchantBySn(any(), any())).thenReturn(merchant);
        List<AcquirerMerchantDto> acquirerMerchantDtos =
                Lists.newArrayList(new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_FUYOU));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(merchant.getId())).thenReturn(acquirerMerchantDtos);

        ContractStatus contractStatus = new ContractStatus();
        contractStatus.setAcquirer(McConstant.ACQUIRER_FUYOU);
        when(contractStatusService.selectByMerchantSn(merchant.getSn())).thenReturn(contractStatus);
        final ArrayList<String> list = Lists.newArrayList("2022071100004879", "2022110300005136");
        when(factory.otherAcquireVenderList(McConstant.ACQUIRER_FUYOU)).thenReturn(list);
        final ListResult listResult = new ListResult();
        when(terminalService.getTerminals(any(), any(), any(), any())).thenReturn(listResult);
        fyT9HandleService.innerBindCheck(dto);
        // 验证方法是否被调用
        verify(factory, times(1)).otherAcquireVenderList(any());
    }

    @Test
    public void testInnerBindCheckFailure() {
        // 校验不通过
        InnerBindCheckDTO dto = new InnerBindCheckDTO();
        dto.setVendorAppAppid("test_vendorAppAppid");
        dto.setMerchantSn("test_merchantSn");


        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn("testMerchantSn");
        merchant.setId("testMerchantID");
        when(merchantService.getMerchantBySn(any(), any())).thenReturn(merchant);
        List<AcquirerMerchantDto> acquirerMerchantDtos =
                Lists.newArrayList(new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_FUYOU));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(merchant.getId())).thenReturn(acquirerMerchantDtos);

        ContractStatus contractStatus = new ContractStatus();
        contractStatus.setAcquirer(McConstant.ACQUIRER_FUYOU);
        when(contractStatusService.selectByMerchantSn(merchant.getSn())).thenReturn(contractStatus);
        final ArrayList<String> list = Lists.newArrayList("2022071100004879", "2022110300005136");
        when(factory.otherAcquireVenderList(McConstant.ACQUIRER_FUYOU)).thenReturn(list);
        final ListResult listResult = new ListResult();
        listResult.setTotal(1);
        when(terminalService.getTerminals(any(), any(), any(), any())).thenReturn(listResult);
        final CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> fyT9HandleService.innerBindCheck(dto));
        assertEquals(PosConstant.EXIST_OTHER_POS, exception.getMessage());
    }


    @Test
    public void testCrmOpenCheckSuccess() {
        // 商户信息和收单机构信息的模拟数据
        MerchantInfo merchantInfo = new MerchantInfo();
        String merchantId = "merchant_id";
        merchantInfo.setId(merchantId);

        AcquirerMerchantDto acquirerMerchantDto = new AcquirerMerchantDto();
        acquirerMerchantDto.setAcquirer("ACQUIRER_CODE");

        List<AcquirerMerchantDto> acquirerMerchantInfo = Arrays.asList(acquirerMerchantDto);

        when(merchantService.getMerchantBySn(dto.getMerchantSn(), null)).thenReturn(merchantInfo);
        when(merchantProviderParamsService.getAcquirerMerchantInfo(merchantId)).thenReturn(acquirerMerchantInfo);
        when(mcAcquirerDAO.getAcquirerName(dto.getAcquire())).thenReturn("收单机构名称");
        when(contractStatusService.selectByMerchantSn(dto.getMerchantSn())).thenReturn(new ContractStatus());
        when(mcAcquirerDAO.getIndirectAcquirerList())
                .thenReturn(Lists.newArrayList(McConstant.ACQUIRER_FUYOU, McConstant.ACQUIRER_LKLV3));

        fyT9HandleService.crmOpenCheck(dto);

        // 验证方法是否被调用
        verify(merchantProviderParamsService, times(1)).getAcquirerMerchantInfo(merchantId);
        verify(factory, times(1)).otherAcquireVenderList(dto.getAcquire());
    }


    @Test
    public void applyPos_success() throws Exception {
        // Arrange
        String merchantSn = "123456";
        String devCode = "dev001";
        String formBody = "{\"contractNo\":598536003190230300,\"bankcard_fee\":{\"debit\":{\"fee\":\"0.4\"},\"credit\":{\"fee\":\"0.55\"}},\"tradeComboId\":3723}\n";
        String payMerchantId = "payMerchantId";

        ApplyPosRequest request = new ApplyPosRequest()
                .setMerchantSn(merchantSn)
                .setDevCode(devCode)
                .setForm_body(formBody);

        MerchantProviderParams acquirerParams = new MerchantProviderParams();
        acquirerParams.setPay_merchant_id(payMerchantId);

        when(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(merchantSn, ProviderEnum.PROVIDER_FUYOU.getValue())).thenReturn(acquirerParams);
        doNothing().when(directStatusBiz).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), anyString());
        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn("testMerchantSn");
        merchant.setId("testMerchantID");
        doReturn(merchant).when(merchantService).getMerchantBySn(any(), any());
        doReturn(new HashMap<>()).when(tradeConfigService).updateBankCardMerchantConfig(anyMap());

        doNothing().when(feeRateService).applyFeeRateOne(anyObject());

        doNothing().when(supportService).removeCachedParams(anyString());
        doReturn("t9TradeAppId").when(subBizParamsBiz).getT9TradeAppId();
        doNothing().when(subBizParamsBiz).updateSubBizParams(anyString(), anyString(), anyInt(), anyObject());

        ContractResponse response = fyT9HandleService.openPos(request);

        assertTrue(response.isSuccess());
        verify(directStatusBiz, times(2)).createOrUpdateDirectStatus(
                eq(merchantSn), eq(devCode), anyInt(), any());
    }


    @Test
    public void getPosActiveInfo_Success() {
        // Arrange
        PosActiveInfoDTO dto = new PosActiveInfoDTO().setTerminalSn("123456");
        Map terminalMap = ImmutableMap.of("merchantId", "merchant_123");
        MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setSn("merchant_123");

        when(terminalService.getTerminalBySn(dto.getTerminalSn())).thenReturn(terminalMap);
        doReturn(merchantInfo).when(merchantService).getMerchantById(any(), any());
        when(contractStatusService.selectByMerchantSn(any())).thenReturn(new ContractStatus().setAcquirer(McConstant.ACQUIRER_FUYOU));
        when(mcAcquirerDAO.getIndirectAcquirerList()).thenReturn(Lists.newArrayList(McConstant.ACQUIRER_FUYOU,McConstant.ACQUIRER_LKLV3));
        doReturn(new DirectStatus().setStatus(DirectStatus.STATUS_SUCCESS)).when(directStatusBiz).getDirectStatusByMerchantSnAndDevCode(any(), any());
        TermQueryResponse termQueryResponse = new TermQueryResponse();
        termQueryResponse.setTermId("acquire_term_123");
        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        contractResponse.setCode(200);
        contractResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(termQueryResponse), Map.class));
        when(fuyouService.termQuery(anyString(), anyString())).thenReturn(contractResponse);
        doReturn(new HashMap<>()).when(tradeConfigService).updateBankCardTerminalConfig(anyMap());
        PosActiveInfo result = fyT9HandleService.getPosActiveInfo(dto);

        // Assert
        assertNotNull(result);
        assertEquals("acquire_term_123", result.getActiveNo());
    }


    @Test
    public void unbind_success() {
        String merchantId = "789012";
        String merchantSn = "345678";
        String deviceFingerprint = "deviceFingerprint";
        String terminalId = "terminalId";

        UnbindDTO dto = new UnbindDTO();
        dto.setMerchantId(merchantId);
        dto.setDeviceFingerprint(deviceFingerprint);
        dto.setTerminalId(terminalId);

        final MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setSn(merchantSn);
        when(merchantService.getMerchantById(any(), any())).thenReturn(merchantInfo);

        doReturn(new HashMap<>()).when(tradeConfigService).updateBankCardTerminalConfig(anyMap());
        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        contractResponse.setCode(200);
        when(fuyouService.termCancel(merchantSn, deviceFingerprint)).thenReturn(contractResponse);
        fyT9HandleService.fyTermCancel(dto);
        verify(fuyouService, times(1)).termCancel(merchantSn, deviceFingerprint);
    }

    @Test
    public void unbind_failure() {
        String merchantId = "789012";
        String deviceFingerprint = "deviceFingerprint";
        String terminalId = "terminalId";

        UnbindDTO dto = new UnbindDTO();
        dto.setMerchantId(merchantId);
        dto.setDeviceFingerprint(deviceFingerprint);
        dto.setTerminalId(terminalId);

        final MerchantInfo merchantInfo = new MerchantInfo();
        merchantInfo.setSn("merchantSn");
        when(merchantService.getMerchantById(any(), any())).thenReturn(merchantInfo);

        final com.wosai.upay.merchant.contract.model.ContractResponse contractResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        contractResponse.setCode(400);
        String message = "模拟失败";
        contractResponse.setMessage(message);
        when(fuyouService.termCancel(any(), any())).thenReturn(contractResponse);

        final CommonPubBizException pubBizException = assertThrows(CommonPubBizException.class, () -> fyT9HandleService.fyTermCancel(dto));
        assertEquals(pubBizException.getMessage(), message);
    }


}
