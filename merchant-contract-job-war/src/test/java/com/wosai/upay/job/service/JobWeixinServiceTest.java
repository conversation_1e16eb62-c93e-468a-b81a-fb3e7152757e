package com.wosai.upay.job.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.info.api.service.IndustryV2Service;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.model.SettlementIdConfig;
import com.wosai.upay.job.model.acquirer.JobContractChannel;
import com.wosai.upay.job.model.wechatAuth.WxMchInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * @Description: WeixinServiceTest
 * <AUTHOR>
 * @Date 2021/11/5 3:03 下午
 **/

@RunWith(SpringRunner.class)
@SpringBootTest
public class JobWeixinServiceTest {

    @Autowired
    JobWeixinService service;

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void getSettlementId() {
        String settlementId = service.getSettlementId("21690003166500");
        System.out.println(settlementId);
    }

    @Test
    public void getSettlementIdV2() {
        String settlementId = service.getSettlementId("上海市静安区凯臻理发店", "23e9fce5-4ecd-4c06-84e8-1de72a2c5f09", 1);
        System.out.println(settlementId);
    }

    @Test
    public void getSettlementConfig() {
        IndustryV2Service industryV2Service = applicationContext.getBean(IndustryV2Service.class);
        RuleBiz ruleBiz = applicationContext.getBean(RuleBiz.class);
        IndustryV2Service mockIndustryService = Mockito.mock(IndustryV2Service.class);
        ApplicationApolloConfig mockApollo = Mockito.mock(ApplicationApolloConfig.class);
        ReflectionTestUtils.setField(ruleBiz, "industryV2Service", mockIndustryService);
        ReflectionTestUtils.setField(ruleBiz, "config", mockApollo);

        String industryId = "industry_id";
        Mockito.doReturn(CollectionUtil.hashMap("code2", "003024")).when(mockIndustryService).getIndustry(industryId);
        // 1. 没有配置
        SettlementIdConfig result = service.getSettlementConfig(industryId);
        Assert.assertNull(result);
        // 2. 有配置
        Mockito.doReturn("{\"003023\":{\"individual\":\"762\",\"others\":\"769\"},\"003024\":{\"institutions\":\"765\",\"individual\":\"762\",\"person\":\"770\",\"enterprise\":\"758\",\"others\":\"767\"}}")
                        .when(mockApollo).getWeixinIndirectIndustry();
        result = service.getSettlementConfig(industryId);
        Assert.assertEquals("765", result.getInstitutions());

        ReflectionTestUtils.setField(ruleBiz, "industryV2Service", industryV2Service);
    }

    @Test
    public void getWxMchInfoBySubMchId() {
        WxMchInfo wxMchInfo = service.getWxMchInfoBySubMchId("1642068686017");
        System.out.println(wxMchInfo);
    }


    @Autowired
    RuleService ruleService;

    @Test
    public void getContractRule() {
        JobContractChannel contractChannel = ruleService.getContractChannel("ums-1018-3");
        System.out.println(contractChannel);
    }

    @Autowired
    ResubmitService resubmitService;

    @Test
    public void resubmit() {
        resubmitService.reSubmitByTaskId(16422500L);
    }
}