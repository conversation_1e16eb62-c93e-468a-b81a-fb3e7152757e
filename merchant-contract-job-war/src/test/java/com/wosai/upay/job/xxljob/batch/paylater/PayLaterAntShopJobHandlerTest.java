package com.wosai.upay.job.xxljob.batch.paylater;

import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterAntShopJobHandlerTest {

    @InjectMocks
    private PayLaterAntShopJobHandler payLaterAntShopJobHandler;

    @Mock
    private PayLaterBiz payLaterBiz;

    @Mock
    private BatchJobParam batchJobParam;

    @Mock
    private PayLaterApplyMapper payLaterApplyMapper;

    @Mock
    private PayLaterApply payLaterApply;

    private List<PayLaterApply> expectedPayLaterApplies;
    private PayLaterApply payLaterApplyB;

    @Before
    public void setUp() {
        expectedPayLaterApplies = Arrays.asList(
                new PayLaterApply().setId(1L),
                new PayLaterApply().setId(2L)
        );
        payLaterApplyB = new PayLaterApply();
        payLaterApplyB.setId(123L);
        when(payLaterApply.getId()).thenReturn(1L);
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsExpectedList() {
        when(batchJobParam.getBatchSize()).thenReturn(10);
        when(batchJobParam.getQueryTime()).thenReturn(System.currentTimeMillis());
        when(payLaterBiz.getPayLaterTasks(
                Mockito.eq(Collections.singletonList(PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING)),
                Mockito.eq(10),
                Mockito.anyLong()
        )).thenReturn(expectedPayLaterApplies);

        List<PayLaterApply> result = payLaterAntShopJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedPayLaterApplies, result);
    }

    @Test
    public void getLockKey_ValidInput_ReturnsCorrectLockKey() {
        String expectedLockKey = "PayLaterAntShopJobHandler:123";
        String actualLockKey = payLaterAntShopJobHandler.getLockKey(payLaterApplyB);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsLockKeyWithNullId() {
        payLaterApplyB.setId(null);
        String expectedLockKey = "PayLaterAntShopJobHandler:null";
        String actualLockKey = payLaterAntShopJobHandler.getLockKey(payLaterApplyB);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_ProcessStatusNotAntShopApplying_NoAction() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZFT_APPLYING);
        when(payLaterApplyMapper.selectByPrimaryKey(1L)).thenReturn(apply);

        payLaterAntShopJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, never()).handleAntShop(any(PayLaterApply.class));
    }

    @Test
    public void doHandleSingleData_ProcessStatusAntShopApplying_HandleAntShopCalled() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING);
        when(payLaterApplyMapper.selectByPrimaryKey(1L)).thenReturn(apply);

        payLaterAntShopJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, times(1)).handleAntShop(payLaterApply);
    }

    @Test
    public void doHandleSingleData_ExceptionThrown_ModifyPayLaterApplyCalled() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ANT_SHOP_APPLYING);
        when(payLaterApplyMapper.selectByPrimaryKey(1L)).thenReturn(apply);
        doThrow(new RuntimeException("Test Exception")).when(payLaterBiz).handleAntShop(payLaterApply);

        payLaterAntShopJobHandler.doHandleSingleData(payLaterApply);

        verify(payLaterBiz, times(1)).modifyPayLaterApply(
                eq(payLaterApply),
                eq(PayLaterConstant.Status.ANT_SHOP_FAIL),
                eq(PayLaterConstant.SubStatus.FAIL),
                eq(PayLaterConstant.ProcessStatus.FAIL),
                eq(PayLaterConstant.Result.ZHIMA_AUDIT_FAIL),
                eq(0)
        );
    }
}
