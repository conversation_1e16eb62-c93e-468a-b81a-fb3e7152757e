package com.wosai.upay.job.util;

import org.junit.Test;
import org.springframework.core.io.support.PropertiesLoaderUtils;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
public class CheckPropertiesUtilTest {

    /**
     * 校验beta配置文件和prod配置文件是否有不同的地方
     */
    @Test
    public void checkProperties() throws IOException {
        Properties betaProperties = PropertiesLoaderUtils.loadAllProperties("application-beta.properties");
        Properties defaultProperties = PropertiesLoaderUtils.loadAllProperties("application-default.properties");
        Properties unitProperties = PropertiesLoaderUtils.loadAllProperties("application-unit.properties");
        Properties prodProperties = PropertiesLoaderUtils.loadAllProperties("application-prod.properties");
        for (Object o : betaProperties.keySet()) {
            if (!prodProperties.containsKey(o)) {
                System.out.println("prod不包含:" + o);
            }
            if (!defaultProperties.containsKey(o)) {
                System.out.println("default不包含:" + o);
            }
            if (!unitProperties.containsKey(o)) {
                System.out.println("unit不包含:" + o);
            }
        }
    }
}
