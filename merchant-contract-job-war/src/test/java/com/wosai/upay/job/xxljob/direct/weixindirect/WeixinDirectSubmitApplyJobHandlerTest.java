package com.wosai.upay.job.xxljob.direct.weixindirect;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.SensorSendBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectExecTypeEnum;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.service.WeiXinDirectService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class WeixinDirectSubmitApplyJobHandlerTest {

    @InjectMocks
    private WeixinDirectSubmitApplyJobHandler handler;

    @Mock
    private WeiXinDirectService weiXinDirectService;

    @Mock
    private MonitorLog monitorLog;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private Environment environment;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private WeixinDirectApplyMapper applyMapper;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private ContractTaskBiz taskBiz;

    @Mock
    private ContractTaskMapper taskMapper;

    @Mock
    private TransactionTemplate transactionTemplate;

    private String weixinDirectOnline = "weixinDirectOnline";

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(handler, "weixinDirectOnline", weixinDirectOnline);
    }

    @Test
    public void submitApplySuccess() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.UN_SUBMIT.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 30000));
        ContractResponse response = new ContractResponse();
        response.setCode(200);
        Mockito.doReturn(response).when(weiXinDirectService).applyment(any(), any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);

        handler.execute(directJobParam);
        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());
    }

    @Test
    public void submitApplySysException() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.UN_SUBMIT.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        ContractResponse response = new ContractResponse();
        response.setCode(500);
        Mockito.doReturn(response).when(weiXinDirectService).applyment(any(), any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, Mockito.times(1)).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        Mockito.verify(chatBotUtil, Mockito.times(1)).sendMessageToContractWarnChatBot(any());

    }

    @Test
    public void submitApplyFail() {
        WeixinDirectApply apply = new WeixinDirectApply();
        apply.setMerchant_sn("merchant_sn").setTask_id(111L).setStatus(WeixinDirectApplyStatus.UN_SUBMIT.getVal()).setDev_code(weixinDirectOnline)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        ContractResponse response = new ContractResponse();
        response.setCode(405);
        Mockito.doReturn(response).when(weiXinDirectService).applyment(any(), any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPriorityAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());
    }

    @Test
    public void generateBusinessCode02() {
        WeixinDirectApply weixinDirectApply = new WeixinDirectApply();
        weixinDirectApply.setMerchant_sn("merchant_sn");
        weixinDirectApply.setId(111L);
        weixinDirectApply.setDev_code(weixinDirectOnline);

        // 1. 有失败的申请单,response_body是撤销, 得到的business_code是新的
        WeixinDirectApply latestFail = new WeixinDirectApply();
        latestFail.setMerchant_sn(weixinDirectApply.getMerchant_sn());
        latestFail.setDev_code(weixinDirectOnline);
        latestFail.setResponse_body("{\"applyment_state\":\"APPLYMENT_STATE_CANCELED\"}");
        latestFail.setRequest_body("{\"business_code\":\"testmerchant_sn110\"}");
        latestFail.setStatus(WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
        latestFail.setSubmit_type(1);
        Mockito.doReturn(latestFail).when(applyMapper).selectLatestFailedApplyByMerchantSn(any(), any(), any());
        Mockito.doReturn(new String[]{"test"}).when(environment).getActiveProfiles();
        String businessCode = ReflectionTestUtils.invokeMethod(handler, "generateBusinessCode", weixinDirectApply);
        assertEquals("testmerchant_sn111", businessCode);
    }

    @Test
    public void generateBusinessCode03() {
        WeixinDirectApply weixinDirectApply = new WeixinDirectApply();
        weixinDirectApply.setMerchant_sn("merchant_sn");
        weixinDirectApply.setId(111L);
        weixinDirectApply.setDev_code(weixinDirectOnline);

        // 1. 有失败的申请单,response_body不是撤销, 得到的business_code是旧的那个
        WeixinDirectApply latestFail = new WeixinDirectApply();
        latestFail.setMerchant_sn(weixinDirectApply.getMerchant_sn());
        latestFail.setDev_code(weixinDirectOnline);
        latestFail.setResponse_body("{\"applyment_state\":\"APPLYMENT_STATE_REJECTED\"}");
        latestFail.setRequest_body("{\"business_code\":\"testmerchant_sn110\"}");
        latestFail.setStatus(WeixinDirectApplyStatus.APPLY_REJECTED.getVal());
        latestFail.setSubmit_type(1);
        Mockito.doReturn(latestFail).when(applyMapper).selectLatestFailedApplyByMerchantSn(any(), any(), any());
        Mockito.doReturn(new String[]{"test"}).when(environment).getActiveProfiles();
        String businessCode = ReflectionTestUtils.invokeMethod(handler, "generateBusinessCode", weixinDirectApply);
        assertEquals("testmerchant_sn110", businessCode);
    }
}
