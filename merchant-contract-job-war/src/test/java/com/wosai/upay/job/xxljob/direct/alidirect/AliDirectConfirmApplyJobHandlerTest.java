package com.wosai.upay.job.xxljob.direct.alidirect;

import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenAgentConfirmRequest;
import com.alipay.api.response.AlipayOpenAgentConfirmResponse;
import com.google.common.collect.Maps;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.AliDirectParamBuilder;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;

@RunWith(MockitoJUnitRunner.class)
public class AliDirectConfirmApplyJobHandlerTest {

    @InjectMocks
    private AliDirectConfirmApplyJobHandler handler;

    @Mock
    private AliPayDirectService aliPayDirectService;

    @Mock
    private AliDirectParamBuilder aliParamBuilder;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private AliDirectApplyMapper applyMapper;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private ContractTaskMapper taskMapper;

    @Mock
    private ContractTaskBiz taskBiz;

    @Mock
    private MonitorLog monitorLog;

    @Value("${ali.direct}")
    private String aliDirectDevCode;


    @Test
    public void confirmApplySuccess() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.ALREADY_SUBMITTED.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));

        AliCommResponse<AlipayOpenAgentConfirmRequest, AlipayOpenAgentConfirmResponse> response = new AliCommResponse<>();
        AlipayOpenAgentConfirmResponse createResponse = new AlipayOpenAgentConfirmResponse();
        createResponse.setUserId("user_id");
        response.setCode(200).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentConfirm(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void confirmApplySysException() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.ALREADY_SUBMITTED.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        AliCommResponse<AlipayOpenAgentConfirmRequest, AlipayOpenAgentConfirmResponse> response = new AliCommResponse<>();
        AlipayOpenAgentConfirmResponse createResponse = new AlipayOpenAgentConfirmResponse();
        createResponse.setUserId("user_id");
        response.setCode(500).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentConfirm(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, times(1)).updateByPrimaryKeySelective(any());
        Mockito.verify(monitorLog, times(1)).recordMonitor(any(), any());
    }

    @Test
    public void confirmApplyFail() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.ALREADY_SUBMITTED.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000));
        AliCommResponse<AlipayOpenAgentConfirmRequest, AlipayOpenAgentConfirmResponse> response = new AliCommResponse<>();
        AlipayOpenAgentConfirmResponse createResponse = new AlipayOpenAgentConfirmResponse();
        createResponse.setUserId("user_id");
        response.setCode(405).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentConfirm(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(transactionTemplate, times(1)).executeWithoutResult(any());
    }

}