package com.wosai.upay.job.providers;
import java.util.Date;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.merchant.contract.model.psbc.bo.MerchantStatusCallBack;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class PsbcProviderTest extends BaseTest {

    @Autowired
    private PsbcProvider psbcProvider;

    @Test
    public void psbcResponseHandleTest() {
        ContractSubTask contractSubTask = new ContractSubTask();
        contractSubTask.setId(42803277L);
        contractSubTask.setP_task_id(0L);
        contractSubTask.setMerchant_sn("21690003315938");
        contractSubTask.setChannel("psbc");
        contractSubTask.setDefault_channel(0);
        contractSubTask.setChange_config(0);
        contractSubTask.setChange_body(0);
        contractSubTask.setTask_type(5);
        contractSubTask.setContract_id("");
        contractSubTask.setPayway(0);
        contractSubTask.setSchedule_status(ScheduleEnum.SCHEDULE_DISABLE.getValue());
        contractSubTask.setSchedule_dep_task_id(0L);
        contractSubTask.setStatus(1);
        contractSubTask.setStatus_influ_p_task(0);
        contractSubTask.setPriority(new Date());
        contractSubTask.setCreate_at(new Date());
        contractSubTask.setUpdate_at(new Date());
        contractSubTask.setVersion(0L);
        contractSubTask.setRequest_body("");
        contractSubTask.setResponse_body("{\"merchantProviderParamsId\":\"5c8139d1-5279-48a4-bcca-47ce5e2fe097\",\"responseParam\":{\"code\":\"200\",\"data\":{\"respDesc\":\"成功\",\"merInnerId\":\"61000026220427001625\",\"respCd\":\"000000\"},\"message\":\"交易成功！\"},\"tradeParam\":{\"contractId\":\"61000026220427001625\"}}");
        contractSubTask.setResult("银行已审核test");
        contractSubTask.setContract_rule("");
        contractSubTask.setRetry(0);
        contractSubTask.setRule_group_id("");

        MerchantStatusCallBack merchantStatusCallBack = new MerchantStatusCallBack();
        merchantStatusCallBack.setSupplierId("");
        merchantStatusCallBack.setReqTraceId("");
        merchantStatusCallBack.setReqDate("");
        merchantStatusCallBack.setSign("");
        merchantStatusCallBack.setMerId("100611000013898");
        merchantStatusCallBack.setOutMerId("");
        merchantStatusCallBack.setMerSta("01");
        merchantStatusCallBack.setBackReason("");

        psbcProvider.psbcResponseHandle(contractSubTask, merchantStatusCallBack);
    }

}
