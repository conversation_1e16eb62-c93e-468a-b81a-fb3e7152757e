package com.wosai.upay.job.scheduler;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.adapter.apollo.model.MailConfigModel;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.externalservice.mail.MailClient;
import com.wosai.upay.job.externalservice.mail.model.MailSendReq;
import com.wosai.upay.job.externalservice.mail.model.MailSendResp;
import com.wosai.upay.job.refactor.dao.McChannelDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyAliBO;
import com.wosai.upay.job.refactor.model.bo.OnlinePaymentApplyExtraBO;
import com.wosai.upay.job.refactor.model.entity.McChannelDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.job.util.DateUtil;
import com.wosai.upay.job.util.ExcelUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OnlinePaymentApplyScheduleTest {

    @InjectMocks
    private OnlinePaymentApplySchedule onlinePaymentApplySchedule;
    @Mock
    private MerchantService mockMerchantService;
    @Mock
    private RedisLock redisLock;
    @Mock
    private DateUtil dateUtil;
    @Mock
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;
    @Mock
    private OnlinePaymentBiz onlinePaymentBiz;
    @Mock
    private MailClient mailClient;
    @Mock
    private ApplicationApolloConfig apolloConfig;

    private MailConfigModel mailConfigModel = JSON.parseObject("{\n" +
            "    \"onlineLkl\": {\n" +
            "        \"id\": \"191\",\n" +
            "        \"to\": \"<EMAIL>,<EMAIL>\",\n" +
            "        \"content\": \"<p>您好：<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;附件为我司申请支付宝谷雨计划的商户明细，烦请查收处理并邮件回复处理结果，谢谢</p>\",\n" +
            "        \"enable\": true\n" +
            "    },\n" +
            "    \"onlineHaike\": {\n" +
            "        \"id\": \"192\",\n" +
            "        \"to\": \"<EMAIL>,<EMAIL>\",\n" +
            "        \"content\": \"<p>您好：<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;附件为我司申请支付宝谷雨计划的商户明细，烦请查收处理并邮件回复处理结果，谢谢</p>\",\n" +
            "        \"enable\": true\n" +
            "    },\n" +
            "    \"onlineSqb\": {\n" +
            "        \"id\": \"286\",\n" +
            "        \"to\": \"<EMAIL>,<EMAIL>\",\n" +
            "        \"enable\": true\n" +
            "    }\n" +
            "}", MailConfigModel.class);


    /**
     * 重新报备 已经有了对应的子商户号
     */
    @Test
    public void reContractAndSubmitAuth01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(Collections.singletonList(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryWaitForReContractApplies();

        onlinePaymentApplySchedule.reContractAndSubmitAuth();
        verify(onlinePaymentBiz, times(1)).reContractAndSubmitAuth(openOnlinePaymentApplyDO);

    }

    /**
     * 需要重新报备 报备成功
     */
    @Test
    public void reContractAndSubmitAuth02() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.PENDING);

        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(Collections.singletonList(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryWaitForReContractApplies();
        ContractBizException exception = new ContractBizException("测试提交失败");
        Mockito.doThrow(exception).when(onlinePaymentBiz).reContractAndSubmitAuth(openOnlinePaymentApplyDO);

        onlinePaymentApplySchedule.reContractAndSubmitAuth();
        verify(onlinePaymentBiz, times(1)).reContractAndSubmitAuth(openOnlinePaymentApplyDO);
        verify(onlinePaymentBiz, times(1)).processApplyException(openOnlinePaymentApplyDO, OnlinePaymentConstant.ApplyProcessStatus.CONTRACT_FAIL, exception);

    }

    /**
     * 授权通过
     */
    @Test
    public void queryWeixinAuth01() {
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUTH);
        openOnlinePaymentApplyDO.setExtra("{\"weixin\":{\"payMerchantId\":\"payMerchantId\"}}");
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(Collections.singletonList(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryWaitForAuthApplies();

        onlinePaymentApplySchedule.queryAuth();
        verify(onlinePaymentBiz, times(1)).queryAuthStatusAndSetDefault(openOnlinePaymentApplyDO);

    }

    @Test
    public void sendEmail() {
        OnlinePaymentApplyExtraBO onlinePaymentApplyExtraBO = new OnlinePaymentApplyExtraBO();
        OnlinePaymentApplyAliBO onlinePaymentApplyAliBO = new OnlinePaymentApplyAliBO();
        onlinePaymentApplyAliBO.setPayMerchantId("payMerchantId");
        onlinePaymentApplyExtraBO.setAli(onlinePaymentApplyAliBO);
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setId(1L);
        openOnlinePaymentApplyDO.setMerchantSn("merchantSn");
        openOnlinePaymentApplyDO.setPayway(PaywayEnum.ALIPAY.getValue());
        openOnlinePaymentApplyDO.setExtra(JSON.toJSONString(onlinePaymentApplyExtraBO));
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        openOnlinePaymentApplyDO.setProcessStatus(OnlinePaymentConstant.ApplyProcessStatus.WAIT_FOR_AUDIT);
        openOnlinePaymentApplyDO.setAcquirer("lklV3");

        MerchantProviderParamsDO merchantProviderParamsDO = new MerchantProviderParamsDO();
        merchantProviderParamsDO.setMerchantName("商户_张三");
        merchantProviderParamsDO.setProviderMerchantId("providerMerchantId");
        merchantProviderParamsDO.setPayMerchantId("payMerchantId");
        merchantProviderParamsDO.setPayway(PaywayEnum.ALIPAY.getValue());
        merchantProviderParamsDO.setProvider(ProviderEnum.PROVIDER_LKLORG.getValue());
        merchantProviderParamsDO.setChannelNo("channelNo");

        Map<String, MerchantProviderParamsDO> map = new HashMap<>();
        map.put("payMerchantId", merchantProviderParamsDO);
        Mockito.doReturn(map).when(merchantProviderParamsDAO).getMerchantProviderParamsByPayMerchantIdList(anyList());
        Mockito.doReturn(true).when(redisLock).lock(anyString(), anyString(), anyLong());
        Mockito.doReturn(Collections.singletonList(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryAliWaitForAuditApplies();
        when(apolloConfig.getMailConfig()).thenReturn(mailConfigModel);

        onlinePaymentApplySchedule.sendEmail();

        verify(mailClient, times(1)).sendEmail(any());
    }

    @Test
    public void sendHaikeEmail_LockFailed_ReturnsEarly() {
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(false);

        onlinePaymentApplySchedule.sendHaikeEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verifyNoMoreInteractions(redisLock, openOnlinePaymentApplyDAO, merchantProviderParamsDAO, dateUtil, mailClient);
    }

    @Test
    public void sendHaikeEmail_EmptyData_LogsAndReturns() throws IOException {
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(true);

        onlinePaymentApplySchedule.sendHaikeEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verify(redisLock, times(1)).unlock(anyString(), anyString());
        verifyNoMoreInteractions(openOnlinePaymentApplyDAO, merchantProviderParamsDAO, mailClient);
    }

    @Test
    public void sendHaikeEmail_WithContent_SendsEmail() throws IOException {
        String[][] contentArray = new String[][]{
                {"01/01/2023", "payMerchantId1", "Merchant1", "新增", "providerMerchantId1"},
                {"01/02/2023", "payMerchantId2", "Merchant2", "新增", "providerMerchantId2"}
        };

        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(true);
        when(dateUtil.getNonWorkingDaysBefore(LocalDate.now())).thenReturn(Collections.singletonList(LocalDate.now()));
        when(openOnlinePaymentApplyDAO.queryAliWaitForAuditAppliesByPriorityAndAcquirer(any(), any(), anyString()))
                .thenReturn(prepareMockApplyList(contentArray));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantIdList(anyList()))
                .thenReturn(prepareMockParamsMap(contentArray));
        when(mockMerchantService.findSimpleMerchants(any(), any())).thenReturn(prepareMockMerchantResult(contentArray));

        MailSendResp mailSendResp = MailSendResp.success();
        when(mailClient.sendEmail(any(MailSendReq.class))).thenReturn(mailSendResp);
        when(apolloConfig.getMailConfig()).thenReturn(mailConfigModel);
        onlinePaymentApplySchedule.sendHaikeEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verify(redisLock, times(1)).unlock(anyString(), anyString());
        verify(dateUtil, times(1)).getNonWorkingDaysBefore(any());
        verify(openOnlinePaymentApplyDAO, times(1)).queryAliWaitForAuditAppliesByPriorityAndAcquirer(any(), any(), anyString());
        verify(merchantProviderParamsDAO, times(1)).getMerchantProviderParamsByPayMerchantIdList(anyList());
        verify(mockMerchantService, times(1)).findSimpleMerchants(any(), any());
        verify(mailClient, times(1)).sendEmail(any(MailSendReq.class));
    }

    @Test
    public void sendLklEmail_LockFailed_ReturnsEarly() {
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(false);

        onlinePaymentApplySchedule.sendLklEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verifyNoMoreInteractions(redisLock, openOnlinePaymentApplyDAO, merchantProviderParamsDAO, dateUtil, mailClient);
    }

    @Test
    public void sendLklEmail_EmptyData_LogsAndReturns() throws IOException {
        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(true);
        onlinePaymentApplySchedule.sendLklEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verify(redisLock, times(1)).unlock(anyString(), anyString());
        verifyNoMoreInteractions(openOnlinePaymentApplyDAO, merchantProviderParamsDAO, mailClient);
    }

    @Test
    public void sendLklEmail_WithContent_SendsEmail() throws IOException {
        String[][] contentArray = new String[][]{
                {"01/01/2023", "payMerchantId1", "Merchant1", "新增", "providerMerchantId1"},
                {"01/02/2023", "payMerchantId2", "Merchant2", "新增", "providerMerchantId2"}
        };

        when(redisLock.lock(anyString(), anyString(), anyLong())).thenReturn(true);
        when(dateUtil.getNonWorkingDaysBefore(LocalDate.now())).thenReturn(Collections.singletonList(LocalDate.now()));
        when(openOnlinePaymentApplyDAO.queryAliWaitForAuditAppliesByPriorityAndAcquirer(any(), any(), anyString()))
                .thenReturn(prepareMockApplyList(contentArray));
        when(merchantProviderParamsDAO.getMerchantProviderParamsByPayMerchantIdList(anyList()))
                .thenReturn(prepareMockParamsMap(contentArray));
        when(mockMerchantService.findSimpleMerchants(any(), any())).thenReturn(prepareMockMerchantResult(contentArray));

        MailSendResp mailSendResp = MailSendResp.success();
        when(mailClient.sendEmail(any(MailSendReq.class))).thenReturn(mailSendResp);
        when(apolloConfig.getMailConfig()).thenReturn(mailConfigModel);

        onlinePaymentApplySchedule.sendLklEmail(LocalDateTime.now());

        verify(redisLock, times(1)).lock(anyString(), anyString(), anyLong());
        verify(redisLock, times(1)).unlock(anyString(), anyString());
        verify(dateUtil, times(1)).getNonWorkingDaysBefore(any());
        verify(openOnlinePaymentApplyDAO, times(1)).queryAliWaitForAuditAppliesByPriorityAndAcquirer(any(), any(), anyString());
        verify(merchantProviderParamsDAO, times(1)).getMerchantProviderParamsByPayMerchantIdList(anyList());
        verify(mockMerchantService, times(1)).findSimpleMerchants(any(), any());
        verify(mailClient, times(1)).sendEmail(any(MailSendReq.class));
    }

    private List<OpenOnlinePaymentApplyDO> prepareMockApplyList(String[][] contentArray) {
        List<OpenOnlinePaymentApplyDO> applyList = new ArrayList<>();
        for (String[] content : contentArray) {
            OpenOnlinePaymentApplyDO apply = new OpenOnlinePaymentApplyDO();
            apply.setExtra(String.format("{\"ali\":{\"payMerchantId\":\"%s\"}}", content[1]));
            apply.setMerchantSn(content[2]);
            applyList.add(apply);
        }
        return applyList;
    }

    private Map<String, MerchantProviderParamsDO> prepareMockParamsMap(String[][] contentArray) {
        Map<String, MerchantProviderParamsDO> paramsMap = new HashMap<>();
        for (String[] content : contentArray) {
            MerchantProviderParamsDO params = new MerchantProviderParamsDO();
            params.setProviderMerchantId(content[4]);
            paramsMap.put(content[1], params);
        }
        return paramsMap;
    }

    private ListResult prepareMockMerchantResult(String[][] contentArray) {
        ListResult listResult = new ListResult();
        List<Map> records = new ArrayList<>();
        for (String[] content : contentArray) {
            Map<String, String> record = new HashMap<>();
            record.put("name", "测试");
            record.put("sn", content[2]);
            records.add(record);
        }
        listResult.setRecords(records);
        return listResult;
    }
}