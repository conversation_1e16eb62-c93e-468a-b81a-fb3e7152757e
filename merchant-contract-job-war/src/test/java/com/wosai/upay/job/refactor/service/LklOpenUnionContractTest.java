package com.wosai.upay.job.refactor.service;

import com.google.common.collect.Lists;
import com.shouqianba.cua.utils.json.JSON;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ContractChannel;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.providers.LklOpenProvider;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.refactor.dao.MerchantProviderParamsDAO;
import com.wosai.upay.job.refactor.model.bo.LklOpenUnionPayTradeParamBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.repository.ProviderTerminalTaskRepository;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * lkl云闪付进件测试
 *
 * <AUTHOR>
 * @date 2024/4/17 10:11
 */
public class LklOpenUnionContractTest extends BaseTest {

    private String lklV3ContractRes = "{\"ver\":\"1.0.0\",\"callback_msg\":[{\"code\":\"000000\",\"data\":{\"contractId\":\"202404172673478643\",\"contractStatus\":\"WAIT_FOR_CONTACT\",\"merCupNo\":\"822333053310E4R\",\"merInnerNo\":\"*******************\",\"orderNo\":\"2475872024041711492932600000\",\"orgCode\":\"247587\",\"termDatas\":[{\"activeNo\":\"748875311040\",\"busiStatus\":\"VALID\",\"busiTypeCode\":\"QR_CODE_CARD\",\"busiTypeName\":\"扫码\",\"devSerialNo\":\"1580000008197805\",\"productCode\":\"SQB_SCAN_CODE\",\"productId\":200701,\"productName\":\"收钱吧扫码\",\"shopId\":\"832945871930568704\",\"termId\":\"832945871959928832\",\"termNo\":\"J8875311\"}]},\"message\":\"成功\",\"callback_time\":1713325772004}],\"appid\":\"\",\"respData\":{\"orderNo\":\"2475872024041711492932600000\",\"orgCode\":\"247587\",\"contractId\":\"202404172673478643\"},\"retCode\":\"000000\",\"retMsg\":\"交易成功\",\"call_lkl_time\":1713325769543,\"cmdRetCode\":\"GLOBAL_SUCCESS\",\"reqId\":\"\",\"timestamp\":1713325769526}";

    @Mock
    private ContractSubTaskDAO contractSubTaskDAO;

    @Mock
    private ProviderTerminalTaskRepository providerTerminalTaskRepository;

    @Mock
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Mock
    private MerchantService merchantService;

    @Mock
    private TradeConfigService tradeConfigService;

    @InjectMocks
    private LklOpenProvider lklOpenProvider;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetLklOpenUnionPayTradeParamByMerchantConfig() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String merchantSn = "testMerchantSn";
        String merchantId = "mockedMerchantId";
        Map<String, Object> mockMerchant = new HashMap<>();
        mockMerchant.put("id", merchantId);
        when(merchantService.getMerchantBySn(merchantSn)).thenReturn(mockMerchant);
        Map<String, Object> mockMerchantConfig = new HashMap<>();
        String config = "{\n" +
                "  \"switches\": {\n" +
                "    \"pay_status\": 1\n" +
                "  },\n" +
                "  \"clearance_provider\": 2,\n" +
                "  \"lakala_trade_params\": {\n" +
                "    \"lakala_merc_id\": \"8224930565102ZB\",\n" +
                "    \"lakala_term_id\": \"H9185126\"\n" +
                "  },\n" +
                "  \"hk_trade_params\": {\n" +
                "    \"hk_mch_id\": \"833F494156990177\"\n" +
                "  },\n" +
                "  \"merchant_daily_max_sum_of_trans\": 300000,\n" +
                "  \"category_merchant_single_max_of_tran\": {}\n" +
                "}";
        mockMerchantConfig.put("params", JSON.parseObject(config, Map.class));
        when(tradeConfigService.getMerchantConfigByMerchantIdAndPayway(merchantId, null)).thenReturn(mockMerchantConfig);
        Method method = LklOpenProvider.class.getDeclaredMethod("getLklOpenUnionPayTradeParamByMerchantConfig", String.class);
        method.setAccessible(true);
        Object result = method.invoke(lklOpenProvider, merchantSn);
        assertEquals(result.getClass(), LklOpenUnionPayTradeParamBO.class);
        LklOpenUnionPayTradeParamBO tradeParam = (LklOpenUnionPayTradeParamBO) result;
        assertEquals("H9185126", tradeParam.getTermId());
        assertEquals("8224930565102ZB", tradeParam.getProviderMerchantId());
    }

    @Test
    public void testContract() {
        String merchantSn = "testMerchantSn";
        String merchantId = "mockedMerchantId";
        Map<String, Object> mockMerchant = new HashMap<>();
        mockMerchant.put("id", merchantId);
        when(merchantService.getMerchantBySn(merchantSn)).thenReturn(mockMerchant);
        ContractSubTaskDO lklV3TypeInTask = new ContractSubTaskDO();
        lklV3TypeInTask.setMerchantSn(merchantSn);
        lklV3TypeInTask.setResponseBody(lklV3ContractRes);
        when(contractSubTaskDAO.listContractSubTaskDOs(any(), any(), any(), any())).thenReturn(Lists.newArrayList(lklV3TypeInTask));
        when(merchantProviderParamsDAO.getMerChantProviderParams(any(), any(), any(), any())).thenReturn(Optional.empty());
        when(merchantProviderParamsDAO.saveMerchantParameters(any())).thenReturn(1);
        doNothing().when(providerTerminalTaskRepository).addBoundTerminalTask(any(), any(), any(Integer.class), any(Integer.class), any(Integer.class), any(),any(), any());
        ContractTask contractTask = new ContractTask();
        contractTask.setMerchant_sn(merchantSn);
        ContractChannel contractChannel = new ContractChannel();
        contractChannel.setChannel_no("247587");
        contractChannel.setChannel("lklV3");
        ContractSubTask contractSubTask = new ContractSubTask();
        contractSubTask.setPayway(17);
        ContractResponse contractResponse = lklOpenProvider.processInsertTaskByRule(contractTask, contractChannel, contractSubTask);
        assertThat(contractResponse).isNotNull();
        Assert.assertEquals(contractResponse.getCode(), 200);
    }
}
