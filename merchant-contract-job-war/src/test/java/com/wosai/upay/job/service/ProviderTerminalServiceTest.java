package com.wosai.upay.job.service;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.model.ProviderTerminalBindConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description: ProviderTerminalServiceTest
 * <AUTHOR>
 * @Date 2023/4/17 16:08
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProviderTerminalServiceTest {

    @Autowired
    ProviderTerminalSerivce serivce;

    @Test
    public void syncAcquireBindConfig(){
        ProviderTerminalBindConfig providerTerminalBindConfig = serivce.syncAcquireBindConfig("37677342-437d-47f9-a257-ed1a0ada6670");
    }

}