package com.wosai.upay.job.refactor.dao;

import com.github.pagehelper.Page;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.dto.ContractRuleDto;
import com.wosai.upay.job.refactor.model.entity.McContractRuleDO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

public class McContractRuleDAOTest extends BaseTest {

    @Autowired
    private McContractRuleDAO dao;

    @Test
    public void updateByRule() {
        McContractRuleDO mcContractRuleDO = new McContractRuleDO()
                .setRule("测试rule")
                .setName("测试rule")
                .setPayway(PaywayEnum.ALIPAY.getValue())
                .setProvider(ProviderEnum.PROVIDER_LKLORG.getValue().toString())
                .setChannel("测试rule")
                .setStatus(0);
        int insert = dao.insert(mcContractRuleDO);
        Assert.assertEquals(1, insert);
        Optional<McContractRuleDO> ruleDOOptional = dao.getDOByRule("测试rule");
        Assert.assertTrue(ruleDOOptional.isPresent());
        dao.updateByRule("测试rule", new McContractRuleDO().setName("xxxx"));
        ruleDOOptional = dao.getDOByRule("测试rule");
        Assert.assertEquals("xxxx", ruleDOOptional.get().getName());
    }

    @Test
    public void getAllEffectiveRule() {
        List<McContractRuleDO> allEffectiveRule = dao.listAllEffectiveRule();
        Assert.assertTrue(WosaiCollectionUtils.isNotEmpty(allEffectiveRule));
        Assert.assertTrue(allEffectiveRule.stream().anyMatch(rule -> rule.getRule().equals("lklV3")));
    }

    @Test
    public void listAllRule() {
        List<McContractRuleDO> allRule = dao.listAllRule();
        Assert.assertTrue(WosaiCollectionUtils.isNotEmpty(allRule));
        Assert.assertTrue(allRule.stream().anyMatch(rule -> rule.getRule().equals("lklV3")));
    }

    @Test
    public void getDOByRules() {
        List<McContractRuleDO> rules = dao.getDOByRules(Arrays.asList("lklV3", "lkl-1033-3-270860769"));
        Assert.assertEquals(2, rules.size());
    }

    @Test
    public void pageMcContractRule() {
        ContractRuleDto dto = new ContractRuleDto();
        dto.setType(2);
        Page<McContractRuleDO> mcContractRuleDOS = dao.pageMcContractRule(new PageInfo(1, 10), dto);
        Assert.assertTrue(mcContractRuleDOS.getTotal() > 10);
        Assert.assertEquals(10, mcContractRuleDOS.getResult().size());

    }
}