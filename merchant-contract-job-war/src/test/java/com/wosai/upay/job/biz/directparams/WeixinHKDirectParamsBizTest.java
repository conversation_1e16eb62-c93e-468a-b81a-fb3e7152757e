package com.wosai.upay.job.biz.directparams;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.model.directparams.WeixinHKDirectParams;
import com.wosai.upay.job.model.dto.MerchantProviderParamsCustomDto;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;


public class WeixinHKDirectParamsBizTest extends BaseTest {

    @Autowired
    WeixinHKDirectParamsBiz weixinHKDirectParamsBiz;
    @MockBean
    MerchantProviderParamsBiz merchantProviderParamsBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    SupportService supportService;
    @MockBean
    MerchantService merchantService;
    @MockBean
    private RuleBiz ruleBiz;

    @Before
    public void reflect() {
        ReflectionTestUtils.setField(weixinHKDirectParamsBiz, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(weixinHKDirectParamsBiz, "supportService", supportService);
        ReflectionTestUtils.setField(weixinHKDirectParamsBiz, "merchantService", merchantService);

        Mockito.doReturn(new HashMap<>()).when(tradeConfigService).getMerchantConfigByMerchantIdAndPayway(Mockito.anyString(), Mockito.anyInt());
        Map merchant = CollectionUtil.hashMap("merchant_id", "test_id", "sn", "sn");
        Mockito.doReturn(merchant).when(merchantService).getMerchant(Mockito.anyString());
    }

    @Test
    public void addDirectParams() {
        WeixinHKDirectParams baseParam = new WeixinHKDirectParams();
        baseParam.setMerchant_sn("sn");
        WeixinHKDirectParams.WeixinTradeParams weixinTradeParams = new WeixinHKDirectParams.WeixinTradeParams();
        weixinTradeParams.setWeixin_appid("app_id");
        weixinTradeParams.setWeixin_mch_id("mch_id");
        baseParam.setWeixin_trade_params(weixinTradeParams);
        weixinHKDirectParamsBiz.addDirectParams(baseParam);
    }

    @Test
    public void deleteDirectParams() {
        weixinHKDirectParamsBiz.deleteDirectParams(new MerchantProviderParamsDto(), "3", "0.38");
    }

    @Test
    public void handleDirectParams() {
        weixinHKDirectParamsBiz.handleDirectParams(new MerchantProviderParamsCustomDto());
    }
}
