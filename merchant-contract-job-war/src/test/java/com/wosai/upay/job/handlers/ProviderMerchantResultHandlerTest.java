package com.wosai.upay.job.handlers;

import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description: UnionMerchantResultHandlerTest
 * <AUTHOR>
 * @Date 2023/4/11 10:32
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class ProviderMerchantResultHandlerTest {

    @Autowired
    ProviderMerchantResultHandler handler;

    @Autowired
    ContractTaskMapper contractTaskMapper;

    @Autowired
    ContractSubTaskMapper contractSubTaskMapper;

    @Autowired
    ContractSubTaskHandler contractSubTaskHandler;

    @Autowired
    TongLianV2PaywayHandler tongLianV2PaywayHandler;

    @Test
    public void doHandle() throws Exception {
        ContractTask task = contractTaskMapper.selectByPrimaryKey(24817019L);
        ContractSubTask subTask = contractSubTaskMapper.selectByPrimaryKey(550414L);
        tongLianV2PaywayHandler.doHandle(task, subTask);
    }

}