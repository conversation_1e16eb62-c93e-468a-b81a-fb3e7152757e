package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.SelfOpenCcbDecpMapper;
import com.wosai.upay.job.model.DO.SelfOpenCcbDecp;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.Assert.*;

public class SelfOpenSelfOpenCcbDecpBizTest extends BaseTest {

    @Autowired
    private SelfOpenCcbDecpMapper selfOpenCcbDecpMapper;

    @Test
    public void test() {
        SelfOpenCcbDecp selfOpenCcbDecp = new SelfOpenCcbDecp();
        selfOpenCcbDecp.setMerchant_sn("merchantSn");
        selfOpenCcbDecp.setOpen_status(3);
        selfOpenCcbDecp.setDecp_id(1L);
        selfOpenCcbDecp.setRequest_body("request_body");
        selfOpenCcbDecp.setResult("开通失败,...");
        selfOpenCcbDecp.setCtime(System.currentTimeMillis());
        selfOpenCcbDecp.setMtime(System.currentTimeMillis());
        selfOpenCcbDecpMapper.insertSelective(selfOpenCcbDecp);
        System.out.println(JSON.toJSONString(selfOpenCcbDecp));
    }
}