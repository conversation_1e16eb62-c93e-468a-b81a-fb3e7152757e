package com.wosai.upay.job.biz.acquirePos;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.StoreInfo;
import com.wosai.mc.service.MerchantService;
import com.wosai.mc.service.StoreService;
import com.wosai.operator.dto.request.CallBackRequest;
import com.wosai.operator.service.BusinessOpenService;
import com.wosai.trade.service.FeeRateService;
import com.wosai.upay.core.model.Terminal;
import com.wosai.upay.core.service.SupportService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.Constants.PosConstant;
import com.wosai.upay.job.biz.AgreementBiz;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.ProviderTerminalBiz;
import com.wosai.upay.job.biz.SubBizParamsBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.ProviderTerminalBindConfigStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.model.PayParamsModel;
import com.wosai.upay.job.model.dto.AcquirerMerchantDto;
import com.wosai.upay.job.model.dto.acquirePos.InnerBindCheckDTO;
import com.wosai.upay.job.model.dto.acquirePos.UnbindDTO;
import com.wosai.upay.job.model.lklV3Pos.ApplyPosRequest;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.refactor.dao.ProviderTerminalBindConfigDAO;
import com.wosai.upay.job.refactor.model.entity.ProviderTerminalBindConfigDO;
import com.wosai.upay.job.service.ContractStatusService;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.merchant.contract.model.tlV2.DTO.MchProductDTO;
import com.wosai.upay.merchant.contract.model.tlV2.ProductInfo;
import com.wosai.upay.merchant.contract.model.tlV2.enume.ProductReqTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.ProductTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.enume.TerminalReqTypeEnum;
import com.wosai.upay.merchant.contract.model.tlV2.response.ProductResponse;
import com.wosai.upay.merchant.contract.model.tlV2.response.TerminalResponse;
import com.wosai.upay.merchant.contract.service.TongLianV2Service;
import org.junit.Before;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.wosai.upay.common.bean.ListResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TlT9HandleServiceTest {

    @Mock
    private MerchantService merchantService;
    @Mock
    private MerchantProviderParamsService merchantProviderParamsService;
    @Mock
    private TerminalService terminalService;
    @Mock
    private StoreService storeService;
    @Mock
    private ContractStatusService contractStatusService;
    @Mock
    private McAcquirerDAO mcAcquirerDAO;
    @Mock
    private T9HandleFactory factory;

    @InjectMocks
    private TlT9HandleService tlT9HandleService;

    @Mock
    private DirectStatusBiz directStatusBiz;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private AgreementBiz agreementBiz;

    @Mock
    private TongLianV2Service tongLianV2Service;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;


    @Mock
    private BusinessOpenService iotBusinessOpenService;

    private static final String TL_POS_DEV_CODE = "tl_pos_devcode";
    private static final int TONGLIAN_INTERNL_CARD_OPEN = 4;

    private ApplyPosRequest buildRequest(String formBody) {
        return new ApplyPosRequest()
                .setMerchantSn("M123456")
                .setDevCode("DEV001")
                .setForm_body(formBody);
    }

    private InnerBindCheckDTO dto;
    private MerchantInfo merchant;
    private StoreInfo store;
    private ListResult listResult;

    @BeforeEach
    void setUp() {
        dto = new InnerBindCheckDTO()
                .setMerchantSn("M123456")
                .setStoreSn("T001");

        merchant = new MerchantInfo()
                .setSn("M123456")
                .setProvince("江苏省")
                .setId("MID001");

        store = new StoreInfo()
                .setProvince("江苏省");

        listResult = new ListResult();
        listResult.setTotal(1);

        ReflectionTestUtils.setField(tlT9HandleService, "tlPosDevCode", TL_POS_DEV_CODE);

    }

    // TC01: 商户未在通联V2通道进件
    @Test
    void testInnerBindCheck_WhenNoTonglianV2Acquirer_ThrowException() {
        // Mock 商户信息
        when(merchantService.getMerchantBySn(anyString(), isNull())).thenReturn(merchant);

        // Mock 进件信息（不含通联V2）
        List<AcquirerMerchantDto> acquirerList = Collections.singletonList(
                new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_LKL));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(anyString())).thenReturn(acquirerList);

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals(PosConstant.FAIL_OTHER_ACQUIRE, exception.getMessage());
    }

    // TC02: 门店号为空
    @Test
    void testInnerBindCheck_WhenStoreSnIsBlank_ThrowException() {
        dto.setStoreSn(null);  // 设置空门店号

        when(merchantService.getMerchantBySn(anyString(), isNull())).thenReturn(merchant);

        // Mock 进件信息包含通联V2
        List<AcquirerMerchantDto> acquirerList = Collections.singletonList(
                new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_TONGLIANV2));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(anyString())).thenReturn(acquirerList);

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals("缺少商户号", exception.getMessage());
    }

    // TC03: 门店省份与商户省份不匹配
    @Test
    void testInnerBindCheck_WhenStoreProvinceNotMatchMerchantProvince_ThrowException() {

        // Mock 门店信息（不同省份）
        store.setProvince("浙江省");
        when(storeService.getStoreBySn(anyString(), isNull())).thenReturn(store);

        // Mock 其他依赖
        when(merchantService.getMerchantBySn(anyString(), isNull())).thenReturn(merchant);
        List<AcquirerMerchantDto> acquirerList = Collections.singletonList(
                new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_TONGLIANV2));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(anyString())).thenReturn(acquirerList);

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals("POS扫码绑定门店，与商户跨省或跨直辖市", exception.getMessage());
    }

    // TC04: 当前收单是通联V2且存在其他通道绑定
    @Test
    void testInnerBindCheck_WhenCurrentAcquirerIsTonglianV2AndExistOtherT9_ThrowException() {
        // Mock 基础信息
        mockCommonSuccessCase();

        // Mock 当前收单机构
        ContractStatus contractStatus = new ContractStatus()
                .setAcquirer(McConstant.ACQUIRER_TONGLIANV2);
        when(contractStatusService.selectByMerchantSn(anyString())).thenReturn(contractStatus);

        // Mock 其他通道检查
        when(factory.otherAcquireVenderList(anyString())).thenReturn(Arrays.asList("V1", "V2"));

        when(terminalService.getTerminals(anyString(), isNull(), isNull(), anyMap()))
                .thenReturn(listResult);  // 存在其他绑定

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals(PosConstant.EXIST_OTHER_POS, exception.getMessage());
    }

    // TC05: 当前收单是间接通道
    @Test
    void testInnerBindCheck_WhenCurrentAcquirerIsIndirect_ThrowException() {
        mockCommonSuccessCase();

        // Mock 间接通道列表
        when(mcAcquirerDAO.getIndirectAcquirerList()).thenReturn(Arrays.asList(McConstant.ACQUIRER_LKL));

        // Mock 当前收单机构
        ContractStatus contractStatus = new ContractStatus()
                .setAcquirer(McConstant.ACQUIRER_LKL);
        when(contractStatusService.selectByMerchantSn(anyString())).thenReturn(contractStatus);

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals(PosConstant.FAIL_OTHER_ACQUIRE, exception.getMessage());
    }

    // TC06: 当前收单是银行通道且存在其他T9绑定
    @Test
    void testInnerBindCheck_WhenCurrentAcquirerIsBankAndExistOtherT9_ThrowException() {
        mockCommonSuccessCase();

        // Mock 当前收单机构为银行
        ContractStatus contractStatus = new ContractStatus()
                .setAcquirer(McConstant.ACQUIRER_CCB);
        when(contractStatusService.selectByMerchantSn(anyString())).thenReturn(contractStatus);

        // Mock 其他通道检查
        when(factory.otherAcquireVenderList(anyString())).thenReturn(Arrays.asList("V1", "V2"));
        final ListResult listResult = new ListResult();
        listResult.setTotal(2);
        when(terminalService.getTerminals(any(), isNull(), isNull(), any())).thenReturn(listResult);

        CommonPubBizException exception = assertThrows(CommonPubBizException.class,
                () -> tlT9HandleService.innerBindCheck(dto));
        assertEquals(PosConstant.EXIST_OTHER_POS, exception.getMessage());
    }

    // 公共mock方法：处理成功进件、终端信息正确、省份一致等情况
    private void mockCommonSuccessCase() {
        // Mock 商户信息
        when(merchantService.getMerchantBySn(anyString(), eq(null))).thenReturn(merchant);

        // Mock 进件信息包含通联V2
        List<AcquirerMerchantDto> acquirerList = Collections.singletonList(
                new AcquirerMerchantDto().setAcquirer(McConstant.ACQUIRER_TONGLIANV2));
        when(merchantProviderParamsService.getAcquirerMerchantInfo(anyString())).thenReturn(acquirerList);
        // Mock 门店信息（省份一致）
        when(storeService.getStoreBySn(anyString(),  eq(null))).thenReturn(store);
    }


    @Mock
    private ProviderTerminalBindConfigDAO providerTerminalBindConfigDAO;
    @Mock
    private ProviderTerminalBiz providerTerminalBiz;

    @Mock
    private TradeConfigService tradeConfigService;

    private static final String MERCHANT_SN = "test_merchant_sn";
    private static final String MERCHANT_ID = "test_merchant_id";
    private static final String TERMINAL_SN = "test_terminal_sn";
    private static final String STORE_ID = "test_store_id";
    private static final String PROVIDER_TERMINAL_ID = "test_provider_terminal_id";

    @Test
    void t9TermAdd_Success_WithExistingTerminal() {
        // 准备测试数据
        Map<String, Object> terminal = new HashMap<>();
        terminal.put(Terminal.MERCHANT_ID, MERCHANT_ID);
        terminal.put(Terminal.STORE_ID, STORE_ID);

        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(MERCHANT_SN);

        StoreInfo store = new StoreInfo();
        store.setSn("test_store_sn");

        List<ProviderTerminalBindConfigDO> existingConfigs = new ArrayList<>();

        TerminalResponse.TerminalInfo terminalInfo = new TerminalResponse.TerminalInfo();
        terminalInfo.setTermcode(PROVIDER_TERMINAL_ID);
        terminalInfo.setStatus("1");

        // Mock服务返回
        when(terminalService.getTerminalBySn(TERMINAL_SN)).thenReturn(terminal);
        when(merchantService.getMerchantById(MERCHANT_ID, null)).thenReturn(merchant);
        when(storeService.getStoreById(STORE_ID, null)).thenReturn(store);
        when(providerTerminalBindConfigDAO.listByMerchantSnProviderPayWay(
                MERCHANT_SN,
                PayParamsModel.PROVIDER_TONGLIAN_V2,
                PaywayEnum.BANK_CARD.getValue()
        )).thenReturn(existingConfigs);

        // Mock通联服务返回
        com.wosai.upay.merchant.contract.model.ContractResponse mockResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        mockResponse.setCode(200);
        Map<String, Object> responseParam = new HashMap<>();
        responseParam.put("termlist", JSONArray.toJSONString(Collections.singletonList(terminalInfo)));
        mockResponse.setResponseParam(responseParam);
        when(tongLianV2Service.termOperate(eq(MERCHANT_SN), eq(TerminalReqTypeEnum.QUERY))).thenReturn(mockResponse);

        // 执行测试
        tlT9HandleService.t9TermAdd(TERMINAL_SN);

        // 验证结果
        ArgumentCaptor<ProviderTerminalBindConfigDO> configCaptor = ArgumentCaptor.forClass(ProviderTerminalBindConfigDO.class);
        verify(providerTerminalBindConfigDAO).insertOne(configCaptor.capture());

        ProviderTerminalBindConfigDO savedConfig = configCaptor.getValue();
        assertEquals(PROVIDER_TERMINAL_ID, savedConfig.getProviderTerminalId());
        assertEquals(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus(), savedConfig.getStatus());
    }

    @Test
    void t9TermAdd_Success_NeedNewTerminal() {
        // 准备测试数据
        Map<String, Object> terminal = new HashMap<>();
        terminal.put(Terminal.MERCHANT_ID, MERCHANT_ID);
        terminal.put(Terminal.STORE_ID, STORE_ID);

        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(MERCHANT_SN);

        StoreInfo store = new StoreInfo();
        store.setSn("test_store_sn");

        List<ProviderTerminalBindConfigDO> existingConfigs = new ArrayList<>();

        // Mock服务返回
        when(terminalService.getTerminalBySn(TERMINAL_SN)).thenReturn(terminal);
        when(merchantService.getMerchantById(MERCHANT_ID, null)).thenReturn(merchant);
        when(storeService.getStoreById(STORE_ID, null)).thenReturn(store);
        when(providerTerminalBindConfigDAO.listByMerchantSnProviderPayWay(
                MERCHANT_SN,
                PayParamsModel.PROVIDER_TONGLIAN_V2,
                PaywayEnum.BANK_CARD.getValue()
        )).thenReturn(existingConfigs);

        // Mock查询返回空列表
        com.wosai.upay.merchant.contract.model.ContractResponse queryResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        queryResponse.setCode(200);
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("termlist", "[]");
        queryResponse.setResponseParam(queryParam);
        when(tongLianV2Service.termOperate(eq(MERCHANT_SN), eq(TerminalReqTypeEnum.QUERY))).thenReturn(queryResponse);

        // Mock添加终端返回
        com.wosai.upay.merchant.contract.model.ContractResponse addResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        addResponse.setCode(200);
        Map<String, Object> addParam = new HashMap<>();
        addParam.put("addterms", PROVIDER_TERMINAL_ID);
        addResponse.setResponseParam(addParam);
        when(tongLianV2Service.termOperate(eq(MERCHANT_SN), eq(TerminalReqTypeEnum.ADD))).thenReturn(addResponse);

        // 执行测试
        tlT9HandleService.t9TermAdd(TERMINAL_SN);

        // 验证结果
        ArgumentCaptor<ProviderTerminalBindConfigDO> configCaptor = ArgumentCaptor.forClass(ProviderTerminalBindConfigDO.class);
        verify(providerTerminalBindConfigDAO).insertOne(configCaptor.capture());

        ProviderTerminalBindConfigDO savedConfig = configCaptor.getValue();
        assertEquals(PROVIDER_TERMINAL_ID, savedConfig.getProviderTerminalId());
        assertEquals(ProviderTerminalBindConfigStatus.PENDING.getStatus(), savedConfig.getStatus());
    }

    @Test
    void t9TermAdd_AlreadyBound() {
        // 准备测试数据
        Map<String, Object> terminal = new HashMap<>();
        terminal.put(Terminal.MERCHANT_ID, MERCHANT_ID);
        terminal.put(Terminal.STORE_ID, STORE_ID);

        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(MERCHANT_SN);

        ProviderTerminalBindConfigDO existingConfig = new ProviderTerminalBindConfigDO();
        existingConfig.setStatus(ProviderTerminalBindConfigStatus.BIND_SUCCESS.getStatus());
        existingConfig.setTerminalSn(TERMINAL_SN);

        List<ProviderTerminalBindConfigDO> existingConfigs = Collections.singletonList(existingConfig);

        // Mock服务返回
        when(terminalService.getTerminalBySn(TERMINAL_SN)).thenReturn(terminal);
        when(merchantService.getMerchantById(MERCHANT_ID, null)).thenReturn(merchant);
        when(providerTerminalBindConfigDAO.listByMerchantSnProviderPayWay(
                MERCHANT_SN,
                PayParamsModel.PROVIDER_TONGLIAN_V2,
                PaywayEnum.BANK_CARD.getValue()
        )).thenReturn(existingConfigs);

        // 执行测试
        tlT9HandleService.t9TermAdd(TERMINAL_SN);

        // 验证结果
        verify(providerTerminalBindConfigDAO, never()).insertOne(any());
        verify(tongLianV2Service, never()).termOperate(anyString(), any());
    }

    @Test
    void t9TermAdd_HasPendingTerminal() {
        // 准备测试数据
        Map<String, Object> terminal = new HashMap<>();
        terminal.put(Terminal.MERCHANT_ID, MERCHANT_ID);
        terminal.put(Terminal.STORE_ID, STORE_ID);

        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(MERCHANT_SN);

        ProviderTerminalBindConfigDO pendingConfig = new ProviderTerminalBindConfigDO();
        pendingConfig.setStatus(ProviderTerminalBindConfigStatus.PENDING.getStatus());
        pendingConfig.setTerminalSn("other_terminal_sn");

        List<ProviderTerminalBindConfigDO> existingConfigs = Collections.singletonList(pendingConfig);

        // Mock服务返回
        when(terminalService.getTerminalBySn(TERMINAL_SN)).thenReturn(terminal);
        when(merchantService.getMerchantById(MERCHANT_ID, null)).thenReturn(merchant);
        when(providerTerminalBindConfigDAO.listByMerchantSnProviderPayWay(
                MERCHANT_SN,
                PayParamsModel.PROVIDER_TONGLIAN_V2,
                PaywayEnum.BANK_CARD.getValue()
        )).thenReturn(existingConfigs);

        // 执行测试
        tlT9HandleService.t9TermAdd(TERMINAL_SN);

        // 验证结果
        verify(providerTerminalBindConfigDAO, never()).insertOne(any());
        verify(tongLianV2Service, never()).termOperate(anyString(), any());
    }

    @Test
    void t9TermUnbind_Success() {
        // 准备测试数据
        UnbindDTO dto = new UnbindDTO();
        dto.setMerchantId(MERCHANT_ID);
        dto.setTerminalId("test_terminal_id");

        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(MERCHANT_SN);

        Map<String, Object> terminal = new HashMap<>();
        terminal.put(Terminal.SN, TERMINAL_SN);
        terminal.put(Terminal.MERCHANT_ID, MERCHANT_ID);

        // Mock服务返回
        when(merchantService.getMerchantById(MERCHANT_ID, null)).thenReturn(merchant);
        when(terminalService.getTerminalBySn(TERMINAL_SN)).thenReturn(terminal);
        when(tradeConfigService.getSystemConfigContentByName(any())).thenReturn(new HashMap<String, Object>());
        when(tradeConfigService.updateBankCardTerminalConfig(anyMap())).thenReturn(new HashMap<>());
        // 执行测试
        tlT9HandleService.t9TermUnbind(TERMINAL_SN);

        // 验证结果
        verify(providerTerminalBindConfigDAO).deleteTerminalLevelProviderTerminalBindConfig(
                eq(MERCHANT_SN),
                eq(PayParamsModel.PROVIDER_TONGLIAN_V2),
                eq(TERMINAL_SN),
                eq(PaywayEnum.BANK_CARD.getValue())
        );
    }


    @Mock
    private FeeRateService feeRateService;
    @Mock
    private SupportService supportService;


    @Mock
    private SubBizParamsBiz subBizParamsBiz;

    private static final String DEV_CODE = "test_dev_code";

    /**
     * 测试正常开通流程
     * 验证条件：
     * 1. 表单参数解析正确
     * 2. 通道参数存在
     * 3. 交易配置更新成功
     * 预期结果：
     * - 返回成功响应
     * - 状态正确更新为处理中和成功
     * - 异步保存电子协议
     * - 交易配置服务被调用
     */
    @Test
    void openPos_WhenAllConditionsMet_ReturnSuccess() throws Exception {
        // 构造请求参数
        Map<String, Object> formBody = new HashMap<>();
        formBody.put("sybSignUrl", "http://sign.com");
        formBody.put("bankcard_fee", Collections.singletonMap("debit", Collections.singletonMap("fee", 0.55)));
        ApplyPosRequest request = new ApplyPosRequest()
                .setMerchantSn("M123456")
                .setDevCode("DEV001")
                .setForm_body(JSON.toJSONString(formBody));

        // Mock 通道参数查询
        MerchantProviderParams mockParams = new MerchantProviderParams();
        mockParams.setPay_merchant_id("TL123456");
        when(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(anyString(), eq(ProviderEnum.PROVIDER_TONGLIAN_V2.getValue())))
                .thenReturn(mockParams);
        when(merchantService.getMerchantBySn("M123456", null))
                .thenReturn(merchant);
        doNothing().when(feeRateService).applyFeeRateOne(anyObject());

        doNothing().when(supportService).removeCachedParams(anyString());

        doReturn("t9TradeAppId").when(subBizParamsBiz).getT9TradeAppId();
        doNothing().when(subBizParamsBiz).updateSubBizParams(anyString(), anyString(), anyInt(), anyObject());

        // 执行测试
        ContractResponse response = tlT9HandleService.openPos(request);

        // 验证结果
        assertTrue(response.isSuccess(), "响应状态应为成功");
        assertNull(response.getMsg(), "成功时消息应为空");

        // 验证状态流转
        verify(directStatusBiz).createOrUpdateDirectStatus("M123456", "DEV001", DirectStatus.STATUS_PROCESS, null);
        verify(directStatusBiz).createOrUpdateDirectStatus("M123456", "DEV001", DirectStatus.STATUS_SUCCESS, null);

        // 验证异步协议保存
        ArgumentCaptor<String> urlCaptor = ArgumentCaptor.forClass(String.class);
        verify(agreementBiz, timeout(1000)).recordAgreementForT9(anyString(), urlCaptor.capture());
        assertEquals("http://sign.com", urlCaptor.getValue(), "协议URL应正确传递");

        // 验证交易配置更新
        verify(tradeConfigService).updateBankCardMerchantConfig(anyMap());
    }

    @Test
    void openPos_FailWhenException() {
        // 准备测试数据
        ApplyPosRequest request = createApplyPosRequest();

        // Mock依赖服务抛出异常
        when(merchantProviderParamsMapper.getAcquirerParamsByMerchantSnAndProvider(MERCHANT_SN, ProviderEnum.PROVIDER_TONGLIAN_V2.getValue()))
                .thenThrow(new RuntimeException("Mock Exception"));

        // 执行测试
        ContractResponse response = tlT9HandleService.openPos(request);

        // 验证结果
        assertFalse(response.isSuccess());
        assertNotNull(response.getMsg());

        // 验证失败状态更新
        verify(directStatusBiz).createOrUpdateDirectStatus(
                eq(MERCHANT_SN),
                eq(DEV_CODE),
                eq(DirectStatus.STATUS_BIZ_FAIL),
                anyString());
    }

    @Test
    void openPos_HandleInvalidFormBody() {
        // 准备测试数据
        ApplyPosRequest request = new ApplyPosRequest();
        request.setMerchantSn(MERCHANT_SN);
        request.setDevCode(DEV_CODE);
        request.setForm_body("invalid_json");

        // 执行测试
        ContractResponse response = tlT9HandleService.openPos(request);

        // 验证结果
        assertFalse(response.isSuccess());
        verify(directStatusBiz).createOrUpdateDirectStatus(
                eq(MERCHANT_SN),
                eq(DEV_CODE),
                eq(DirectStatus.STATUS_BIZ_FAIL),
                anyString());
    }

    private ApplyPosRequest createApplyPosRequest() {
        ApplyPosRequest request = new ApplyPosRequest();
        request.setMerchantSn(MERCHANT_SN);
        request.setDevCode(DEV_CODE);

        Map<String, Object> formBody = new HashMap<>();
        formBody.put("sybSignUrl", "http://example.com");
        formBody.put("bankcard_fee", createBankCardFeeMap());
        formBody.put("tradecombo_id", 123L);

        request.setForm_body(JSONObject.toJSONString(formBody));
        return request;
    }

    private Map<String, Object> createBankCardFeeMap() {
        Map<String, Object> feeMap = new HashMap<>();

        Map<String, String> debitMap = new HashMap<>();
        debitMap.put("fee", "0.55");
        debitMap.put("max", "15");
        feeMap.put("debit", debitMap);

        Map<String, String> creditMap = new HashMap<>();
        creditMap.put("fee", "0.6");
        feeMap.put("credit", creditMap);

        return feeMap;
    }





    /**
     * 测试场景：产品不存在，创建新产品
     */
    @Test
    void applyBankCardProduct_CreateNew() {

        // 准备测试数据
        String merchantId = "TEST_MERCHANT_ID";
        String merchantSn = "TEST_MERCHANT_SN";
        Map<String, Object> feeMap = createTestFeeMap();

        // Mock商户信息
        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(merchantSn);
        when(merchantService.getMerchantById(merchantId, null))
                .thenReturn(merchant);

        // Mock产品查询响应 - 返回空列表
        com.wosai.upay.merchant.contract.model.ContractResponse queryResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        queryResponse.setCode(200);
        ProductResponse productResponse = new ProductResponse();
        productResponse.setProdlist("[]");
        queryResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(productResponse)));
        final MchProductDTO queryDto = new MchProductDTO();
        queryDto.setMerchantSn(merchantSn);
        queryDto.setFeeMap(feeMap);
        queryDto.setProductTypeEnum(ProductTypeEnum.BANK_CARD);
        queryDto.setReqTypeEnum(ProductReqTypeEnum.QUERY);
        when(tongLianV2Service.bankCardProduct(any()))
                .thenReturn(queryResponse);

        // Mock产品添加响应
        com.wosai.upay.merchant.contract.model.ContractResponse addResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        addResponse.setCode(200);

        final MchProductDTO addProductDTO =  new MchProductDTO();
        addProductDTO.setReqTypeEnum(ProductReqTypeEnum.ADD);
        addProductDTO.setProductTypeEnum(ProductTypeEnum.BANK_CARD);
        addProductDTO.setMerchantSn(merchantSn);
        addProductDTO.setFeeMap(feeMap);
        when(tongLianV2Service.bankCardProduct(addProductDTO))
                .thenReturn(addResponse);
        // 创建mock对象
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        doNothing().when(valueOperations).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));

        // 执行测试
        tlT9HandleService.applyBankCardProduct(merchantId, feeMap);

        // 验证Redis缓存设置
        verify(valueOperations).set(
                merchantSn + TL_POS_DEV_CODE,
                "1",
                7,
                TimeUnit.DAYS
        );

        // 验证产品操作
        ArgumentCaptor<MchProductDTO> productCaptor = ArgumentCaptor.forClass(MchProductDTO.class);
        verify(tongLianV2Service, times(2)).bankCardProduct(productCaptor.capture());

        // 验证查询请求
        assertEquals(ProductReqTypeEnum.QUERY, queryDto.getReqTypeEnum());
        assertEquals(ProductTypeEnum.BANK_CARD, queryDto.getProductTypeEnum());
        assertEquals(merchantSn, queryDto.getMerchantSn());

        // 验证添加请求
        MchProductDTO addDto = productCaptor.getAllValues().get(1);
        assertEquals(ProductReqTypeEnum.ADD, addDto.getReqTypeEnum());
        assertEquals(ProductTypeEnum.BANK_CARD, addDto.getProductTypeEnum());
        assertEquals(merchantSn, addDto.getMerchantSn());
        validateFeeMap(addDto.getFeeMap());
    }

    /**
     * 测试场景：产品已存在且费率一致，无需更新
     */
    @Test
    void applyBankCardProduct_ExistingSameFee() {
        // 准备测试数据
        String merchantId = "TEST_MERCHANT_ID";
        String merchantSn = "TEST_MERCHANT_SN";
        Map<String, Object> feeMap = createTestFeeMap();

        // Mock商户信息
        MerchantInfo merchant = new MerchantInfo();
        merchant.setId(merchantId);
        merchant.setSn(merchantSn);
        when(merchantService.getMerchantById(merchantId, null))
                .thenReturn(merchant);

        // Mock产品查询响应 - 返回相同费率的产品
        com.wosai.upay.merchant.contract.model.ContractResponse queryResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        queryResponse.setCode(200);
        ProductResponse productResponse = new ProductResponse();
        ProductInfo existingProduct = new ProductInfo();
        existingProduct.setPid("P0001");
        existingProduct.setMtrxcode("VSP001");
        existingProduct.setFeerate("5.5");  // 0.55 * 10
        existingProduct.setCreditrate("6.5"); // 0.65 * 10
        existingProduct.setToplimit("1000");
        productResponse.setProdlist(JSONArray.toJSONString(Lists.newArrayList(existingProduct)));
        queryResponse.setResponseParam(JSONObject.parseObject(JSONObject.toJSONString(productResponse)));
        when(tongLianV2Service.bankCardProduct(any(MchProductDTO.class)))
                .thenReturn(queryResponse);

        // 执行测试
        tlT9HandleService.applyBankCardProduct(merchantId, feeMap);

        // 验证只进行了查询操作
        verify(tongLianV2Service, times(1)).bankCardProduct(any(MchProductDTO.class));
        verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));

        // 验证通知IOT
        ArgumentCaptor<CallBackRequest> callbackCaptor = ArgumentCaptor.forClass(CallBackRequest.class);
        verify(iotBusinessOpenService).resultCallBack(callbackCaptor.capture());

        CallBackRequest capturedRequest = callbackCaptor.getValue();
        assertEquals(TONGLIAN_INTERNL_CARD_OPEN, capturedRequest.getBizType());
        assertEquals(merchantId, capturedRequest.getMerchantId());
        assertEquals(1, capturedRequest.getAuditResult());
    }

    /**
     * 测试场景：通联服务返回错误
     */
    @Test
    void applyBankCardProduct_ServiceError() {
        // 准备测试数据
        String merchantId = "TEST_MERCHANT_ID";
        String merchantSn = "TEST_MERCHANT_SN";
        Map<String, Object> feeMap = createTestFeeMap();

        // Mock商户信息
        MerchantInfo merchant = new MerchantInfo();
        merchant.setSn(merchantSn);
        when(merchantService.getMerchantById(merchantId, null))
                .thenReturn(merchant);

        // Mock服务错误响应
        com.wosai.upay.merchant.contract.model.ContractResponse errorResponse = new com.wosai.upay.merchant.contract.model.ContractResponse();
        errorResponse.setCode(500);
        errorResponse.setMessage("Service Error");
        when(tongLianV2Service.bankCardProduct(any(MchProductDTO.class)))
                .thenReturn(errorResponse);

        // 执行测试并验证异常
        CommonPubBizException exception = assertThrows(
                CommonPubBizException.class,
                () -> tlT9HandleService.applyBankCardProduct(merchantId, feeMap)
        );
        assertEquals("Service Error", exception.getMessage());
    }

    private Map<String, Object> createTestFeeMap() {
        Map<String, Object> feeMap = new HashMap<>();
        Map<String, String> debitFee = new HashMap<>();
        debitFee.put("fee", "0.55");
        debitFee.put("max", "1000");
        Map<String, String> creditFee = new HashMap<>();
        creditFee.put("fee", "0.65");
        feeMap.put("debit", debitFee);
        feeMap.put("credit", creditFee);
        return feeMap;
    }

    private void validateFeeMap(Map<String, Object> feeMap) {
        assertNotNull(feeMap.get("debit"));
        assertNotNull(feeMap.get("credit"));

        @SuppressWarnings("unchecked")
        Map<String, String> debitFee = (Map<String, String>) feeMap.get("debit");
        assertEquals("0.55", debitFee.get("fee"));
        assertEquals("1000", debitFee.get("max"));

        @SuppressWarnings("unchecked")
        Map<String, String> creditFee = (Map<String, String>) feeMap.get("credit");
        assertEquals("0.65", creditFee.get("fee"));
    }




}