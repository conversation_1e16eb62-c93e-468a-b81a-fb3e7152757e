package com.wosai.upay.job.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.databus.event.merchant.contract.MerchantContractOpinionEvent;
import com.wosai.databus.event.merchant.contract.MerchantContractWeixinAuthEvent;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.monitor.MonitorLog;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.*;

public class DataBusBizTest extends H2DbBaseTest {

    @SpyBean
    private DataBusBiz biz;

    @Mock
    MerchantService merchantService;

    @MockBean
    MonitorLog monitorLog;

    @MockBean
    private TradeManageBiz tradeManageBiz;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(biz, "merchantService", merchantService);

        Mockito.doNothing().when(tradeManageBiz).openMerchantPay(any(), any());
        Mockito.doNothing().when(tradeManageBiz).closeMerchantPay(any(), any());
    }

    @Test
    public void insertContractStatus() {
        Mockito.doReturn(CollectionUtil.hashMap("id", "id")).when(merchantService).getMerchantBySn(anyString());
        assertEquals(1, biz.insert(2, "lkl-mch1", "xxx"));
    }

    @Test
    public void updateContractStatus() {
        Mockito.doReturn(CollectionUtil.hashMap("id", "id")).when(merchantService).getMerchantBySn(any());
        assertEquals(1, biz.insert(2, "lkl-mch", "xxx"));
    }

    @Test
    public void insertPayForVerifyEvent() {
        assertEquals(1, biz.insertPayForEvent("xx", "xx",1));
    }

    @Test
    public void insertWeixinAuthEvent() {
        assertEquals(1, biz.insertWeixinAuthEvent(new MerchantContractWeixinAuthEvent()));
    }

    @Test
    public void insertOpinionEvent() {
        assertEquals(1, biz.insertOpinionEvent(new MerchantContractOpinionEvent()));
    }
}