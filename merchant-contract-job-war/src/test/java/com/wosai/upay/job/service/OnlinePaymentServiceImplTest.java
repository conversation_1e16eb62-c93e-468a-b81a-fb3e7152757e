package com.wosai.upay.job.service;

import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBusinessLicenseService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.OnlinePaymentConstant;
import com.wosai.upay.job.biz.OnlinePaymentBiz;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.onlinePayment.*;
import com.wosai.upay.job.refactor.dao.OpenOnlinePaymentApplyDAO;
import com.wosai.upay.job.refactor.model.entity.OpenOnlinePaymentApplyDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class OnlinePaymentServiceImplTest {

    @InjectMocks
    private OnlinePaymentServiceImpl onlinePaymentService;
    @Mock
    private MerchantService merchantService;
    @Mock
    private OnlinePaymentBiz onlinePaymentBiz;
    @Mock
    private ContractStatusMapper contractStatusMapper;
    @Mock
    private OpenOnlinePaymentApplyDAO openOnlinePaymentApplyDAO;
    @Mock
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Test
    public void testOpenOnlinePayment() {
        OnlinePaymentOpenReq req = new OnlinePaymentOpenReq();
        req.setMerchantSn("merchantSn");

        //1. 有未完成的申请单
        Mockito.doReturn(new ContractStatus().setAcquirer("lklV3").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApplyByAcquirer(req.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), "lklV3");
        OnlinePaymentOpenResp onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.APPLYING, onlinePaymentOpenResp.getStatus());

        //2. 有成功的申请单，但是不是当前收单机构下的
        Mockito.doReturn(new ContractStatus().setAcquirer("lklV3").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        Mockito.doReturn(Optional.empty()).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApplyByAcquirer(req.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), "lklV3");
        onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.APPLYING, onlinePaymentOpenResp.getStatus());

        //3. 间连扫码未开通成功
        Mockito.doReturn(null).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.FAIL, onlinePaymentOpenResp.getStatus());
        assertEquals("【cc001】您暂未开通支付功能，请联系客户经理处理", onlinePaymentOpenResp.getFailMsg());

        //4. 渠道不支持开通线上收款
        Mockito.doReturn(new ContractStatus().setAcquirer("fuyou").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.FAIL, onlinePaymentOpenResp.getStatus());
        assertEquals("【cc002】您当前所在的支付机构暂不支持开通，请联系客户经理处理", onlinePaymentOpenResp.getFailMsg());

        //6. 海科小微不支持开通线上收款
        Mockito.doReturn(new ContractStatus().setAcquirer("haike").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue())).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(any());
        onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.FAIL, onlinePaymentOpenResp.getStatus());
        assertEquals("【cc003】您当前所在的支付机构暂不支持开通，请联系客户经理处理", onlinePaymentOpenResp.getFailMsg());

        //6. 海科个体支持开通线上收款
        Mockito.doReturn(new ContractStatus().setAcquirer("haike").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        Mockito.doReturn(Optional.empty()).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApplyByAcquirer(req.getMerchantSn(), PaywayEnum.WEIXIN.getValue(), "haike");
        Mockito.doReturn(CollectionUtil.hashMap(MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.INDIVIDUAL.getValue())).when(merchantBusinessLicenseService).getBusinessLicenseByMerchantId(any());
        onlinePaymentOpenResp = onlinePaymentService.openOnlinePayment(req);
        assertEquals(OnlinePaymentConstant.ApplyStatus.APPLYING, onlinePaymentOpenResp.getStatus());
    }

    @Test
    public void testCheckMerchantAllowOpenOnlinePayment() {
        OnlinePaymentOpenCheckReq req = new OnlinePaymentOpenCheckReq();
        req.setMerchantSn("merchantSn");
        //1. 间连扫码未开通
        OnlinePaymentOpenCheckResp resp = onlinePaymentService.checkMerchantAllowOpenOnlinePayment(req);
        assertEquals(Integer.valueOf(0), resp.getCheckResult());
        assertEquals("【cc001】您暂未开通支付功能，请联系客户经理处理", resp.getCheckFailMsg());

        //2.渠道不允许
        ContractStatus contractStatus = new ContractStatus()
                .setMerchant_sn(req.getMerchantSn())
                .setStatus(ContractStatus.STATUS_SUCCESS)
                .setAcquirer("fuyou");
        Mockito.doReturn(contractStatus).when(contractStatusMapper).selectByMerchantSn(req.getMerchantSn());
        resp = onlinePaymentService.checkMerchantAllowOpenOnlinePayment(req);
        assertEquals(Integer.valueOf(0), resp.getCheckResult());
        assertEquals("【cc002】您当前所在的支付机构暂不支持开通，请联系客户经理处理", resp.getCheckFailMsg());

        //3.校验通过
        contractStatus.setAcquirer("lklV3");
        resp = onlinePaymentService.checkMerchantAllowOpenOnlinePayment(req);
        assertEquals(Integer.valueOf(1), resp.getCheckResult());

        //4.海科小微不允许微信
        contractStatus.setAcquirer("haike");
        resp = onlinePaymentService.checkMerchantAllowOpenOnlinePayment(req);
        assertEquals(Integer.valueOf(0), resp.getCheckResult());

        //4.海科小微允许支付宝
        contractStatus.setAcquirer("haike");
        req.setPayway(PaywayEnum.ALIPAY.getValue());
        resp = onlinePaymentService.checkMerchantAllowOpenOnlinePayment(req);
        assertEquals(Integer.valueOf(1), resp.getCheckResult());
    }

    @Test
    public void testQueryMerchantOnlinePaymentOpenStatus() {
        MerchantOnlinePaymentOpenStatusReq req = new MerchantOnlinePaymentOpenStatusReq();
        req.setMerchantId("merchantId");
        Mockito.doReturn(CollectionUtil.hashMap("sn", "merchantSn")).when(merchantService).getMerchantByMerchantId(req.getMerchantId());
        Mockito.doReturn(new ContractStatus().setAcquirer("lklV3").setStatus(ContractStatus.STATUS_SUCCESS)).when(contractStatusMapper).selectByMerchantSn("merchantSn");

        OpenOnlinePaymentApplyDO openOnlinePaymentApplyDO = new OpenOnlinePaymentApplyDO();
        Mockito.doReturn(Optional.of(openOnlinePaymentApplyDO)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApplyByAcquirer("merchantSn", PaywayEnum.WEIXIN.getValue(), "lklV3");
        // 1.申请中
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.PENDING);
        Integer status = onlinePaymentService.queryMerchantOnlinePaymentOpenStatus(req);
        assertEquals(OnlinePaymentConstant.OpenStatus.OPENING, status);

        // 2.开通成功
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.APPLYING);
        status = onlinePaymentService.queryMerchantOnlinePaymentOpenStatus(req);
        assertEquals(OnlinePaymentConstant.OpenStatus.OPENING, status);

        // 3.开通失败
        openOnlinePaymentApplyDO.setStatus(OnlinePaymentConstant.ApplyStatus.FAIL);
        status = onlinePaymentService.queryMerchantOnlinePaymentOpenStatus(req);
        assertEquals(OnlinePaymentConstant.OpenStatus.OPEN_FAIL, status);

        // 4.待开通
        Mockito.doReturn(Optional.empty()).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApplyByAcquirer("merchantSn", PaywayEnum.WEIXIN.getValue(), "lklV3");
        status = onlinePaymentService.queryMerchantOnlinePaymentOpenStatus(req);
        assertEquals(OnlinePaymentConstant.OpenStatus.NOT_OPEN, status);
    }

    @Test
    public void testQueryOnlinePaymentSubMchIdAuthStatus() {
        OnlinePaymentSubMchIdAuthStatusQueryReq req = new OnlinePaymentSubMchIdAuthStatusQueryReq();
        req.setMerchantId("merchantId");
        Mockito.doReturn(CollectionUtil.hashMap("sn", "merchantSn")).when(merchantService).getMerchantByMerchantId("merchantId");
        Mockito.doReturn(Optional.empty()).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.WEIXIN.getValue());
        ContractBizException contractBizException = Assertions.assertThrows(ContractBizException.class, () -> onlinePaymentService.queryOnlinePaymentSubMchIdAuthStatus(req));
        assertEquals("线上收款申请单不存在", contractBizException.getMessage());

        OpenOnlinePaymentApplyDO apply = new OpenOnlinePaymentApplyDO();
        Mockito.doReturn(Optional.of(apply)).when(openOnlinePaymentApplyDAO).queryLatestOpenOnlinePaymentApply("merchantSn", PaywayEnum.WEIXIN.getValue());
        Mockito.doReturn(true).when(onlinePaymentBiz).queryAuthStatusAndSetDefault(apply);
        Boolean authStatus = onlinePaymentService.queryOnlinePaymentSubMchIdAuthStatus(req);
        assertTrue(authStatus);
    }
}