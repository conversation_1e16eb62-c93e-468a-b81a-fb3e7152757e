package com.wosai.upay.job.service;

import com.google.common.collect.Maps;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.MerchantProviderParamsBiz;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.direct.WeixinDirectBiz;
import com.wosai.upay.job.enume.WeixinDirectApplyStatus;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.mapper.WeixinDirectApplyMapper;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.WeixinDirectApply;
import com.wosai.upay.job.model.direct.*;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.Matchers.*;

public class WeixinDirectApplyServiceImplTest extends BaseTest {

    @InjectMocks
    private WeixinDirectApplyServiceImpl directApplyService;

    @Mock
    private WeixinDirectBiz weixinDirectBiz;

    @Mock
    private WeixinDirectApplyMapper applyMapper;

    @Mock
    private MerchantProviderParamsBiz paramsBiz;

    @Mock
    private RedisLock redisLock;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Test
    public void applyWeixinDirectPay() {
        WeixinDirectReq req = new WeixinDirectReq();
        req.setMerchant_sn("merchant_sn").setDev_code("dev_code");
        req.setContact_info(new WeixinDirectReq.Contact_info().setContact_name("name")
                        .setContact_id_number("123123123").setMobile_phone("123123123").setContact_email("<EMAIL>"));
        req.setApp_info(new WeixinDirectReq.App_info().setFee_rate("0.38"));
        ContractResponse contractResponse = directApplyService.applyWeixinDirectPay(req);
        assertTrue(contractResponse.isSuccess());

        Mockito.doThrow(new CommonInvalidParameterException("模拟校验失败")).when(weixinDirectBiz).preCheck(req.getMerchant_sn(), req.getDev_code());
        contractResponse = directApplyService.applyWeixinDirectPay(req);
        assertFalse(contractResponse.isSuccess());
        assertTrue(contractResponse.getMsg().contains("模拟校验失败"));
    }

    @Test
    public void queryApplyStatus() {
        thrown.expect(CommonPubBizException.class);
        directApplyService.queryApplyStatus("merchant_sn","dev_code","crm_app");
    }

    @Test
    public void getApplyStatusByType() {
        directApplyService.getApplyStatusByType("merchant_sn", "dev_code","crm_app");
    }

    @Test
    public void applyWeixinDirectPayToManual() {
        WeixinDirectReq weixinDirectReq = new WeixinDirectReq();
        weixinDirectReq.setMerchant_sn("merchant_sn").setDev_code("dev_code");
        ContractResponse contractResponse = directApplyService.applyWeixinDirectPayToManual(weixinDirectReq);
        assertTrue(contractResponse.isSuccess());
        Mockito.doThrow(new CommonInvalidParameterException("模拟校验失败")).when(weixinDirectBiz).transferToManual(any());
        contractResponse = directApplyService.applyWeixinDirectPayToManual(weixinDirectReq);
        assertFalse(contractResponse.isSuccess());
    }

    @Test
    public void changeWeixinDirectApplyStatus() {
        ChangeApplyStatusReq changeApplyStatusReq = new ChangeApplyStatusReq();
        changeApplyStatusReq.setMerchantSn("merchant_sn").setDevCode("dev_code")
                .setStatus(WeixinDirectApplyStatus.APPLY_ACCEPTED.getVal()).setSubMerchantSn("sub_merchant_sn");
        ContractResponse contractResponse = directApplyService.changeWeixinDirectApplyStatus(changeApplyStatusReq);
        assertTrue(contractResponse.isSuccess());
    }

    @Test
    public void bindMerchantWeixinDirectPay() {
        BindMerchantReq bindMerchantReq = new BindMerchantReq();
        bindMerchantReq.setMerchant_sn("merchant_sn").setDev_code("dev_code")
                .setBind_merchant_sn("bind_merchant_sn");
        //1 提交失败 --> 校验失败
        Mockito.doThrow(new CommonInvalidParameterException("模拟校验失败")).when(weixinDirectBiz).preCheck(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code());
        ContractResponse contractResponse = directApplyService.bindMerchantWeixinDirectPay(bindMerchantReq);
        assertFalse(contractResponse.isSuccess());
        Mockito.doNothing().when(weixinDirectBiz).preCheck(bindMerchantReq.getMerchant_sn(), bindMerchantReq.getDev_code());

        //2 提交失败 --> 被绑定商户无微信直连参数
        contractResponse = directApplyService.bindMerchantWeixinDirectPay(bindMerchantReq);
        assertFalse(contractResponse.isSuccess());

        //3 主体一致 --> 直接成功
        Mockito.doReturn(Maps.newHashMap()).when(weixinDirectBiz).checkIsSameSubject(any());
        Mockito.doReturn(new MerchantProviderParamsDto()).when(paramsBiz).getDirectParams(anyString(), anyInt(), anyInt());
        contractResponse = directApplyService.bindMerchantWeixinDirectPay(bindMerchantReq);
        assertTrue(contractResponse.isSuccess());

    }

    @Test
    public void getWeixinDirectIndustry() {
        WeixinDirectIndustry result = directApplyService.getWeixinDirectIndustry("industry_id");
        assertNull(result);
    }

    @Test
    public void getDirectApplyByTaskId() {
        // 存在
        Mockito.doReturn(new WeixinDirectApply().setTask_id(100L)).when(applyMapper).selectApplyByTaskId(2585388);
        WeixinDirectApplyDto result = directApplyService.getDirectApplyByTaskId(2585388);
        assertEquals(100, result.getTask_id().intValue());
        // 不存在
        result = directApplyService.getDirectApplyByTaskId(-1);
        assertNull(result);
    }
}