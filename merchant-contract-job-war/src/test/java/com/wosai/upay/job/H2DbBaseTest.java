package com.wosai.upay.job;

import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.consumer.ContractEventConsumer;
import com.wosai.upay.job.controller.UMBController;
import com.wosai.upay.job.providers.LzbProvider;
import com.wosai.upay.job.service.LuzhouCallBackService;
import org.junit.Ignore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.jdbc.Sql;

/**
 * <AUTHOR>
 * @date 2020-05-09
 */
@SpringBootTest(properties = {"spring.datasource.url=jdbc:h2:mem:db;MODE=MySQL;", "spring.datasource.driverClassName=org.h2.Driver"})
@Sql(scripts = {"classpath:h2.sql", "classpath:init.sql"})
@Ignore
public class H2DbBaseTest extends BaseTest {

    @MockBean
    private RuleContext ruleContext;

    @MockBean
    private LzbProvider lzbProvider;

    @MockBean
    private LuzhouCallBackService luzhouCallBackService;

    @MockBean
    private UMBController umbController;
}
