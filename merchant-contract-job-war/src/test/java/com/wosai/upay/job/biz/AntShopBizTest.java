package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.alipay.api.request.AlipayOpenSpOperationApplyRequest;
import com.alipay.api.request.AlipayOpenSpOperationResultQueryRequest;
import com.alipay.api.response.AlipayOpenSpOperationApplyResponse;
import com.alipay.api.response.AlipayOpenSpOperationResultQueryResponse;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.model.Store;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.AntShopTaskConstant;
import com.wosai.upay.job.Constants.BlueSeaConstant;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.mapper.AntShopTaskMapper;
import com.wosai.upay.job.model.AntShopTaskExtra;
import com.wosai.upay.job.model.DO.AntShopTask;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.util.TxnSeqNoWorker;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.service.BlueSeaService;
import com.wosai.upay.merchant.contract.service.NewBlueSeaService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

public class AntShopBizTest extends H2DbBaseTest {

    @InjectMocks
    private AntShopBiz antShopBiz;

    @Mock
    private MerchantService merchantService;

    @Mock
    private BlueSeaBiz blueSeaBiz;

    @Autowired
    private AntShopTaskMapper antShopTaskMapper;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Autowired
    private TxnSeqNoWorker txnSeqNoWorker;

    @Mock
    private NewBlueSeaService newBlueSeaService;

    @Mock
    private BlueSeaService blueSeaService;

    @Mock
    private ComposeAcquirerBiz composeAcquirerBiz;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(antShopBiz, "antShopTaskMapper", antShopTaskMapper);
        ReflectionTestUtils.setField(antShopBiz, "txnSeqNoWorker", txnSeqNoWorker);
    }

    @Test
    public void handleAntShop() {
        String merchantSn = "merchantSn";
        String merchantId = "merchantId";
        String storeSn = "storeSn";
        Integer businessType = AntShopTaskConstant.BusinessType.SCAN_CODE;
        // 1 商户信息不存在
        CommonResult result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(CommonResult.BIZ_FAIL, result.getCode());

        // 2 门店信息不存在
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, merchantId)).when(merchantService).getMerchantBySn(merchantSn);
        result = antShopBiz.handleAntShop(merchantSn, null, businessType);
        assertEquals(CommonResult.BIZ_FAIL, result.getCode());

        // 3 门店merchant_id和merchant_sn的id不一致
        Mockito.doReturn(CollectionUtil.hashMap(Store.MERCHANT_ID, "noEqualMerchantId")).when(blueSeaBiz).getStoreBySn(storeSn);
        result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(CommonResult.BIZ_FAIL, result.getCode());

        // 4 支付宝子商户号为空
        Mockito.doReturn(CollectionUtil.hashMap(Store.MERCHANT_ID, merchantId)).when(blueSeaBiz).getStoreBySn(storeSn);
        Mockito.doReturn(new Tuple2<>("", false)).when(blueSeaBiz).getInUseMchId(merchantSn);
        result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(CommonResult.BIZ_FAIL, result.getCode());

        // 5 支付宝门店创建中
        AntShopTask antShopTask = AntShopTask.builder().id(1L).merchant_id(merchantId).merchant_sn(merchantSn).status(AntShopTaskConstant.TaskStatus.WAIT_OPERATION_APPLY).build();
        antShopTaskMapper.insert(antShopTask);
        Mockito.doReturn(new Tuple2<>("2088888888", true)).when(blueSeaBiz).getInUseMchId(merchantSn);
        result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(210, result.getCode());

        // 6 支付宝门店创建成功
        antShopTask.setStatus(AntShopTaskConstant.TaskStatus.SUCCESS);
        antShopTaskMapper.updateByPrimaryKey(antShopTask);
        result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(CommonResult.SUCCESS, result.getCode());
    }

    /**
     * 直连相关
     */
    @Test
    public void handleAntShop02() {
        String merchantSn = "merchantSn";
        String merchantId = "merchantId";
        String storeSn = "storeSn";
        String storeSn2 = "storeSn2";
        Integer businessType = AntShopTaskConstant.BusinessType.SCAN_CODE;

        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, merchantId)).when(merchantService).getMerchantBySn(merchantSn);
        Mockito.doReturn(CollectionUtil.hashMap(Store.MERCHANT_ID, "noEqualMerchantId")).when(blueSeaBiz).getStoreBySn(storeSn);
        Mockito.doReturn(CollectionUtil.hashMap(Store.MERCHANT_ID, merchantId)).when(blueSeaBiz).getStoreBySn(storeSn);
        Mockito.doReturn(CollectionUtil.hashMap(Store.MERCHANT_ID, merchantId)).when(blueSeaBiz).getStoreBySn(storeSn2);


        // 7 直连支付宝商户号不正确
        Mockito.doReturn(new Tuple2<>("2087888888", true)).when(blueSeaBiz).getInUseMchId(merchantSn);
        CommonResult result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(CommonResult.BIZ_FAIL, result.getCode());


        // 8 直连支付宝商户 状态为代运营
        Mockito.doReturn(new Tuple2<>("2088888888", true)).when(blueSeaBiz).getInUseMchId(merchantSn);
        result = antShopBiz.handleAntShop(merchantSn, storeSn, businessType);
        assertEquals(210, result.getCode());
        List<AntShopTask> antShopTasks = antShopTaskMapper.selectByStoreSn(merchantSn, storeSn, "2088888888");
        assertEquals(AntShopTaskConstant.TaskStatus.WAIT_OPERATION_APPLY, antShopTasks.get(0).getStatus().intValue());
        AntShopTaskExtra extra = JSON.parseObject(antShopTasks.get(0).getExtra(), AntShopTaskExtra.class);
        assertTrue(extra.getIsDirect());

        // 9 直连支付宝商户 状态为待创建蚂蚁门店,有过成功的记录，返回值直接返回SUCCESS
        antShopTaskMapper.updateByPrimaryKey(antShopTasks.get(0).setStatus(AntShopTaskConstant.TaskStatus.SUCCESS));
        Mockito.doReturn(new Tuple2<>("2088888888", true)).when(blueSeaBiz).getInUseMchId(merchantSn);
        result = antShopBiz.handleAntShop(merchantSn, storeSn2, businessType);
        assertEquals(CommonResult.SUCCESS, result.getCode());
        AntShopTask antShopTask = antShopTaskMapper.selectByMerchantSnAndALiMchId(merchantSn, "2088888888", AntShopTaskConstant.TaskStatus.QUALIFY);
        assertNotNull(antShopTask);
        extra = JSON.parseObject(antShopTasks.get(0).getExtra(), AntShopTaskExtra.class);
        assertTrue(extra.getIsDirect());
    }

    /**
     * 发起代运营操作
     */
    @Test
    public void handleSubmitOperationApply() {
        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setId(2L).setAli_mch_id("ali_mch_id").setExtra(JSON.toJSONString(new AntShopTaskExtra().setIsDirect(true)))
            .setRetry(5).setMerchant_sn("merchantSn").setMerchant_id("merchantId");
        antShopTaskMapper.insert(antShopTask);

        AlipayOpenSpOperationApplyRequest request = new AlipayOpenSpOperationApplyRequest();
        AlipayOpenSpOperationApplyResponse applyResponse = new AlipayOpenSpOperationApplyResponse();
        applyResponse.setBatchNo("20210630");
        AliCommResponse<AlipayOpenSpOperationApplyRequest, AlipayOpenSpOperationApplyResponse> response = new AliCommResponse<>();
        response.setReq(request).setResp(applyResponse);

        Mockito.doReturn(response).when(newBlueSeaService).alipayOpenSpOperationApply(any());

        // 1 发起代运营成功 状态变为已提交运营审核，extra中存储了batchNo
        response.setCode(200);
        antShopBiz.handleSubmitOperationApply(antShopTask);
        AntShopTask result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(AntShopTaskConstant.TaskStatus.ALREADY_OPERATION_APPLY, result.getStatus().intValue());
        AntShopTaskExtra extra = JSON.parseObject(result.getExtra(), AntShopTaskExtra.class);
        assertEquals(applyResponse.getBatchNo(), extra.getBatchNo());

        // 2 发起代运营系统异常, retry增加1
        response.setCode(500);
        antShopBiz.handleSubmitOperationApply(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(antShopTask.getRetry() + 1, result.getRetry().intValue());

        // 3 业务异常 直接失败
        response.setCode(400);
        antShopBiz.handleSubmitOperationApply(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(AntShopTaskConstant.TaskStatus.FAIL, result.getStatus());
    }

    /**
     * 查询运营审核状态
     */
    @Test
    public void queryOperationApplyResult() {
        AntShopTask antShopTask = new AntShopTask();
        antShopTask.setId(3L).setAli_mch_id("ali_mch_id").setExtra(JSON.toJSONString(new AntShopTaskExtra().setIsDirect(true).setSubmitOperationTime(System.currentTimeMillis())))
                .setRetry(5).setMerchant_sn("merchantSn").setMerchant_id("merchantId");
        antShopTaskMapper.insert(antShopTask);

        AlipayOpenSpOperationResultQueryRequest request = new AlipayOpenSpOperationResultQueryRequest();
        AlipayOpenSpOperationResultQueryResponse queryResponse = new AlipayOpenSpOperationResultQueryResponse();
        AliCommResponse<AlipayOpenSpOperationResultQueryRequest, AlipayOpenSpOperationResultQueryResponse> response = new AliCommResponse<>();
        response.setReq(request).setResp(queryResponse);
        Mockito.doReturn(response).when(newBlueSeaService).alipayOpenSpOperationResultQuery(any());

        // 1 代运营操作授权返回未完成
        response.setCode(200);
        queryResponse.setHandleStatus(BlueSeaConstant.HandleStatus.HANDLE_STATUS_PROCESSS);
        antShopBiz.queryOperationApplyResult(antShopTask);
        AntShopTask result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(antShopTask.getRetry() + 1, result.getRetry().intValue());

        // 2 代运营操作授权成功
        response.setCode(200);
        queryResponse.setHandleStatus(BlueSeaConstant.HandleStatus.HANDLE_STATUS_SUCCESS);
        antShopBiz.queryOperationApplyResult(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(AntShopTaskConstant.TaskStatus.QUALIFY, result.getStatus());

        // 3 代运营操作返回系统异常
        response.setCode(500);
        antShopBiz.queryOperationApplyResult(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(antShopTask.getRetry() + 1, result.getRetry().intValue());

        // 4 代运营操作返回业务异常
        response.setCode(400);
        antShopBiz.queryOperationApplyResult(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(AntShopTaskConstant.TaskStatus.FAIL, result.getStatus());

        // 5 超时了一天
        antShopTask.setExtra(JSON.toJSONString(new AntShopTaskExtra().setSubmitOperationTime(1605034705386L)));
        antShopTaskMapper.updateByPrimaryKey(antShopTask);
        antShopBiz.queryOperationApplyResult(antShopTask);
        result = antShopTaskMapper.selectByPrimaryKey(antShopTask.getId());
        assertEquals(AntShopTaskConstant.TaskStatus.FAIL, result.getStatus());


    }
}