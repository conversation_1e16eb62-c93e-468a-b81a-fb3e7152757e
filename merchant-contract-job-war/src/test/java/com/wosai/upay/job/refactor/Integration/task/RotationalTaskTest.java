package com.wosai.upay.job.refactor.Integration.task;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.job.refactor.task.BnsInActiveMerchantCleanTask;
import com.wosai.upay.job.refactor.task.rotational.FuYouBusinessLicenseManualAuditSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.service.FuyouService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Optional;

/**
 * 轮询任务测试
 *
 * <AUTHOR>
 * @date 2025/04/24 15:08
 */
public class RotationalTaskTest extends BaseTest {

    @Resource
    private RotationalTask rotationalTask;

    @Autowired
    private FuyouService fuyouService;

    @Autowired
    protected ContractParamsBiz contractParamsBiz;

    @Resource
    private ContractTaskDAO contractTaskDAO;


    @Test
    public void testCreateTask() {
        String contractId = "888888888";
        RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                .merchantSn("21690004008314")
                .contractTaskId(44291358435L)
                .contractSubTaskId(1824343L)
                .rotationId(contractId)
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.FU_YOU_LICENSE_MANUAL_AUDIT)
                .belongToContractTask(true)
                .addParam(FuYouBusinessLicenseManualAuditSubTaskProcessor.PROVIDER_MERCHANT_ID, "0749226FY433337")
                .build();
        rotationalTask.buildTaskForContractTask(rotationalTaskContext);
    }


    @Test
    public void testManualReview() {
        Optional<ContractTaskDO> taskOpt = contractTaskDAO.getByPrimaryKey(44291358435L);
        if (!taskOpt.isPresent()) {
            return;
        }
        fuyouService.updateBasicMerchantByManualReview("0749226FY433337", JSON.parseObject(taskOpt.get().getEventContext()),
                contractParamsBiz.buildContractParamsByPayMchId("0749226FY433337", FuyouParam.class), "");
    }

    @Test
    public void testProcessTask() {
        rotationalTask.batchHandleTasksByMainTaskIds(Lists.newArrayList(13154L));
    }


    @Test
    public void testChangeCardLkl() {
        String merchantSn = "21690004050220";
        String contractId = "21690003464117";
        String auditId = "493814";
        RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                .belongToContractTask(false)
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT)
                .merchantSn(merchantSn)
                .rotationId(contractId)
                .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_KEY, AcquirerTypeEnum.LKL_V3.getValue())
                .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.AUDIT_ID_KEY, auditId)
                .build();
        rotationalTask.buildTaskForContractTask(rotationalTaskContext);
    }


    @Test
    public void testChangeCardFuYou() {
        String merchantSn = "21690004029338";
        String fuYouAcquirerId = "2372419FY456078";
        String modifyNo = "323546665";
        String auditId = "493814";
        RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                .belongToContractTask(false)
                .subTaskTypeEnum(RotationalSubTaskTypeEnum.DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT)
                .merchantSn(merchantSn)
                .rotationId(modifyNo)
                .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_KEY, AcquirerTypeEnum.FU_YOU.getValue())
                .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.AUDIT_ID_KEY, auditId)
                .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_MERCHANT_ID, fuYouAcquirerId)
                .build();
        rotationalTask.buildTaskForContractTask(rotationalTaskContext);
    }

}
