//package com.wosai.upay.job.xxljob.direct.contracttask;
//
//import com.wosai.upay.job.BaseTest;
//import com.wosai.upay.job.mapper.ContractTaskMapper;
//import com.zaxxer.hikari.HikariDataSource;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.session.ExecutorType;
//import org.apache.ibatis.session.SqlSession;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.junit.Ignore;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.sql.DataSource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.TimeUnit;
//
//import static org.junit.Assert.*;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@ActiveProfiles("default")
//@Slf4j
//public class SqlSessionLeakTest {
//
//    @Autowired
//    private SqlSessionFactory sqlSessionFactory;sqlSessionFactory
//
//    @Autowired
//    private DataSource dataSource;
//
//    @Test
//    public void testSqlSessionLeak() throws InterruptedException {
//        HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
//        int maxPoolSize = hikariDataSource.getMaximumPoolSize();
//        log.info("Connection pool max size: {}", maxPoolSize);
//
//        // 记录初始活跃连接数
//        int initialActiveConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
//        log.info("Initial active connections: {}", initialActiveConnections);
//
//        List<SqlSession> unclosedSessions = new ArrayList<>();
//
//        try {
//            // 获取所有可用连接
//            for (int i = 0; i < maxPoolSize; i++) {
//                SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
//                ContractTaskMapper mapper = sqlSession.getMapper(ContractTaskMapper.class);
//                // 执行查询以确保实际获取到连接
//                mapper.selectByPrimaryKey(1L);
//                unclosedSessions.add(sqlSession);
//                log.info("Opened SqlSession {}, active connections: {}",
//                    i + 1, hikariDataSource.getHikariPoolMXBean().getActiveConnections());
//            }
//
//            // 验证连接池已满
//            int activeConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
//            log.info("Current active connections: {}", activeConnections);
//            assertEquals("All connections should be in use", maxPoolSize, activeConnections);
//
//            // 设置较短的连接超时时间
//            hikariDataSource.setConnectionTimeout(1000); // 1秒
//
//            // 尝试获取新的 SqlSession，应该会快速失败
//            long startTime = System.currentTimeMillis();
//            try {
//                SqlSession newSession = sqlSessionFactory.openSession();
//                ContractTaskMapper mapper = newSession.getMapper(ContractTaskMapper.class);
//                mapper.selectByPrimaryKey(1L);
//                fail("Expected connection pool exhaustion did not occur");
//            } catch (Exception e) {
//                long timeSpent = System.currentTimeMillis() - startTime;
//                log.info("Connection acquisition failed after {} ms with error: {}", timeSpent, e.getMessage());
//                assertTrue("Connection timeout exception expected",
//                    e.getMessage().contains("Connection is not available") ||
//                    e.getMessage().contains("timeout"));
//            }
//
//        } finally {
//            // 恢复默认连接超时时间
//            hikariDataSource.setConnectionTimeout(30000); // 30秒
//
//            // 清理：关闭所有未关闭的 SqlSession
//            unclosedSessions.forEach(session -> {
//                try {
//                    session.close();
//                } catch (Exception e) {
//                    log.error("Error closing SqlSession", e);
//                }
//            });
//
//            // 等待连接池恢复
//            Thread.sleep(1000);
//            int finalActiveConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
//            log.info("Active connections after cleanup: {}", finalActiveConnections);
//            assertEquals("Connection pool should return to initial state",
//                initialActiveConnections, finalActiveConnections);
//        }
//    }
//
//    @Test
//    public void testProperSqlSessionHandling() throws InterruptedException {
//        HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
//        int initialActiveConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
//
//        int numberOfOperations = 100;
//        CountDownLatch latch = new CountDownLatch(numberOfOperations);
//
//        // 使用try-with-resources正确处理SqlSession
//        for (int i = 0; i < numberOfOperations; i++) {
//            new Thread(() -> {
//                try (SqlSession sqlSession = sqlSessionFactory.openSession()) {
//                    ContractTaskMapper mapper = sqlSession.getMapper(ContractTaskMapper.class);
//                    mapper.selectByPrimaryKey(1L);
//                } catch (Exception e) {
//                    log.error("Error in thread", e);
//                } finally {
//                    latch.countDown();
//                }
//            }).start();
//        }
//
//        boolean completed = latch.await(10, TimeUnit.SECONDS);
//        assertTrue("All threads completed", completed);
//
//        // 验证连接池回到初始状态
//        int finalActiveConnections = hikariDataSource.getHikariPoolMXBean().getActiveConnections();
//        log.info("Final active connections: {}", finalActiveConnections);
//        assertEquals("Connection pool should return to initial state",
//            initialActiveConnections, finalActiveConnections);
//    }
//}