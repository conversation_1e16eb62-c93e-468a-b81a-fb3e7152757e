package com.wosai.upay.job.xxljob.batch.contracttask;

import com.google.common.collect.Lists;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.context.ContractTaskContext;
import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskBankNetInJobHandlerTest {

    @InjectMocks
    private ContractTaskBankNetInJobHandler contractTaskBankNetInJobHandler;

    @Mock
    private GeneralRuleService generalRuleService;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    private ContractTaskContext context;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Before
    public void setUp() {
        // 设置模拟行为
        when(generalRuleService.getFirstWeekDayAfterDate(Mockito.anyString())).thenReturn("2023-10-09");

        ContractTask task = new ContractTask();
        task.setId(12345L);
        task.setMerchant_sn("merchantSn");
        context = new ContractTaskContext(task, "reviewComplete");
    }

    @Test
    public void queryTaskItems_ValidInput_ReturnsContractTaskContextList() {
        // 准备
        BatchJobParam param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        ContractTask task1 = new ContractTask();
        task1.setId(1L);
        task1.setMerchant_name("Merchant1");

        ContractTask task2 = new ContractTask();
        task2.setId(2L);
        task2.setMerchant_name("Merchant2");

        List<ContractTask> tasks = Arrays.asList(task1, task2);
        when(contractTaskMapper.selectBankContractTaskTodo(Mockito.anyString(), Mockito.anyInt(),Mockito.anyList())).thenReturn(tasks);

        // 执行
        List<ContractTaskContext> result = contractTaskBankNetInJobHandler.queryTaskItems(param);

        // 验证
        assertEquals(2, result.size());
        assertEquals("2023-10-09", result.get(0).getReviewComplete());
        assertEquals("2023-10-09", result.get(1).getReviewComplete());
        assertEquals(task1, result.get(0).getTask());
        assertEquals(task2, result.get(1).getTask());
    }

    @Test
    public void getLockKey_ValidContext_ReturnsCorrectLockKey() {
        String expectedLockKey = "ContractTaskBankNetInJobHandler:merchantSn";
        String actualLockKey = contractTaskBankNetInJobHandler.getLockKey(context);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_SuccessStatus_Returns() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.SUCCESS.getVal());
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_FailStatus_Returns() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.FAIL.getVal());
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_PendingStatus_SetsCompleteAt() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.PENDING.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1))
                .thenReturn(Collections.emptyList());

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1);
        assertNotNull(contractTask.getComplete_at());
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_EmptySubTaskList_LogsInfo() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.PROGRESSING.getVal()).setMerchant_sn("merchantSn");
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(anyString(), eq(1L), eq(1)))
                .thenReturn(Collections.emptyList());

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1);
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_SubTaskInChannels_UpdatesPriority() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.PROGRESSING.getVal()).setMerchant_sn("merchantSn");
        ContractSubTask contractSubTask = new ContractSubTask().setChannel(ProviderUtil.CHANNELS.get(0)).setContract_id("123").setCreate_at(new Date()).setChannel("lklV3");
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(anyString(), eq(1L), eq(1)))
                .thenReturn(Arrays.asList(contractSubTask));

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1);
        verify(contractTaskMapper, times(1)).updatePriority(anyString(), eq(1L));
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_SubTaskNotInChannels_HandlesSubTask() throws Exception {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.PROGRESSING.getVal()).setMerchant_sn("merchantSn");
        ContractSubTask contractSubTask = new ContractSubTask().setChannel("unknown").setContract_id("123");
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenReturn(contractTask);
        when(contractSubTaskMapper.selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1))
                .thenReturn(Arrays.asList(contractSubTask));

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(contractSubTaskMapper, times(1)).selectByMerchantSnAndPTaskIdAndScheduleStatus(contractTask.getMerchant_sn(), 1, 1);
        verify(subTaskHandlerContext, times(1)).handle(eq(contractTask), eq(contractSubTask));
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_Exception_CatchesAndLogs() {
        ContractTask contractTask = new ContractTask().setId(1L).setStatus(TaskStatus.PROGRESSING.getVal());
        ContractTaskContext context = new ContractTaskContext(contractTask, "2023-10-01");

        when(contractTaskMapper.selectByPrimaryKey(1L)).thenThrow(new RuntimeException("Test exception"));

        contractTaskBankNetInJobHandler.doHandleSingleData(context);

        verify(contractTaskMapper, times(1)).selectByPrimaryKey(1L);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
        verifyNoMoreInteractions(contractTaskMapper, contractSubTaskMapper, subTaskHandlerContext, chatBotUtil);
    }
}
