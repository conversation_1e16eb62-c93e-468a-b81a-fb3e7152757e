package com.wosai.upay.job.refactor.Integration.service;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.model.dto.IndustryCodeV2DTO;
import com.wosai.upay.job.refactor.service.localcache.IndustryMappingLocalCacheService;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 行业映射本地缓存服务测试
 *
 * <AUTHOR>
 * @date 2024/3/12 13:34
 */

public class IndustryMappingLocalCacheServiceTest extends BaseTest {

    @Resource
    private IndustryMappingLocalCacheService industryMappingLocalCacheService;

    @Test
    public void testGetIndustryMappingByIndustryId() {
        Optional<IndustryCodeV2DTO> mappingDTO = industryMappingLocalCacheService.getIndustryMappingByIndustryId("3e7620ae-c30b-4bce-aac6-b88af6e0a47f");
        assertThat(mappingDTO).isPresent();
        assertThat(mappingDTO.get().getECnyCode()).isEqualTo("2043");
    }
}
