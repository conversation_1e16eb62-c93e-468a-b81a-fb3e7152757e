package com.wosai.upay.job.biz.acquirer;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.acquirer.AcquirerChangeSaveDTO;
import com.wosai.upay.job.util.CommonUtil;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class AcquirerChangeDaoTest extends BaseTest {

    private static final String merchantSn = "123";
    private static final String merchantId = "123";
    private static final String lkl = "lkl";
    private static final String tonglian = "tonglian";
    private static final Boolean immediately = Boolean.FALSE;
    private static final String tradeAppId = "1";

    @Autowired
    private AcquirerChangeDao dao;

    @Test
    public void getLatestUnFinishedApply() {
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        dao.save(dto);
        McAcquirerChange change = dao.getLatestUnFinishedApply(merchantSn);
        Assert.assertNotNull(change);
    }

    @Test
    public void getLatestSuccessApply() {
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        McAcquirerChange change = dao.save(dto);
        Assert.assertNull(dao.getLatestSuccessApply(merchantSn, lkl));
        dao.updateStatus(change, AcquirerChangeStatus.SUCCESS, "xx");
        Assert.assertNotNull(dao.getLatestSuccessApply(merchantSn, lkl));
    }

    @Test
    public void getAcquirerChangeByApplyId() {
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        McAcquirerChange change = dao.save(dto);
        Assert.assertNotNull(dao.getAcquirerChangeByApplyId(change.getApply_id()));
    }

    @Test
    public void getChangeApplies() {
        List<McAcquirerChange> changes = dao.getChangeApplies(AcquirerChangeStatus.PENDING, 10, 2 * 60 * 60 * 1000L);
        Assert.assertEquals(0, changes.size());

        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        dao.save(dto);
        changes = dao.getChangeApplies(AcquirerChangeStatus.PENDING, 10, 2 * 60 * 60 * 1000L);
        Assert.assertEquals(1, changes.size());
    }

    @Test
    public void updateStatusWitExtra() {
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        McAcquirerChange change = dao.save(dto);
        dao.updateStatusWitExtra(change, AcquirerChangeStatus.SUCCESS, "xx", CollectionUtil.hashMap(
                "aa", "aa"
        ));
        change = dao.getAcquirerChangeByApplyId(change.getApply_id());
        Assert.assertEquals(AcquirerChangeStatus.SUCCESS, (int) change.getStatus());
        Assert.assertEquals("xx", change.getMemo());
        Map extra = CommonUtil.string2Map(change.getExtra());
        Assert.assertEquals("aa", extra.get("aa"));
    }

    @Test
    public void appendExtra() {
        final AcquirerChangeSaveDTO dto = new AcquirerChangeSaveDTO();
        dto.setMerchantSn(merchantSn);
        dto.setMerchantId(merchantId);
        dto.setSourceAcquirer(lkl);
        dto.setTargetAcquirer(tonglian);
        dto.setImmediately(immediately);
        dto.setTradeAppId(tradeAppId);
        dto.setForceChange(false);
        McAcquirerChange change = dao.save(dto);
        dao.appendExtra(change, CollectionUtil.hashMap(
                "aa", "aa"
        ));
        change = dao.getAcquirerChangeByApplyId(change.getApply_id());
        Map extra = CommonUtil.string2Map(change.getExtra());
        Assert.assertEquals("aa", extra.get("aa"));
    }
}