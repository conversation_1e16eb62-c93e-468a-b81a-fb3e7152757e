package com.wosai.upay.job.providers;


import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.biz.AgentAppidBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.model.*;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.service.AcquirerService;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.merchant.contract.service.TongLianService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class TongLianProviderTest extends BaseTest {

    private static ContractRule rule;

    static {
        rule = new ContractRule();
        rule.setIs_insert_influ_ptask(true);
        rule.setIs_update_influ_ptask(true);
        rule.setIs_default(true);
    }

    @Autowired
    TongLianProvider tongLianProvider;
    @MockBean
    PayForProvider payForProvider;
    @MockBean(name = "tongLianService")
    TongLianService tongLianService;
    @MockBean
    AcquirerService acquirerService;
    @MockBean
    ComposeAcquirerBiz composeAcquirerBiz;
    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    RuleContext ruleContext;
    @MockBean
    AgentAppidBiz agentAppidBiz;

    @Before
    public void before() {
        ReflectionTestUtils.setField(tongLianProvider, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(tongLianProvider, "tongLianService", tongLianService);
    }

    @Test
    public void produceInsertTaskByRule() {
        tongLianProvider.produceInsertTaskByRule("sn", new ContractEvent(), new HashMap<>(), rule);
    }

    @Test
    public void produceUpdateTaskByRule() {
        ContractEvent event = new ContractEvent().setEvent_type(4);
        tongLianProvider.produceUpdateTaskByRule("sn", event, new HashMap<>(), rule);
        event.setEvent_type(1);
        Map context = new HashMap<>();
        Map bank = CollectionUtil.hashMap("bank", "bank");
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("cardRequestParam", bank);
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(0);
        List<String> msg = Arrays.asList("msg111");
        Map eventContext = CollectionUtil.hashMap("msg", msg);
        event.setEvent_msg(JSON.toJSONString(eventContext));
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(2);
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        event.setEvent_type(9);
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "0");
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "1");
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
        context.put("crmUpdate", "2");
        tongLianProvider.produceUpdateTaskByRule("sn", event, context, rule);
    }

    @Test
    public void processInsertTaskByRule() {
        //不需要代付
        ContractTask task = new ContractTask();
        Map context = new HashMap();
        task.setEvent_context(JSON.toJSONString(context));
        Mockito.doReturn(AcquirerTypeEnum.LKL.getValue()).when(acquirerService).getMerchantAcquirer(Mockito.anyString());
        ContractSubTask subTask = new ContractSubTask();
        subTask.setPayway(0);
        Mockito.doReturn(AcquirerTypeEnum.TONG_LIAN.getValue()).when(composeAcquirerBiz).getMerchantAcquirer(Mockito.anyString());
        tongLianProvider.processInsertTaskByRule(task, new ContractChannel(), subTask);
        subTask.setPayway(2);
        tongLianProvider.processInsertTaskByRule(task, new ContractChannel(), subTask);
        subTask.setPayway(3);
        tongLianProvider.processInsertTaskByRule(task, new ContractChannel(), subTask);
        //需要代付
        Map bank = new HashMap();
        context.put("bankAccount", bank);
        bank.put("type", 1);
        bank.put("id_type", 3);
        subTask.setPayway(0);
        task.setEvent_context(JSON.toJSONString(context));
        Mockito.doReturn(AcquirerTypeEnum.TONG_LIAN.getValue()).when(acquirerService).getMerchantAcquirer(Mockito.anyString());
        tongLianProvider.processInsertTaskByRule(task, new ContractChannel(), subTask);
        Mockito.doReturn(new PayForTask()).when(payForProvider).produceTask(Mockito.anyMap(), Mockito.anyObject());
        task.setType(ProviderUtil.CONTRACT_TYPE_INSERT);
        subTask.setMerchant_sn("processInsertTaskByRule");
        tongLianProvider.processInsertTaskByRule(task, new ContractChannel(), subTask);
    }

    @Test
    public void processUpdateTaskByRule() {
        ContractTask task = new ContractTask();
        task.setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT).setEvent_context("{}");
        ContractSubTask subTask = new ContractSubTask();
        subTask.setPayway(0);
        tongLianProvider.processUpdateTaskByRule(task, new ContractChannel(), subTask);
        subTask.setPayway(2);
        tongLianProvider.processUpdateTaskByRule(task, new ContractChannel(), subTask);
        subTask.setPayway(3);
        tongLianProvider.processUpdateTaskByRule(task, new ContractChannel(), subTask);
        task.setType(ProviderUtil.CONTRACT_TYPE_UPDATE_BANK_ACCOUNT);
        List<Integer> subTypes = Arrays.asList(ProviderUtil.SUB_TASK_TASK_TYPE_CONTRACT, ProviderUtil.SUB_TASK_TASK_TYPE_SETTLEMENT_ACCOUNTS
                , ProviderUtil.SUB_TASK_TASK_TYPE_BASIC_INFORMATION, ProviderUtil.SUB_TASK_TASK_TYPE_CARD_UPDATE, ProviderUtil.SUB_TASK_TASK_TYPE_CRM_BOTH
                , ProviderUtil.SUB_TASK_TASK_TYPE_STATUS_UPDATE, ProviderUtil.SUB_TASK_TASK_TYPE_MERCHANT_FEERATE);
        subTypes.forEach(r -> {
            subTask.setTask_type(r);
            tongLianProvider.processUpdateTaskByRule(task, new ContractChannel(), subTask);
        });
    }
}
