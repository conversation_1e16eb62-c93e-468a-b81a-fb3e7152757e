package com.wosai.upay.job.providers;


import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.service.MerchantEnrolService;
import com.wosai.upay.bank.model.verify.AccountApplyResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.enume.ScheduleEnum;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.PayForTask;
import com.wosai.upay.job.service.TaskResultService;
import com.wosai.upay.remit.exception.RemitException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;


public class PayForProviderTest extends BaseTest {

    @Autowired
    PayForProvider payForProvider;
    @MockBean
    private ContractTaskMapper contractTaskMapper;
    @MockBean
    private ContractSubTaskMapper contractSubTaskMapper;

    @MockBean
    private TaskResultService taskResultService;

    @MockBean
    private AccountVerifyService accountVerifyService;
    @MockBean
    private MerchantEnrolService merchantEnrolService;
    @SpyBean
    private ApplicationApolloConfig applicationApolloConfig;


    @Before
    public void before() {
        ReflectionTestUtils.setField(payForProvider, "taskResultService", taskResultService);
        ReflectionTestUtils.setField(payForProvider, "accountVerifyService", accountVerifyService);
        ReflectionTestUtils.setField(payForProvider, "merchantEnrolService", merchantEnrolService);
    }


    @Test
    public void produceTask() {
        Mockito.doReturn(true).when(merchantEnrolService).invokeNewEnrolRule(Mockito.anyString());
        Mockito.doReturn(true).when(applicationApolloConfig).getPublicPayForRule();
        Map bank = CollectionUtil.hashMap("type", 1, "id_type", 3);
        Map context = CollectionUtil.hashMap("bankAccount", bank);
        payForProvider.produceTask(context, new ContractSubTask().setSchedule_dep_task_id(0L).setSchedule_status(ScheduleEnum.SCHEDULE_ENABLE.getValue()));
    }

    @Test
    public void processTask() {
        PayForTask payForTask = new PayForTask();
        payForTask.setContext_param("{\n" +
                "\"bankAccount\":{\n" +
                "\"id\":\"id\"\n" +
                "}\n" +
                "\n" +
                "}");
        AccountApplyResp resp = new AccountApplyResp();
        resp.setSyns(true).setLegal(true);
        Mockito.doReturn(resp).when(accountVerifyService).apply(Mockito.anyObject());
        Mockito.doReturn(new ContractTask().setEvent_context("{\n" +
                "\"bankAccount\":{\n" +
                "\"id\":\"id\"\n" +
                "}\n" +
                "\n" +
                "}")).when(contractTaskMapper).selectByPrimaryKey(Mockito.anyLong());
        Mockito.doReturn(new ContractSubTask()).when(contractSubTaskMapper).selectByPrimaryKey(Mockito.anyLong());
        payForProvider.processTask(payForTask);
        resp.setSyns(false).setNeed_verify(true);
        payForProvider.processTask(payForTask);
        resp.setSyns(false).setNeed_verify(true);
        payForProvider.processTask(payForTask);
        Mockito.doThrow(new RemitException("test")).when(accountVerifyService).apply(Mockito.anyObject());
        payForProvider.processTask(payForTask);
        Mockito.doThrow(new RuntimeException("throwable")).when(accountVerifyService).apply(Mockito.anyObject());
        payForProvider.processTask(payForTask);

    }
}
