package com.wosai.upay.job.util;

import com.wosai.upay.side.service.GeneralRuleService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DateUtilTest {

    @InjectMocks
    private DateUtil dateUtil;
    @Mock
    private GeneralRuleService generalRuleService;

    @Test
    public void getNonWorkingDaysBefore01() {
        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-07");
        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-06");
        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-05");
        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-04");
        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-03");
        List<LocalDate> notSendDate = dateUtil.getNonWorkingDaysBefore(LocalDate.parse("2024-10-08", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        Assert.assertEquals(6, notSendDate.size());
        Assert.assertEquals("2024-10-03", notSendDate.get(5).toString());
        Assert.assertEquals("2024-10-04", notSendDate.get(4).toString());
        Assert.assertEquals("2024-10-05", notSendDate.get(3).toString());
        Assert.assertEquals("2024-10-06", notSendDate.get(2).toString());
        Assert.assertEquals("2024-10-07", notSendDate.get(1).toString());
        Assert.assertEquals("2024-10-08", notSendDate.get(0).toString());
    }

    @Test
    public void getNonWorkingDaysBefore02() {
        List<LocalDate> notSendDate = dateUtil.getNonWorkingDaysBefore(LocalDate.parse("2024-10-09", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        Assert.assertEquals(1, notSendDate.size());
        Assert.assertEquals("2024-10-09", notSendDate.get(0).toString());

        Mockito.doReturn(true).when(generalRuleService).isHoliday("2024-10-08");
        notSendDate = dateUtil.getNonWorkingDaysBefore(LocalDate.parse("2024-10-08", DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        Assert.assertEquals(0, notSendDate.size());
    }
}