package com.wosai.upay.job.refactor.event;


import com.google.common.cache.Cache;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.RuleGroup;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.refactor.dao.McAcquirerDAO;
import com.wosai.upay.job.util.ProviderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskEventListenerTest {

    @InjectMocks
    private ContractTaskEventListener contractTaskEventListener;

    @Mock
    private RuleContext ruleContext;

    @Mock
    private ContractTaskEvent contractTaskEvent;

    @Mock
    private ContractTask contractTask;

    @Mock
    private MonitorLog monitorLog;

    @Before
    public void setUp() {
        when(contractTaskEvent.getContractTask()).thenReturn(contractTask);
        Cache cache = (Cache) ReflectionTestUtils.getField(contractTaskEventListener, ContractTaskEventListener.class, "CACHE");
        cache.invalidateAll();
    }

    @Test
    public void onApplicationEvent_RuleGroupIdEmpty_ShouldReturn() {
        when(contractTask.getRule_group_id()).thenReturn("");

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(contractTask, never()).getType();
        verify(contractTask, never()).getStatus();
    }

    @Test
    public void onApplicationEvent_TypeNotInList_ShouldReturn() {
        when(contractTask.getRule_group_id()).thenReturn("someId");
        when(contractTask.getType()).thenReturn("unknownType");

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(contractTask, never()).getStatus();
    }

    @Test
    public void onApplicationEvent_StatusNotFinished_ShouldReturn() {
        when(contractTask.getRule_group_id()).thenReturn("someId");
        when(contractTask.getType()).thenReturn(ProviderUtil.CONTRACT_TYPE_INSERT);
        when(contractTask.getStatus()).thenReturn(TaskStatus.PROGRESSING.getVal());

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(ruleContext, never()).getRuleGroup(any());
    }

    @Test
    public void onApplicationEvent_AcquirerNameEmpty_ShouldLogError() {
        when(contractTask.getRule_group_id()).thenReturn("someId");
        when(contractTask.getType()).thenReturn(ProviderUtil.CONTRACT_TYPE_INSERT);
        when(contractTask.getStatus()).thenReturn(TaskStatus.SUCCESS.getVal());
        doReturn(null).when(ruleContext).getRuleGroup("someId");

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(monitorLog, never()).recordObject(any());
    }

    @Test
    public void onApplicationEvent_AllConditionsMet_ShouldCallTaskResultSuccess() {
        when(contractTask.getRule_group_id()).thenReturn("someId");
        when(contractTask.getType()).thenReturn(ProviderUtil.CONTRACT_TYPE_INSERT);
        when(contractTask.getStatus()).thenReturn(TaskStatus.SUCCESS.getVal());
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        doReturn(ruleGroup).when(ruleContext).getRuleGroup("someId");

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(monitorLog).recordObject(any());
    }

    @Test
    public void onApplicationEvent_AllConditionsMet_ShouldCallTaskResultFail() {
        when(contractTask.getRule_group_id()).thenReturn("someId");
        when(contractTask.getType()).thenReturn(ProviderUtil.CONTRACT_TYPE_INSERT);
        when(contractTask.getStatus()).thenReturn(TaskStatus.FAIL.getVal());
        when(contractTask.getResult()).thenReturn("{\"channel\":\"haike\",\"message\":\"信息不一致\",\"code\":460,\"result\":\"信息不一致\",\"payway\":0}");

        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        doReturn(ruleGroup).when(ruleContext).getRuleGroup("someId");

        contractTaskEventListener.onApplicationEvent(contractTaskEvent);

        verify(monitorLog).recordObject(any());
    }
}
