package com.wosai.upay.job.xxljob.batch.contracttask;

import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.ProviderUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractTaskFuyouWaitForCallbackJobHandlerTest {

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ContractTaskMapper contractTaskMapper;

    @Mock
    private SubTaskHandlerContext subTaskHandlerContext;

    @Mock
    private ChatBotUtil chatBotUtil;

    @InjectMocks
    private ContractTaskFuyouWaitForCallbackJobHandler handler;

    private BatchJobParam param;

    private ContractSubTask subTask;

    @Before
    public void setUp() {
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L); // 1秒

        subTask = new ContractSubTask();
        subTask.setId(1L);
        subTask.setP_task_id(2L);
        subTask.setMerchant_sn("testMerchant");
        subTask.setTask_type(1);
    }

    @Test
    public void queryTaskItems_ValidParam_ReturnsTasks() {
        List<ContractSubTask> expectedTasks = new ArrayList<>();
        expectedTasks.add(new ContractSubTask());
        expectedTasks.add(new ContractSubTask());

        when(contractSubTaskMapper.getNoInfluenceSubTaskWaitCallBack(any(), any()))
                .thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = handler.queryTaskItems(param);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void doHandleSingleData_StatusSuccess_Returns() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.SUCCESS.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(subTask.getId())).thenReturn(subTaskLast);

        handler.doHandleSingleData(subTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(subTask.getId());
        verifyNoInteractions(contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_StatusFail_Returns() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.FAIL.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(subTask.getId())).thenReturn(subTaskLast);

        handler.doHandleSingleData(subTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(subTask.getId());
        verifyNoInteractions(contractTaskMapper, subTaskHandlerContext, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_FuyouUnionpay_CallsHandle() throws Exception {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.PROGRESSING.getVal());
        subTaskLast.setChannel(ProviderUtil.FUYOU_CHANNEL);
        subTaskLast.setPayway(PaywayEnum.UNIONPAY.getValue());

        ContractTask task = new ContractTask();

        when(contractSubTaskMapper.selectByPrimaryKey(subTask.getId())).thenReturn(subTaskLast);
        when(contractTaskMapper.selectByPrimaryKey(subTask.getP_task_id())).thenReturn(task);

        handler.doHandleSingleData(subTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(subTask.getId());
        verify(contractTaskMapper, times(1)).selectByPrimaryKey(subTask.getP_task_id());
        verify(subTaskHandlerContext, times(1)).handle(task, subTaskLast);
        verifyNoInteractions(chatBotUtil);
    }

    @Test
    public void doHandleSingleData_Exception_SendsWarningMessage() {
        ContractSubTask subTaskLast = new ContractSubTask();
        subTaskLast.setStatus(TaskStatus.PROGRESSING.getVal());

        when(contractSubTaskMapper.selectByPrimaryKey(subTask.getId())).thenThrow(new RuntimeException("Test exception"));

        handler.doHandleSingleData(subTask);

        verify(contractSubTaskMapper, times(1)).selectByPrimaryKey(subTask.getId());
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
        verifyNoInteractions(contractTaskMapper, subTaskHandlerContext);
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        subTask.setId(123L);
        String lockKey = handler.getLockKey(subTask);
        assertEquals("ContractTaskFuyouWaitForCallbackJobHandler:123", lockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        subTask.setId(null);
        String lockKey = handler.getLockKey(subTask);
        assertEquals("ContractTaskFuyouWaitForCallbackJobHandler:null", lockKey);
    }
}
