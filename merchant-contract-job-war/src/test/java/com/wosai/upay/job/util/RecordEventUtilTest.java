package com.wosai.upay.job.util;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.service.SelfHelpNetInEventService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.HashMap;
import java.util.Map;


public class RecordEventUtilTest extends BaseTest {


    @Autowired
    RecordEventUtil recordEventUtil;

    @MockBean
    SelfHelpNetInEventService selfHelpNetInEventService;


    @Test
    public void createEventAfterChangeMerchant() {
        String[] msg = {"key", "key2", "key3"};
        Map before = CollectionUtil.hashMap("key", "key", "key2", "key2", "key3", "key3");
        Map after = CollectionUtil.hashMap("key", "keya", "key2a", "key2", "key3", "key3a");
        recordEventUtil.createEventAfterChangeMerchant(before, after, "2121", new HashMap(), msg);
    }
}
