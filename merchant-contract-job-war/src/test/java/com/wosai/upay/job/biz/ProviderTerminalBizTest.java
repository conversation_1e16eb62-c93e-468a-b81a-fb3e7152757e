package com.wosai.upay.job.biz;

import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.StoreService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import com.wosai.upay.job.providers.LklV3Provider;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.Resource;

public class ProviderTerminalBizTest extends BaseTest {

    @Autowired
    private ProviderTerminalBiz providerTerminalBiz;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private StoreService storeService;

    @Test
    public void boundTerminalTest() {
        ProviderTerminalTask task = getProviderTerminalTask(848608);
        providerTerminalBiz.boundTerminal(task);
    }

    @Resource
    private LklV3Provider lklV3Provider;

    @Test
    public void testDoCreateProviderTerminal() {
        lklV3Provider.doCreateProviderTerminal("21690003710457", 1032);
    }

    private ProviderTerminalTask getProviderTerminalTask(int id) {
        String format = "select * from `provider_terminal_task` where `id`=%d";
        String sql = String.format(format, id);
        ProviderTerminalTask p = jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<ProviderTerminalTask>(ProviderTerminalTask.class));
        return p;
    }

    @Test
    public void test() {
        getProviderTerminalTask(221);
//        Map map = merchantService.getMerchantBySn("**************");
//        System.out.println("ok");
    }

    @Test
    public void merchantConnectionProviderTerminalTest() {
        providerTerminalBiz.merchantConnectionProviderTerminal("**************","********", "provider_merchant_id", 1023);
        System.out.println("ok");
    }

    @Test
    public void lklBankCardTermNoResultQuery(){
        providerTerminalBiz.initLklBankCardTermNoTask("**************","A7181912","****************",null,null);
    }
}
