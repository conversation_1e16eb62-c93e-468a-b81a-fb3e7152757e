package com.wosai.upay.job.xxljob.direct.alidirect;

import com.alipay.api.request.AlipayOpenAgentFacetofaceSignRequest;
import com.alipay.api.response.AlipayOpenAgentFacetofaceSignResponse;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.AliDirectParamBuilder;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class AliDirectSubmitApplyJobHandlerTest {

    @InjectMocks
    private AliDirectSubmitApplyJobHandler handler;

    @Mock
    private AliPayDirectService aliPayDirectService;

    @Mock
    private AliDirectParamBuilder aliParamBuilder;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private AliDirectApplyMapper applyMapper;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private ContractTaskBiz taskBiz;

    @Mock
    private ContractTaskMapper taskMapper;

    @Mock
    private MonitorLog monitorLog;

    @Value("${ali.direct}")
    private String aliDirectDevCode;


    @Test
    public void submitApplySuccess() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.WAIT_FOR_SUBMITTING.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        AliCommResponse<AlipayOpenAgentFacetofaceSignRequest, AlipayOpenAgentFacetofaceSignResponse> response = new AliCommResponse<>();
        AlipayOpenAgentFacetofaceSignResponse createResponse = new AlipayOpenAgentFacetofaceSignResponse();
        response.setCode(200).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentFacetofaceSign(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, Mockito.times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void submitApplySysException() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.WAIT_FOR_SUBMITTING.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        AliCommResponse<AlipayOpenAgentFacetofaceSignRequest, AlipayOpenAgentFacetofaceSignResponse> response = new AliCommResponse<>();
        AlipayOpenAgentFacetofaceSignResponse createResponse = new AlipayOpenAgentFacetofaceSignResponse();
        response.setCode(500).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentFacetofaceSign(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, Mockito.times(1)).updateByPrimaryKeySelective(any());
        Mockito.verify(monitorLog, Mockito.times(1)).recordMonitor(any(), any());

    }

    @Test
    public void submitApplyFail() {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.WAIT_FOR_SUBMITTING.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        applyMapper.insertSelective(apply);
        AliCommResponse<AlipayOpenAgentFacetofaceSignRequest, AlipayOpenAgentFacetofaceSignResponse> response = new AliCommResponse<>();
        AlipayOpenAgentFacetofaceSignResponse createResponse = new AlipayOpenAgentFacetofaceSignResponse();
        response.setCode(405).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentFacetofaceSign(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        Mockito.doReturn(new ContractTask().setEvent_context("{}")).when(taskMapper).selectByPrimaryKey(111L);

        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());
    }

}