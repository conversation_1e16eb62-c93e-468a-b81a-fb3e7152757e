package com.wosai.upay.job.xxljob.direct.authandcombo;

import com.wosai.model.SystemResponse;
import com.wosai.service.SystemService;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AuthAndComboSubmitJobHandlerTest {

    @InjectMocks
    private AuthAndComboSubmitJobHandler authAndComboSubmitJobHandler;

    @Mock
    private AuthAndComboTaskMapper authAndComboTaskMapper;

    @Mock
    private AuthAndComboTaskBiz authAndComboTaskBiz;

    @Mock
    private SystemService systemService;

    private DirectJobParam param;

    @Before
    public void setUp() {
        param = new DirectJobParam();
        param.setQueryTime(1000L);
        param.setBatchSize(10);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "AuthAndComboSubmitJobHandler";
        String actualLockKey = authAndComboSubmitJobHandler.getLockKey();
        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_ShouldUpdateStatusOnSuccessfulTaskCreation() throws Exception {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setId(1L);
        task.setMerchant_sn("test_sn");

        when(authAndComboTaskMapper.selectWaitForSubmit(any(Date.class), anyInt())).thenReturn(Arrays.asList(task));
        when(systemService.createAuthTask(any())).thenReturn(new SystemResponse(true, "success"));

        authAndComboSubmitJobHandler.execute(param);

        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(eq(task), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH), isNull());
    }

    @Test
    public void execute_ShouldUpdateStatusOnFailedTaskCreation() throws Exception {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setId(1L);
        task.setMerchant_sn("test_sn");

        when(authAndComboTaskMapper.selectWaitForSubmit(any(Date.class), anyInt())).thenReturn(Arrays.asList(task));
        when(systemService.createAuthTask(any())).thenReturn(new SystemResponse(false, "failure"));

        authAndComboSubmitJobHandler.execute(param);

        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(eq(task), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH), isNull());
    }

    @Test
    public void execute_ShouldRetryOnTaskCreationFailure() throws Exception {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setId(1L);
        task.setMerchant_sn("test_sn");

        when(authAndComboTaskMapper.selectWaitForSubmit(any(Date.class), anyInt())).thenReturn(Collections.singletonList(task));
        when(systemService.createAuthTask(any()))
                .thenThrow(new RuntimeException("First attempt failed"))
                .thenReturn(new SystemResponse(true, "success"));

        authAndComboSubmitJobHandler.execute(param);

        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(eq(task), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_WAIT_AUTH), isNull());
    }
}
