package com.wosai.upay.job.xxljob.batch.contractevent;

import com.wosai.upay.job.handlers.EventHandlerContext;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.batch.contractevent.ContractEventCreateNotNetInTaskJobHandler;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractEventCreateNotNetInTaskJobHandlerTest {

    @InjectMocks
    private ContractEventCreateNotNetInTaskJobHandler handler;

    @Mock
    private ContractEventMapper contractEventMapper;

    @Mock
    private EventHandlerContext eventHandlerContext;

    private BatchJobParam param;
    private ContractEvent contractEvent;

    @Before
    public void setUp() {
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        contractEvent = new ContractEvent();
        contractEvent.setId(1L);
    }

    @Test
    public void queryTaskItems_ValidParameters_ReturnsContractEvents() {
        // 准备
        List<ContractEvent> expectedEvents = Arrays.asList(
                new ContractEvent().setId(1L),
                new ContractEvent().setId(2L)
        );
        when(contractEventMapper.selectForCreateTasks(Mockito.anyString(), Mockito.eq(10)))
                .thenReturn(expectedEvents);

        // 执行
        List<ContractEvent> result = handler.queryTaskItems(param);

        // 验证
        assertEquals(expectedEvents, result);
        Mockito.verify(contractEventMapper).selectForCreateTasks(Mockito.anyString(), Mockito.eq(10));
    }

    @Test
    public void doHandleSingleData_StatusPending_HandleCalled() throws Exception {
        contractEvent.setStatus(ContractEvent.STATUS_PENDING);
        when(contractEventMapper.selectByPrimaryKey(1L)).thenReturn(contractEvent);

        handler.doHandleSingleData(contractEvent);

        verify(eventHandlerContext, times(1)).handle(contractEvent);
    }

    @Test
    public void doHandleSingleData_StatusNotPending_HandleNotCalled() throws Exception {
        contractEvent.setStatus(ContractEvent.STATUS_SUCCESS);
        when(contractEventMapper.selectByPrimaryKey(1L)).thenReturn(contractEvent);

        handler.doHandleSingleData(contractEvent);

        verify(eventHandlerContext, never()).handle(contractEvent);
    }

    @Test
    public void doHandleSingleData_ExceptionThrown_LogsWarning() throws Exception {
        when(contractEventMapper.selectByPrimaryKey(1L)).thenThrow(new RuntimeException("Test exception"));

        handler.doHandleSingleData(contractEvent);

        // 验证日志记录，如果需要的话
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsExpectedString() {
        contractEvent.setId(123L);
        String expectedLockKey = "ContractEventCreateNotNetInTaskJobHandler:123";
        String actualLockKey = handler.getLockKey(contractEvent);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsExpectedString() {
        contractEvent.setId(null);
        String expectedLockKey = "ContractEventCreateNotNetInTaskJobHandler:null";
        String actualLockKey = handler.getLockKey(contractEvent);
        assertEquals(expectedLockKey, actualLockKey);
    }
}
