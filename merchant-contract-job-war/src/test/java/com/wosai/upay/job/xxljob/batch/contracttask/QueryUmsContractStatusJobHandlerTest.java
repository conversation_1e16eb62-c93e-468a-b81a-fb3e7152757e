package com.wosai.upay.job.xxljob.batch.contracttask;

import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.UmsCallBackBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.util.StringUtil;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.provider.ChinaUmsParam;
import com.wosai.upay.merchant.contract.service.ChinaUmsService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class QueryUmsContractStatusJobHandlerTest {

    @Mock
    private ContractSubTaskMapper contractSubTaskMapper;

    @Mock
    private ChinaUmsService umsService;

    @Mock
    private ContractParamsBiz contractParamsBiz;

    @Mock
    private UmsCallBackBiz umsCallBackBiz;

    @Mock
    private ChatBotUtil chatBotUtil;

    @InjectMocks
    private QueryUmsContractStatusJobHandler queryUmsContractStatusJobHandler;

    private BatchJobParam batchJobParam;
    private ContractSubTask contractSubTask;

    @Before
    public void setUp() {
        batchJobParam = new BatchJobParam();
        batchJobParam.setBatchSize(10);
        batchJobParam.setQueryTime(1000L); // 1秒

        contractSubTask = new ContractSubTask();
        contractSubTask.setId(123L);
        contractSubTask.setContract_id("testContractId");
    }

    @Test
    public void queryTaskItems_WithValidInput_ReturnsContractSubTaskList() {
        // 准备
        List<ContractSubTask> expectedTasks = new ArrayList<>();
        ContractSubTask task = new ContractSubTask();
        task.setId(1L);
        expectedTasks.add(task);

        when(contractSubTaskMapper.selectUmsContractQueryTask(batchJobParam.getBatchSize(), StringUtil.formatDate(System.currentTimeMillis() - batchJobParam.getQueryTime())))
                .thenReturn(expectedTasks);

        List<ContractSubTask> actualTasks = queryUmsContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedTasks, actualTasks);
    }

    @Test
    public void queryTaskItems_WithNoTasks_ReturnsEmptyList() {
        // 准备
        when(contractSubTaskMapper.selectUmsContractQueryTask(batchJobParam.getBatchSize(), StringUtil.formatDate(System.currentTimeMillis() - batchJobParam.getQueryTime())))
                .thenReturn(new ArrayList<>());

        List<ContractSubTask> actualTasks = queryUmsContractStatusJobHandler.queryTaskItems(batchJobParam);

        assertEquals(0, actualTasks.size());
    }

    @Test
    public void getLockKey_ValidInput_ReturnsCorrectLockKey() {
        String expectedLockKey = "QueryUmsContractStatusJobHandler:123";
        String actualLockKey = queryUmsContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_NullId_ReturnsLockKeyWithNull() {
        contractSubTask.setId(null);
        String expectedLockKey = "QueryUmsContractStatusJobHandler:null";
        String actualLockKey = queryUmsContractStatusJobHandler.getLockKey(contractSubTask);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void doHandleSingleData_StatusIs5Or6_ShouldReturnEarly() {
        ContractSubTask contractSubTaskLast = new ContractSubTask();
        contractSubTaskLast.setStatus(5);

        when(contractSubTaskMapper.selectByContractId(contractSubTask.getContract_id())).thenReturn(contractSubTaskLast);

        queryUmsContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByContractId(contractSubTask.getContract_id());
        verifyNoMoreInteractions(contractParamsBiz, umsService, umsCallBackBiz, chatBotUtil);
    }

    @Test
    public void doHandleSingleData_StatusIsNot5Or6_ShouldProcessFurther() {
        ContractSubTask contractSubTaskLast = new ContractSubTask();
        contractSubTaskLast.setStatus(1);

        ChinaUmsParam umsParam = new ChinaUmsParam();
        ContractResponse contractResponse = new ContractResponse();

        when(contractSubTaskMapper.selectByContractId(contractSubTask.getContract_id())).thenReturn(contractSubTaskLast);
        when(contractParamsBiz.buildContractParamsByContractSubTask(contractSubTaskLast, ChinaUmsParam.class)).thenReturn(umsParam);
        when(umsService.queryContractStatusByContractId(contractSubTaskLast, umsParam)).thenReturn(contractResponse);

        queryUmsContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByContractId(contractSubTask.getContract_id());
        verify(contractParamsBiz, times(1)).buildContractParamsByContractSubTask(contractSubTaskLast, ChinaUmsParam.class);
        verify(umsService, times(1)).queryContractStatusByContractId(contractSubTaskLast, umsParam);
        verify(umsCallBackBiz, times(1)).handUmsMerchantContractResult(contractResponse, contractSubTaskLast);
        verifyNoMoreInteractions(chatBotUtil);
    }

    @Test
    public void doHandleSingleData_ExceptionOccurs_ShouldLogAndSendWarning() {
        ContractSubTask contractSubTaskLast = new ContractSubTask();
        contractSubTaskLast.setStatus(1);

        when(contractSubTaskMapper.selectByContractId(contractSubTask.getContract_id())).thenReturn(contractSubTaskLast);
        doThrow(new RuntimeException("Test Exception")).when(contractParamsBiz).buildContractParamsByContractSubTask(contractSubTaskLast, ChinaUmsParam.class);

        queryUmsContractStatusJobHandler.doHandleSingleData(contractSubTask);

        verify(contractSubTaskMapper, times(1)).selectByContractId(contractSubTask.getContract_id());
        verify(contractParamsBiz, times(1)).buildContractParamsByContractSubTask(contractSubTaskLast, ChinaUmsParam.class);
        verify(chatBotUtil, times(1)).sendMessageToContractWarnChatBot(anyString());
        verifyNoMoreInteractions(umsService, umsCallBackBiz);
    }
}
