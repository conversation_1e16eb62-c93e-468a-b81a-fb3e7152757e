package com.wosai.upay.job.providers;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.BlueSeaTaskMapper;
import com.wosai.upay.job.model.DO.BlueSeaTask;
import com.wosai.upay.job.payactivity.alipay.AlipayUniversityActivity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

public class AlipayUniversityActivityTest extends BaseTest {
    @Autowired
    private BlueSeaTaskMapper blueSeaTaskMapper;
    @Autowired
    private AlipayUniversityActivity universityActivity;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    public void testGroup() {
        String sql = "update bluesea_task set activity_order_id = '测试,可以删除',change_order_id=? where id = ?";
        int i = jdbcTemplate.update(sql, "order_id999999999", 2855L);
        System.out.println(i);
//        BlueSeaTask blueSeaTask = blueSeaTaskMapper.selectByPrimaryKey(2855L);
//        universityActivity.checkGroupMeal(blueSeaTask);
    }
}
