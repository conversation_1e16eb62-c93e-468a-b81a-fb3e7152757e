package com.wosai.upay.job.xxljob.direct;

import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;
import com.wosai.data.util.CollectionUtil;
import com.wosai.task.service.TaskInstanceService;
import com.wosai.upay.core.service.TerminalService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.RedisLock;
import com.wosai.upay.job.biz.SupplyParamsBiz;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class SupplyParamsJobHandlerTest {

    @InjectMocks
    private SupplyParamsJobHandler supplyParamsJobHandler;
    @Mock
    private Client hnSlsClient;

    @Mock
    private Client hdSlsClient;

    @Mock
    private SupplyParamsBiz supplyParamsBiz;

    @Mock
    private TerminalService terminalService;
    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Mock
    private RedisLock redisLock;

    @Mock
    private StringRedisTemplate redisTemplate;

    @Mock
    private Environment environment;

    @Mock
    private TaskInstanceService taskInstanceService;

    /**
     * 有日志数据
     */
    @Test
    public void supplyParams01() throws LogException {
        GetLogsResponse getLogsResponse = Mockito.mock(GetLogsResponse.class);
        Mockito.doReturn(true).when(getLogsResponse).IsCompleted();

        ArrayList<LogContent> logContents = new ArrayList<>();
        LogContent logContent = new LogContent("", "terminalSn");
        logContents.add(logContent);
        Mockito.doReturn(Collections.singletonList(new QueriedLog("", new LogItem(1, logContents)))).when(getLogsResponse).getLogs();
        Mockito.doReturn(Mockito.mock(ValueOperations.class)).when(redisTemplate).opsForValue();
        Mockito.doReturn(getLogsResponse).when(hnSlsClient).GetLogs(any());
        Mockito.doReturn(getLogsResponse).when(hdSlsClient).GetLogs(any());
        Mockito.doReturn(CollectionUtil.hashMap("merchant_sn", "merchantSn")).when(terminalService).getTerminalBySn("terminalSn");
        Mockito.doReturn(true).when(supplyParamsBiz).supplyLklParams(anyString());
        Mockito.doReturn(CollectionUtil.hashMap("upay-gateway", "query", "sz-qr-wap-pay", "query")).when(applicationApolloConfig).getErrorLogQueryRequest();
        supplyParamsJobHandler.execute(new DirectJobParam());
        Mockito.verify(terminalService, Mockito.times(1)).getTerminalBySn(any());
        Mockito.verify(supplyParamsBiz, Mockito.times(1)).supplyLklParams(any());
        Mockito.verify(taskInstanceService, Mockito.times(1)).startTaskForRpc(any());
    }



}