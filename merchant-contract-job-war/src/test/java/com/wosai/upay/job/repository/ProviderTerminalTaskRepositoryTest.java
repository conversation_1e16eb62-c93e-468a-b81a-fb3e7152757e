package com.wosai.upay.job.repository;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.mapper.ProviderTerminalTaskMapper;
import com.wosai.upay.job.model.DO.ProviderTerminalTask;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class ProviderTerminalTaskRepositoryTest extends BaseTest {


    @Autowired
    private ProviderTerminalTaskRepository providerTerminalTaskRepository;

    @Autowired
    private ProviderTerminalTaskMapper terminalTaskMapper;

    @Test
    public void exitBoundTerminalExceptionTest() {

        ProviderTerminalTask providerTerminalTask = getProviderTerminalTask();
        String subMerchant = "";
        providerTerminalTaskRepository.exitBoundTerminalException(providerTerminalTask, subMerchant);
    }

    private ProviderTerminalTask getProviderTerminalTask() {
        return terminalTaskMapper.selectByPrimaryKey(11003L);
    }
}
