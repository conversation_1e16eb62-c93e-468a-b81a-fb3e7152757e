package com.wosai.upay.job.biz;

import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.service.IMerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import static javax.management.timer.Timer.ONE_MINUTE;

public class MultiEventBizTest extends BaseTest {

    @InjectMocks
    private MultiEventBiz multiEventBiz;

    @Mock
    private IMerchantService iMerchantService;

    @Mock
    private ApplicationApolloConfig applicationApolloConfig;

    @Test
    public void testGetPrimaryContractTimeOutByOrg() {
        String merchantSn = "merchantSn";
        long timeOutTime = multiEventBiz.getPrimaryContractTimeOutByOrg(merchantSn);
        Assert.assertEquals(5 * ONE_MINUTE, timeOutTime);

        Mockito.doReturn(CollectionUtil.hashMap("organization_path", "00069,00052")).when(iMerchantService).getMerchantBySn(merchantSn);
        Mockito.doReturn(CollectionUtil.hashMap("00069", 2222, "other", 1111)).when(applicationApolloConfig).getPrimaryContractTimeOut();
        Mockito.doReturn(CollectionUtil.hashMap("00069", 2222, "other", 1111)).when(applicationApolloConfig).getPrimaryContractTimeOut();
        timeOutTime = multiEventBiz.getPrimaryContractTimeOutByOrg(merchantSn);
        Assert.assertEquals(2222, timeOutTime);

        Mockito.doReturn(CollectionUtil.hashMap("organization_path", "00052")).when(iMerchantService).getMerchantBySn(merchantSn);
        timeOutTime = multiEventBiz.getPrimaryContractTimeOutByOrg(merchantSn);
        Assert.assertEquals(1111, timeOutTime);
    }
}