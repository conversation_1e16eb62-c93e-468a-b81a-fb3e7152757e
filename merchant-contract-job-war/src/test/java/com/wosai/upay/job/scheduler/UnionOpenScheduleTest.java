package com.wosai.upay.job.scheduler;

import com.wosai.data.util.CollectionUtil;
import com.wosai.merchant.service.IMerchantBankAccountService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.model.CommonModel;
import com.wosai.upay.job.model.ContractResponse;
import com.wosai.upay.job.service.ContractEventService;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * @Description: TODO
 * <AUTHOR>
 * @Date 2020/9/7 3:50 PM
 **/


@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class UnionOpenScheduleTest {

//    @Autowired
//    UnionOpenSchedule unionOpenSchedule;

    @Autowired
    MerchantBankService merchantBankService;

    @Test
    public void test(){
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null,
                CollectionUtil.hashMap("merchant_id", "********-6e84-40f2-b6e4-a5ee7350b231", "default_status", 1));
        System.out.println(listResult);
    }

    @Autowired
    ContractEventService contractEventService;

    @Test
    public void refreshTest(){
        ContractResponse response = contractEventService.refreshMerchant("**************", 1, "SP");
        System.out.println(response);
    }

}