package com.wosai.upay.job.biz;

import com.wosai.business.log.service.BizObjectColumnService;
import com.wosai.business.log.service.BizOpLogService;
import com.wosai.business.log.service.BusinessLogService;
import com.wosai.upay.job.BaseTest;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;

public class BusinessLogBizTest extends BaseTest {

    @Autowired
    private BusinessLogBiz biz;

    @MockBean
    private BizOpLogService bizOpLogService;

    @MockBean
    private BizObjectColumnService bizObjectColumnService;

    @MockBean
    private BusinessLogService businessLogService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(biz, "bizOpLogService", bizOpLogService);
        ReflectionTestUtils.setField(biz, "bizObjectColumnService", bizObjectColumnService);
        ReflectionTestUtils.setField(biz, "businessLogService", businessLogService);
    }

    @Test
    public void sendMerchantConfigBusinessLog() {
        biz.sendMerchantConfigBusinessLog(new HashMap(), new HashMap(), "id", "name", "remark");
    }

    @Test
    public void sendWithdrawLog() throws Exception {
        biz.sendWithdrawLog("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3", 100, "test");
    }

    @Test
    public void sendChangeAcquirerLog() throws Exception {
        biz.sendChangeAcquirerLog("00311d8cf29a-af38-f9c4-a4d0-f8ea92a3", "lkl", "tonglian", "切换收单机构成功");
    }
}