package com.wosai.upay.job.refactor.Integration.service;


import com.shouqianba.service.ContractRelatedMappingConfigService;
import com.shouqianba.workflow.bean.CallBackBean;
import com.shouqianba.workflow.service.CallBackService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.handlers.SubTaskHandlerContext;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.service.ContractTaskServiceImpl;
import com.wosai.upay.job.service.task.TrackContractTaskResultTaskService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.List;


/**
 * 新增任务测试
 *
 * <AUTHOR>
 */
@Slf4j
public class InsertTaskTest extends BaseTest {

    @Resource
    private ContractTaskServiceImpl contractTaskServiceImpl;


    @Test
    public void testInsertTask() {
        String merchantSn = "21690003361868";
        Long id = contractTaskServiceImpl.insertFuYouUpdateMerchantInfoTask(merchantSn);
    }

    @Test
    public void testUpdatePhotoForLkl() {
        String merchantSn = "21690003918405";
        contractTaskServiceImpl.updateFirstStorePhotoSyncToAcquirer(merchantSn, "a81663ac-85e3-4161-9c93-0b512016043b");
    }



    @Test
    public void testUpdatePhotoForFuYou() {
        String merchantSn = "21690003936048";
        contractTaskServiceImpl.updateFirstStorePhotoSyncToAcquirer(merchantSn, "a81663ac-85e3-4161-9c93-0b512016043b");
    }

    @Autowired
    private SubTaskHandlerContext subTaskHandlerContext;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Autowired
    private ContractSubTaskMapper contractSubTaskMapper;

    @Test
    public void testPhotoForFuYou() throws Exception {
        ContractTask contractTask = contractTaskMapper.selectByPrimaryKey(44291132804L);
        List<ContractSubTask> contractSubTasks = contractSubTaskMapper.selectByPTaskId(44291132804L);
        for (ContractSubTask contractSubTask : contractSubTasks) {
            subTaskHandlerContext.handle(contractTask, contractSubTask);
        }
    }

    @Autowired
    private CallBackService callbackService;

    @Test
    public void testUpdateAudit() {
        CallBackBean test = CallBackBean.builder().resultType(2).message("test").auditId(489979L).templateId(325613L).build();
        callbackService.addComment(test);
    }

    @Resource
    private TrackContractTaskResultTaskService trackContractTaskService;

    @Resource
    private ContractRelatedMappingConfigService contractRelatedMappingConfigService;

//    @Resource
//    private ContractTaskSchedule contractTaskSchedule;

    @Test
    public void insertTaskTest() {
        trackContractTaskService.insertTask(TrackContractTaskResultTaskService.BUSINESS_SCENE_TYPE_UPDATE_FIRST_STORE, "21690003915349",
                "489979", 44291064207L, 325613L);
    }

    @Test
    public void testUploadPic() {
//        contractTaskSchedule.picUploadSupply();
    }


}
