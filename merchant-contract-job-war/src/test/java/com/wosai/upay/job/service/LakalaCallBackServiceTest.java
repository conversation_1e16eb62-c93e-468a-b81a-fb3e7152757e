package com.wosai.upay.job.service;


import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BlackListBiz;
import com.wosai.upay.job.mapper.ContractSubTaskMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractTask;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;


public class LakalaCallBackServiceTest extends BaseTest {

    @SpyBean
    LakalaCallBackService service;
    @MockBean
    private ContractSubTaskMapper contractSubTaskMapper;
    @MockBean
    private ContractTaskMapper contractTaskMapper;
    @MockBean
    TaskResultService taskResultService;
    @MockBean
    private BlackListBiz blackListBiz;

    @Before
    public void before() {
        ReflectionTestUtils.setField(service, "taskResultService", taskResultService);
    }

    @Test
    public void querySubTaskByContractId() {
        service.querySubTaskByContractId("2121211");
    }

    @Test
    public void updateContractSubTask() {
        Map callBack = CollectionUtil.hashMap("contractMemo", "contractMemo");
        Mockito.doReturn(new ContractTask().setId(1L).setStatus(5)).when(contractTaskMapper).selectByPrimaryKey(Mockito.anyLong());
        Mockito.doReturn(null).when(contractSubTaskMapper).selectByContractId(anyString());
        service.updateContractSubTask(false, "12121", callBack);
        ContractSubTask subTask = new ContractSubTask();
        subTask.setResponse_body("{}").setStatus_influ_p_task(1);
        Mockito.doReturn(subTask).when(contractSubTaskMapper).selectByContractId(anyString());
        service.updateContractSubTask(false, "12121", callBack);
        Mockito.doReturn(1).when(contractSubTaskMapper).setSubTaskResult(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString(), Mockito.anyString(), Mockito.anyLong());
        service.updateContractSubTask(false, "12121", callBack);
        service.updateContractSubTask(true, "12121", callBack);

    }

    @Test
    public void savePicTaskByContract() {
        Mockito.doReturn(Arrays.asList(new ContractTask())).when(contractTaskMapper).selectTaskTodoByMerchantSn(anyString());
        Mockito.doReturn(1).when(contractSubTaskMapper).insert(any());
        service.savePicTaskByContract("212121", new HashMap<>());
        Mockito.doReturn(0).when(contractSubTaskMapper).insert(any());
        service.savePicTaskByContract("212121", new HashMap<>());
    }

    @Test
    public void savePicTaskByRisk() {
        Mockito.doReturn(0).when(contractTaskMapper).insert(any());
        service.savePicTaskByRisk("12121", new HashMap<>(), 5);
        Mockito.doReturn(1).when(contractTaskMapper).insert(any());
        service.savePicTaskByRisk("12121", new HashMap<>(), 5);

    }
}
