package com.wosai.upay.job.service;


import com.wosai.common.exception.CommonPubBizException;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.MultiProviderContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.DO.MultiProviderContractEvent;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.model.RuleGroup;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;


public class ContractEventServiceTest extends H2DbBaseTest {

    @InjectMocks
    private ContractEventServiceImpl contractEventService;

    @Mock
    private RuleContext ruleContext;

    @Mock
    private BusinessRuleBiz businessRuleBiz;

    @Autowired
    private ContractEventMapper contractEventMapper;

    @Autowired
    private MultiProviderContractEventMapper multiEventMapper;

    @Autowired
    private ContractStatusMapper contractStatusMapper;

    @Autowired
    private ContractTaskMapper contractTaskMapper;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(contractEventService, "contractEventMapper", contractEventMapper);
        ReflectionTestUtils.setField(contractEventService, "multiEventMapper", multiEventMapper);
        ReflectionTestUtils.setField(contractEventService, "contractStatusMapper", contractStatusMapper);
        ReflectionTestUtils.setField(contractEventService, "contractTaskMapper", contractTaskMapper);
    }

    @Test
    public void saveContractEvent1() {
        String merchantSn = "saveContractEvent1";
        String ruleGroupId = "lklV3";
        String platform = "crm";

        // 1 规则组不存在
        CommonPubBizException exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform));
        Assert.assertEquals("规则组不存在", exception.getMessage());
        // 2 商户还未进件，规则组必须指明收单机构
        Mockito.doReturn(new RuleGroup()).when(ruleContext).getRuleGroup(ruleGroupId);
        exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform));
        Assert.assertEquals("商户还未进件，规则组必须指明收单机构", exception.getMessage());
        // 3 走到lklV3
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(ruleGroupId);
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(ruleGroupId);
        ContractEvent contractEvent = contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform);

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        ContractEvent event = contractEventMapper.selectByPrimaryKey(contractEvent.getId());
        Assert.assertEquals(event.getRule_group_id(), ruleGroupId);
        Assert.assertEquals(contractStatus.getAcquirer(), ruleGroupId);
    }

    /**
     * 传进来的是tonglian，但是不符合通联要求
     */
    @Test
    public void saveContractEvent2() {
        String merchantSn = "saveContractEvent2";
        String ruleGroupId = "tonglian";
        String platform = "crm";

        // 3 走到lkl
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(ruleGroupId);
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(ruleGroupId);
        RuleGroup ruleGroup1 = new RuleGroup();
        ruleGroup1.setAcquirer("lkl");
        Mockito.doReturn(ruleGroup1).when(ruleContext).getRuleGroup(McConstant.RULE_GROUP_LKL);
        ContractEvent contractEvent = contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform);

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        ContractEvent event = contractEventMapper.selectByPrimaryKey(contractEvent.getId());
        Assert.assertEquals(McConstant.RULE_GROUP_LKL, event.getRule_group_id());
        Assert.assertEquals("lkl", contractStatus.getAcquirer());
    }

    /**
     * ums
     */
    @Test
    public void saveContractEvent3() {
        String merchantSn = "saveContractEvent3";
        String ruleGroupId = "ums";
        String platform = "crm";

        // 3 走到lklV3
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(ruleGroupId);
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(ruleGroupId);
        RuleGroup ruleGroup1 = new RuleGroup();
        ruleGroup1.setAcquirer("lkl");
        Mockito.doReturn(ruleGroup1).when(ruleContext).getRuleGroup(McConstant.RULE_GROUP_LKL);
        ContractEvent contractEvent = contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform);

        ContractStatus contractStatus = contractStatusMapper.selectByMerchantSn(merchantSn);
        ContractEvent event = contractEventMapper.selectByPrimaryKey(contractEvent.getId());
        Assert.assertEquals(McConstant.RULE_GROUP_LKL, event.getRule_group_id());
        Assert.assertEquals("lkl", contractStatus.getAcquirer());
    }

    /**
     * 已经有了收单机构不能设定一个新的
     */
    @Test
    public void saveContractEvent4() {
        String merchantSn = "saveContractEvent4";
        String ruleGroupId = "lklV3";
        String platform = "crm";

        contractStatusMapper.insertSelective(new ContractStatus().setMerchant_sn(merchantSn).setAcquirer("tonglian"));

        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setAcquirer(ruleGroupId);
        Mockito.doReturn(ruleGroup).when(ruleContext).getRuleGroup(ruleGroupId);
        CommonPubBizException exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEvent(merchantSn, ruleGroupId, platform));
        Assert.assertEquals("商户已经进件，规则组不能设定收单机构", exception.getMessage());
    }

    /**
     * 主通道次通道都有
     * 分别是tonglian和lklV3
     */
    @Test
    public void saveContractEventV21() {
        String merchantSn = "saveContractEventV21";
        String platform = "crm";
        NetInRuleGroups netInRuleGroups = new NetInRuleGroups()
                .setPrimaryRuleGroupId("tonglian").setSecondaryRuleGroupId("lklV3");
        Mockito.doReturn(netInRuleGroups).when(businessRuleBiz).getRuleGroupId(merchantSn);

        // 1 主通道对应的规则组不存在
        CommonPubBizException exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEventV2(merchantSn, null, platform));
        Assert.assertEquals("主通道规则组不存在", exception.getMessage());

        // 2 次通道对应的规则组不存在
        RuleGroup primaryRuleGroup = new RuleGroup();
        primaryRuleGroup.setAcquirer("tonglian");
        Mockito.doReturn(primaryRuleGroup).when(ruleContext).getRuleGroup(netInRuleGroups.getPrimaryRuleGroupId());

        exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEventV2(merchantSn, null, platform));
        Assert.assertEquals("次通道规则组不存在", exception.getMessage());

        // 3 contract_status不存在，成功创建了multi_event
        RuleGroup secondaryRuleGroup = new RuleGroup();
        secondaryRuleGroup.setAcquirer("lklV3");
        Mockito.doReturn(secondaryRuleGroup).when(ruleContext).getRuleGroup(netInRuleGroups.getSecondaryRuleGroupId());
        ContractEvent event = contractEventService.saveContractEventV2(merchantSn, null, platform);

        MultiProviderContractEvent multiEvent = multiEventMapper.selectByPrimaryKey(event.getId());
        Assert.assertEquals(netInRuleGroups.getPrimaryRuleGroupId(), multiEvent.getPrimary_group_id());
        Assert.assertEquals(netInRuleGroups.getSecondaryRuleGroupId(), multiEvent.getSecondary_group_id());

    }

    /**
     * 已经有了contract_status，并且和这次的主通道不一致
     */
    @Test
    public void saveContractEventV22() {
        String merchantSn = "saveContractEventV22";
        String platform = "crm";
        NetInRuleGroups netInRuleGroups = new NetInRuleGroups()
                .setPrimaryRuleGroupId("tonglian").setSecondaryRuleGroupId("lklV3");
        Mockito.doReturn(netInRuleGroups).when(businessRuleBiz).getRuleGroupId(merchantSn);

        RuleGroup primaryRuleGroup = new RuleGroup();
        primaryRuleGroup.setAcquirer("tonglian");
        Mockito.doReturn(primaryRuleGroup).when(ruleContext).getRuleGroup(netInRuleGroups.getPrimaryRuleGroupId());

        RuleGroup secondaryRuleGroup = new RuleGroup();
        secondaryRuleGroup.setAcquirer("lklV3");
        Mockito.doReturn(secondaryRuleGroup).when(ruleContext).getRuleGroup(netInRuleGroups.getSecondaryRuleGroupId());

        contractStatusMapper.insertSelective(new ContractStatus().setMerchant_sn(merchantSn).setAcquirer("ums"));
        CommonPubBizException exception = Assert.assertThrows(CommonPubBizException.class, () -> contractEventService.saveContractEventV2(merchantSn, null, platform));
        Assert.assertEquals("商户已经进件，规则组不能设定收单机构", exception.getMessage());
    }
}
