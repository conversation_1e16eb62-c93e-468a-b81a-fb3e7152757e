package com.wosai.upay.job.refactor.Integration.service;


import com.shouqianba.cua.enums.contract.FuYouContractSettleTypeEnum;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

;

/**
 * 富友服务测试
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
public class FuYouAcquirerHandleTest extends BaseTest {

    @Resource
    private FuYouAcquirerFacade fuYouAcquirerFacade;


    @Test
    public void getSettleTest() {
        String contractSettleType = fuYouAcquirerFacade.getContractSettleTypeByMerchantSn("21690003626772");
        assertThat(contractSettleType).isEqualTo(FuYouContractSettleTypeEnum.AUTO_SETTLEMENT_D1.getValue().toString());
        String contractSettleType1 = fuYouAcquirerFacade.getContractSettleTypeByMerchantSn("21690003827905");
        assertThat(contractSettleType1).isEqualTo(FuYouContractSettleTypeEnum.MANUAL_SETTLEMENT_D1.getValue().toString());
        assertThat(fuYouAcquirerFacade.getContractSettleTypeByMerchantSn(null)).isEqualTo(FuYouContractSettleTypeEnum.MANUAL_SETTLEMENT_D1.getValue().toString());
    }



}
