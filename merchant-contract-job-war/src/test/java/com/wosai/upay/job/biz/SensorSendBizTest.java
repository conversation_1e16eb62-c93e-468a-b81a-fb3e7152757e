package com.wosai.upay.job.biz;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wosai.upay.bank.model.MerchantBankAccount;
import com.wosai.upay.bank.model.MerchantBusinessLicense;
import com.wosai.upay.bank.service.BankBusinessLicenseService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.mapper.ContractStatusMapper;
import com.wosai.upay.job.model.ContractStatus;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Date;

import static org.junit.Assert.*;

public class SensorSendBizTest extends BaseTest {

    @InjectMocks
    private SensorSendBiz sensorSendBiz;

    @Mock
    private BankBusinessLicenseService bankBusinessLicenseService;

    @Mock
    private ContractStatusMapper contractStatusMapper;

    @Mock
    private KafkaTemplate<String,Object> kafkaTemplate;

    @Mock
    private MerchantService merchantService;

    @Test
    public void sendMessageToSensor() {
        sensorSendBiz.sendMessageToSensor("merchantId","merchantSn",1,"fail error");
    }

    @Test
    public void send() {
        //抛空指针
        ReflectionTestUtils.invokeMethod(sensorSendBiz,"send","merchantId","merchantSn",1,"fail error");
        //正常
        Mockito.doReturn(new ContractStatus().setCreate_at(new Date()).setUpdate_at(new Date())).when(contractStatusMapper).selectByMerchantSn("merchantSn");
        Mockito.doReturn(new MerchantBankAccount().setType(2).setBank_name("中国工商银行")).when(bankBusinessLicenseService).getMerchantBankAccountByMerchantId("merchantId");
        ReflectionTestUtils.invokeMethod(sensorSendBiz,"send","merchantId","merchantSn",1,"fail error");
    }

    @Test
    public void getType() {
        MerchantBusinessLicense license = new MerchantBusinessLicense();
        Mockito.doReturn(license).when(bankBusinessLicenseService).getBusinessLicenseByMerchantId("merchantId");
        Object result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "getType", new MerchantBankAccount().setType(2));
        assertEquals(2,result);
        result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "getType", new MerchantBankAccount().setType(1).setMerchant_id("merchantId"));
        assertEquals(3,result);
        license.setLegal_person_id_number("123");
        result = ReflectionTestUtils.invokeMethod(sensorSendBiz, "getType", new MerchantBankAccount().setType(1).setIdentity("123").setMerchant_id("merchantId"));
        assertEquals(1,result);
    }
    @Test
    public void sendContractTaskMessage2SensorTest() {
        final PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(100);
        Mockito.doReturn(new ListResult()).when(merchantService).findMerchants(pageInfo, Maps.newHashMap());
        sensorSendBiz.sendContractTaskMessage2Sensor(Lists.newArrayList());
    }

}