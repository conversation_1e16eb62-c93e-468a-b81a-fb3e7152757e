package com.wosai.upay.job.xxljob.direct.bankdirectapply;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.bank.service.BankService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.Constants.AcquirerChangeStatus;
import com.wosai.upay.job.Constants.BankDirectApplyConstant;
import com.wosai.upay.job.biz.bankDirect.BankHandleService;
import com.wosai.upay.job.biz.bankDirect.BankHandleServiceFactory;
import com.wosai.upay.job.biz.bankDirect.HxbImportBiz;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.mapper.BankDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.mapper.McAcquirerChangeMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.BankDirectApply;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.service.ContractTaskService;
import com.wosai.upay.job.util.ChatBotUtil;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import org.checkerframework.checker.units.qual.C;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.fail;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class BankDirectChangeAcquireResultJobHandlerTest {

    @InjectMocks
    private BankDirectChangeAcquireResultJobHandler bankDirectChangeAcquireResultJobHandler;

    @Mock
    private BankDirectApplyMapper bankDirectApplyMapper;

    @Mock
    private McAcquirerChangeMapper acquirerChangeMapper;

    @Mock
    private ChatBotUtil chatBotUtil;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private HxbImportBiz hxbImportBiz;

    @Mock
    private MerchantBankService merchantBankService;

    @Mock
    private ContractTaskMapper contractTaskMapper;
    @Mock
    private BankService bankService;
    @Mock
    private ContractTaskService contractTaskService;
    @Mock
    private BankHandleServiceFactory factory;
    @Mock
    private MerchantService merchantService;

    private BankDirectApply bankDirectApply;
    private Map<String, Object> extraMap;

    @Before
    public void setUp() {
        bankDirectApply = new BankDirectApply();
        bankDirectApply.setMerchant_sn("testMerchantSn");
        extraMap = new HashMap<>();
        extraMap.put(BankDirectApplyConstant.Extra.ACQUIRE, "testAcquirer");
        bankDirectApply.setExtra("{\"acquire\":\"testAcquirer\"}");
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        String expectedLockKey = "BankDirectChangeAcquireResultJobHandler";
        String actualLockKey = bankDirectChangeAcquireResultJobHandler.getLockKey();

        assertEquals("The lock key should match the expected value", expectedLockKey, actualLockKey);
    }

    @Test
    public void execute_AcquireChangeSuccessNoCard() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);

        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPublicCard() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 2, "default_status", 0));

        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(bankService, times(1)).deletedMerchantBankAccountPre(any());
        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPersonalCardCreateSyncTask() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\"}");
        apply.setExtra("{}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));
        when(contractTaskService.syncBankAccount2Acquire(any(), any(), any())).thenReturn(new ContractTask().setId(123L));
        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(contractTaskService, times(1)).syncBankAccount2Acquire(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPersonalCardSyncTaskSuccess() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\",\"task_id\":\"123\"}");
        apply.setExtra("{\"task_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));
        when(contractTaskMapper.selectByPrimaryKey(123L)).thenReturn(new ContractTask().setStatus(5));
        when(factory.getBankHandleService(any())).thenReturn(Mockito.mock(BankHandleService.class));
        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(bankService, times(1)).replaceBankAccountForBankDirect("123");
        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPersonalCardSyncTaskFail() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\",\"task_id\":\"123\"}");
        apply.setExtra("{\"task_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));
        when(contractTaskMapper.selectByPrimaryKey(123L)).thenReturn(new ContractTask().setStatus(6));
        when(factory.getBankHandleService(any())).thenReturn(Mockito.mock(BankHandleService.class));
        when(merchantBankService.findMerchantBankAccountPres(any(), any())).thenReturn(new ListResult(1, Arrays.asList(new HashMap())));
        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPersonalCardSyncTaskProcessing() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\",\"task_id\":\"123\"}");
        apply.setExtra("{\"task_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));
        when(contractTaskMapper.selectByPrimaryKey(123L)).thenReturn(new ContractTask().setStatus(1));
        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeSuccessWithPersonalCardSyncTaskIsNull() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\",\"task_id\":\"123\"}");
        apply.setExtra("{\"task_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.SUCCESS);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));
        when(contractTaskService.syncBankAccount2Acquire(any(), any(), any())).thenReturn(new ContractTask().setId(123L));

        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(contractTaskService, times(1)).syncBankAccount2Acquire(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void execute_AcquireChangeFail() {
        DirectJobParam param = new DirectJobParam();
        param.setBatchSize(10);
        param.setQueryTime(1000L);

        BankDirectApply apply = new BankDirectApply();
        apply.setMerchant_sn("123");
        apply.setDev_code("devCode");
        apply.setForm_body("{\"bank_pre_id\":\"123\",\"task_id\":\"123\"}");
        apply.setExtra("{\"task_id\":\"123\"}");

        McAcquirerChange acquireChange = new McAcquirerChange();
        acquireChange.setStatus(AcquirerChangeStatus.FAIL);

        when(bankDirectApplyMapper.listByProcessStatusAndPriorityLimit(anyList(), anyString(), anyInt()))
                .thenReturn(Collections.singletonList(apply));
        when(acquirerChangeMapper.getLastedByMerchantSnAndTargetAcquirer(any(), any()))
                .thenReturn(acquireChange);
        when(merchantBankService.getMerchantBankAccountPre("123")).thenReturn(CollectionUtil.hashMap("type", 1, "default_status", 0));

        bankDirectChangeAcquireResultJobHandler.execute(param);

        verify(bankService, times(1)).deletedMerchantBankAccountPre(any());
        verify(directStatusBiz, times(1)).createOrUpdateDirectStatus(anyString(), anyString(), anyInt(), any());
        verify(hxbImportBiz, times(1)).createOrUpdateBizOpenInfo(any(), any(), any());
        verify(bankDirectApplyMapper, times(1)).updateByPrimaryKeySelective(any());    }

}
