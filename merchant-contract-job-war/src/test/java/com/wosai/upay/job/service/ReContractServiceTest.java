package com.wosai.upay.job.service;


import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.trade.service.FeeRateService;
import com.wosai.trade.service.result.ListMchFeeRateResult;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.externalservice.customer.CustomerRelationClient;
import com.wosai.upay.job.externalservice.paybusiness.PayBusinessOpenClient;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.AllowChangeIndustryRequest;
import com.wosai.upay.job.model.AllowChangeIndustryResult;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.ReContract;
import entity.common.OrganizationEs;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class ReContractServiceTest {


    @InjectMocks
    private ReContractServiceImpl reContractService;
    @Mock
    private WechatAuthBiz wechatAuthBiz;
    @Mock
    private FeeRateService feeRateService;
    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;
    @Mock
    private MerchantService merchantService;
    @Mock
    private CustomerRelationClient customerRelationClient;
    @Mock
    private PayBusinessOpenClient payBusinessOpenClient;

    @Test
    public void reContract() {
        ReContract reContract = new ReContract().setMerchant_sn("21690002971171").setRule("lkl-1016-3-32631798").setRemark("remark");
        reContractService.reContract(reContract);
    }

    @Test
    public void testAllowChangeIndustry01() {
        AllowChangeIndustryRequest request = new AllowChangeIndustryRequest();
        request.setMerchantSn("merchantSn");
        request.setTargetIndustryId("targetIndustryId");

        // 1. 目标行业配置的结算ID信息为空
        AllowChangeIndustryResult result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("目标行业配置的结算ID信息为空", result.getMessage());

        // 2. 在用微信交易参数不存在
        Mockito.doReturn(CollectionUtil.hashMap("person", "767"))
                .when(wechatAuthBiz).getSettlementConfig(request.getTargetIndustryId());
        result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("当前在用微信子商户号结算ID和目标行业配置信息不匹配", result.getMessage());

        // 3. 在用微信交易参数的结算ID和目标行业配置不一致
        MerchantProviderParams wxParams = new MerchantProviderParams();
        wxParams.setWx_settlement_id("777");
        Mockito.doReturn(wxParams).when(merchantProviderParamsMapper).getUseWeiXinParam(request.getMerchantSn());
        result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("当前在用微信子商户号结算ID和目标行业配置信息不匹配", result.getMessage());

        // 4. 目标行业和商户所在行业配置的套餐为空
        wxParams.setWx_settlement_id("767");
        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, "merchantId"))
                .when(merchantService).getMerchantBySn(request.getMerchantSn());
        Mockito.doReturn(new OrganizationEs()).when(customerRelationClient).getMchIndirectOrg("merchantId");
        result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("目标行业和商户所在行业配置的套餐为空", result.getMessage());

        // 5. 商户在用的微信套餐信息不存在
        Mockito.doReturn(Arrays.asList(CollectionUtil.hashMap(DaoConstants.ID, 30), CollectionUtil.hashMap(DaoConstants.ID, 40)))
                .when(payBusinessOpenClient).queryPayCombos(any());
        Mockito.doReturn(new ArrayList<>()).when(feeRateService).listMchFeeRates(request.getMerchantSn());
        result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("商户在用的微信套餐信息不存在", result.getMessage());
    }

    @Test
    public void testAllowChangeIndustry02() {
        AllowChangeIndustryRequest request = new AllowChangeIndustryRequest();
        request.setMerchantSn("merchantSn");
        request.setTargetIndustryId("targetIndustryId");

        Mockito.doReturn(CollectionUtil.hashMap("person", "777"))
                .when(wechatAuthBiz).getSettlementConfig(request.getTargetIndustryId());
        MerchantProviderParams wxParams = new MerchantProviderParams();
        wxParams.setWx_settlement_id("777");
        Mockito.doReturn(wxParams).when(merchantProviderParamsMapper).getUseWeiXinParam(request.getMerchantSn());

        Mockito.doReturn(CollectionUtil.hashMap(DaoConstants.ID, "merchantId"))
                .when(merchantService).getMerchantBySn(request.getMerchantSn());
        Mockito.doReturn(new OrganizationEs()).when(customerRelationClient).getMchIndirectOrg("merchantId");
        Mockito.doReturn(Arrays.asList(CollectionUtil.hashMap(DaoConstants.ID, 30), CollectionUtil.hashMap(DaoConstants.ID, 40)))
                .when(payBusinessOpenClient).queryPayCombos(any());
        Mockito.doReturn(Collections.singletonList(new ListMchFeeRateResult().setTradeComboId(31L).setPayWay(3))).when(feeRateService).listMchEffectFeeRates(request.getMerchantSn());
        AllowChangeIndustryResult result = reContractService.allowChangeIndustry(request);
        Assert.assertFalse(result.getAllow());
        Assert.assertEquals("商户在用微信套餐与目标行业和组织配置的套餐不一致", result.getMessage());

        Mockito.doReturn(Collections.singletonList(new ListMchFeeRateResult().setTradeComboId(30L).setPayWay(3))).when(feeRateService).listMchEffectFeeRates(request.getMerchantSn());
        result = reContractService.allowChangeIndustry(request);
        Assert.assertTrue(result.getAllow());
    }
}
