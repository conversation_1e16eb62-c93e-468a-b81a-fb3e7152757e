package com.wosai.upay.job.xxljob.direct.authandcombo;

import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.wosai.upay.job.Constants.MerchantChangeDataConstant;
import com.wosai.upay.job.biz.AuthAndComboTaskBiz;
import com.wosai.upay.job.mapper.AuthAndComboTaskMapper;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.AuthAndComboTask;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AuthAndComboForceChangeJobHandlerTest {

    @InjectMocks
    private AuthAndComboForceChangeJobHandler authAndComboForceChangeJobHandler;

    @Mock
    private AuthAndComboTaskMapper authAndComboTaskMapper;

    @Mock
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Mock
    private AuthAndComboTaskBiz authAndComboTaskBiz;

    private DirectJobParam directJobParam;

    @Before
    public void setUp() {
        directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(10);
        directJobParam.setStartTime(1000L);
        directJobParam.setEndTime(2000L);
    }

    @Test
    public void getLockKey_ShouldReturnCorrectLockKey() {
        // 调用方法
        String lockKey = authAndComboForceChangeJobHandler.getLockKey();

        // 验证返回值
        assertEquals("AuthAndComboForceChangeJobHandler", lockKey);
    }

    @Test
    public void execute_NoTasks_NoProcessing() {
        when(authAndComboTaskMapper.selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt())).thenReturn(new ArrayList<>());

        authAndComboForceChangeJobHandler.execute(directJobParam);

        verify(authAndComboTaskMapper, times(1)).selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt());
        verifyNoInteractions(authAndComboTaskBiz);
    }

    @Test
    public void execute_ParametersNotExist_ExceptionThrown() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        when(authAndComboTaskMapper.selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(null);

        authAndComboForceChangeJobHandler.execute(directJobParam);

        verify(authAndComboTaskMapper, times(1)).selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt());
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(any(AuthAndComboTask.class), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_FAIL), anyString());
    }

    @Test
    public void execute_ProviderNotFuyou_ChangeTradeParamsCalled() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setProvider(ProviderEnum.PROVIDER_TONGLIAN.getValue());
        params.setDeleted(false);

        when(authAndComboTaskMapper.selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);

        authAndComboForceChangeJobHandler.execute(directJobParam);

        verify(authAndComboTaskMapper, times(1)).selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt());
        verify(authAndComboTaskBiz, times(1)).changeTradeParamsAndApplyCombo(any(AuthAndComboTask.class), any(MerchantProviderParams.class));
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(any(AuthAndComboTask.class), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED), isNull());
    }

    @Test
    public void execute_ExceptionDuringProcessing_StatusChangedToFail() {
        AuthAndComboTask task = new AuthAndComboTask();
        task.setSub_mch_id("123");
        List<AuthAndComboTask> tasks = new ArrayList<>();
        tasks.add(task);

        MerchantProviderParams params = new MerchantProviderParams();
        params.setProvider(ProviderEnum.PROVIDER_FUYOU.getValue());
        params.setDeleted(false);

        when(authAndComboTaskMapper.selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt())).thenReturn(tasks);
        when(merchantProviderParamsMapper.selectByPayMerchantId("123")).thenReturn(params);
        doThrow(new RuntimeException("Test exception")).when(authAndComboTaskBiz).changeStatusAndSendMessage(any(AuthAndComboTask.class), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED), isNull());

        authAndComboForceChangeJobHandler.execute(directJobParam);

        verify(authAndComboTaskMapper, times(1)).selectForceChangeTradeParams(any(Date.class), any(Date.class), anyInt());
        verify(authAndComboTaskBiz, times(1)).changeStatusAndSendMessage(any(AuthAndComboTask.class), eq(MerchantChangeDataConstant.AUTH_AND_COMBO_TASK_SUCCEED), isNull());
    }
}
