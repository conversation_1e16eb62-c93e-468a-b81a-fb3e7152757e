package com.wosai.upay.job.service;

import com.wosai.data.util.CollectionUtil;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.core.service.TradeConfigService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessLogBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.biz.RuleBiz;
import com.wosai.upay.job.biz.RuleContext;
import com.wosai.upay.job.exception.ApplicationException;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.ContractRule;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.application.CommonResult;
import com.wosai.upay.job.model.application.RuleContractRequest;
import com.wosai.upay.job.model.application.ZhimaAppCreateReq;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doThrow;

/**
 * Created by hzq on 19/11/27.
 */
public class ContractApplicationServiceTest extends BaseTest {

    @SpyBean
    ContractApplicationService contractApplicationService;

    @SpyBean
    MerchantProviderParamsService merchantProviderParamsService;

    @MockBean
    TradeConfigService tradeConfigService;
    @MockBean
    ParamContextBiz paramContextBiz;
    @MockBean
    RuleBiz ruleBiz;
    @MockBean
    MerchantProviderParamsMapper merchantProviderParamsMapper;
    @MockBean
    RuleContext ruleContext;
    @MockBean
    private MerchantService merchantService;
    @MockBean
    private BusinessLogBiz businessLogBiz;

    @Before
    public void before() {
        ReflectionTestUtils.setField(contractApplicationService, "merchantProviderParamsService", merchantProviderParamsService);
//        ReflectionTestUtils.setField(contractApplicationService, "zhimaCreditService", zhimaCreditService);
        ReflectionTestUtils.setField(contractApplicationService, "tradeConfigService", tradeConfigService);
        ReflectionTestUtils.setField(contractApplicationService, "merchantService", merchantService);
    }

    @Test
    public void testNotInvalidArg() {
        ZhimaAppCreateReq req = new ZhimaAppCreateReq().setMerchant_sn("sn");
        CommonResult result;
        try {
            result = contractApplicationService.openZhimaCreditDbb(req);
        } catch (ApplicationException e) {

        }
        Mockito.doReturn("mchId").when(merchantProviderParamsService)
                .getInUsePayMchId(Mockito.anyString(), Mockito.anyInt());
        ContractResponse contractResponse = new ContractResponse().setCode(200);
//        Mockito.doReturn(contractResponse).when(zhimaCreditService).createZhimaCreditMerchant(Mockito.anyObject());
        result = contractApplicationService.openZhimaCreditDbb(req);
        Assert.assertEquals(200, result.getCode());

        req.setTerminal_ids(Arrays.asList("id1", "id2")).setFee_rate("0.38");
        result = contractApplicationService.openZhimaCreditDbb(req);
        Mockito.doReturn(CollectionUtil.hashMap("id", "id")).when(tradeConfigService)
                .getTerminalConfigByTerminalIdAndPayway(Mockito.anyString(), Mockito.anyInt());

    }

    @Test
    public void testUnexpectedEx() {
        ZhimaAppCreateReq req = new ZhimaAppCreateReq();
        req.setMerchant_sn("mch_sn");
        doThrow(new RuntimeException("ex")).when(merchantProviderParamsService).getInUsePayMchId(anyString(), anyInt());
        CommonResult result = contractApplicationService.openZhimaCreditDbb(req);
        Assert.assertEquals(result.getCode(), CommonResult.ERROR);
        Assert.assertEquals(result.getMsg(), "ex");
    }


    @Test
    public void testContractByRule() throws ContextParamException {
        RuleContractRequest ruleContractRequest = new RuleContractRequest().setMerchantId("id")
                .setReContract(true).setRule("test").setPlat("test")
                .setCustomFields(CollectionUtil.hashMap(ContractApplicationService.MERCHANT_NAME, "name"
                        , ContractApplicationService.SHORT_NAME, "name", ContractApplicationService.SHORT_NAME, "name"
                        , ContractApplicationService.BUSINESS, "name"));
        Mockito.doReturn(CollectionUtil.hashMap("sn", "sn")).when(merchantService).getMerchantByMerchantId(Mockito.anyString());
        Mockito.doReturn(CollectionUtil.hashMap("merchant", new HashMap<>())).when(paramContextBiz).getParamContextByMerchantSn(Mockito.anyString(), Mockito.anyObject());
        Map contractRes = new HashMap<>();
        Mockito.doReturn(contractRes).when(ruleBiz)
                .contractByRule(Mockito.anyString(), Mockito.anyObject(), Mockito.anyMap(), Mockito.anyBoolean());
        Mockito.doReturn(new ContractRule()).when(ruleContext).getContractRule(Mockito.anyString());
        Mockito.doReturn(new MerchantProviderParams()).when(merchantProviderParamsMapper).selectByPrimaryKey(Mockito.anyString());
        contractApplicationService.contractByRule(ruleContractRequest);
        ruleContractRequest.setTerminals(Arrays.asList("id1", "ids2"));
        contractApplicationService.contractByRule(ruleContractRequest);
    }
}
