package com.wosai.upay.job.biz;

import com.wosai.risk.service.RiskOrderGenerateService;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ContractSubTask;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.Matchers.any;

public class BlackListBizTest extends BaseTest {

    @SpyBean
    private BlackListBiz biz;

    @Mock
    private RiskOrderGenerateService riskOrderGenerateService;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(biz, "riskOrderGenerateService", riskOrderGenerateService);
    }

    @Test
    public void syncBlacklist() {
        Mockito.doReturn(true).when(riskOrderGenerateService).syncBlacklist(any());
        biz.syncBlacklist(null, "merchant_sn", null, "商户号:1680003288858 报错来源:拉卡拉 错误提示:其他（自行填写）,[江苏炒货重庆观音桥店]身份证号:211222196407213048在黑名单中！");
    }
}