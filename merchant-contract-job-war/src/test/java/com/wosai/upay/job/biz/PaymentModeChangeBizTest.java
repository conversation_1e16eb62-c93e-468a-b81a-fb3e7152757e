package com.wosai.upay.job.biz;

import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.model.PaymentModeChangeReq;
import com.wosai.upay.job.util.ChatBotUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.env.Environment;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static com.wosai.upay.job.model.PaymentModeChangeReq.TARGET_PAYMENT_MODE_ALIPAY;

@RunWith(SpringRunner.class)
public class PaymentModeChangeBizTest {

    private PaymentModeChangeBiz paymentModeChangeBiz;

    private ChatBotUtil chatBotUtil;

    @MockBean
    private MerchantService merchantService;

    @MockBean
    private ApplicationApolloConfig applicationApolloConfig;

    @MockBean
    private Environment environment;

    @Before
    public void setUp() {
        paymentModeChangeBiz = new PaymentModeChangeBiz();
        chatBotUtil = new ChatBotUtil();
        ReflectionTestUtils.setField(chatBotUtil, "applicationApolloConfig", applicationApolloConfig);
        ReflectionTestUtils.setField(chatBotUtil, "environment", environment);
        ReflectionTestUtils.setField(paymentModeChangeBiz, "merchantService", merchantService);
        ReflectionTestUtils.setField(paymentModeChangeBiz, "chatBotUtil", chatBotUtil);
        Mockito.doReturn(new String[]{"prod"}).when(environment).getActiveProfiles();
    }

    @Test
    public void testFeishuWarn() {
        PaymentModeChangeReq req = new PaymentModeChangeReq();
        req.setMerchantId("1000000001");
        req.setTargetPaymentMode(TARGET_PAYMENT_MODE_ALIPAY);
        Map map = new HashMap();
        map.put("sn", "200000001");
        Mockito.doReturn(map).when(merchantService).getMerchantByMerchantId(Mockito.anyString());
        paymentModeChangeBiz.changePaymentMode(req);
    }
}
