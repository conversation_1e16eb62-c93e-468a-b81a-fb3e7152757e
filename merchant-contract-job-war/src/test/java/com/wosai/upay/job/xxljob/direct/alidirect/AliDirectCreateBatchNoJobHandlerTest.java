package com.wosai.upay.job.xxljob.direct.alidirect;

import com.alipay.api.request.AlipayOpenAgentCreateRequest;
import com.alipay.api.response.AlipayOpenAgentCreateResponse;
import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.biz.ContractTaskBiz;
import com.wosai.upay.job.biz.direct.AliDirectParamBuilder;
import com.wosai.upay.job.biz.direct.DirectStatusBiz;
import com.wosai.upay.job.enume.AliDirectApplyStatus;
import com.wosai.upay.job.enume.TaskStatus;
import com.wosai.upay.job.mapper.AliDirectApplyMapper;
import com.wosai.upay.job.mapper.ContractTaskMapper;
import com.wosai.upay.job.model.ContractTask;
import com.wosai.upay.job.model.DO.AliDirectApply;
import com.wosai.upay.job.model.DirectStatus;
import com.wosai.upay.job.monitor.MonitorLog;
import com.wosai.upay.job.xxljob.model.DirectJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import com.wosai.upay.merchant.contract.model.AliCommResponse;
import com.wosai.upay.merchant.contract.service.AliPayDirectService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Arrays;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;

@RunWith(MockitoJUnitRunner.class)
public class AliDirectCreateBatchNoJobHandlerTest {

    @InjectMocks
    private AliDirectCreateBatchNoJobHandler handler;

    @Mock
    private AliPayDirectService aliPayDirectService;

    @Mock
    private AliDirectParamBuilder aliParamBuilder;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private JobThreadPoolExecutor directJobHandlerThreadPool;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private AliDirectApplyMapper applyMapper;

    @Mock
    private DirectStatusBiz directStatusBiz;

    @Mock
    private ContractTaskBiz taskBiz;

    @Mock
    private ContractTaskMapper taskMapper;

    @Mock
    private MonitorLog monitorLog;

    @Value("${ali.direct}")
    private String aliDirectDevCode;


    @Test
    public void createBatchNoSuccess() throws InterruptedException {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.UN_SUBMIT.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        AliCommResponse<AlipayOpenAgentCreateRequest, AlipayOpenAgentCreateResponse> response = new AliCommResponse<>();
        AlipayOpenAgentCreateResponse createResponse = new AlipayOpenAgentCreateResponse();
        createResponse.setBatchNo("123456");
        response.setCode(200).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentCreate(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());
    }

    @Test
    public void createBatchNoSysException() throws InterruptedException {
        ContractTask task = new ContractTask();
        task.setMerchant_sn("merchant_sn").setStatus(TaskStatus.PENDING.getVal());
        taskBiz.insert(task);
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.UN_SUBMIT.getVal()).setTask_id(task.getId())
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        applyMapper.insertSelective(apply);
        AliCommResponse<AlipayOpenAgentCreateRequest, AlipayOpenAgentCreateResponse> response = new AliCommResponse<>();
        AlipayOpenAgentCreateResponse createResponse = new AlipayOpenAgentCreateResponse();
        createResponse.setBatchNo("123456");
        response.setCode(500).setResp(createResponse);
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentCreate(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(applyMapper, Mockito.times(1)).updateByPrimaryKeySelective(any());
    }

    @Test
    public void createBatchNoFail() throws InterruptedException {
        AliDirectApply apply = new AliDirectApply();
        apply.setMerchant_sn("merchant_sn").setStatus(AliDirectApplyStatus.UN_SUBMIT.getVal()).setTask_id(111L)
                .setPriority(new Date(System.currentTimeMillis() - 5000L));
        AliCommResponse<AlipayOpenAgentCreateRequest, AlipayOpenAgentCreateResponse> response = new AliCommResponse<>();
        AlipayOpenAgentCreateResponse createResponse = new AlipayOpenAgentCreateResponse();
        createResponse.setBatchNo("123456");
        response.setCode(405).setResp(createResponse).setMessage("失败");
        Mockito.doReturn(response).when(aliPayDirectService).alipayOpenAgentCreate(any());
        Mockito.doReturn(Arrays.asList(apply)).when(applyMapper).getAppliesByPrioirtyAndStatus(any(), any(), any(), any());
        DirectJobParam directJobParam = new DirectJobParam();
        directJobParam.setBatchSize(1);
        directJobParam.setQueryTime(1800000L);
        handler.execute(directJobParam);

        Mockito.verify(transactionTemplate, Mockito.times(1)).executeWithoutResult(any());
    }
}