package com.wosai.upay.job.biz.direct;

import com.wosai.upay.job.H2DbBaseTest;
import com.wosai.upay.job.model.DirectStatus;
import org.junit.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.kafka.core.KafkaTemplate;

import static org.junit.Assert.*;

public class DirectApplyStatusBizTest extends H2DbBaseTest {

    @Autowired
    private DirectStatusBiz directStatusBiz;

    @MockBean
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Test
    public void createOrUpdateDirectStatus() {
        directStatusBiz.createOrUpdateDirectStatus("createOrUpdateDirectStatus","dev_code",1,null);
        DirectStatus directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("createOrUpdateDirectStatus", "dev_code");
        assertNotNull(directStatus);

        directStatusBiz.createOrUpdateDirectStatus("createOrUpdateDirectStatus","dev_code",2,null);
        directStatus = directStatusBiz.getDirectStatusByMerchantSnAndDevCode("createOrUpdateDirectStatus", "dev_code");
        assertEquals(2, directStatus.getStatus().intValue());
    }

    @Test
    public void sendStatusChangeMessage() {
        directStatusBiz.sendStatusChangeMessage("sendStatusChangeMessage","dev_code",1,null);
    }

    @Test
    public void getDirectStatusByMerchantSnAndDevCode() {
    }
}