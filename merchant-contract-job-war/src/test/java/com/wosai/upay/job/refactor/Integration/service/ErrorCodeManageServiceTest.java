package com.wosai.upay.job.refactor.Integration.service;

import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.model.ErrorInfo;
import com.wosai.upay.job.model.dto.response.ErrorInfoPromptTextRspDTO;
import com.wosai.upay.job.service.ErrorCodeManageService;
import org.junit.Test;

import javax.annotation.Resource;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 结算账户类型支持收单机构服务测试
 *
 * <AUTHOR>
 * @date 2024/3/14 16:59
 */
public class ErrorCodeManageServiceTest extends BaseTest {

    @Resource
    private ErrorCodeManageService errorCodeManageService;

    @Test
    public void tempTest() {
        ErrorInfo rsp = errorCodeManageService.getPromptMessage("app",
                "拉卡拉巴拉巴拉对私+非身份证，收钱吧代付通路返回银行打款失败", "lkl");
        assertThat(rsp).isNotNull();
        assertThat(rsp.getMsg()).isEqualTo("账户验证不通过，请检查银行账号和账户名称是否正确。");
        assertThat(rsp.getCode()).isEqualTo("MP24");
    }

    @Test
    public void testGetAllEndPointPromptMessage() {
        List<ErrorInfoPromptTextRspDTO> rspList = errorCodeManageService.getAllEndPointPromptMessage("工商经营状态异常", "wechat_auth");
        assertThat(rspList).isNotNull().isNotEmpty();
    }

}
