package com.wosai.upay.job.xxljob.batch.contractevent;

import com.wosai.upay.job.handlers.EventHandlerContext;
import com.wosai.upay.job.mapper.ContractEventMapper;
import com.wosai.upay.job.model.ContractEvent;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ContractEventCreateNetInTaskJobHandlerTest {

    @Mock
    private ContractEventMapper contractEventMapper;

    @InjectMocks
    private ContractEventCreateNetInTaskJobHandler contractEventCreateNetInTaskJobHandler;

    @Mock
    private EventHandlerContext eventHandlerContext;

    private BatchJobParam batchJobParam;
    private ContractEvent contractEventA; // Renamed from contractEvent to avoid conflict
    private ContractEvent contractEventB; // Added from B

    @Before
    public void setUp() {
        batchJobParam = new BatchJobParam();
        contractEventA = new ContractEvent();
        contractEventA.setId(1L);
        contractEventB = new ContractEvent(); // Added from B
    }

    @Test
    public void queryTaskItems_PositiveQueryTimeAndBatchSize_ReturnsNonEmptyList() {
        batchJobParam.setQueryTime(1000L);
        batchJobParam.setBatchSize(10);

        List<ContractEvent> expectedEvents = new ArrayList<>();
        expectedEvents.add(new ContractEvent());
        when(contractEventMapper.selectNetInEvent(Mockito.anyString(), Mockito.eq(10))).thenReturn(expectedEvents);

        List<ContractEvent> actualEvents = contractEventCreateNetInTaskJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedEvents, actualEvents);
    }

    @Test
    public void queryTaskItems_ZeroQueryTime_PositiveBatchSize_ReturnsNonEmptyList() {
        batchJobParam.setQueryTime(0L);
        batchJobParam.setBatchSize(10);

        List<ContractEvent> expectedEvents = new ArrayList<>();
        expectedEvents.add(new ContractEvent());
        when(contractEventMapper.selectNetInEvent(Mockito.anyString(), Mockito.eq(10))).thenReturn(expectedEvents);

        List<ContractEvent> actualEvents = contractEventCreateNetInTaskJobHandler.queryTaskItems(batchJobParam);

        assertEquals(expectedEvents, actualEvents);
    }

    @Test
    public void queryTaskItems_PositiveQueryTime_ZeroBatchSize_ReturnsEmptyList() {
        batchJobParam.setQueryTime(1000L);
        batchJobParam.setBatchSize(0);

        when(contractEventMapper.selectNetInEvent(Mockito.anyString(), Mockito.eq(0))).thenReturn(new ArrayList<>());

        List<ContractEvent> actualEvents = contractEventCreateNetInTaskJobHandler.queryTaskItems(batchJobParam);

        assertEquals(new ArrayList<>(), actualEvents);
    }

    @Test
    public void queryTaskItems_ZeroQueryTime_ZeroBatchSize_ReturnsEmptyList() {
        batchJobParam.setQueryTime(0L);
        batchJobParam.setBatchSize(0);

        when(contractEventMapper.selectNetInEvent(Mockito.anyString(), Mockito.eq(0))).thenReturn(new ArrayList<>());

        List<ContractEvent> actualEvents = contractEventCreateNetInTaskJobHandler.queryTaskItems(batchJobParam);

        assertEquals(new ArrayList<>(), actualEvents);
    }

    @Test
    public void queryTaskItems_EmptyResultFromMapper_ReturnsEmptyList() {
        batchJobParam.setQueryTime(1000L);
        batchJobParam.setBatchSize(10);

        when(contractEventMapper.selectNetInEvent(Mockito.anyString(), Mockito.eq(10))).thenReturn(new ArrayList<>());

        List<ContractEvent> actualEvents = contractEventCreateNetInTaskJobHandler.queryTaskItems(batchJobParam);

        assertEquals(new ArrayList<>(), actualEvents);
    }

    @Test
    public void doHandleSingleData_EventStatusPending_EventHandled() throws Exception {
        contractEventA.setStatus(ContractEvent.STATUS_PENDING);
        when(contractEventMapper.selectByPrimaryKey(contractEventA.getId())).thenReturn(contractEventA);

        contractEventCreateNetInTaskJobHandler.doHandleSingleData(contractEventA);

        verify(eventHandlerContext, times(1)).handle(contractEventA);
    }

    @Test
    public void doHandleSingleData_EventStatusNotPending_EventNotHandled() throws Exception {
        contractEventA.setStatus(ContractEvent.STATUS_SUCCESS);
        when(contractEventMapper.selectByPrimaryKey(contractEventA.getId())).thenReturn(contractEventA);

        contractEventCreateNetInTaskJobHandler.doHandleSingleData(contractEventA);

        verify(eventHandlerContext, never()).handle(contractEventA);
    }

    @Test
    public void doHandleSingleData_ExceptionDuringHandling_ExceptionLogged() throws Exception {
        contractEventA.setStatus(ContractEvent.STATUS_PENDING);
        when(contractEventMapper.selectByPrimaryKey(contractEventA.getId())).thenReturn(contractEventA);
        doThrow(new RuntimeException("Test exception")).when(eventHandlerContext).handle(contractEventA);

        contractEventCreateNetInTaskJobHandler.doHandleSingleData(contractEventA);
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        contractEventB.setId(123L);
        String lockKey = contractEventCreateNetInTaskJobHandler.getLockKey(contractEventB);
        assertEquals("ContractEventCreateNetInTaskJobHandler:123", lockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        contractEventB.setId(null);
        String lockKey = contractEventCreateNetInTaskJobHandler.getLockKey(contractEventB);
        assertEquals("ContractEventCreateNetInTaskJobHandler:null", lockKey);
    }
}
