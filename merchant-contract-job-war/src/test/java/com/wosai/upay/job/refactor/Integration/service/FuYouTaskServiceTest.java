package com.wosai.upay.job.refactor.Integration.service;


import com.wosai.upay.job.BaseTest;;
import com.wosai.upay.job.model.dto.response.InsertFuYouDayZeroTaskResultRspDTO;
import com.wosai.upay.job.refactor.service.impl.FouYouTaskServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.annotation.Resource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 富友任务调度服务测试
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
public class FuYouTaskServiceTest extends BaseTest {

    @Resource
    private FouYouTaskServiceImpl fouYouTaskService;


    @Test
    public void batchInsertD0Task() {
        fouYouTaskService.insertFuYouOpenDayZeroTask("21690003630387", false, null);
    }

    @Test
    public void testInsertOpenDayZeroTask() {
        InsertFuYouDayZeroTaskResultRspDTO rspDTO = fouYouTaskService.insertFuYouOpenDayZeroTask("21690003716577", false, null);
        assertThat(rspDTO).isNotNull();
        assertThat(rspDTO.isInsertSuccess()).isTrue();
    }

    @Test
    public void testInsertOpenDayZeroCompleteTask() {
        InsertFuYouDayZeroTaskResultRspDTO rspDTO = fouYouTaskService.insertFuYouOpenDayZeroTask("21690003696883", true, null);
        assertThat(rspDTO).isNotNull();
        assertThat(rspDTO.isInsertSuccess()).isFalse();
    }

    @Test
    public void testInsertOpenDayZeroCheckTask() {
        InsertFuYouDayZeroTaskResultRspDTO rspDTO = fouYouTaskService.insertFuYouOpenDayZeroTask("21690003718437", false, 10);
        assertThat(rspDTO).isNotNull();
    }

    @Test
    public void testProcessFuYouOpenDayZeroTaskByTaskId() {
        fouYouTaskService.processFuYouOpenDayZeroTaskByTaskId(43997906L, true);
    }


    @Test
    public void testQueryFuYouOpenDayZeroResultByTaskId() {
        String message = fouYouTaskService.queryFuYouOpenDayZeroResultByTaskId(43998003L);
        assertThat(message).isNotBlank();
    }

    @Test
    public void testProcessFuYouOpenDayZeroTask() {
        fouYouTaskService.processFuYouOpenDayZeroTask(10, 10, false);
    }

    @Test
    public void testQueryFuYouOpenDayZeroResult() {
        fouYouTaskService.queryFuYouOpenDayZeroResultTask(10, 10, false);
    }

    @Test
    public void testFuYouOpenDayZero() {
        String merchantSn = "21690003630387";
        fouYouTaskService.insertFuYouOpenDayZeroTask("21690003630387", true, null);
        fouYouTaskService.processFuYouOpenDayZeroTask(10, 10, false);
        fouYouTaskService.queryFuYouOpenDayZeroResultTask(10, 10, false);
    }


}
