package com.wosai.upay.job.xxljob.batch.paylater;

import com.alibaba.fastjson.JSONObject;
import com.wosai.upay.job.Constants.PayLaterConstant;
import com.wosai.upay.job.biz.PayLaterBiz;
import com.wosai.upay.job.mapper.PayLaterApplyMapper;
import com.wosai.upay.job.model.payLater.PayLaterApply;
import com.wosai.upay.job.model.payLater.StatusLog;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PayLaterZhimaMerchantTimeoutJobHandlerTest {

    @InjectMocks
    private PayLaterZhimaMerchantTimeoutJobHandler handler;

    @Mock
    private PayLaterBiz payLaterBiz;

    @Mock
    private PayLaterApplyMapper payLaterApplyMapper;

    private PayLaterApply payLaterApply;
    private BatchJobParam param;

    private PayLaterApply payLaterApplyB;

    @Before
    public void setUp() {
        payLaterApply = new PayLaterApply();
        param = new BatchJobParam();
        param.setBatchSize(10);
        param.setQueryTime(System.currentTimeMillis());

        payLaterApplyB = new PayLaterApply();
        payLaterApplyB.setId(1L);
        payLaterApplyB.setMerchant_sn("testMerchant");
    }

    @Test
    public void getLockKey_WithNonNullId_ReturnsCorrectLockKey() {
        payLaterApply.setId(123L);
        String expectedLockKey = "PayLaterZhimaMerchantTimeoutJobHandler:123";
        String actualLockKey = handler.getLockKey(payLaterApply);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void getLockKey_WithNullId_ReturnsLockKeyWithNull() {
        payLaterApply.setId(null);
        String expectedLockKey = "PayLaterZhimaMerchantTimeoutJobHandler:null";
        String actualLockKey = handler.getLockKey(payLaterApply);
        assertEquals(expectedLockKey, actualLockKey);
    }

    @Test
    public void queryTaskItems_NonEmptyList_ReturnsList() {
        List<PayLaterApply> expectedList = new ArrayList<>();
        expectedList.add(new PayLaterApply());

        when(payLaterBiz.getPayLaterTasks(Collections.singletonList(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING), param.getBatchSize(), param.getQueryTime()))
                .thenReturn(expectedList);

        List<PayLaterApply> result = handler.queryTaskItems(param);

        assertEquals(expectedList, result);
    }

    @Test
    public void queryTaskItems_EmptyList_ReturnsEmptyList() {
        List<PayLaterApply> result = handler.queryTaskItems(param);
        assertTrue(result.isEmpty());
    }

    @Test
    public void doHandleSingleData_ProcessStatusNotZhimaApplying_NoModification() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZFT_APPLYING);

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApplyB.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApplyB);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApplyB.getId());
        verify(payLaterBiz, never()).modifyPayLaterApply(any(), anyInt(), anyInt(), anyInt(), anyString(), anyInt());
    }

    @Test
    public void doHandleSingleData_ProcessStatusListEmpty_NoModification() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING);
        apply.setExtra("{}");

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApplyB.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApplyB);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApplyB.getId());
        verify(payLaterBiz, never()).modifyPayLaterApply(any(), anyInt(), anyInt(), anyInt(), anyString(), anyInt());
    }

    @Test
    public void doHandleSingleData_ZhimaApplyingStatusTimeout_ModifyToFail() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING);

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(PayLaterConstant.Extra.PROCESS_STATUS, Arrays.asList(
                new StatusLog(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING, new Date().getTime() - 31 * 24 * 60 * 60 * 1000L, "test")
        ));
        apply.setExtra(JSONObject.toJSONString(extraMap));

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApplyB.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApplyB);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApplyB.getId());
        verify(payLaterBiz, times(1)).modifyPayLaterApply(eq(payLaterApplyB), eq(PayLaterConstant.Status.ZHIMA_FAIL),
                eq(PayLaterConstant.SubStatus.FAIL), eq(PayLaterConstant.ProcessStatus.FAIL), eq("芝麻商户审核失败"), eq(0));
    }

    @Test
    public void doHandleSingleData_ZhimaApplyingStatusNotTimeout_NoModification() {
        PayLaterApply apply = new PayLaterApply();
        apply.setProcess_status(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING);

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(PayLaterConstant.Extra.PROCESS_STATUS, Arrays.asList(
                new StatusLog(PayLaterConstant.ProcessStatus.ZHIMA_APPLYING, new Date().getTime(), "test")
        ));
        apply.setExtra(JSONObject.toJSONString(extraMap));

        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApplyB.getId())).thenReturn(apply);

        handler.doHandleSingleData(payLaterApplyB);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApplyB.getId());
        verify(payLaterBiz, never()).modifyPayLaterApply(any(), anyInt(), anyInt(), anyInt(), anyString(), anyInt());
    }

    @Test
    public void doHandleSingleData_ExceptionDuringProcessing_ModifyToFail() {
        when(payLaterApplyMapper.selectByPrimaryKey(payLaterApplyB.getId())).thenThrow(new RuntimeException("Test Exception"));

        handler.doHandleSingleData(payLaterApplyB);

        verify(payLaterApplyMapper, times(1)).selectByPrimaryKey(payLaterApplyB.getId());
        verify(payLaterBiz, times(1)).modifyPayLaterApply(eq(payLaterApplyB), eq(PayLaterConstant.Status.ZHIMA_FAIL),
                eq(PayLaterConstant.SubStatus.FAIL), eq(PayLaterConstant.ProcessStatus.FAIL), eq(PayLaterConstant.Result.ZHIMA_AUDIT_FAIL), eq(0));
    }
}
